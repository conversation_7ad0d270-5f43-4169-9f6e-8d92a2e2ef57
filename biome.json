{"$schema": "https://biomejs.dev/schemas/2.1.2/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": true, "includes": ["**/*.js", "**/*.mjs", "**/*.ts", "**/*.vue", "**/*.json", "**/*.jsonc"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf"}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noExcessiveCognitiveComplexity": "warn", "noVoid": "off"}, "correctness": {"noUnusedVariables": "warn", "useExhaustiveDependencies": "off"}, "style": {"noNonNullAssertion": "off", "useConst": "error", "useTemplate": "warn"}, "suspicious": {"noExplicitAny": "warn", "noArrayIndexKey": "warn"}, "nursery": {"useSortedClasses": "off"}}}, "javascript": {"formatter": {"quoteStyle": "single", "semicolons": "always", "trailingCommas": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParentheses": "asNeeded"}, "globals": ["$fetch", "navigateTo", "useNuxtApp", "useRuntimeConfig", "useState", "useCookie", "useRoute", "useRouter", "definePageMeta", "defineNuxtRouteMiddleware", "defineNuxtPlugin", "useHead", "useSeoMeta", "useI18n", "useToast", "ref", "reactive", "computed", "watch", "onMounted", "onUnmounted", "nextTick"]}, "json": {"formatter": {"indentStyle": "space", "indentWidth": 2}}, "css": {"formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2}}, "overrides": [{"includes": ["**/*.vue"], "linter": {"rules": {"style": {"useConst": "off"}, "correctness": {"noUnusedVariables": "off"}}}}, {"includes": ["**/nuxt.config.ts", "**/nuxt.config.js"], "linter": {"rules": {"style": {"noDefaultExport": "off"}}}}, {"includes": ["**/*.d.ts"], "linter": {"rules": {"correctness": {"noUnusedVariables": "off"}}}}], "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}