export interface Customer {
  _id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  displayName?: string;
  avatar?: string;
  bio?: string;
  phone?: string;
  dateOfBirth?: Date;
  gender?: 'male' | 'female' | 'other';
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  siteId: string;
  points: number;
  customershipLevel: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond';
  isActive: boolean;
  isEmailVerified: boolean;
  lastLogin?: Date;
  preferences: CustomerPreferences;
  socialLinks?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface CustomerPreferences {
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
  };
  privacy: {
    showProfile: boolean;
    showActivity: boolean;
    allowMessages: boolean;
  };
  language: string;
  timezone: string;
  newsletter: boolean;
}

export interface CustomerActivity {
  _id: string;
  customerId: string;
  type: 'login' | 'purchase' | 'comment' | 'like' | 'share' | 'review';
  description: string;
  points?: number;
  metadata?: Record<string, any>;
  createdAt: Date;
}

export interface CustomerAnalytics {
  totalCustomers: number;
  activeCustomers: number;
  newCustomersThisMonth: number;
  customersByLevel: Array<{
    level: string;
    count: number;
  }>;
  topCustomers: Array<{
    customer: Customer;
    points: number;
    activities: number;
  }>;
  activityByPeriod: Array<{
    period: string;
    activities: number;
    newCustomers: number;
  }>;
}
