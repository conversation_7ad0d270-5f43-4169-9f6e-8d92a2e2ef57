export interface Product {
  _id: string;
  name: string;
  description?: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category: string;
  tags: string[];
  sku: string;
  stock: number;
  isActive: boolean;
  isFeatured: boolean;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  variants?: Array<{
    name: string;
    options: string[];
    price?: number;
  }>;
  siteId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProductFilters {
  category?: string;
  isActive?: boolean;
  isFeatured?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

export interface Cart {
  _id: string;
  userId?: string;
  userId?: string;
  siteId: string;
  items: CartItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  currency: string;
  expiresAt: Date;
}

export interface CartItem {
  productId: string;
  quantity: number;
  price: number;
  variant?: {
    name: string;
    value: string;
  };
}

export interface Order {
  _id: string;
  orderNumber: string;
  userId?: string;
  userId?: string;
  siteId: string;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  currency: string;
  status:
    | 'pending'
    | 'confirmed'
    | 'processing'
    | 'shipped'
    | 'delivered'
    | 'cancelled'
    | 'refunded';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod: string;
  shippingAddress: Address;
  billingAddress?: Address;
  notes?: string;
  trackingNumber?: string;
  shippedAt?: Date;
  deliveredAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrderItem {
  productId: string;
  name: string;
  quantity: number;
  price: number;
  variant?: {
    name: string;
    value: string;
  };
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone?: string;
}

export interface ProductAnalytics {
  totalProducts: number;
  totalSales: number;
  totalOrders: number;
  topProducts: Product[];
  salesByPeriod: Array<{
    period: string;
    sales: number;
    orders: number;
  }>;
}
