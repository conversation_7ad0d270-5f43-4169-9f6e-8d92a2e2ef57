export interface Site {
  _id: string;
  name: string;
  description?: string;
  domain: string;
  subdomain?: string;
  customDomain?: string;
  logo?: string;
  favicon?: string;
  ownerId: string;
  ownerType: 'user' | 'member';
  settings: SiteSettings;
  theme: SiteTheme;
  isActive: boolean;
  isPremium: boolean;
  subscription?: SiteSubscription;
  analytics: SiteAnalytics;
  createdAt: Date;
  updatedAt: Date;
}

export interface SiteSettings {
  seo: {
    title?: string;
    description?: string;
    keywords?: string[];
    ogImage?: string;
  };
  contact: {
    email?: string;
    phone?: string;
    address?: string;
  };
  social: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    youtube?: string;
  };
  features: {
    blog: boolean;
    ecommerce: boolean;
    membership: boolean;
    analytics: boolean;
  };
  privacy: {
    allowIndexing: boolean;
    cookieConsent: boolean;
    dataCollection: boolean;
  };
  maintenance?: {
    isEnabled: boolean;
    message: string;
    allowedIPs: string[];
    scheduledStart?: Date | null;
    scheduledEnd?: Date | null;
    showMaintenancePage: boolean;
  };
}

export interface SiteTheme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
  fonts: {
    heading: string;
    body: string;
  };
  layout: {
    header: string;
    footer: string;
    sidebar: string;
  };
}

export interface SiteSubscription {
  plan: 'free' | 'basic' | 'pro' | 'enterprise';
  status: 'active' | 'cancelled' | 'expired' | 'past_due';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  trialEnd?: Date;
}

export interface SiteAnalytics {
  totalVisitors: number;
  uniqueVisitors: number;
  pageViews: number;
  bounceRate: number;
  averageSessionDuration: number;
  topPages: Array<{
    path: string;
    views: number;
    uniqueViews: number;
  }>;
  trafficSources: Array<{
    source: string;
    visitors: number;
    percentage: number;
  }>;
}

export interface SiteTemplate {
  _id: string;
  name: string;
  description: string;
  preview: string;
  category: string;
  tags: string[];
  isPremium: boolean;
  config: Record<string, any>;
}
