export interface BlogPost {
  _id: string;
  title: string;
  content: string;
  excerpt?: string;
  slug: string;
  featuredImage?: string;
  category: string;
  tags: string[];
  isPublished: boolean;
  publishedAt?: Date;
  authorId: string;
  authorType: 'user' | 'member';
  siteId: string;
  seo: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
  };
  analytics: {
    views: number;
    likes: number;
    shares: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface BlogComment {
  _id: string;
  content: string;
  authorId: string;
  authorType: 'user' | 'member';
  postId: string;
  parentId?: string;
  isApproved: boolean;
  siteId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface BlogFilters {
  category?: string;
  isPublished?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

export interface BlogAnalytics {
  totalPosts: number;
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  topPosts: BlogPost[];
  viewsByPeriod: Array<{
    period: string;
    views: number;
    posts: number;
  }>;
}

export interface BlogCategory {
  name: string;
  slug: string;
  description?: string;
  color?: string;
}
