export interface TypeField {
  name: string;
  type: string;
  required: boolean;
  options?: string[];
  defaultValue?: any;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

export interface TypeSettings {
  showInMenu: boolean;
  showInHomepage: boolean;
  allowSubtypes: boolean;
  hasPrice: boolean;
  hasInventory: boolean;
  hasGallery: boolean;
  hasReviews: boolean;
  hasTags: boolean;
  hasCategories: boolean;
  hasCustomFields: boolean;
  hasSeo: boolean;
  hasMeta: boolean;
}

export interface TypeSeo {
  title: string;
  description: string;
  keywords: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
}

export interface Type {
  _id: string;
  name: string;
  description: string;
  slug: string;
  contentType: string;
  icon: string;
  status: 'active' | 'inactive' | 'draft' | 'archived';
  order: number;
  seo: TypeSeo;
  settings: TypeSettings;
  fields: TypeField[];
  usageCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTypeData {
  name: string;
  description: string;
  slug: string;
  contentType: string;
  icon?: string;
  status?: string;
  order?: number;
  seo?: Partial<TypeSeo>;
  settings?: Partial<TypeSettings>;
  fields?: TypeField[];
}

export interface UpdateTypeData extends Partial<CreateTypeData> {}

export interface TypeAnalytics {
  totalTypes: number;
  activeTypes: number;
  totalUsage: number;
  averageUsagePerType: number;
  topContentType: string;
  growthRate: number;
  typesByContentType: Record<string, number>;
  recentTypes: Type[];
} 