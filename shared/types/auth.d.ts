export interface User {
  _id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: 'user' | 'admin' | 'moderator';
  isActive: boolean;
  isEmailVerified: boolean;
  twoFactorEnabled: boolean;
  lastLogin?: Date;
  avatar?: string;
  cover?: string;
  moneyPoint: number;
  goldPoint: number;
  googleId?: string;
  referralCode: string;
  totalReferrals: number;
  privacySettings: {
    showEmail: boolean;
    showProfile: boolean;
    allowInvites: boolean;
  };
  notifications: {
    email: boolean;
    marketing: boolean;
    referrals: boolean;
  };
  createdAt: Date | string;
  updatedAt: Date | string;
  exp?: number;
}

export interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  loading: boolean;
  error: string | null;
}

interface LoginAttempt {
  email: string;
  ip: string;
  timestamp: string;
  userAgent: string;
  success: boolean;
  reason?: string;
}
