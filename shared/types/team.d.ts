export interface Team {
  id: string;
  name: string;
  description?: string;
  siteId: string;
  createdAt: string;
  updatedAt: string;
}

export interface TeamMember {
  id: string;
  userId: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'owner' | 'admin' | 'editor' | 'viewer';
  status: 'active' | 'inactive' | 'suspended';
  joinedAt: string;
  permissions: string[];
}

export interface TeamRole {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  isDefault: boolean;
  isEditable: boolean;
}

export interface TeamInvitation {
  id: string;
  email: string;
  role: 'admin' | 'editor' | 'viewer';
  message?: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  invitedBy: string;
  invitedAt: string;
  expiresAt: string;
  token: string;
}

export interface TeamSettings {
  allowPublicJoin: boolean;
  requireApproval: boolean;
  maxMembers?: number;
  allowedDomains?: string[];
  autoAssignRole?: string;
}

export interface TeamPermission {
  id: string;
  name: string;
  description: string;
  category: string;
}

export interface TeamActivity {
  id: string;
  action: string;
  description: string;
  userId?: string;
  userId?: string;
  userType: 'user' | 'member';
  targetType: string;
  targetId?: string;
  metadata?: Record<string, any>;
  createdAt: string;
}

export interface TeamAnalytics {
  totalMembers: number;
  activeMembers: number;
  totalInvitations: number;
  pendingInvitations: number;
  membersByRole: Array<{
    role: string;
    count: number;
  }>;
  activityByPeriod: Array<{
    period: string;
    activities: number;
  }>;
}
