# ระบบจัดการคลังรูปภาพ (Galleries)

ระบบจัดการคลังรูปภาพที่สามารถใช้ร่วมกันได้สำหรับสินค้า หมวดหมู่ บทความ บล็อก และอื่นๆ

## โครงสร้างไฟล์

```
app/pages/dashboard/[siteId]/galleries/
├── index.vue                    # หน้าแสดงรายการแกลเลอรี
├── create.vue                   # หน้าสร้างแกลเลอรีใหม่
└── [galleryId]/
    ├── index.vue               # หน้าดูรายละเอียดแกลเลอรี
    └── edit.vue                # หน้าแก้ไขแกลเลอรี
```

## คุณสมบัติหลัก

### 1. หน้าแสดงรายการแกลเลอรี (`index.vue`)
- แสดงรายการแกลเลอรีทั้งหมด
- กรองตามประเภท (สินค้า, หมวดหมู่, บทความ, บล็อก, แบนเนอร์, อื่นๆ)
- ค้นหาแกลเลอรี
- แสดงสถิติ (จำนวนแกลเลอรี, รูปภาพทั้งหมด, ขนาดรวม)
- สลับมุมมอง Grid/List
- การจัดการแกลเลอรี (ดู, แก้ไข, ลบ)

### 2. หน้าสร้างแกลเลอรีใหม่ (`create.vue`)
- กรอกข้อมูลพื้นฐาน (ชื่อ, คำอธิบาย, ประเภท)
- อัปโหลดรูปภาพแบบ Drag & Drop
- รองรับไฟล์หลายรูปพร้อมกัน
- ตรวจสอบประเภทและขนาดไฟล์
- เพิ่ม Alt Text สำหรับแต่ละรูป
- แสดงตัวอย่างและสถิติ

### 3. หน้าดูรายละเอียดแกลเลอรี (`[galleryId]/index.vue`)
- แสดงรูปภาพทั้งหมดในแกลเลอรี
- ค้นหาและเรียงลำดับรูปภาพ
- สลับมุมมอง Grid/List
- ดูรายละเอียดรูปภาพแบบ Modal
- คัดลอก URL รูปภาพ
- ดาวน์โหลดรูปภาพ
- ลบรูปภาพ
- แสดงสถิติการใช้งาน

### 4. หน้าแก้ไขแกลเลอรี (`[galleryId]/edit.vue`)
- แก้ไขข้อมูลพื้นฐาน
- เพิ่มรูปภาพใหม่
- แก้ไข Alt Text ของรูปภาพ
- เลือกและลบรูปภาพหลายรูปพร้อมกัน
- แสดงสถานะการใช้งานของรูปภาพ

## ประเภทแกลเลอรี

1. **สินค้า** (product) - สำหรับรูปภาพสินค้า
2. **หมวดหมู่** (category) - สำหรับรูปภาพหมวดหมู่
3. **บทความ** (article) - สำหรับรูปภาพบทความ
4. **บล็อก** (blog) - สำหรับรูปภาพบล็อก
5. **แบนเนอร์** (banner) - สำหรับรูปภาพแบนเนอร์
6. **อื่นๆ** (other) - สำหรับรูปภาพประเภทอื่น

## การตรวจสอบไฟล์

- **ประเภทไฟล์**: JPG, PNG, GIF, WebP
- **ขนาดไฟล์**: สูงสุด 5MB ต่อไฟล์
- **Alt Text**: ข้อความทางเลือกสำหรับการเข้าถึง

## คุณสมบัติเพิ่มเติม

### การอัปโหลดรูปภาพ
- รองรับ Drag & Drop
- อัปโหลดหลายไฟล์พร้อมกัน
- แสดง Progress และ Preview
- ตรวจสอบไฟล์อัตโนมัติ

### การจัดการรูปภาพ
- เลือกหลายรูปพร้อมกัน
- ลบแบบกลุ่ม
- คัดลอก URL
- ดาวน์โหลดรูปภาพ
- แสดงสถานะการใช้งาน

### การค้นหาและกรอง
- ค้นหาตามชื่อและคำอธิบาย
- กรองตามประเภทแกลเลอรี
- เรียงลำดับตามวันที่, ชื่อ, ขนาด
- สลับทิศทางการเรียง

### สถิติและข้อมูล
- จำนวนแกลเลอรีทั้งหมด
- จำนวนรูปภาพทั้งหมด
- ขนาดไฟล์รวม
- ขนาดเฉลี่ยต่อรูป
- จำนวนการใช้งาน

## การใช้งาน

### สร้างแกลเลอรีใหม่
1. คลิก "สร้างแกลเลอรีใหม่"
2. กรอกชื่อและคำอธิบาย
3. เลือกประเภทแกลเลอรี
4. อัปโหลดรูปภาพ
5. เพิ่ม Alt Text
6. บันทึก

### จัดการรูปภาพ
1. เข้าไปในแกลเลอรี
2. เลือกรูปภาพที่ต้องการ
3. ใช้เมนู Actions หรือ Bulk Actions
4. ดำเนินการตามต้องการ

### แก้ไขแกลเลอรี
1. คลิก "แก้ไข" ในแกลเลอรี
2. แก้ไขข้อมูลพื้นฐาน
3. เพิ่ม/ลบ/แก้ไขรูปภาพ
4. บันทึกการเปลี่ยนแปลง

## เทคโนโลยีที่ใช้

- **Vue 3** - Framework หลัก
- **Nuxt 3** - Meta framework
- **Vuelidate** - การตรวจสอบฟอร์ม
- **Nuxt UI** - UI Components
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling

## การปรับแต่ง

### เพิ่มประเภทแกลเลอรีใหม่
แก้ไขใน `galleryTypes` ในแต่ละไฟล์:

```typescript
const galleryTypes = ref([
  // ประเภทที่มีอยู่...
  { value: 'new-type', label: 'ประเภทใหม่', icon: 'i-heroicons-star', color: 'indigo' },
]);
```

### เปลี่ยนขนาดไฟล์สูงสุด
แก้ไขใน `handleFiles` function:

```typescript
// เปลี่ยนจาก 5MB เป็น 10MB
if (file.size > 10 * 1024 * 1024) {
  // แสดงข้อผิดพลาด
}
```

### เพิ่มประเภทไฟล์ที่รองรับ
แก้ไขใน `accept` attribute และ validation:

```html
<input accept="image/*,video/*" />
```

```typescript
if (!file.type.startsWith('image/') && !file.type.startsWith('video/')) {
  // แสดงข้อผิดพลาด
}
```

## การพัฒนาต่อ

### คุณสมบัติที่อาจเพิ่มในอนาคต
1. **การแก้ไขรูปภาพ** - Crop, Resize, Filter
2. **การจัดกลุ่มรูปภาพ** - Folder หรือ Tag
3. **การซิงค์กับ Cloud Storage** - AWS S3, Google Cloud
4. **การปรับขนาดอัตโนมัติ** - Multiple sizes
5. **การค้นหาด้วย AI** - Image recognition
6. **การแชร์แกลเลอรี** - Public gallery links
7. **การสำรองข้อมูล** - Backup และ restore
8. **การจัดการสิทธิ์** - User permissions

### API Endpoints ที่ควรมี
```
GET    /api/galleries              # ดึงรายการแกลเลอรี
POST   /api/galleries              # สร้างแกลเลอรีใหม่
GET    /api/galleries/:id          # ดึงข้อมูลแกลเลอรี
PUT    /api/galleries/:id          # อัปเดตแกลเลอรี
DELETE /api/galleries/:id          # ลบแกลเลอรี

GET    /api/galleries/:id/images   # ดึงรูปภาพในแกลเลอรี
POST   /api/galleries/:id/images   # อัปโหลดรูปภาพ
PUT    /api/images/:id             # อัปเดตข้อมูลรูปภาพ
DELETE /api/images/:id             # ลบรูปภาพ
```

## การทดสอบ

### Test Cases ที่ควรมี
1. **การสร้างแกลเลอรี** - ทดสอบการสร้างแกลเลอรีใหม่
2. **การอัปโหลดรูปภาพ** - ทดสอบการอัปโหลดไฟล์ต่างๆ
3. **การตรวจสอบไฟล์** - ทดสอบการตรวจสอบประเภทและขนาด
4. **การค้นหาและกรอง** - ทดสอบการค้นหาและกรองข้อมูล
5. **การลบรูปภาพ** - ทดสอบการลบรูปภาพเดี่ยวและหลายรูป
6. **การแก้ไขข้อมูล** - ทดสอบการแก้ไขข้อมูลแกลเลอรี

## สรุป

ระบบจัดการคลังรูปภาพนี้ให้ความสะดวกในการจัดเก็บและจัดการรูปภาพสำหรับใช้งานในส่วนต่างๆ ของเว็บไซต์ ด้วยการออกแบบที่ยืดหยุ่นและใช้งานง่าย ผู้ใช้สามารถจัดการรูปภาพได้อย่างมีประสิทธิภาพ

การพัฒนาในอนาคตสามารถเพิ่มคุณสมบัติเพิ่มเติมได้ตามความต้องการ เช่น การแก้ไขรูปภาพ การจัดกลุ่ม หรือการซิงค์กับ Cloud Storage เพื่อให้ระบบมีความสมบูรณ์มากยิ่งขึ้น