# การอัปเดตระบบ SEO และ i18n

## สิ่งที่แก้ไข

### 1. แก้ไขปัญหา i18n Keys ที่ขาดหายไป

#### ปัญหาเดิม:
```
WARN [intlify] Not found 'seo.default.title' key in 'th' locale messages.
WARN [intlify] Not found 'seo.default.description' key in 'th' locale messages.
WARN [intlify] Not found 'seo.default.keywords' key in 'th' locale messages.
WARN [intlify] Not found 'auth.signin.title' key in 'th' locale messages.
WARN [intlify] Not found 'auth.signin.description' key in 'th' locale messages.
```

#### การแก้ไข:
- ✅ แก้ไขโครงสร้าง JSON ในไฟล์ `i18n/locales/th-TH.json`
- ✅ แก้ไขโครงสร้าง JSON ในไฟล์ `i18n/locales/en-US.json`
- ✅ แก้ไขโครงสร้าง JSON ในไฟล์ `i18n/locales/lo-LA.json`
- ✅ เปลี่ยนจาก `"auth.signin.title"` เป็น `"auth": { "signin": { "title": "..." } }`

### 2. ปรับปรุงระบบ useSeoMeta ให้ยืดหยุ่นและใช้งานง่าย

#### ฟีเจอร์ใหม่:
- ✅ รองรับ Reactive Values (ref, computed)
- ✅ เพิ่ม Composables เฉพาะหน้า
- ✅ ปรับปรุง TypeScript Interface
- ✅ เพิ่มฟังก์ชัน updateSeo() สำหรับอัปเดต SEO แบบ dynamic
- ✅ รองรับ Structured Data
- ✅ ปรับปรุงการจัดการ Meta Tags

#### Composables ใหม่:
- `useHomeSeo()` - สำหรับหน้าหลัก
- `useBlogSeo()` - สำหรับหน้าบล็อก
- `useProductsSeo()` - สำหรับหน้าสินค้า
- `useDashboardSeo()` - สำหรับหน้า Dashboard
- `useAuthSeo()` - สำหรับหน้า Login/Signup
- `useHelpSeo()` - สำหรับหน้าช่วยเหลือ
- `useAnalyticsSeo()` - สำหรับหน้าวิเคราะห์
- `useOrdersSeo()` - สำหรับหน้าคำสั่งซื้อ
- `useCustomersSeo()` - สำหรับหน้าลูกค้า
- `useErrorSeo()` - สำหรับหน้า Error
- `useStructuredData()` - สำหรับ Structured Data

### 3. อัปเดตไฟล์ที่ใช้ useSeoMeta

#### ไฟล์ที่อัปเดตแล้ว:
- ✅ `app/app.vue` - ใช้ `useHomeSeo()`
- ✅ `app/pages/index.vue` - ใช้ `useHomeSeo()`
- ✅ `app/pages/help.vue` - ใช้ `useHelpSeo()`
- ✅ `app/pages/dashboard/[siteId]/blog/index.vue` - ใช้ `useBlogSeo()`
- ✅ `app/pages/dashboard/[siteId]/products/index.vue` - ใช้ `useProductsSeo()`

### 4. สร้างเอกสารการใช้งาน

- ✅ สร้าง `app/composables/README.md` - คู่มือการใช้งานแบบครบถ้วน
- ✅ ตัวอย่างการใช้งานในแต่ละภาษา (ไทย, อังกฤษ, ลาว)
- ✅ Best Practices และการแก้ไขปัญหา

## การใช้งานใหม่

### ก่อนหน้า (เก่า):
```typescript
useSeoMeta({
  title: t('home') + ' - ' + t('seo.default.title'),
  description: t('seo.default.description'),
  keywords: t('seo.default.keywords'),
  type: 'website'
});
```

### ตอนนี้ (ใหม่):
```typescript
// ง่ายขึ้น - ใช้ composable เฉพาะ
useHomeSeo();

// หรือแบบ reactive
const title = computed(() => `${productName.value} - ร้านค้า`);
useSeoMeta({
  title,
  description: computed(() => productDescription.value),
  type: 'product'
});

// อัปเดตแบบ dynamic
const { updateSeo } = useSeoMeta({ title: 'เริ่มต้น' });
updateSeo({ title: 'ใหม่', description: 'คำอธิบายใหม่' });
```

## ประโยชน์ที่ได้รับ

1. **ไม่มี Warning i18n อีกต่อไป** - แก้ไขปัญหา key ที่ขาดหายไป
2. **ใช้งานง่ายขึ้น** - มี composables เฉพาะหน้า
3. **ยืดหยุ่นมากขึ้น** - รองรับ reactive values
4. **SEO ดีขึ้น** - มี structured data และ meta tags ครบถ้วน
5. **รองรับหลายภาษา** - ทำงานได้ดีกับ i18n
6. **Type Safety** - มี TypeScript interface ที่ดีขึ้น
7. **Performance ดีขึ้น** - ใช้ computed และ reactive อย่างถูกต้อง

## ตัวอย่างการใช้งาน

### หน้าหลัก:
```vue
<script setup>
// เพียงบรรทัดเดียว!
useHomeSeo();
</script>
```

### หน้าสินค้า:
```vue
<script setup>
const product = ref({ name: 'iPhone 15', description: 'สมาร์ทโฟนรุ่นใหม่' });

useProductSeo(
  product.value.name,
  product.value.description,
  '/images/iphone15.jpg'
);
</script>
```

### หน้าบทความ:
```vue
<script setup>
const article = ref({
  title: 'วิธีใช้ Nuxt 4',
  description: 'เรียนรู้การใช้งาน Nuxt 4',
  author: 'นักเขียน',
  publishedTime: '2024-01-01T00:00:00Z'
});

useArticleSeo(
  article.value.title,
  article.value.description,
  article.value.author,
  article.value.publishedTime
);
</script>
```

## การทดสอบ

เพื่อทดสอบว่าระบบทำงานถูกต้อง:

1. เปิด Developer Tools > Console
2. ตรวจสอบว่าไม่มี Warning i18n
3. ดู Meta tags ใน `<head>` ว่าครบถ้วน
4. ทดสอบเปลี่ยนภาษาและดู SEO ว่าเปลี่ยนตาม

## สิ่งที่ต้องทำต่อ (ถ้าต้องการ)

- [ ] อัปเดตไฟล์อื่นๆ ที่ยังใช้ `useSeoMeta` แบบเก่า
- [ ] เพิ่ม Composables สำหรับหน้าอื่นๆ ที่ยังไม่มี
- [ ] ทดสอบ SEO ด้วย Google Search Console
- [ ] เพิ่ม Open Graph images สำหรับแต่ละหน้า