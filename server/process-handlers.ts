// Global process error handlers
process.on('uncaughtException', (error) => {
  const nodeError = error as any;
  if (nodeError.code === 'EPIPE' || nodeError.code === 'ECONNRESET') {
    console.log('Connection error (uncaught):', nodeError.code);
    return;
  }
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  if (reason && typeof reason === 'object' && 'code' in reason) {
    const error = reason as any;
    if (error.code === 'EPIPE' || error.code === 'ECONNRESET') {
      console.log('Connection error (unhandled rejection):', error.code);
      return;
    }
  }
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Handle SIGPIPE gracefully
process.on('SIGPIPE', () => {
  console.log('SIGPIPE received - connection broken');
}); 