export default defineEventHandler(async event => {
  try {
    const siteId = getRouterParam(event, 'siteId');
    const body = await readBody(event);
    const { email, role, message } = body;

    if (!siteId) {
      throw createError({
        statusCode: 400,
        message: 'Site ID is required',
      });
    }

    if (!email || !role) {
      throw createError({
        statusCode: 400,
        message: 'Email and role are required',
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw createError({
        statusCode: 400,
        message: 'รูปแบบอีเมลไม่ถูกต้อง',
      });
    }

    // Validate role
    const validRoles = ['admin', 'editor', 'viewer'];
    if (!validRoles.includes(role)) {
      throw createError({
        statusCode: 400,
        message: 'บทบาทไม่ถูกต้อง',
      });
    }

    // Mock invitation creation
    const invitation = {
      id: Date.now().toString(),
      email,
      role,
      message: message || '',
      status: 'pending',
      invitedBy: 'สมชาย ใจดี', // ในอนาคตควรดึงจาก auth
      invitedAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
      token: `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };

    // Mock email sending
    console.log(`[Team Invitation] Sending invitation to ${email} with role ${role}`);

    return {
      success: true,
      data: invitation,
      message: 'ส่งคำเชิญเรียบร้อยแล้ว',
    };
  } catch (error: any) {
    console.error('[Team Invitation API] Error:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || 'เกิดข้อผิดพลาดในการส่งคำเชิญ',
    });
  }
});
