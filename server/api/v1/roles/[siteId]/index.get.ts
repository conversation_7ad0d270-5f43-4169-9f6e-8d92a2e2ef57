export default defineEventHandler(async event => {
  const siteId = getRouterParam(event, 'siteId');
  const query = getQuery(event);
  if (!siteId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Site ID is required',
    });
  }

  const { page = '1', limit = '20' } = query;

  // Call backend API
  return await apiFetch(
    `/role/${siteId}/roles`,
    {
      method: 'GET',
      query: { page, limit },
      requireAuth: true,
    },
    event
  );
});
