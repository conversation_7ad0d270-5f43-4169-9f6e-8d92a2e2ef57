export default defineEventHandler(async (event): Promise<any> => {
    const body = await readBody(event);

    // ส่งข้อมูลไปยัง backend API ตาม schema ที่ต้องการ
    const response = await apiFetch('/discount/validate', {
        method: 'POST',
        body: {
            discountCode: body.discountCode,
            orderAmount: body.orderAmount,
            target: body.target || 'package',
            items: body.items || [],
        },
        requireAuth: true,
    }, event);

    return response;
});