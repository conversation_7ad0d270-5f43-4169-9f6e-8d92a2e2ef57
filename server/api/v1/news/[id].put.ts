export default defineEventHandler(async (event): Promise<any> => {
  try {
    const body = await readBody(event);
    const query = getQuery(event);
    const siteId = query.siteId as string;
    const newsId = event.context.params?.id as string;

    if (!siteId || !newsId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'ไม่พบ Site ID หรือ News ID',
      });
    }

    // เรียก API จาก backend
    const response: any = await $fetch(`${process.env.BACKEND_URL}/api/v1/news/${newsId}`, {
      method: 'PUT',
      body,
      headers: {
        'x-site-id': siteId,
        'x-user-id': 'user-id', // ต้องดึงจาก auth middleware
        'Content-Type': 'application/json',
      },
    });

    return response;
  } catch (error: any) {
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || 'เกิดข้อผิดพลาดในการอัปเดตข่าว',
    });
  }
});
