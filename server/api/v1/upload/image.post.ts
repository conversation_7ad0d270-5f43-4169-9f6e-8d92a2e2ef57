import { mkdir, writeFile } from 'fs/promises';
import { join } from 'path';

export default defineEventHandler(async event => {
  try {
    // Get form data
    const formData = await readFormData(event);
    const image = formData.get('image') as File;
    const base64 = formData.get('base64') as string;

    if (!image || !base64) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields',
      });
    }

    // Validate file type
    if (!image.type.startsWith('image/')) {
      throw createError({
        statusCode: 400,
        statusMessage: 'File must be an image',
      });
    }

    // Validate file size (5MB max)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (image.size > maxSize) {
      throw createError({
        statusCode: 400,
        statusMessage: 'File size must be less than 5MB',
      });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const extension = image.name.split('.').pop() || 'jpg';
    const filename = `image_${timestamp}.${extension}`;

    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), 'public', 'uploads');
    await mkdir(uploadsDir, { recursive: true });

    // Save file to uploads directory
    const filePath = join(uploadsDir, filename);
    const buffer = Buffer.from(await image.arrayBuffer());
    await writeFile(filePath, buffer);

    // Return the public URL
    const baseUrl =
      process.env.NODE_ENV === 'production' ? 'https://your-domain.com' : 'http://localhost:3000';
    const publicUrl = `${baseUrl}/uploads/${filename}`;

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      success: true,
      data: {
        url: publicUrl,
        filename: filename,
        size: image.size,
        type: image.type,
      },
      message: 'Image uploaded successfully',
    };
  } catch (error: any) {
    console.error('Image upload error:', error);

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Internal server error',
    });
  }
});
