export default defineEventHandler(async (event): Promise<any> => {
  try {
    const query = getQuery(event);
    const siteId = query.siteId as string;

    if (!siteId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'ไม่พบ Site ID',
      });
    }

    // เรียก API จาก backend
    const response: any = await $fetch(`${process.env.BACKEND_URL}/api/v1/novels`, {
      method: 'GET',
      query: {
        ...query,
        siteId,
      },
      headers: {
        'x-site-id': siteId,
      },
    });

    return response;
  } catch (error: any) {
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || 'เกิดข้อผิดพลาดในการดึงข้อมูลนิยาย',
    });
  }
});
