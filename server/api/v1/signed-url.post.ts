import { v2 as cloudinary } from 'cloudinary';
import { defineEventHandler } from 'h3';

export default defineEventHandler(async event => {
  try {
    const {
      publicId,
      restricted = true,
      width,
      height,
      quality = 'auto',
      format = 'auto',
      crop = 'fill',
      gravity = 'auto'
    } = await readBody(event);

    if (!publicId) {
      throw new Error('Public ID is required');
    }

    // ตั้งค่า Cloudinary จาก runtime config
    const config = useRuntimeConfig();

    // ตรวจสอบ config ที่ถูกต้อง
    const cloudName = config.public.cloudinaryCloudName;
    const apiKey = config.cloudinaryApiKey;
    const apiSecret = config.cloudinaryApiSecret;

    console.log('Cloudinary configuration:', {
      cloud_name: cloudName,
      api_key: apiKey,
      api_secret: apiSecret ? '***' : 'missing',
    });

    if (!cloudName || !apiKey || !apiSecret) {
      throw new Error('Cloudinary configuration is missing');
    }

    cloudinary.config({
      cloud_name: cloudName,
      api_key: api<PERSON><PERSON>,
      api_secret: apiSecret,
    });

    let imageUrl: string;

    // Build transformation object สำหรับ Cloudinary
    const transformationOptions: any = {};
    
    if (width || height) {
      transformationOptions.crop = crop;
      if (width) transformationOptions.width = width;
      if (height) transformationOptions.height = height;
      if (gravity !== 'auto') transformationOptions.gravity = gravity;
    }
    
    if (quality !== 'auto') {
      transformationOptions.quality = quality;
    }
    
    if (format !== 'auto') {
      transformationOptions.format = format;
    }

    if (restricted) {
      // สำหรับรูปภาพ restricted - สร้าง signed URL
      console.log('Generating restricted URL for:', publicId, 'with transformation:', transformationOptions);

      try {
        // ใช้ Signed URL แบบง่ายๆ
        console.log('Creating signed URL...');
        
        imageUrl = cloudinary.url(publicId, {
          secure: true,
          sign_url: true,
          type: 'upload',
          resource_type: 'image',
          ...transformationOptions,
        });

        console.log('Generated signed URL:', imageUrl);
      } catch (signingError) {
        console.error('Signing error:', signingError);

        // Fallback: ใช้ public URL แทน
        imageUrl = cloudinary.url(publicId, {
          secure: true,
          resource_type: 'image',
          ...transformationOptions,
        });
        console.log('Fallback to public URL:', imageUrl);
      }
    } else {
      // สำหรับรูปภาพ public ใช้ URL ธรรมดา
      imageUrl = cloudinary.url(publicId, {
        secure: true,
        resource_type: 'image',
        ...transformationOptions,
      });
    }

    console.log('Generated URL:', imageUrl);
    return { url: imageUrl };
  } catch (error) {
    console.error('Cloudinary API Error:', error);
    return { error: error instanceof Error ? error.message : String(error) };
  }
});
