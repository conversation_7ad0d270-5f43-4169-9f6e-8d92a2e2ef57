import type { H3Event } from 'h3';
import { createError, defineEventHand<PERSON>, getQuery } from 'h3';
import { apiFetch } from '../../../utils/apiFetch';

export default defineEventHandler(async (event: H3Event) => {
  try {
    const query = getQuery(event);
    const token = query.token as string;

    if (!token) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        message: 'Token ไม่ถูกต้องหรือหมดอายุ',
      });
    }

    const response = await apiFetch('/users/verify-reset-token', {
      method: 'POST',
      body: { token },
    });

    return response;
  } catch (error: any) {
    console.error('Verify reset token error:', error);
    throw error;
  }
});
