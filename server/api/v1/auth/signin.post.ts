import { createSession } from '../../../utils/session';

export default defineEventHandler(async (event): Promise<any> => {
  const body = await readBody(event);
  const response = await apiFetch(
    '/user/signin',
    {
      method: 'POST',
      body: {
        email: body.email,
        password: body.password,
      },
      requireAuth: false,
    },
    event
  );

  console.log('Signin response:', response);
  console.log('Token:', response.data?.token);
  console.log('RefreshToken:', response.data?.refreshToken);

  // Create session with user data and tokens
  await createSession(event, response.data.token, response.data.refreshToken);

  // setCookie(event, 'session', response.data.token, {
  //   httpOnly: true,
  //   secure: process.env.NODE_ENV === 'production',
  //   sameSite: 'lax',
  //   path: '/',
  //   maxAge: 60 * 60 * 24 * 7, // 7 days
  // });

  return response;
});
