import { createError, define<PERSON><PERSON><PERSON>and<PERSON> } from 'h3';
import { getUserSession, refreshSessionToken } from '../../../utils/session';
import { apiFetch } from '../../../utils/apiFetch';

export default defineEventHandler(async event => {
  try {
    const session = await getUserSession(event);

    if (!session) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
        message: 'ไม่พบ session',
      });
    }
    console.log('sessionRefresh', session);

    // เรียก backend เพื่อ refresh token
    const response = await apiFetch('/user/refresh-token', {
      method: 'POST',
      body: { refreshToken: session.refreshToken },
      requireAuth: false,
    }, event);

    const newToken = response?.data?.token;
    const refreshed = await refreshSessionToken(event, newToken);

    if (!refreshed) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
        message: 'ไม่สามารถ refresh token ได้',
      });
    }

    // Get updated session
    const newSession = await getUserSession(event);

    return {
      success: true,
      status: 200,
      message: 'Refresh token สำเร็จ',
      data: {
        user: {
          _id: newSession?.userId,
          email: newSession?.email,
          role: newSession?.role,
        },
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error: any) {
    console.dir(error);

    const statusMessage = error?.data?.statusMessage || error?.statusMessage || 'เกิดข้อผิดพลาด';
    const message = error?.data?.message || error?.message || 'มีบางอย่างผิดพลาด';

    throw createError({
      statusCode: error?.status || error?.statusCode || 500,
      statusMessage: statusMessage,
      message: message,
      data: error?.data,
    });
  }
});
