// /home/<USER>/Documents/webapp/template-website/frontend-nuxtui/server/api/auth/signup.post.ts
import { createError, defineEventHandler, getRequestIP, readBody } from 'h3';
import { apiFetch } from '../../../utils/apiFetch';

interface HonoResponseError {
  statusCode: number;
  statusMessage: string;
  message: string;
  data?: any;
}

export default defineEventHandler(async event => {
  let body: any = null;

  try {
    body = await readBody(event);
    const ip = getRequestIP(event);
    const userAgent = event.node.req.headers['user-agent'] || 'unknown';

    if (!body || !body.userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        message: 'ไม่พบ userId',
      });
    }

    const { userId, ...updateData } = body;

    // Call backend API
    const honoResponse: any = await apiFetch(`/users/${userId}`, {
      method: 'PUT',
      body: updateData,
    });

    if (!honoResponse?.user) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Internal Server Error',
        message: 'ไม่พบข้อมูลผู้ใช้ในการตอบกลับจาก backend',
      });
    }

    const response = {
      success: true,
      status: 200,
      message: 'อัปเดตข้อมูลสำเร็จ',
      data: {
        user: honoResponse?.user,
        token: honoResponse?.token,
        refreshToken: honoResponse?.refreshToken,
      },
      timestamp: new Date().toISOString(),
    };

    return response;
  } catch (error: any) {
    console.dir(error);
    throw error;
  }
});
