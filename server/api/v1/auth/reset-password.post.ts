import { createError, defineEventHandler, readBody } from 'h3';
import { apiFetch } from '../../../utils/apiFetch';

export default defineEventHandler(async event => {
  try {
    const body = await readBody(event);
    const { token, newPassword } = body;

    if (!token || !newPassword) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        message: 'กรุณากรอก token และรหัสผ่านใหม่',
      });
    }

    const response = await apiFetch('/users/reset-password', {
      method: 'POST',
      body: { token, newPassword },
    });

    return {
      success: true,
      status: 200,
      message: 'รีเซ็ตรหัสผ่านสำเร็จ',
      data: response,
      timestamp: new Date().toISOString(),
    };
  } catch (error: any) {
    console.dir(error);
    throw error;
  }
});
