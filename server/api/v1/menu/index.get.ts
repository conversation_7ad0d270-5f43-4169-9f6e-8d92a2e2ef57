export default defineEventHandler(async event => {
  const query = getQuery(event);
  const { siteId, page = 1, limit = 10, status, location, search } = query;

  // Validate siteId
  if (!siteId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'siteId is required',
    });
  }

  try {
    // Mock menu data - ในอนาคตจะดึงจาก database
    const mockMenus = [
      {
        _id: 'menu1',
        siteId: siteId as string,
        name: 'เมนูหลัก',
        description: 'เมนูหลักของเว็บไซต์',
        location: 'header',
        status: 'active',
        items: [
          {
            id: 'item1',
            label: 'หน้าแรก',
            type: 'page',
            pageId: 'home',
            url: '/',
            openInNewTab: false,
            order: 1,
          },
          {
            id: 'item2',
            label: 'สินค้า',
            type: 'page',
            pageId: 'products',
            url: '/products',
            openInNewTab: false,
            order: 2,
          },
          {
            id: 'item3',
            label: 'เกี่ยวกับเรา',
            type: 'page',
            pageId: 'about',
            url: '/about',
            openInNewTab: false,
            order: 3,
          },
        ],
        showOnMobile: true,
        showOnDesktop: true,
        cssClass: 'main-menu',
        order: 1,
        createdAt: new Date('2024-01-01').toISOString(),
        updatedAt: new Date('2024-01-01').toISOString(),
      },
      {
        _id: 'menu2',
        siteId: siteId as string,
        name: 'เมนูด้านล่าง',
        description: 'เมนูที่แสดงในส่วน footer',
        location: 'footer',
        status: 'active',
        items: [
          {
            id: 'item4',
            label: 'นโยบายความเป็นส่วนตัว',
            type: 'page',
            pageId: 'privacy',
            url: '/privacy',
            openInNewTab: false,
            order: 1,
          },
          {
            id: 'item5',
            label: 'เงื่อนไขการใช้งาน',
            type: 'page',
            pageId: 'terms',
            url: '/terms',
            openInNewTab: false,
            order: 2,
          },
        ],
        showOnMobile: true,
        showOnDesktop: true,
        cssClass: 'footer-menu',
        order: 2,
        createdAt: new Date('2024-01-02').toISOString(),
        updatedAt: new Date('2024-01-02').toISOString(),
      },
    ];

    // Filter menus based on query parameters
    let filteredMenus = mockMenus;

    if (status) {
      filteredMenus = filteredMenus.filter(menu => menu.status === status);
    }

    if (location) {
      filteredMenus = filteredMenus.filter(menu => menu.location === location);
    }

    if (search) {
      const searchLower = search.toString().toLowerCase();
      filteredMenus = filteredMenus.filter(
        menu =>
          menu.name.toLowerCase().includes(searchLower) ||
          menu.description.toLowerCase().includes(searchLower)
      );
    }

    // Pagination
    const pageNum = parseInt(page.toString());
    const limitNum = parseInt(limit.toString());
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedMenus = filteredMenus.slice(startIndex, endIndex);

    const total = filteredMenus.length;
    const totalPages = Math.ceil(total / limitNum);

    return {
      success: true,
      data: {
        menus: paginatedMenus,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages,
        },
      },
      message: 'ดึงข้อมูลเมนูสำเร็จ',
    };
  } catch (error: any) {
    console.error('Error fetching menus:', error);

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
    });
  }
});
