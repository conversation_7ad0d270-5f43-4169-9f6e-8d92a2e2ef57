export default defineEventHandler(async event => {
  const query = getQuery(event);
  const { siteId } = query;

  // Validate siteId
  if (!siteId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'siteId is required',
    });
  }

  try {
    const body = await readBody(event);

    // Validate required fields
    const requiredFields = ['name', 'location'];
    for (const field of requiredFields) {
      if (!body[field]) {
        throw createError({
          statusCode: 400,
          statusMessage: `${field} is required`,
        });
      }
    }

    // Create new menu (mock implementation)
    const newMenu = {
      _id: Date.now().toString(),
      siteId: siteId as string,
      name: body.name,
      description: body.description || '',
      location: body.location,
      status: body.status || 'active',
      items: body.items || [],
      showOnMobile: body.showOnMobile !== undefined ? body.showOnMobile : true,
      showOnDesktop: body.showOnDesktop !== undefined ? body.showOnDesktop : true,
      cssClass: body.cssClass || '',
      order: body.order || 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // In a real implementation, you would save to database here
    // await saveMenuToDatabase(newMenu);

    return {
      success: true,
      data: newMenu,
      message: 'สร้างเมนูสำเร็จ',
    };
  } catch (error: any) {
    console.error('Error creating menu:', error);

    // If it's already a createError, re-throw it
    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
    });
  }
});
