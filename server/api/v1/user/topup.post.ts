export default defineEventHandler(async event => {
  try {
    const body = await readBody(event);
    const { type, amount, packageId, paymentMethod } = body;

    // Validate required fields
    if (!type || !amount || !packageId || !paymentMethod) {
      throw createError({
        statusCode: 400,
        message: 'ข้อมูลไม่ครบถ้วน',
      });
    }

    // Mock payment processing
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Simulate payment success (95% success rate)
    const isSuccess = Math.random() > 0.05;

    if (!isSuccess) {
      throw createError({
        statusCode: 400,
        message: 'การชำระเงินล้มเหลว กรุณาลองใหม่อีกครั้ง',
      });
    }

    // Mock transaction data
    const transaction = {
      id: Date.now().toString(),
      type: 'topup',
      pointType: type,
      amount: amount,
      method: paymentMethod,
      status: 'completed',
      createdAt: new Date().toISOString(),
      description: `เติม ${type === 'moneyPoint' ? 'Money Points' : 'Gold Points'} ${amount.toLocaleString()} พอยต์`,
    };

    return {
      success: true,
      data: {
        transaction,
        newBalance: {
          moneyPoint: type === 'moneyPoint' ? 1250 + amount : 1250,
          goldPoint: type === 'goldPoint' ? 85 + amount : 85,
        },
      },
      message: 'เติมเงินสำเร็จ',
    };
  } catch (error: any) {
    console.error('[User Topup API] Error:', error);

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      message: 'เกิดข้อผิดพลาดในการเติมเงิน',
    });
  }
});
