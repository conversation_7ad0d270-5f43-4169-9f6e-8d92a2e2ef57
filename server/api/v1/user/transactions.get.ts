export default defineEventHandler(async event => {
  try {
    const query = getQuery(event);
    const type = query.type as string;
    const limit = parseInt(query.limit as string) || 10;

    // Mock data - ในอนาคตควรดึงจาก database
    const mockTransactions = [
      {
        id: '1',
        type: 'topup',
        pointType: 'moneyPoint',
        amount: 500,
        method: 'credit_card',
        status: 'completed',
        createdAt: new Date('2024-01-15T10:30:00Z').toISOString(),
        description: 'เติม Money Points 500 พอยต์',
      },
      {
        id: '2',
        type: 'topup',
        pointType: 'goldPoint',
        amount: 100,
        method: 'bank_transfer',
        status: 'completed',
        createdAt: new Date('2024-01-10T14:20:00Z').toISOString(),
        description: 'เติม Gold Points 100 พอยต์',
      },
      {
        id: '3',
        type: 'topup',
        pointType: 'moneyPoint',
        amount: 1000,
        method: 'mobile_banking',
        status: 'pending',
        createdAt: new Date('2024-01-08T09:15:00Z').toISOString(),
        description: 'เติม Money Points 1,000 พอยต์',
      },
      {
        id: '4',
        type: 'topup',
        pointType: 'goldPoint',
        amount: 50,
        method: 'e_wallet',
        status: 'completed',
        createdAt: new Date('2024-01-05T16:45:00Z').toISOString(),
        description: 'เติม Gold Points 50 พอยต์',
      },
      {
        id: '5',
        type: 'topup',
        pointType: 'moneyPoint',
        amount: 2000,
        method: 'credit_card',
        status: 'failed',
        createdAt: new Date('2024-01-03T11:30:00Z').toISOString(),
        description: 'เติม Money Points 2,000 พอยต์',
      },
    ];

    // Filter by type if specified
    let transactions = mockTransactions;
    if (type) {
      transactions = mockTransactions.filter(t => t.type === type);
    }

    // Limit results
    transactions = transactions.slice(0, limit);

    return {
      success: true,
      data: transactions,
      message: 'ดึงประวัติการทำรายการสำเร็จ',
    };
  } catch (error: any) {
    console.error('[User Transactions API] Error:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'เกิดข้อผิดพลาดในการดึงประวัติการทำรายการ',
    });
  }
});
