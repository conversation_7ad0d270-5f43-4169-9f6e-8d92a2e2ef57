import { requireAuth } from '../../../utils/auth';
export default defineEventHandler(async (event): Promise<any> => {
  const config = useRuntimeConfig();

  try {
    const body = await readBody(event);

    // Require authentication and get session
    const session = await requireAuth(event);

    // Call backend API
    const response: any = await $fetch(
      `${config.public.apiBase}${config.public.apiVersion}/user/change-password`,
      {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${session.accessToken}`,
          'Content-Type': 'application/json',
        },
        body,
      }
    );

    return response;
  } catch (error: any) {
    console.error('Error changing password:', error);

    if (error.statusCode === 400) {
      throw createError({
        statusCode: 400,
        statusMessage: error.data?.message || 'Invalid request data',
      });
    }

    if (error.statusCode === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized - Please login again',
      });
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
    });
  }
});
