export default defineEventHandler(async event => {
  const query = getQuery(event);
  const { siteId, category, search, status, page = 1, limit = 20 } = query;

  // Validate siteId
  if (!siteId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'siteId is required',
    });
  }

  try {
    // Mock blog posts data
    const mockPosts = [
      {
        _id: '1',
        title: 'วิธีการเริ่มต้นธุรกิจออนไลน์',
        slug: 'how-to-start-online-business',
        excerpt: 'คู่มือครบวงจรสำหรับการเริ่มต้นธุรกิจออนไลน์ที่ประสบความสำเร็จ',
        content: '<p>เนื้อหาบทความ...</p>',
        featuredImage: '/images/blog1.jpg',
        category: 'business',
        tags: ['ธุรกิจ', 'ออนไลน์', 'เริ่มต้น'],
        status: 'published',
        author: {
          _id: 'author1',
          name: 'นายอนุชา ใจดี',
          avatar: '/images/author1.jpg',
        },
        publishedAt: new Date('2024-01-15'),
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-15'),
        views: 1250,
        likes: 45,
        comments: 12,
        seo: {
          title: 'วิธีการเริ่มต้นธุรกิจออนไลน์ - คู่มือครบวงจร',
          description: 'เรียนรู้วิธีการเริ่มต้นธุรกิจออนไลน์ที่ประสบความสำเร็จ',
          keywords: ['ธุรกิจออนไลน์', 'เริ่มต้นธุรกิจ'],
        },
      },
      {
        _id: '2',
        title: 'เทคนิคการตลาดดิจิทัลที่มีประสิทธิภาพ',
        slug: 'effective-digital-marketing-techniques',
        excerpt: 'เรียนรู้เทคนิคการตลาดดิจิทัลที่จะช่วยเพิ่มยอดขายให้กับธุรกิจของคุณ',
        content: '<p>เนื้อหาบทความ...</p>',
        featuredImage: '/images/blog2.jpg',
        category: 'marketing',
        tags: ['การตลาด', 'ดิจิทัล', 'เทคนิค'],
        status: 'published',
        author: {
          _id: 'author2',
          name: 'นางสาวสุดา ใจงาม',
          avatar: '/images/author2.jpg',
        },
        publishedAt: new Date('2024-01-20'),
        createdAt: new Date('2024-01-18'),
        updatedAt: new Date('2024-01-20'),
        views: 890,
        likes: 32,
        comments: 8,
        seo: {
          title: 'เทคนิคการตลาดดิจิทัลที่มีประสิทธิภาพ',
          description: 'เรียนรู้เทคนิคการตลาดดิจิทัลที่จะช่วยเพิ่มยอดขาย',
          keywords: ['การตลาดดิจิทัล', 'เทคนิคการตลาด'],
        },
      },
      {
        _id: '3',
        title: 'แนวโน้มอีคอมเมิร์ซในปี 2024',
        slug: 'ecommerce-trends-2024',
        excerpt: 'ติดตามแนวโน้มและนวัตกรรมใหม่ ๆ ในวงการอีคอมเมิร์ซ',
        content: '<p>เนื้อหาบทความ...</p>',
        featuredImage: '/images/blog3.jpg',
        category: 'technology',
        tags: ['อีคอมเมิร์ซ', 'แนวโน้ม', '2024'],
        status: 'draft',
        author: {
          _id: 'author1',
          name: 'นายอนุชา ใจดี',
          avatar: '/images/author1.jpg',
        },
        publishedAt: null,
        createdAt: new Date('2024-01-25'),
        updatedAt: new Date('2024-01-25'),
        views: 0,
        likes: 0,
        comments: 0,
        seo: {
          title: 'แนวโน้มอีคอมเมิร์ซในปี 2024',
          description: 'ติดตามแนวโน้มและนวัตกรรมใหม่ ๆ ในวงการอีคอมเมิร์ซ',
          keywords: ['อีคอมเมิร์ซ', 'แนวโน้ม'],
        },
      },
    ];

    // Filter posts based on query parameters
    let filteredPosts = mockPosts;

    if (category) {
      filteredPosts = filteredPosts.filter(post => post.category === category);
    }

    if (status) {
      filteredPosts = filteredPosts.filter(post => post.status === status);
    }

    if (search) {
      const searchTerm = search.toString().toLowerCase();
      filteredPosts = filteredPosts.filter(
        post =>
          post.title.toLowerCase().includes(searchTerm) ||
          post.excerpt.toLowerCase().includes(searchTerm) ||
          post.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    // Pagination
    const totalPosts = filteredPosts.length;
    const pageNum = parseInt((page || 1).toString());
    const limitNum = parseInt((limit || 20).toString());
    const totalPages = Math.ceil(totalPosts / limitNum);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;

    const paginatedPosts = filteredPosts.slice(startIndex, endIndex);

    return {
      success: true,
      data: {
        posts: paginatedPosts,
        total: totalPosts,
        page: pageNum,
        limit: limitNum,
        totalPages: totalPages,
      },
      message: 'Blog posts retrieved successfully',
    };
  } catch (error: any) {
    console.error('Error fetching blog posts:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
    });
  }
});
