import { createError, define<PERSON><PERSON><PERSON><PERSON><PERSON>, getCookie } from 'h3';
import { apiFetch } from '../../../utils/apiFetch';
import { getRouterParam } from 'h3';

export default defineEventHandler(async event => {
  const siteId = getRouterParam(event, 'siteId');

  if (!siteId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Site ID is required',
    });
  }
  return await apiFetch(
    `/site/get-site/${null}/${siteId}`,
    {
      method: 'GET',
      requireAuth: false,
    },
    event
  );
});
