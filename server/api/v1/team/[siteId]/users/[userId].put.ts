export default defineEventHandler(async event => {
  try {
    const siteId = getRouterParam(event, 'siteId');
    const userId = getRouterParam(event, 'userId');
    const body = await readBody(event);
    const { role, status } = body;

    if (!siteId || !userId) {
      throw createError({
        statusCode: 400,
        message: 'Site ID and Member ID are required',
      });
    }

    // Validate role if provided
    if (role) {
      const validRoles = ['admin', 'editor', 'viewer'];
      if (!validRoles.includes(role)) {
        throw createError({
          statusCode: 400,
          message: 'บทบาทไม่ถูกต้อง',
        });
      }
    }

    // Validate status if provided
    if (status) {
      const validStatuses = ['active', 'inactive', 'suspended'];
      if (!validStatuses.includes(status)) {
        throw createError({
          statusCode: 400,
          message: 'สถานะไม่ถูกต้อง',
        });
      }
    }

    // Mock member update
    const updatedMember = {
      id: userId,
      userId: 'user' + userId,
      name: 'สมชาย ใจดี',
      email: 'somcha<PERSON>@example.com',
      avatar: null,
      role: role || 'editor',
      status: status || 'active',
      joinedAt: new Date('2024-01-01').toISOString(),
      updatedAt: new Date().toISOString(),
      permissions:
        role === 'admin'
          ? ['manage_members', 'manage_content', 'view_analytics']
          : role === 'editor'
            ? ['edit_content', 'view_analytics']
            : ['view_analytics'],
    };

    return {
      success: true,
      data: updatedMember,
      message: 'อัปเดตข้อมูลสมาชิกสำเร็จ',
    };
  } catch (error: any) {
    console.error('[Team Member Update API] Error:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || 'เกิดข้อผิดพลาดในการอัปเดตข้อมูลสมาชิก',
    });
  }
});
