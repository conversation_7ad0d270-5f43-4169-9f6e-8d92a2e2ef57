export default defineEventHandler(async event => {
  try {
    const siteId = getRouterParam(event, 'siteId');
    const userId = getRouterParam(event, 'userId');

    if (!siteId || !userId) {
      throw createError({
        statusCode: 400,
        message: 'Site ID and Member ID are required',
      });
    }

    // Mock member removal
    console.log(`[Team Member Removal] Removing member ${userId} from site ${siteId}`);

    return {
      success: true,
      message: 'ลบสมาชิกออกจากทีมสำเร็จ',
    };
  } catch (error: any) {
    console.error('[Team Member Removal API] Error:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || 'เกิดข้อผิดพลาดในการลบสมาชิก',
    });
  }
});
