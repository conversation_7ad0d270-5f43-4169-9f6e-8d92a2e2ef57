export default defineEventHandler(async event => {
  try {
    const siteId = getRouterParam(event, 'siteId');

    if (!siteId) {
      throw createError({
        statusCode: 400,
        message: 'Site ID is required',
      });
    }

    // Mock roles data
    const roles = [
      {
        id: 'owner',
        name: 'เจ้าของ',
        description: 'สิทธิ์เต็มในการจัดการเว็บไซต์',
        permissions: ['all'],
        isDefault: false,
        isEditable: false,
      },
      {
        id: 'admin',
        name: 'ผู้ดูแล',
        description: 'จัดการสมาชิกและเนื้อหาได้',
        permissions: ['manage_members', 'manage_content', 'view_analytics', 'manage_settings'],
        isDefault: false,
        isEditable: true,
      },
      {
        id: 'editor',
        name: 'บรรณาธิการ',
        description: 'แก้ไขเนื้อหาและดูสถิติได้',
        permissions: ['edit_content', 'view_analytics'],
        isDefault: true,
        isEditable: true,
      },
      {
        id: 'viewer',
        name: 'ผู้ดู',
        description: 'ดูเนื้อหาและสถิติได้เท่านั้น',
        permissions: ['view_analytics'],
        isDefault: false,
        isEditable: true,
      },
    ];

    return {
      success: true,
      data: roles,
      message: 'ดึงข้อมูลบทบาทสำเร็จ',
    };
  } catch (error: any) {
    console.error('[Team Roles API] Error:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || 'เกิดข้อผิดพลาดในการดึงข้อมูลบทบาท',
    });
  }
});
