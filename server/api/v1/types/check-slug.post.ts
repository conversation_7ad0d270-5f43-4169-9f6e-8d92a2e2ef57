import { requireAuth } from '../../../utils/auth';

export default defineEventHandler(async (event): Promise<any> => {
  const config = useRuntimeConfig();

  try {
    // Get query parameters
    const query = getQuery(event);
    const siteId = query.siteId as string;

    if (!siteId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Site ID is required',
      });
    }

    // Require authentication and get session
    const session = await requireAuth(event);
    
    if (!session) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized - Please login again',
      });
    }

    // Get request body
    const body = await readBody(event);
    const { slug, excludeId } = body;

    if (!slug) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Slug is required',
      });
    }

    // Call backend API
    const response: any = await $fetch(`${config.public.apiBase}/api/v1/types/check-slug`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
        'Content-Type': 'application/json',
      },
      query: { siteId },
      body: { slug, excludeId },
    });

    return response;
  } catch (error: any) {
    console.error('Error checking slug availability:', error);

    if (error.statusCode === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized - Please login again',
      });
    }

    if (error.statusCode === 400) {
      throw createError({
        statusCode: 400,
        statusMessage: error.data?.message || 'Invalid request data',
      });
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
    });
  }
}); 