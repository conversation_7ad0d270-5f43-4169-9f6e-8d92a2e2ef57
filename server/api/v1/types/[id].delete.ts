import { requireAuth } from '../../../utils/auth';

export default defineEventHandler(async (event): Promise<any> => {
  const config = useRuntimeConfig();

  try {
    // Get query parameters
    const query = getQuery(event);
    const siteId = query.siteId as string;
    const typeId = getRouterParam(event, 'id');

    if (!siteId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Site ID is required',
      });
    }

    if (!typeId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Type ID is required',
      });
    }

    // Require authentication and get session
    const session = await requireAuth(event);
    
    if (!session) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized - Please login again',
      });
    }

    // Call backend API
    const response: any = await $fetch(`${config.public.apiBase}/api/v1/types/${typeId}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
        'Content-Type': 'application/json',
      },
      query: { siteId },
    });

    return response;
  } catch (error: any) {
    console.error('Error deleting type:', error);

    if (error.statusCode === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized - Please login again',
      });
    }

    if (error.statusCode === 400) {
      throw createError({
        statusCode: 400,
        statusMessage: error.data?.message || 'Invalid request data',
      });
    }

    if (error.statusCode === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Type not found',
      });
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
    });
  }
}); 