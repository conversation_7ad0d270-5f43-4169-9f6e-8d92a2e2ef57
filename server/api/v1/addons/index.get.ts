import { defineEvent<PERSON><PERSON><PERSON> } from 'h3';
import { $fetch } from 'ofetch';

export default defineEventHandler(async event => {
  try {
    // ส่งข้อมูลไป backend จริง
    const res = await $fetch(``, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return res;
  } catch (error: any) {
    console.error('Error fetching addons:', error);
    return {
      success: false,
      message: error.message || 'ไม่สามารถโหลดข้อมูล addons ได้',
      data: [],
    };
  }
});
