export default defineEventHandler(async event => {
  const query = getQuery(event);
  const { siteId } = query;

  // Validate siteId
  if (!siteId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'siteId is required',
    });
  }

  try {
    // Mock cart data - replace with actual database query
    const mockCart = {
      _id: 'cart_' + siteId,
      items: [
        {
          productId: '1',
          product: {
            _id: '1',
            name: 'เสื้อยืดคุณภาพดี',
            price: 299,
            salePrice: 249,
            images: ['/images/product1.jpg'],
          },
          quantity: 2,
          variant: {
            สี: 'ขาว',
            ขนาด: 'M',
          },
          price: 249,
          total: 498,
        },
        {
          productId: '2',
          product: {
            _id: '2',
            name: 'กางเกงยีนส์',
            price: 890,
            salePrice: 790,
            images: ['/images/product2.jpg'],
          },
          quantity: 1,
          variant: {
            ขนาด: 'L',
          },
          price: 790,
          total: 790,
        },
      ],
      subtotal: 1288,
      tax: 90.16,
      shipping: 50,
      total: 1428.16,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return {
      success: true,
      data: mockCart,
      message: 'Cart retrieved successfully',
    };
  } catch (error: any) {
    console.error('Error fetching cart:', error);

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
    });
  }
});
