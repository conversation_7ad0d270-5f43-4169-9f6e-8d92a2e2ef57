import { requireAuth } from '../../../utils/auth';
export default defineEventHandler(async (event): Promise<any> => {
  const config = useRuntimeConfig();

  try {
    const productId = getRouterParam(event, 'id');
    const query = getQuery(event);
    const siteId = query.siteId as string;

    if (!productId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Product ID is required',
      });
    }

    if (!siteId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Site ID is required',
      });
    }

    // Require authentication and get session
    const session = await requireAuth(event);

    // Call backend API
    const response: any = await $fetch(`${config.public.apiBase}/api/v1/product/${productId}`, {
      method: 'DELETE' as any,
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
        'Content-Type': 'application/json',
      },
      query: { siteId },
    });

    return response;
  } catch (error: any) {
    console.error('Error deleting product:', error);

    if (error.statusCode === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized - Please login again',
      });
    }

    if (error.statusCode === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Product not found',
      });
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
    });
  }
});
