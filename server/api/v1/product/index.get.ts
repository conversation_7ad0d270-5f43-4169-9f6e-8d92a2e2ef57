import { requireAuth } from '../../../utils/auth';
export default defineEventHandler(async (event): Promise<any> => {
  const config = useRuntimeConfig();

  try {
    // Get query parameters
    const query = getQuery(event);
    const siteId = query.siteId as string;

    if (!siteId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Site ID is required',
      });
    }

    // Require authentication and get session
    const session = await requireAuth(event);

    // Call backend API
    const response: any = await $fetch(`${config.public.apiBase}/api/v1/product`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
        'Content-Type': 'application/json',
      },
      query: {
        siteId,
        page: query.page || 1,
        limit: query.limit || 20,
        status: query.status,
        type: query.type,
        category: query.category,
        search: query.search,
        sortBy: query.sortBy || 'createdAt',
        sortOrder: query.sortOrder || 'desc',
      },
    });

    return response;
  } catch (error: any) {
    console.error('Error fetching products:', error);

    if (error.statusCode === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized - Please login again',
      });
    }

    if (error.statusCode === 400) {
      throw createError({
        statusCode: 400,
        statusMessage: error.data?.message || 'Invalid request data',
      });
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error',
    });
  }
});
