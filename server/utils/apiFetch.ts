import { $fetch } from 'ofetch';
import { createError, H3Event, parseCookies } from 'h3';
import { requireAuth } from './auth';

interface CustomFetchOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: any;
  query?: Record<string, any>;
  accessToken?: string;
  requireAuth?: boolean; // เพิ่ม option นี้
}

export async function apiFetch<T = any>(
  path: string,
  options: CustomFetchOptions = {},
  event?: H3Event
): Promise<T> {
  const config = useRuntimeConfig();
  let accessToken = options.accessToken;

  // ถ้าต้องการ auth และมี event ให้ดึง token จาก session
  if (options.requireAuth && event && !accessToken) {
    const session = await requireAuth(event);
    accessToken = session?.accessToken;
  }

  try {
    const response = await $fetch(`${config.public.apiBase}${config.public.apiVersion}${path}`, {
      method: options.method || 'GET',
      headers: {
        ...(accessToken ? { Authorization: `Bearer ${accessToken}` } : {}),
        'Content-Type': 'application/json',
        Accept: 'application/json',
        ...(options.headers || {}),
      },
      body: options.body,
      query: options.query,
      credentials: 'include', // แนบ cookie ทุกครั้ง
    });

    console.log('response Fetch', response);

    // ตรวจสอบ response structure
    if (!response?.success) {
      throw createError({
        statusCode: 500,
        statusMessage: 'ล้มเหลว',
        message: 'เชื่อมต่อกับระบบล้มเหลว',
      });
    }

    // onResponse: ถ้ามี .data ให้ return .data, ถ้าไม่มีก็ return ทั้ง response
    // return (response as any).data ?? response;
    return response;
  } catch (error: any) {
    console.error('Error details:', {
      status: error?.status,
      statusCode: error?.statusCode,
      statusText: error?.statusText,
      message: error?.message,
      data: error?.data,
      response: error?.response,
    });

    // ถ้า backend ส่ง statusMessage มา ให้ใช้ตัวนั้น
    const statusMessage = error?.data?.statusMessage || 'เกิดข้อผิดพลาด';
    const message = error?.data?.message || 'มีบางอย่างผิดพลาด';

    throw createError({
      statusCode: error?.status || error?.statusCode || 500,
      statusMessage: statusMessage,
      message: message,
      data: error?.data,
    });
  }
}
