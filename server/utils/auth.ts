import { createError, type H3Event } from 'h3';
import { clearUserSession, getUserSession, refreshSessionToken } from './session';
import { apiFetch } from './apiFetch';

export async function requireAuth(event: H3Event) {
  // ลองดึง session ปกติก่อน
  let session = await getUserSession(event);

  // ถ้าไม่มี session ลองดึงแบบ allowExpired
  if (!session) {
    console.log('No valid session found, trying to get expired session...');
    session = await getUserSession(event, true);

    if (!session) {
      console.log('No session found at all');
      await handleAuthFailure(event, 'No session found');
      return; // This will never be reached due to handleAuthFailure throwing
    }
  }

  // ตรวจสอบ token หมดอายุ
  const now = Math.floor(Date.now() / 1000);
  const isExpired = session.expired || (typeof session.exp === 'number' && session.exp < now);

  console.log('Token expiry check:', {
    now,
    exp: session.exp,
    expired: isExpired,
    timeLeft: session.exp ? session.exp - now : 'N/A',
    hasRefreshToken: !!session.refreshToken
  });

  if (isExpired) {
    console.log('Token expired, attempting refresh');

    if (!session.refreshToken) {
      console.log('No refresh token available');
      await handleAuthFailure(event, 'Session expired - no refresh token');
      return; // This will never be reached due to handleAuthFailure throwing
    }

    try {
      console.log('Calling refresh token API...');
      // เรียก backend เพื่อ refresh token
      const response = await apiFetch('/user/refresh-token', {
        method: 'POST',
        body: { refreshToken: session.refreshToken },
        requireAuth: false,
      }, event);

      console.log('Refresh token response:', response);

      const newToken = response?.data?.token;
      const newRefreshToken = response?.data?.refreshToken;

      if (!newToken) {
        console.log('No new token received from refresh');
        await handleAuthFailure(event, 'Token refresh failed - no new token');
        return; // This will never be reached due to handleAuthFailure throwing
      }

      console.log('Setting new session tokens...');
      await refreshSessionToken(event, newToken, newRefreshToken);

      const newSession = await getUserSession(event);
      console.log('Token refreshed successfully, new session:', {
        userId: newSession?.userId,
        exp: newSession?.exp,
        timeLeft: newSession?.exp ? newSession.exp - now : 'N/A'
      });

      return newSession;

    } catch (error: any) {
      console.error('Token refresh error:', error);

      // ถ้า refresh token หมดอายุหรือไม่ถูกต้อง
      if (error?.statusCode === 401 || error?.status === 401) {
        await handleAuthFailure(event, 'Refresh token expired or invalid');
      } else {
        await handleAuthFailure(event, `Token refresh failed: ${error?.message || 'Unknown error'}`);
      }
      return; // This will never be reached due to handleAuthFailure throwing
    }
  }

  console.log('Session is valid, returning session');
  return session;
}

// ฟังก์ชันจัดการเมื่อ authentication ล้มเหลว
async function handleAuthFailure(event: H3Event, reason: string): Promise<never> {
  console.log('Authentication failed:', reason);

  // ล้าง session
  clearUserSession(event);

  // ส่ง response พิเศษที่บอกให้ client redirect
  throw createError({
    statusCode: 401,
    statusMessage: 'Authentication failed',
    data: {
      reason,
      shouldRedirect: true,
      redirectTo: '/signin'
    }
  });
}

export async function getOptionalAuth(event: H3Event) {
  try {
    return await getUserSession(event);
  } catch {
    return null;
  }
}
