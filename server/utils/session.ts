import { deleteCookie, getCookie, type H3Event, setCookie } from 'h3';
import { jwtVerify } from 'jose';
const config = useRuntimeConfig();

export interface SessionData {
  userId?: string;
  email?: string;
  role?: string;
  accessToken?: string;
  refreshToken?: string;
  exp?: number;
  iat?: number;
  [key: string]: any;
}

const JWT_SECRET = new TextEncoder().encode(
  config.jwtSecret || 'your-super-secret-jwt-key-change-in-production'
);

// ปรับใหม่: รับ token จาก backend มา set cookie ตรงๆ
export async function createSession(event: H3Event, token: string, refreshToken?: string) {
  setCookie(event, 'session', token, {
    httpOnly: true,
    secure: config.public.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/',
    maxAge: 60 * 60 * 24 * 7, // 7 days
  });

  // เก็บ refreshToken ใน cookie แยก
  if (refreshToken) {
    setCookie(event, 'refreshToken', refreshToken, {
      httpOnly: true,
      secure: config.public.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 60 * 60 * 24 * 30, // 30 days
    });
  }

  return token;
}

export async function getUserSession(event: H3Event, allowExpired = false): Promise<SessionData | null> {
  const sessionCookie = getCookie(event, 'session');
  const refreshTokenCookie = getCookie(event, 'refreshToken');

  if (!sessionCookie) {
    return null;
  }

  try {
    console.log('Verifying JWT token:', {
      tokenLength: sessionCookie.length,
      tokenStart: sessionCookie.substring(0, 20) + '...',
      currentTime: Math.floor(Date.now() / 1000),
      allowExpired
    });

    const { payload } = await jwtVerify(sessionCookie, JWT_SECRET, {
      clockTolerance: allowExpired ? '24h' : '5m', // อนุญาต expired token เมื่อต้องการ
    });

    console.log('JWT verification successful:', {
      userId: payload.userId,
      exp: payload.exp,
      timeLeft: payload.exp ? (payload.exp as number) - Math.floor(Date.now() / 1000) : 'N/A'
    });

    return {
      ...payload as SessionData,
      accessToken: sessionCookie, // เก็บ token ต้นฉบับไว้ใช้
      refreshToken: refreshTokenCookie,
    };
  } catch (error: any) {
    console.error('JWT verification failed:', {
      error: error.message,
      cause: error.cause,
      tokenLength: sessionCookie?.length,
      currentTime: Math.floor(Date.now() / 1000),
      allowExpired
    });

    // ถ้า allowExpired = true และเป็น exp error ให้ลองอีกครั้งด้วย clockTolerance ที่ใหญ่ขึ้น
    if (allowExpired && error.cause?.claim === 'exp') {
      try {
        console.log('Attempting to decode expired token...');
        const { payload } = await jwtVerify(sessionCookie, JWT_SECRET, {
          clockTolerance: '365d' // อนุญาตให้หมดอายุได้ 1 ปี
        });

        console.log('Expired token decoded successfully');
        return {
          ...payload as SessionData,
          accessToken: sessionCookie,
          refreshToken: refreshTokenCookie,
          expired: true // flag ว่า token หมดอายุ
        };
      } catch (decodeError) {
        console.error('Failed to decode expired token:', decodeError);
        return null;
      }
    }

    return null;
  }
}

export function clearUserSession(event: H3Event) {
  deleteCookie(event, 'session', {
    path: '/',
  });
  deleteCookie(event, 'refreshToken', {
    path: '/',
  });
}

// ปรับใหม่: รับ token ที่ refresh จาก backend มา setCookie ใหม่
export async function refreshSessionToken(event: H3Event, newToken: string, newRefreshToken?: string): Promise<boolean> {
  if (!newToken) return false;
  await createSession(event, newToken, newRefreshToken);
  return true;
}
