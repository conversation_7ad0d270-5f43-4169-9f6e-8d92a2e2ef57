export default defineNuxtRouteMiddleware(async to => {
  // ตรวจสอบเฉพาะหน้าที่เกี่ยวข้องกับ dashboard และมี siteId
  if (!to.path.startsWith('/dashboard/') || to.path === '/dashboard') {
    return;
  }

  // ดึง siteId จาก path
  const siteId = to.params.siteId as string;
  if (!siteId) {
    return;
  }

  // ข้ามการตรวจสอบสำหรับหน้าต่ออายุ subscription
  if (to.path.includes('/subscription/renew')) {
    return;
  }

  try {
    // เรียก API จริงเพื่อดึงข้อมูล subscription
    const { $fetch } = useNuxtApp() as any;
    const response = await $fetch('/v1/site');
    const sites = response.data?.sites || response.data || [];
    const site = sites.find((s: any) => s._id === siteId);

    if (!site) {
      console.error('Site not found:', siteId);
      return;
    }

    // ตรวจสอบว่า subscription หมดอายุหรือไม่
    const isExpired = (subscription: any): boolean => {
      if (!subscription) return false;

      if (subscription.endDate) {
        return new Date() > new Date(subscription.endDate);
      }

      if (subscription.status === 'expired' || subscription.status === 'past_due') {
        return true;
      }

      return !subscription.isActive;
    };

    // ตรวจสอบว่า subscription จะหมดอายุในอีก 7 วันหรือไม่
    const isExpiringSoon = (subscription: any): boolean => {
      if (!subscription) return false;

      const sevenDaysFromNow = new Date();
      sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);

      if (subscription.endDate) {
        const endDate = new Date(subscription.endDate);
        return endDate <= sevenDaysFromNow && endDate > new Date();
      }

      return false;
    };

    // ตรวจสอบว่า subscription หมดอายุหรือไม่
    if (isExpired(site.subscription)) {
      // Redirect ไปหน้าต่ออายุ
      return navigateTo(`/dashboard/subscription/renew?siteId=${siteId}`);
    }

    // ตรวจสอบว่า subscription จะหมดอายุเร็วๆ นี้หรือไม่
    if (isExpiringSoon(site.subscription)) {
      // แสดงการแจ้งเตือนผ่าน toast
      const { $toast } = useNuxtApp() as any;
      const endDate = new Date(site.subscription.endDate);
      const daysLeft = Math.ceil(
        (endDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      );

      $toast?.add({
        title: 'การสมัครสมาชิกจะหมดอายุเร็วๆ นี้',
        description: `เว็บไซต์ "${site.name}" จะหมดอายุในอีก ${daysLeft} วัน กรุณาต่ออายุการใช้งาน`,
        color: 'warning',
        timeout: 10000,
        actions: [
          {
            label: 'ต่ออายุ',
            click: () => navigateTo(`/dashboard/subscription/renew?siteId=${siteId}`),
          },
        ],
      });
    }
  } catch (error) {
    console.error('Error checking subscription:', error);
    // หากเกิดข้อผิดพลาด ให้ผ่านไปได้ (ไม่บล็อกการใช้งาน)
  }
});
