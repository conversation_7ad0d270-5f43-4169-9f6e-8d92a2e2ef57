export default defineNuxtRouteMiddleware(async (to) => {
    // Skip on server-side และหน้าที่ไม่ต้องการ auth
    if (import.meta.server) return;

    const authStore = useAuthStore();
    const { refreshSession } = useSession();

    // Skip สำหรับหน้าที่ไม่ต้องการ authentication
    const publicPages = ['/signin', '/signup', '/forgot-password', '/reset-password', '/'];
    if (publicPages.includes(to.path)) return;

    // ถ้าไม่มี user ใน store ให้ skip (จะถูกจัดการโดย auth middleware)
    if (!authStore.isAuthenticated) return;

    try {
        // ลองเรียก API เพื่อตรวจสอบ token
        const response = await $fetch('/api/v1/user/profile', {
            method: 'GET',
        });

        // ถ้าสำเร็จแสดงว่า token ยังใช้ได้
        console.log('Token is still valid');

    } catch (error: any) {
        console.log('Token validation failed:', error);

        // ถ้าเป็น 401 error ลอง refresh token
        if (error?.statusCode === 401 || error?.status === 401) {
            console.log('Attempting to refresh token...');

            try {
                const refreshSuccess = await refreshSession();

                if (refreshSuccess) {
                    console.log('Token refreshed successfully');
                    // โหลดข้อมูล user ใหม่
                    await authStore.loadUserDetail();
                } else {
                    console.log('Token refresh failed, clearing auth');
                    await authStore.clearAuth();
                }

            } catch (refreshError) {
                console.error('Token refresh error:', refreshError);
                await authStore.clearAuth();
            }
        }
    }
});