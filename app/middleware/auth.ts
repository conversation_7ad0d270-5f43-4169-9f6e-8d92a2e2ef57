import { useAuthStore } from '~/stores/auth';

export default defineNuxtRouteMiddleware(async to => {
  const authStore = useAuthStore();

  // ตรวจสอบว่ามีข้อมูลผู้ใช้ใน store หรือไม่
  if (!authStore.getUser) {
    try {
      // พยายามโหลดข้อมูลผู้ใช้จาก session
      const user = await authStore.loadUserDetail();

      // ถ้าโหลดไม่ได้หรือไม่มี user ให้ redirect ไป signin
      if (!user) {
        console.log('No user found, redirecting to signin');
        return navigateTo('/signin');
      }
    } catch (error) {
      console.error('Failed to load user detail in middleware:', error);
      return navigateTo('/signin');
    }
  }

  // ตรวจสอบว่าหลังจากโหลดข้อมูลแล้วยังมีผู้ใช้หรือไม่
  if (!authStore.getUser) {
    console.log('No authenticated user, redirecting to signin');
    return navigateTo('/signin');
  }

  // หน้าที่เกี่ยวข้องกับการ authentication
  const authPages = ['/signin', '/signup', '/forgot-password', '/reset-password'];

  // ถ้าเข้าสู่ระบบแล้วและพยายามเข้าถึงหน้า auth ให้ redirect ไป dashboard
  if (authPages.includes(to.path)) {
    return navigateTo('/dashboard');
  }
});