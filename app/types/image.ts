export type ImageCrop = 'fit' | 'thumb' | 'fill' | 'scale' | 'crop'

export type ImageFormat = 'auto' | 'webp' | 'jpg' | 'png'

export type ImageObjectFit = 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'

export type ImageRounded = boolean | 'sm' | 'md' | 'lg' | 'xl' | 'full'

export type ImageShadow = boolean | 'sm' | 'md' | 'lg' | 'xl'

export type ImageLoading = 'lazy' | 'eager'

export type ImageCrossorigin = 'anonymous' | 'use-credentials'

export interface ImageProps {
    // Image source
    src?: string | null | undefined
    alt?: string

    // Dimensions
    width?: number | string
    height?: number | string
    aspectRatio?: string

    // Processing options
    processWidth?: number
    processHeight?: number
    crop?: ImageCrop
    quality?: number
    format?: ImageFormat

    // Layout & styling
    objectFit?: ImageObjectFit
    objectPosition?: string
    rounded?: ImageRounded
    border?: boolean
    shadow?: ImageShadow

    // Fallback options
    fallbackSrc?: string
    showFallback?: boolean
    fallbackIcon?: string
    fallbackText?: string
    fallbackBg?: string

    // Behavior
    lazy?: boolean
    eager?: boolean
    clickable?: boolean
    zoomable?: boolean

    // Custom classes
    class?: string
    imageClass?: string
    containerClass?: string
    fallbackClass?: string

    // Accessibility
    title?: string
    loading?: ImageLoading

    // Advanced
    sizes?: string
    srcset?: string
    crossorigin?: ImageCrossorigin
}

export interface ImageEmits {
    click: [event: MouseEvent]
    load: [event: Event]
    error: [event: Event]
    zoom: [event: MouseEvent]
}

// Preset configurations for common use cases
export interface ImagePreset {
    // Product images
    product: {
        thumbnail: Partial<ImageProps>
        card: Partial<ImageProps>
        gallery: Partial<ImageProps>
        hero: Partial<ImageProps>
    }

    // Avatar images
    avatar: {
        xs: Partial<ImageProps>
        sm: Partial<ImageProps>
        md: Partial<ImageProps>
        lg: Partial<ImageProps>
        xl: Partial<ImageProps>
    }

    // Content images
    content: {
        inline: Partial<ImageProps>
        featured: Partial<ImageProps>
        banner: Partial<ImageProps>
    }

    // UI images
    ui: {
        icon: Partial<ImageProps>
        logo: Partial<ImageProps>
        background: Partial<ImageProps>
    }
}