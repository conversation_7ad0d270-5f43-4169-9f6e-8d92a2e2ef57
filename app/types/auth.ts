// User type definition
export interface User {
  _id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: 'user' | 'admin';
  avatar?: string;
  cover?: string;
  isEmailVerified: boolean;
  moneyPoint: number;
  goldPoint: number;
  createdAt: Date | string;
  updatedAt: Date | string;
  exp?: number;
}

// API Response type
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  statusMessage?: string;
  timestamp?: string;
  data?: T;
  error?: any;
}

// Auth response types
export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
  user: User;
}

// Auth store state
export interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  loading: boolean;
  error: string | null;
}
