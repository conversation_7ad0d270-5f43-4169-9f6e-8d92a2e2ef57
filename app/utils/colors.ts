export const colors = {
	primary: '#228be6',
	dark50: '#C1C2C5',
	dark100: '#A6A7AB',
	dark200: '#909296',
	dark300: '#5c5f66',
	dark400: '#373A40',
	dark500: '#2C2E33',
	dark600: '#25262b',
	dark700: '#1A1B1E',
	dark800: '#141517',
	dark900: '#101113',
	gray50: '#f8f9fa',
	gray100: '#f1f3f5',
	gray200: '#e9ecef',
	gray300: '#dee2e6',
	gray400: '#ced4da',
	gray500: '#adb5bd',
	gray600: '#868e96',
	gray700: '#495057',
	gray800: '#343a40',
	gray900: '#212529',
	red50: '#fff5f5',
	red100: '#ffe3e3',
	red200: '#ffc9c9',
	red300: '#ffa8a8',
	red400: '#ff8787',
	red500: '#ff6b6b',
	red600: '#fa5252',
	red700: '#f03e3e',
	red800: '#e03131',
	red900: '#c92a2a',
	pink50: '#fff0f6',
	pink100: '#ffdeeb',
	pink200: '#fcc2d7',
	pink300: '#faa2c1',
	pink400: '#f783ac',
	pink500: '#f06595',
	pink600: '#e64980',
	pink700: '#d6336c',
	pink800: '#c2255c',
	pink900: '#a61e4d',
	grape50: '#f8f0fc',
	grape100: '#f3d9fa',
	grape200: '#eebefa',
	grape300: '#e599f7',
	grape400: '#da77f2',
	grape500: '#cc5de8',
	grape600: '#be4bdb',
	grape700: '#ae3ec9',
	grape800: '#9c36b5',
	grape900: '#862e9c',
	violet50: '#f3f0ff',
	violet100: '#e5dbff',
	violet200: '#d0bfff',
	violet300: '#b197fc',
	violet400: '#9775fa',
	violet500: '#845ef7',
	violet600: '#7950f2',
	violet700: '#7048e8',
	violet800: '#6741d9',
	violet900: '#5f3dc4',
	indigo50: '#edf2ff',
	indigo100: '#dbe4ff',
	indigo200: '#bac8ff',
	indigo300: '#91a7ff',
	indigo400: '#748ffc',
	indigo500: '#5c7cfa',
	indigo600: '#4c6ef5',
	indigo700: '#4263eb',
	indigo800: '#3b5bdb',
	indigo900: '#364fc7',
	blue50: '#e7f5ff',
	blue100: '#d0ebff',
	blue200: '#a5d8ff',
	blue300: '#74c0fc',
	blue400: '#4dabf7',
	blue500: '#339af0',
	blue600: '#228be6',
	blue700: '#1c7ed6',
	blue800: '#1971c2',
	blue900: '#1864ab',
	cyan50: '#e3fafc',
	cyan100: '#c5f6fa',
	cyan200: '#99e9f2',
	cyan300: '#66d9e8',
	cyan400: '#3bc9db',
	cyan500: '#22b8cf',
	cyan600: '#15aabf',
	cyan700: '#1098ad',
	cyan800: '#0c8599',
	cyan900: '#0b7285',
	teal50: '#e6fcf5',
	teal100: '#c3fae8',
	teal200: '#96f2d7',
	teal300: '#63e6be',
	teal400: '#38d9a9',
	teal500: '#20c997',
	teal600: '#12b886',
	teal700: '#0ca678',
	teal800: '#099268',
	teal900: '#087f5b',
	green50: '#ebfbee',
	green100: '#d3f9d8',
	green200: '#b2f2bb',
	green300: '#8ce99a',
	green400: '#69db7c',
	green500: '#51cf66',
	green600: '#40c057',
	green700: '#37b24d',
	green800: '#2f9e44',
	green900: '#2b8a3e',
	lime50: '#f4fce3',
	lime100: '#e9fac8',
	lime200: '#d8f5a2',
	lime300: '#c0eb75',
	lime400: '#a9e34b',
	lime500: '#94d82d',
	lime600: '#82c91e',
	lime700: '#74b816',
	lime800: '#66a80f',
	lime900: '#5c940d',
	yellow50: '#fff9db',
	yellow100: '#fff3bf',
	yellow200: '#ffec99',
	yellow300: '#ffe066',
	yellow400: '#ffd43b',
	yellow500: '#fcc419',
	yellow600: '#fab005',
	yellow700: '#f59f00',
	yellow800: '#f08c00',
	yellow900: '#e67700',
	orange50: '#fff4e6',
	orange100: '#ffe8cc',
	orange200: '#ffd8a8',
	orange300: '#ffc078',
	orange400: '#ffa94d',
	orange500: '#ff922b',
	orange600: '#fd7e14',
	orange700: '#f76707',
	orange800: '#e8590c',
	orange900: '#d9480f'
} as const;

// Type definitions
export type ColorName = keyof typeof colors;
export type ColorValue = typeof colors[ColorName];

// Utility functions
export const getColor = (name: ColorName): ColorValue => colors[name];

export const getColorWithOpacity = (name: ColorName, opacity: number): string => {
	const color = colors[name];
	// Convert hex to rgba
	const r = parseInt(color.slice(1, 3) || '00', 16);
	const g = parseInt(color.slice(3, 5) || '00', 16);
	const b = parseInt(color.slice(5, 7) || '00', 16);
	return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

// Color categories for easier access
export const colorCategories = {
	primary: ['primary'],
	dark: ['dark50', 'dark100', 'dark200', 'dark300', 'dark400', 'dark500', 'dark600', 'dark700', 'dark800', 'dark900'],
	gray: ['gray50', 'gray100', 'gray200', 'gray300', 'gray400', 'gray500', 'gray600', 'gray700', 'gray800', 'gray900'],
	red: ['red50', 'red100', 'red200', 'red300', 'red400', 'red500', 'red600', 'red700', 'red800', 'red900'],
	pink: ['pink50', 'pink100', 'pink200', 'pink300', 'pink400', 'pink500', 'pink600', 'pink700', 'pink800', 'pink900'],
	grape: ['grape50', 'grape100', 'grape200', 'grape300', 'grape400', 'grape500', 'grape600', 'grape700', 'grape800', 'grape900'],
	violet: ['violet50', 'violet100', 'violet200', 'violet300', 'violet400', 'violet500', 'violet600', 'violet700', 'violet800', 'violet900'],
	indigo: ['indigo50', 'indigo100', 'indigo200', 'indigo300', 'indigo400', 'indigo500', 'indigo600', 'indigo700', 'indigo800', 'indigo900'],
	blue: ['blue50', 'blue100', 'blue200', 'blue300', 'blue400', 'blue500', 'blue600', 'blue700', 'blue800', 'blue900'],
	cyan: ['cyan50', 'cyan100', 'cyan200', 'cyan300', 'cyan400', 'cyan500', 'cyan600', 'cyan700', 'cyan800', 'cyan900'],
	teal: ['teal50', 'teal100', 'teal200', 'teal300', 'teal400', 'teal500', 'teal600', 'teal700', 'teal800', 'teal900'],
	green: ['green50', 'green100', 'green200', 'green300', 'green400', 'green500', 'green600', 'green700', 'green800', 'green900'],
	lime: ['lime50', 'lime100', 'lime200', 'lime300', 'lime400', 'lime500', 'lime600', 'lime700', 'lime800', 'lime900'],
	yellow: ['yellow50', 'yellow100', 'yellow200', 'yellow300', 'yellow400', 'yellow500', 'yellow600', 'yellow700', 'yellow800', 'yellow900'],
	orange: ['orange50', 'orange100', 'orange200', 'orange300', 'orange400', 'orange500', 'orange600', 'orange700', 'orange800', 'orange900']
} as const;

// Semantic color mappings
export const semanticColors = {
	primary: colors.primary,
	secondary: colors.grape500,
	success: colors.teal500,
	warning: colors.yellow500,
	error: colors.red500,
	info: colors.indigo500,
	neutral: colors.gray500,
	
	// Text colors
	textPrimary: colors.gray900,
	textSecondary: colors.gray700,
	textTertiary: colors.gray500,
	textInverse: colors.gray50,
	
	// Background colors
	bgPrimary: colors.gray50,
	bgSecondary: colors.gray100,
	bgTertiary: colors.gray200,
	bgInverse: colors.gray900,
	
	// Border colors
	borderPrimary: colors.gray300,
	borderSecondary: colors.gray200,
	borderInverse: colors.gray700,
} as const; 