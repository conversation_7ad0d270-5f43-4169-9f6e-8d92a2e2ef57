/* Dashboard Responsive Styles */

/* Mobile First Approach */
@media (max-width: 768px) {
	/* Hide desktop sidebar */
	.desktop-sidebar {
		display: none;
	}

	/* Mobile navigation styles */
	.mobile-nav {
		display: block;
	}

	/* Adjust card spacing on mobile */
	.dashboard-card {
		padding: 1rem;
	}

	/* Stack quick actions vertically on mobile */
	.quick-actions-grid {
		grid-template-columns: 1fr;
		gap: 0.75rem;
	}

	/* Adjust stats grid on mobile */
	.stats-grid {
		grid-template-columns: repeat(2, 1fr);
		gap: 1rem;
	}
}

/* Tablet Styles */
@media (min-width: 768px) and (max-width: 1024px) {
	.stats-grid {
		grid-template-columns: repeat(2, 1fr);
	}

	.quick-actions-grid {
		grid-template-columns: repeat(2, 1fr);
	}
}

/* Desktop Styles */
@media (min-width: 1024px) {
	/* Show desktop sidebar */
	.desktop-sidebar {
		display: block;
	}

	/* Hide mobile navigation */
	.mobile-nav {
		display: none;
	}

	/* Full grid layout for desktop */
	.stats-grid {
		grid-template-columns: repeat(4, 1fr);
	}

	.quick-actions-grid {
		grid-template-columns: repeat(4, 1fr);
	}
}

/* Sidebar Styles */
.sidebar {
	transition: transform 0.3s ease-in-out;
}

.sidebar-overlay {
	background-color: rgba(0, 0, 0, 0.5);
	backdrop-filter: blur(4px);
}

/* Card Hover Effects */
.dashboard-card {
	transition: all 0.2s ease-in-out;
}

.dashboard-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Navigation Active State */
.nav-item-active {
	background-color: rgb(59 130 246 / 0.1);
	color: rgb(59 130 246);
	font-weight: 500;
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
	.dashboard-card:hover {
		box-shadow: 0 10px 25px rgba(255, 255, 255, 0.05);
	}

	.nav-item-active {
		background-color: rgb(59 130 246 / 0.2);
		color: rgb(147 197 253);
	}
}

/* Animation Classes */
.fade-in {
	animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.slide-in-left {
	animation: slideInLeft 0.3s ease-in-out;
}

@keyframes slideInLeft {
	from {
		opacity: 0;
		transform: translateX(-20px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

/* Loading States */
.loading-skeleton {
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	animation: loading 1.5s infinite;
}

@keyframes loading {
	0% {
		background-position: 200% 0;
	}
	100% {
		background-position: -200% 0;
	}
}

/* Responsive Text */
.responsive-text {
	font-size: clamp(0.875rem, 2vw, 1rem);
}

.responsive-title {
	font-size: clamp(1.25rem, 4vw, 1.5rem);
}

/* Utility Classes */
.glass-effect {
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-bg {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Focus States for Accessibility */
.focus-ring:focus {
	outline: 2px solid rgb(59 130 246);
	outline-offset: 2px;
}

/* Print Styles */
@media print {
	.no-print {
		display: none !important;
	}

	.dashboard-card {
		box-shadow: none;
		border: 1px solid #e5e7eb;
	}
}
