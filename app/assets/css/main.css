
/* @import "tailwindcss" theme(static); */
@import "tailwindcss";
/* @plugin "@tailwindcss/typography"; */
@import "@nuxt/ui";
@import "./dashboard.css";

/* Dynamic Theme - สำหรับสีที่เปลี่ยนได้ */
@theme {
	/* Primary Colors */
	--color-primary: rgb(34, 139, 230);
	--color-secondary: rgb(151, 80, 221);
	--color-success: rgb(32, 201, 151);
	--color-error: rgb(250, 82, 82);

	/* Dark Colors */
	--color-dark-50: rgb(193, 194, 197);
	--color-dark-100: rgb(166, 167, 171);
	--color-dark-200: rgb(144, 146, 150);
	--color-dark-300: rgb(92, 95, 102);
	--color-dark-400: rgb(55, 58, 64);
	--color-dark-500: rgb(44, 46, 51);
	--color-dark-600: rgb(37, 38, 43);
	--color-dark-700: rgb(26, 27, 30);
	--color-dark-800: rgb(20, 21, 23);
	--color-dark-900: rgb(16, 17, 19);

	/* Gray Colors */
	--color-gray-50: rgb(248, 249, 250);
	--color-gray-100: rgb(241, 243, 245);
	--color-gray-200: rgb(233, 236, 239);
	--color-gray-300: rgb(222, 226, 230);
	--color-gray-400: rgb(206, 212, 218);
	--color-gray-500: rgb(173, 181, 189);
	--color-gray-600: rgb(134, 142, 150);
	--color-gray-700: rgb(73, 80, 87);
	--color-gray-800: rgb(52, 58, 64);
	--color-gray-900: rgb(33, 37, 41);

	/* Red Colors */
	--color-red-50: rgb(255, 245, 245);
	--color-red-100: rgb(255, 227, 227);
	--color-red-200: rgb(255, 201, 201);
	--color-red-300: rgb(255, 168, 168);
	--color-red-400: rgb(255, 135, 135);
	--color-red-500: rgb(255, 107, 107);
	--color-red-600: rgb(250, 82, 82);
	--color-red-700: rgb(240, 62, 62);
	--color-red-800: rgb(224, 49, 49);
	--color-red-900: rgb(201, 42, 42);

	/* Pink Colors */
	--color-pink-50: rgb(255, 240, 246);
	--color-pink-100: rgb(255, 222, 235);
	--color-pink-200: rgb(252, 194, 215);
	--color-pink-300: rgb(250, 162, 193);
	--color-pink-400: rgb(247, 131, 172);
	--color-pink-500: rgb(240, 101, 149);
	--color-pink-600: rgb(230, 73, 128);
	--color-pink-700: rgb(214, 51, 108);
	--color-pink-800: rgb(194, 37, 92);
	--color-pink-900: rgb(166, 30, 77);

	/* Grape Colors */
	--color-grape-50: rgb(248, 240, 252);
	--color-grape-100: rgb(243, 217, 250);
	--color-grape-200: rgb(238, 190, 250);
	--color-grape-300: rgb(229, 153, 247);
	--color-grape-400: rgb(218, 119, 242);
	--color-grape-500: rgb(204, 93, 232);
	--color-grape-600: rgb(190, 75, 219);
	--color-grape-700: rgb(174, 62, 201);
	--color-grape-800: rgb(156, 54, 181);
	--color-grape-900: rgb(134, 46, 156);

	/* Violet Colors */
	--color-violet-50: rgb(243, 240, 255);
	--color-violet-100: rgb(229, 219, 255);
	--color-violet-200: rgb(208, 191, 255);
	--color-violet-300: rgb(177, 151, 252);
	--color-violet-400: rgb(151, 117, 250);
	--color-violet-500: rgb(132, 94, 247);
	--color-violet-600: rgb(121, 80, 242);
	--color-violet-700: rgb(112, 72, 232);
	--color-violet-800: rgb(103, 65, 217);
	--color-violet-900: rgb(95, 61, 196);

	/* Indigo Colors */
	--color-indigo-50: rgb(237, 242, 255);
	--color-indigo-100: rgb(219, 228, 255);
	--color-indigo-200: rgb(186, 200, 255);
	--color-indigo-300: rgb(145, 167, 255);
	--color-indigo-400: rgb(116, 143, 252);
	--color-indigo-500: rgb(92, 124, 250);
	--color-indigo-600: rgb(76, 110, 245);
	--color-indigo-700: rgb(66, 99, 235);
	--color-indigo-800: rgb(59, 91, 219);
	--color-indigo-900: rgb(54, 79, 199);

	/* Blue Colors */
	--color-blue-50: rgb(231, 245, 255);
	--color-blue-100: rgb(208, 235, 255);
	--color-blue-200: rgb(165, 216, 255);
	--color-blue-300: rgb(116, 192, 252);
	--color-blue-400: rgb(77, 171, 247);
	--color-blue-500: rgb(51, 154, 240);
	--color-blue-600: rgb(34, 139, 230);
	--color-blue-700: rgb(28, 126, 214);
	--color-blue-800: rgb(25, 113, 194);
	--color-blue-900: rgb(24, 100, 171);

	/* Cyan Colors */
	--color-cyan-50: rgb(227, 250, 252);
	--color-cyan-100: rgb(197, 246, 250);
	--color-cyan-200: rgb(153, 233, 242);
	--color-cyan-300: rgb(102, 217, 232);
	--color-cyan-400: rgb(59, 201, 219);
	--color-cyan-500: rgb(34, 184, 207);
	--color-cyan-600: rgb(21, 170, 191);
	--color-cyan-700: rgb(16, 152, 173);
	--color-cyan-800: rgb(12, 133, 153);
	--color-cyan-900: rgb(11, 114, 133);

	/* Teal Colors */
	--color-teal-50: rgb(230, 252, 245);
	--color-teal-100: rgb(195, 250, 232);
	--color-teal-200: rgb(150, 242, 215);
	--color-teal-300: rgb(99, 230, 190);
	--color-teal-400: rgb(56, 217, 169);
	--color-teal-500: rgb(32, 201, 151);
	--color-teal-600: rgb(18, 184, 134);
	--color-teal-700: rgb(12, 166, 120);
	--color-teal-800: rgb(9, 146, 104);
	--color-teal-900: rgb(8, 127, 91);

	/* Green Colors */
	--color-green-50: rgb(235, 251, 238);
	--color-green-100: rgb(211, 249, 216);
	--color-green-200: rgb(178, 242, 187);
	--color-green-300: rgb(140, 233, 154);
	--color-green-400: rgb(105, 219, 124);
	--color-green-500: rgb(81, 207, 102);
	--color-green-600: rgb(64, 192, 87);
	--color-green-700: rgb(55, 178, 77);
	--color-green-800: rgb(47, 158, 68);
	--color-green-900: rgb(43, 138, 62);

	/* Lime Colors */
	--color-lime-50: rgb(244, 252, 227);
	--color-lime-100: rgb(233, 250, 200);
	--color-lime-200: rgb(216, 245, 162);
	--color-lime-300: rgb(192, 235, 117);
	--color-lime-400: rgb(169, 227, 75);
	--color-lime-500: rgb(148, 216, 45);
	--color-lime-600: rgb(130, 201, 30);
	--color-lime-700: rgb(116, 184, 22);
	--color-lime-800: rgb(102, 168, 15);
	--color-lime-900: rgb(92, 148, 13);

	/* Yellow Colors */
	--color-yellow-50: rgb(255, 249, 219);
	--color-yellow-100: rgb(255, 243, 191);
	--color-yellow-200: rgb(255, 236, 153);
	--color-yellow-300: rgb(255, 224, 102);
	--color-yellow-400: rgb(255, 212, 59);
	--color-yellow-500: rgb(252, 196, 25);
	--color-yellow-600: rgb(250, 176, 5);
	--color-yellow-700: rgb(245, 159, 0);
	--color-yellow-800: rgb(240, 140, 0);
	--color-yellow-900: rgb(230, 119, 0);

	/* Orange Colors */
	--color-orange-50: rgb(255, 244, 230);
	--color-orange-100: rgb(255, 232, 204);
	--color-orange-200: rgb(255, 216, 168);
	--color-orange-300: rgb(255, 192, 120);
	--color-orange-400: rgb(255, 169, 77);
	--color-orange-500: rgb(255, 146, 43);
	--color-orange-600: rgb(253, 126, 20);
	--color-orange-700: rgb(247, 103, 7);
	--color-orange-800: rgb(232, 89, 12);
	--color-orange-900: rgb(217, 72, 15);

	/* Dark mode variables - ใช้สีที่เข้มขึ้นสำหรับ dark mode */
	--color-primary-dark: rgb(74, 158, 255);
	--color-secondary-dark: rgb(167, 139, 250);
	--color-success-dark: rgb(52, 211, 153);
	--color-error-dark: rgb(248, 113, 113);
}

/* Font definitions */
@theme static {
	--font-default: "Sarabun", sans-serif;
	--font-k2d: "K2D", sans-serif;
	--font-sarabun: "Sarabun", sans-serif;
}

:root {
	--ui-bg: rgb(248, 249, 250);
	--ui-text: rgb(33, 37, 41);

	--ui-container: 1536px;
	--ui-bg-1: rgb(248, 249, 250);
	--ui-bg-2: rgb(241, 243, 245);
	--ui-bg-3: rgb(233, 236, 239);
 
	--ui-text-1: rgb(33, 37, 41);
	--ui-text-2: rgb(73, 80, 87);
	--ui-text-3: rgb(134, 142, 150);
	--ui-border: rgb(222, 226, 230);
	--bg-image: url("@/assets/images/bg/bg.avif");

	--ui-neutral: rgb(134, 142, 150);
	/* ใช้ dynamic theme variables */
	--ui-primary: var(--color-primary);
	--ui-secondary: var(--color-secondary);
	--ui-success: var(--color-success);
	--ui-info: rgb(76, 110, 245);
	--ui-warning: rgb(250, 176, 5);
	--ui-error: var(--color-error);
}

.dark {
	--ui-bg: rgb(26, 27, 30); 
	--ui-text: rgb(248, 249, 250);
	
	--ui-bg-1: rgb(26, 27, 30);
	--ui-bg-2: rgb(37, 38, 43);
	--ui-bg-3: rgb(44, 46, 51);
 
	--ui-text-1: rgb(248, 249, 250);
	--ui-text-2: rgb(173, 181, 189);
	--ui-text-3: rgb(134, 142, 150);
	--ui-border: rgb(44, 46, 51);
	--bg-image: url("@/assets/images/bg/bg-dark.avif");

	/* ใช้ dark mode variables */
	--ui-primary: var(--color-primary-dark);
	--ui-secondary: var(--color-secondary-dark);
	--ui-success: var(--color-success-dark);
	--ui-info: rgb(76, 110, 245);
	--ui-warning: rgb(250, 176, 5);
	--ui-error: var(--color-error-dark);
}

/* .router-link-active.router-link-exact-active {
  @apply text-black dark:text-white font-bold bg-white dark:bg-black sm:bg-white/0 sm:dark:bg-black/0;
}   */



html,
body {
	scroll-behavior: smooth;
	cursor: default;
	font-family: var(--font-sarabun);
	min-height: 100vh;
	font-weight: 300;
	/* เพิ่มบรรทัดนี้ */
}

/* นำตัวแปร CSS ไปใช้กับ body หรือ element ที่ต้องการ */
body {
	transition:
		background-color 0.3s ease,
		color 0.3s ease;
	background-image: var(--bg-image);
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	background-attachment: fixed;
	/* ทำให้พื้นหลังไม่เลื่อนตาม */
	min-height: 100vh;
	/* ให้ครอบคลุมความสูงทั้งหน้า */
}

body,
[class*="bg-"],
[class*="text-"],
[class*="border-"] {
	transition:
		background-color 0.4s cubic-bezier(.4, 0, .2, 1),
		color 0.4s cubic-bezier(.4, 0, .2, 1),
		border-color 0.4s cubic-bezier(.4, 0, .2, 1);
}

.font-k2d {
	font-family: var(--font-k2d);
}