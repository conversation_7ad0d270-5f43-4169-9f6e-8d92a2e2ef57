<script setup lang="ts">
import * as locales from '@nuxt/ui/locale'; // Import locales จาก Nuxt UI
// import { Analytics } from "@vercel/analytics/nuxt";
// import { SpeedInsights } from "@vercel/speed-insights/nuxt";


const { locale, t } = useI18n();

// ใช้ computed property เพื่อเข้าถึง locale object จาก Nuxt UI อย่างปลอดภัยแบบ Static
const uiLocale = computed(() => {
  switch (locale.value) {
    case 'en':
      return locales.en; // เข้าถึงแบบ Static
    case 'th':
      return locales.th; // เข้าถึงแบบ Static
    // เพิ่ม case สำหรับภาษาอื่นๆ ที่ Nuxt UI รองรับ ถ้าจำเป็น
    default:
      return locales.en; // ค่าสำรองสำหรับภาษาที่ไม่รองรับ (เช่น 'lo')
  }
});

// ใช้ useSeoMeta สำหรับ SEO เริ่มต้น
useHomeSeo();
</script>

<template>
  <!-- <SpeedInsights/>
  <Analytics /> -->
  <UApp :locale="uiLocale" :toaster="{ position: 'top-right' }">
    <NuxtLoadingIndicator />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </UApp>
</template>