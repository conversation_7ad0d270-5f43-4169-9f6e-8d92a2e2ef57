<script setup lang="ts">
import type { AccordionItem, FormSubmitEvent } from '@nuxt/ui';
import { z } from 'zod';
import { useAuthStore } from '~/stores/auth';
import { acceptTermsSchema, emailSchema, passwordSchema } from '~/utils/validationSchemas'; // Import Zod Schemas

const { t } = useI18n();

// ใช้ SEO meta สำหรับหน้า signup
useAuthSeo(false);

definePageMeta({
  layout: 'empty',
  middleware: ['guest'],
});

// Store & Config
const authStore = useAuthStore() as unknown as {
  setUser: (user: any) => void;
  setToken: (token: string) => void;
  setRefreshToken: (token: string) => void;
};

// State
const error = ref<string>('');
const isLoading = ref(false);
const rememberMe = ref(false);
const showPassword = ref(false);

// Form Schema (ใช้ Zod)
const schema = z
  .object({
    email: emailSchema,
    password: passwordSchema,
    confirmPassword: z.string().min(1, 'กรุณากรอกรหัสผ่านอีกครั้ง'), // ใช้แค่ schema พื้นฐานของ string + required
    acceptTerms: acceptTermsSchema,
  })
  .refine(data => data.password === data.confirmPassword, {
    // แก้ typo data.data เป็น data
    message: 'รหัสผ่านและยืนยันรหัสผ่าน ต้องตรงกัน',
    path: ['confirmPassword'], // กำหนดว่า error นี้จะผูกกับ field confirmPassword
  });

// Form State (ใช้ Reactive)
type Schema = z.output<typeof schema>;
const state = reactive<Partial<Schema>>({
  email: '<EMAIL>',
  password: 'testtest',
  confirmPassword: 'testtest',
  acceptTerms: false,
});

// Computed property เพื่อตรวจสอบความถูกต้องของฟอร์มด้วย Zod
const isFormValid = computed(() => {
  const result = schema.safeParse(state);
  return result.success;
});

// Methods (ปรับใช้กับ UForm และ Zod)
async function handleSubmit(event: any) {
  try {
    error.value = '';
    isLoading.value = true;

    const response = await $fetch<
      ApiResponse<{
        user: User;
        token: string;
        refreshToken: string;
      }>
    >('/api/v1/auth/signup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: {
        email: event.data.email,
        password: event.data.password,
        name: event.data.email?.split('@')[0] || 'User',
      },
    });

    console.log('response', response);

    if (!response.success) {
      throw new Error(response.message || 'เกิดข้อผิดพลาดในการสมัครสมาชิก');
    }

    navigateTo('/signin');

    useToast().add({
      title: 'สมัครสมาชิกสำเร็จ',
      description: 'ยินดีต้อนรับ! กรุณายืนยันอีเมลของคุณ',
      icon: 'i-heroicons-check-circle',
      color: 'success',
      duration: 1500,
    });
  } catch (err: any) {
    console.dir(err);
    // ป้องกัน error กรณี err.response หรือ err.response.status เป็น undefined
    const statusCode = err?.response?.status || err?.status || 500;
    const responseBody = err?.response?._data || err?.data || {};
    useToast().add({
      title: responseBody?.statusMessage || 'ล้มเหลว',
      description:
        responseBody?.message ||
        responseBody?.statusMessage ||
        `เกิดข้อผิดพลาดในการสมัครสมาชิก (${statusCode})`,
      icon: 'i-heroicons-exclamation-triangle',
      color: 'error',
      duration: 3000,
    });
  } finally {
    isLoading.value = false;
  }
}

// Terms & Conditions
const termList = ref<AccordionItem[]>([
  {
    icon: 'solar:info-circle-bold',
    label: 'การมีสิทธิ์ของผู้ใช้',
    content:
      'เว็บไซต์นี้เปิดให้บริการเฉพาะแก่องค์กรและบุคคลที่บรรลุนิติภาวะและมีความเหมาะสมที่จะทำธุรกรรมที่มีผลผูกมัดทางกฎหมายตามข้อกฎหมายที่เกี่ยวข้องได้ หากคุณไม่มีคุณสมบัติเหมาะสม ไม่อนุญาตให้ใช้งานเว็บไซต์',
  },
  {
    icon: 'solar:info-circle-bold',
    label: 'การแก้ไขเปลี่ยนแปลงข้อตกลง',
    content:
      'ทางผู้ให้บริการขอสงวนสิทธิที่จะแก้ไขหรือเปลี่ยนแปลงข้อตกลงการใช้งานที่ระบุไว้ในเว็บไซต์นี้ ท่านมีหน้าที่ตรวจสอบข้อตกลงการใช้งานเว็บไซต์นี้ รวมถึงข้อกำหนดเพิ่มเติมใดๆ ที่ระบุไว้ในเว็บไซต์ของเราอย่างสม่ำเสมอ',
  },
  {
    icon: 'solar:info-circle-bold',
    label: 'การใช้และการเปิดเผยข้อมูลส่วนบุคคล',
    content:
      'เว็บไซต์แห่งนี้ อนุญาติให้ผู้ใช้งาน เสพข้อมูล เพิ่มเนื้อหาเองได้โดยอิสระ และทางผู้ให้บริการจะไม่ขอรับผิดชอบกับเนื้อหาที่ผู้ใช้งาน เขียน อัพโหลด หรือเพิ่มเข้ามาในเว็บไซต์แห่งนี้',
  },
  {
    icon: 'solar:info-circle-bold',
    label: 'การชดใช้ค่าเสียหาย',
    content:
      'ท่านตกลงว่าจะชดใช้ค่าเสียหายให้แก่ทางเราสำหรับข้อเรียกร้อง ความสูญเสีย ค่าใช้จ่าย และความเสียหายใดๆ ที่ทางเราต้องรับภาระหรือเกิดขึ้นแก่การให้บริการเว็บไซต์หรือบริการต่างๆ',
  },
  {
    icon: 'solar:info-circle-bold',
    label: 'นโยบายคุ้มครองข้อมูลส่วนบุคคล',
    content:
      'ข้อมูลส่วนบุคคลของผู้ใช้งานสำหรับการใช้งานเว็บไซต์นี้ ได้รับความคุ้มครองภายใต้นโยบายคุ้มครองข้อมูลส่วนบุคคล (Privacy Policy)',
  },
  {
    icon: 'solar:info-circle-bold',
    label: 'นโยบายการรักษาความมั่นคงปลอดภัย',
    content:
      'ทางเราได้เลือกใช้เทคโนโลยี มาตรการรักษาความมั่นคงปลอดภัยในการทำธุรกรรมบนเครือข่ายอินเทอร์เน็ต เพื่อป้องกันข้อมูลของผู้ใช้งาน',
  },
  {
    icon: 'solar:info-circle-bold',
    label: 'การละเมิดข้อตกลงและเงื่อนไข',
    content:
      'ในกรณีที่มีการละเมิดข้อตกลงและเงื่อนไขการใช้งานเว็บไซต์ของเรา ทางเราขอสงวนสิทธิ์ที่จะระงับการเข้าถึงหรือยกเลิกการให้บริการใด ๆ',
  },
  {
    icon: 'solar:info-circle-bold',
    label: 'กฎหมายที่บังคับใช้',
    content: 'ข้อตกลงและเงื่อนไขการการใช้งานเว็บไซต์นี้อยู่ภายใต้การบังคับใช้แห่งกฎหมายไทย',
  },
]);
</script>

<template>
  <UContainer>
    <div class="flex flex-col md:flex-row w-full gap-5">
      <!-- Terms & Conditions -->
      <div class="w-full sm:min-w-sm space-y-5">
        <h2 class="text-lg text-center font-bold">
          ข้อตกลงและเงื่อนไขการใช้งานเว็บไซต์
        </h2>
        <UAccordion :items="termList" />
      </div>

      <!-- Signup Form -->
      <div class="w-full max-w-md space-y-3 mx-auto">
        <UForm :schema="schema" :state="state" class="space-y-5" @submit="handleSubmit">
          <div class="text-center space-y-2">
            <h2 class="text-xl sm:text-2xl font-bold tracking-tight">สมัครสมาชิกใหม่</h2>
            <p class="max-sm:text-sm">
              กรอกอีเมลของคุณและรหัสผ่านของคุณเพื่อสมัครสมาชิก
            </p>
          </div>

          <!-- Email Field -->
          <UFormField size="xl" :label="$t('form.email')" name="email" required>

            <UInput icon="solar:letter-line-duotone" placeholder="<EMAIL>" id="email" v-model="state.email"
              name="email" type="email" class="w-full" />
          </UFormField>

          <!-- Password Field -->
          <UFormField size="xl" :label="$t('form.password')" name="password" required>

            <UInput icon="solar:password-minimalistic-input-line-duotone" id="password" v-model="state.password"
              name="password" :type="showPassword ? 'text' : 'password'" placeholder="••••••••" class="w-full"
              :ui="{ trailing: 'pe-1' }">
              <template #trailing>
                <UButton color="neutral" variant="ghost"
                  :icon="showPassword ? 'solar:eye-closed-line-duotone' : 'solar:eye-line-duotone'"
                  :aria-label="showPassword ? 'ซ่อนรหัสผ่าน' : 'แสดงรหัสผ่าน'" :aria-pressed="showPassword"
                  @click="showPassword = !showPassword" />
              </template>
            </UInput>
          </UFormField>

          <!-- Confirm Password Field -->
          <UFormField size="xl" :label="$t('form.passwordconfirm')" name="confirmPassword" required>

            <UInput icon="solar:password-minimalistic-input-line-duotone" id="confirmPassword"
              v-model="state.confirmPassword" name="confirmPassword" :type="showPassword ? 'text' : 'password'"
              placeholder="••••••••" class="w-full" :ui="{ trailing: 'pe-1' }">
              <template #trailing>
                <UButton color="neutral" variant="ghost"
                    :icon="showPassword ? 'solar:eye-closed-line-duotone' : 'solar:eye-line-duotone'"
                  :aria-label="showPassword ? 'ซ่อนรหัสผ่าน' : 'แสดงรหัสผ่าน'" :aria-pressed="showPassword"
                  @click="showPassword = !showPassword" />
              </template>
            </UInput>
          </UFormField>

          <!-- Terms Checkbox -->
          <UFormField size="xl" name="acceptTerms" required>

            <UCheckbox v-model="state.acceptTerms" name="acceptTerms"
              label="ฉันได้อ่านและยอมรับข้อตกลงและเงื่อนไขการใช้งานเว็บไซต์" required />
          </UFormField>

          <!-- Submit Button -->
          <!-- UForm จะจัดการ disabled state จาก validation status เมื่อใช้ :schema -->
          <UButton type="submit" icon="solar:user-plus-line-duotone" size="xl" color="primary" block
            :loading="isLoading" :disabled="!isFormValid">
            {{ $t('auth.signup.label') }}
          </UButton>
        </UForm>

        <!-- Navigation Buttons -->
        <div class="flex flex-row gap-3 p-1">
          <UButton block to="/signin" :label="$t('auth.signin.label')" color="info" variant="soft" />
          <UButton block to="/" :label="$t('main')" color="warning" variant="soft" />
        </div>
      </div>
    </div>
  </UContainer>
</template>