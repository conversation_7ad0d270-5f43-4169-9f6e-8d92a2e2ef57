<template>
	<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
		<div class="container mx-auto py-8">
			<div class="mb-8">
				<h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
					Color System Test
				</h1>
				<p class="text-gray-600 dark:text-gray-400">
					ทดสอบระบบสีใหม่ที่ปรับปรุงแล้ว
				</p>
			</div>

			<!-- Color Examples Component -->
			<ColorExamples />

			<!-- Additional Color Utilities -->
			<div class="mt-12 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
				<h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
					Color Utilities
				</h2>
				
				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<!-- Color with Opacity -->
					<div class="space-y-4">
						<h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
							Colors with Opacity
						</h3>
						<div class="space-y-2">
							<div 
								v-for="(opacity, index) in opacityExamples" 
								:key="index"
								class="p-3 rounded border flex items-center justify-between"
								:style="{ backgroundColor: opacity.color }"
							>
								<span class="font-medium" :style="{ color: opacity.contrastColor }">
									{{ opacity.name }}
								</span>
								<span class="text-sm" :style="{ color: opacity.contrastColor }">
									{{ opacity.value }}
								</span>
							</div>
						</div>
					</div>

					<!-- Color Variants -->
					<div class="space-y-4">
						<h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
							Color Variants
						</h3>
						<div class="space-y-2">
							<div 
								v-for="(variant, index) in colorVariantsWithContrast" 
								:key="index"
								class="p-3 rounded border flex items-center justify-between"
								:style="{ backgroundColor: variant.color }"
							>
								<span class="font-medium" :style="{ color: variant.contrastColor }">
									Variant {{ index + 1 }}
								</span>
								<span class="text-sm" :style="{ color: variant.contrastColor }">
									{{ variant.color }}
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Usage Examples -->
			<div class="mt-8 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
				<h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
					Usage Examples
				</h2>
				
				<div class="space-y-6">
					<!-- Buttons -->
					<div>
						<h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
							Button Examples
						</h3>
						<div class="flex flex-wrap gap-4">
							<button 
								class="px-4 py-2 rounded-lg font-medium transition-colors"
								:style="{ backgroundColor: colors.primary, color: getContrastColor(colors.primary) }"
							>
								Primary Button
							</button>
							<button 
								class="px-4 py-2 rounded-lg font-medium transition-colors"
								:style="{ backgroundColor: semanticColors.success, color: getContrastColor(semanticColors.success) }"
							>
								Success Button
							</button>
							<button 
								class="px-4 py-2 rounded-lg font-medium transition-colors"
								:style="{ backgroundColor: semanticColors.error, color: getContrastColor(semanticColors.error) }"
							>
								Error Button
							</button>
							<button 
								class="px-4 py-2 rounded-lg font-medium transition-colors"
								:style="{ backgroundColor: semanticColors.warning, color: getContrastColor(semanticColors.warning) }"
							>
								Warning Button
							</button>
						</div>
					</div>

					<!-- Cards -->
					<div>
						<h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
							Card Examples
						</h3>
						<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
							<div 
								class="p-4 rounded-lg border"
								:style="{ backgroundColor: colors.blue50, borderColor: colors.blue200 }"
							>
								<h4 class="font-semibold mb-2" :style="{ color: colors.blue900 }">
									Info Card
								</h4>
								<p class="text-sm" :style="{ color: colors.blue700 }">
									This is an example info card using the new color system.
								</p>
							</div>
							<div 
								class="p-4 rounded-lg border"
								:style="{ backgroundColor: colors.green50, borderColor: colors.green200 }"
							>
								<h4 class="font-semibold mb-2" :style="{ color: colors.green900 }">
									Success Card
								</h4>
								<p class="text-sm" :style="{ color: colors.green700 }">
									This is an example success card using the new color system.
								</p>
							</div>
							<div 
								class="p-4 rounded-lg border"
								:style="{ backgroundColor: colors.red50, borderColor: colors.red200 }"
							>
								<h4 class="font-semibold mb-2" :style="{ color: colors.red900 }">
									Error Card
								</h4>
								<p class="text-sm" :style="{ color: colors.red700 }">
									This is an example error card using the new color system.
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
const { 
	getColorByName, 
	getColorWithOpacityByName, 
	getSemanticColor, 
	hexToRgb, 
	hexToRgba, 
	getColorPalette, 
	getAllColors, 
	getAllSemanticColors, 
	isLightColor, 
	getContrastColor, 
	generateColorVariants,
	colors,
	semanticColors 
} = useColors()

// Opacity examples
const opacityExamples = computed(() => [
	{ 
		name: 'Primary 10%', 
		color: getColorWithOpacityByName('primary', 0.1), 
		value: getColorWithOpacityByName('primary', 0.1),
		contrastColor: getContrastColor(getColorWithOpacityByName('primary', 0.1))
	},
	{ 
		name: 'Primary 25%', 
		color: getColorWithOpacityByName('primary', 0.25), 
		value: getColorWithOpacityByName('primary', 0.25),
		contrastColor: getContrastColor(getColorWithOpacityByName('primary', 0.25))
	},
	{ 
		name: 'Primary 50%', 
		color: getColorWithOpacityByName('primary', 0.5), 
		value: getColorWithOpacityByName('primary', 0.5),
		contrastColor: getContrastColor(getColorWithOpacityByName('primary', 0.5))
	},
	{ 
		name: 'Primary 75%', 
		color: getColorWithOpacityByName('primary', 0.75), 
		value: getColorWithOpacityByName('primary', 0.75),
		contrastColor: getContrastColor(getColorWithOpacityByName('primary', 0.75))
	},
	{ 
		name: 'Success 25%', 
		color: getColorWithOpacityByName('teal500', 0.25), 
		value: getColorWithOpacityByName('teal500', 0.25),
		contrastColor: getContrastColor(getColorWithOpacityByName('teal500', 0.25))
	},
	{ 
		name: 'Error 25%', 
		color: getColorWithOpacityByName('red500', 0.25), 
		value: getColorWithOpacityByName('red500', 0.25),
		contrastColor: getContrastColor(getColorWithOpacityByName('red500', 0.25))
	}
])

// Color variants
const colorVariants = computed(() => generateColorVariants(colors.primary, 5))

const colorVariantsWithContrast = computed(() =>
	colorVariants.value.map(color => ({
		color,
		contrastColor: getContrastColor(color)
	}))
)
</script> 