<script setup lang="ts">
const { t } = useI18n();

// import { useAuthStore } from '~/stores/auth';

// const authStore = useAuthStore();
// const isLoggedIn = computed(() => authStore.isAuthenticated);

// ใช้ SEO meta สำหรับหน้าแรก
useHomeSeo();

definePageMeta({
    name: 'IndexPage',
    layout: 'empty',
    // middleware: ['guest']
});

const skillListsNew = [
    {
        title: 'เริ่มต้นได้อย่างรวดเร็ว',
        icon: 'noto-v1:helicopter',
        detail: 'สมัคร และเปิดใช้งานเว็บไซต์ผ่านระบบอัตโนมัติบนเว็บไซต์ได้ง่ายๆ ไม่เกิน 3 นาทีก็พร้อมใช้งานแล้ว!',
    },
    {
        title: 'จัดการสินค้าได้ง่าย',
        icon: 'noto-v1:woman-technologist-medium-skin-tone',
        detail: 'จัดการสินค้า ตั้งค่า ปรับแต่ง และดูผลรายงานความเคลื่อนไหวต่างๆ ได้จากหน้าเดียว',
    },
    {
        title: 'ราคาเริ่มต้นแค่ 29 บาท',
        icon: 'noto-v1:glowing-star',
        detail: 'สามารถเช่าวันใช้งานได้ไม่จำกัด ราคาเริ่มต้น แค่ 29 บาท คุ้มค่ามาก',
    },
    {
        title: 'เติมเงิน อัตโนมัติ',
        icon: 'noto-v1:convenience-store',
        detail: 'รองรับระบบเติมเงินผ่าน Gift Wallet, สแกน Qr-Code, Redeam และเติมผ่านแอดมิน',
    },
    {
        title: 'โดเมน หรือ ซัพโดเมน',
        icon: 'noto-v1:globe-showing-asia-australia',
        detail:
            'ใช้งานซัพโดเมนจากเราได้ฟรี หรือสามารถใช้โดเมนส่วนตัวได้ง่ายๆ โดยมาพร้อม SSL มาให้ฟรี พร้อมต่ออายุหายห่วง',
    },
    {
        title: 'สินค้า และหมวดหมู่ ไม่จำกัด',
        icon: 'noto-v1:package',
        detail: 'รองรับการเพิ่มสินค้า สต็อคสินค้า และสร้างหมวดหมูสินค้าได้อย่างอิสระ และไม่จำกัดจำนวน',
    },
    {
        title: 'รองรับสินค้า หลากหลายรูปแบบ',
        icon: 'noto-v1:backpack',
        detail:
            'รองรับการวางขายสินค้าหลากหลายประเภท เช่น บัตรเติมเงิน, เบอร์โทรศัพท์, ไอดีเกม, ไอดีแอพต่างๆ, รหัสโค้ดต่างๆ และอื่นๆ มากมาย',
    },
    {
        title: 'ระบบสมาชิก สะสมแต้ม และโปรไฟล์',
        icon: 'noto-v1:military-medal',
        detail:
            'มีระบบสมาชิกสมบูรณ์ที่สุด สามารถให้สมาชิกเติมสะสมแต้ม ระบบนายหน้าสมาชิกช่วยขายได้ส่วนแบ่ง พร้อมโปรไฟล์เพื่อดูประวัติและรายละเอียดต่างๆ ของสมาชิก',
    },
    {
        title: 'ระบบเกมให้เลือกมากมาย',
        icon: 'noto-v1:game-die',
        detail: 'มีระบบเกม เช่น กล่องสุ่ม, วงล้อมหาสนุก, ลานเลื่อนมหาสนุก และอื่นๆ',
    },
    {
        title: 'Style เว็บไซต์',
        icon: 'noto-v1:performing-arts',
        detail:
            'รองรับการเลือกใช้ Style เว็บไซต์ได้เอง มีมากมาย และเพิ่มเรื่อยๆ ในอนาคต รวมทั้งสามารถ ปรับแต่งได้อย่างอิสระ',
    },
    {
        title: 'ทดลองระบบ ในอนาคต',
        icon: 'noto-v1:bell-with-slash',
        detail:
            'เว็บไซต์มีการพัฒนาอยู่อย่างต่อเนื่อง ผู้ใช้บริการจึงสามารถเลือกทดลองระบบใหม่ๆได้ง่ายๆ เพียงปิด-เปิด การทำงานของระบบ นั้นๆ ในคลิกเดียว',
    },
    {
        title: 'ซัพพอร์ต ช่วยเหลือเป็นอย่างดี',
        icon: 'noto-v1:exclamation-question-mark',
        detail:
            'มีคำถามที่พบบ่อย, วิธีเริ่มต้นใช้งาน, กลุ่ม Discord และ แฟนเพจ รองรับคอยช่วยเหลือ และตอบคำถามได้อย่างรวดเร็วที่สุด',
    },
];
</script>

<template>
    <div class="max-w-7xl mx-auto">

        <div class="text-center">
            <h1 class="text-2xl font-bold tracking-tight sm:text-4xl">
                ระบบเช่าใช้งานเว็บไซต์ครบวงจร
            </h1>
            <p class="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300">
                เช่าใช้งานเว็บไซต์ที่เหมาะกับความต้องการของคุณ พร้อมการจัดการที่ง่ายดายและปลอดภัย
            </p>

            <!-- Section: Navigation Links -->
            <div class="flex flex-col w-full sm:flex-row gap-3 mx-auto my-5">
                <!-- <NuxtLink v-if="isLoggedIn" to="/dashboard"
                    class="btn-custom-home from-indigo-500 via-sky-500 to-emerald-400">
                    <Icon name="solar:card-2-line-duotone" class="size-8" />
                    <span>จัดการเว็บไซต์</span>
                </NuxtLink> -->
                <NuxtLink to="/signup" class="btn-custom-home from-indigo-500 via-purple-500 to-pink-400">
                    <Icon name="solar:user-plus-bold-duotone" class="size-8" />
                    <span>{{ t('auth.signup.label') }}</span>
                </NuxtLink>
                <NuxtLink to="/signin" class="btn-custom-home from-indigo-500 via-sky-500 to-emerald-400">
                    <Icon name="solar:login-3-bold-duotone" class="size-8" />
                    <span>{{ t('auth.signin.label') }}</span>
                </NuxtLink>
                <NuxtLink to="/help" class="btn-custom-home from-red-500 via-orange-400 to-yellow-400">
                    <Icon name="solar:book-2-line-duotone" class="size-8" />
                    <span>{{ t('help.title') }}</span>
                </NuxtLink>
            </div>
        </div>

        <div class="mx-auto max-w-2xl lg:text-center space-y-1 my-5">
            <!-- <h2 class="text-base font-semibold leading-7 text-primary-600 dark:text-primary-400">ฟีเจอร์ครบครัน -->
            <!-- </h2> -->
            <p class="font-bold tracking-tight sm:text-xl">
                ทุกสิ่งที่คุณต้องการสำหรับการเช่าใช้งานเว็บไซต์
            </p>
            <p class="leading-8 text-gray-600 dark:text-gray-300">
                ระบบเช่าใช้งานเว็บไซต์ของเรามาพร้อมกับฟีเจอร์ที่ครบครัน ใช้งานง่าย และปลอดภัย
            </p>
        </div>

    </div>
    <div class="max-w-7xl mx-auto">
        <!-- Section: Skill Lists -->
        <div class="grid gap-4 grid-cols-1 md:grid-cols-2">
            <div v-for="(item, i) in skillListsNew" :key="i"
                class="flex flex-row gap-2 md:gap-4 p-2 md:p-4 rounded-lg bg-[var(--ui-bg-2)]/90">
                <Icon :name="item.icon" class="h-10 w-10 sm:w-12 sm:h-12" />
                <div class="flex flex-1 flex-col">
                    <span class="text-base md:text-lg font-medium">{{ item?.title }}</span>
                    <span class="text-sm md:text-base font-light">{{ item?.detail }}</span>
                </div>
            </div>
        </div>
    </div>


</template>
<style scoped>
@reference "@/assets/css/main.css";

.btn-custom-home {
    @apply flex justify-center items-center p-5 bg-gradient-to-tr hover:scale-102 transition-all duration-300 h-14 md:h-16 gap-1 text-white rounded-lg opacity-90 w-full;
}

.btn-custom-home span {
    @apply text-lg md:text-xl font-semibold;
}

.observer {
    height: 20px;
    background: transparent;
}
</style>