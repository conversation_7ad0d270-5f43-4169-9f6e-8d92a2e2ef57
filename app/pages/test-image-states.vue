<template>
    <div class="container mx-auto py-8 space-y-8">
        <h1 class="text-3xl font-bold mb-8">UiImage - สถานะการโหลดและ Fallbacks</h1>

        <!-- Loading States -->
        <section>
            <h2 class="text-2xl font-semibold mb-6">🔄 สถานะการโหลด (Loading States)</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">

                <!-- Loading State -->
                <div>
                    <h3 class="text-lg font-medium mb-3">กำลังโหลด</h3>
                    <UiImage :src="slowLoadingImage" alt="Slow loading image" width="200" height="150" rounded="lg"
                        border />
                    <p class="text-sm text-gray-600 mt-2">รูปที่โหลดช้า - จะแสดง loading spinner</p>
                </div>

                <!-- Custom Loading -->
                <div>
                    <h3 class="text-lg font-medium mb-3">Loading แบบกำหนดเอง</h3>
                    <UiImage :src="slowLoadingImage" alt="Custom loading" width="200" height="150" rounded="lg" border>
                        <template #loading>
                            <div
                                class="absolute inset-0 bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center">
                                <div class="text-center">
                                    <Icon name="solar:download-bold-duotone"
                                        class="w-8 h-8 text-blue-600 animate-bounce mx-auto mb-2" />
                                    <p class="text-sm text-blue-600">กำลังดาวน์โหลด...</p>
                                </div>
                            </div>
                        </template>
                    </UiImage>
                    <p class="text-sm text-gray-600 mt-2">Loading state แบบกำหนดเอง</p>
                </div>

                <!-- Fast Loading -->
                <div>
                    <h3 class="text-lg font-medium mb-3">โหลดเสร็จแล้ว</h3>
                    <UiImage src="https://picsum.photos/200/150?random=1" alt="Fast loading image" width="200"
                        height="150" rounded="lg" border @load="handleImageLoad" />
                    <p class="text-sm text-gray-600 mt-2">รูปที่โหลดเร็ว - แสดงรูปทันที</p>
                </div>
            </div>
        </section>

        <!-- Error States & Fallbacks -->
        <section>
            <h2 class="text-2xl font-semibold mb-6">❌ สถานะ Error และ Fallbacks</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">

                <!-- Default Fallback -->
                <div>
                    <h3 class="text-lg font-medium mb-3">Fallback เริ่มต้น</h3>
                    <UiImage src="https://invalid-url.com/broken.jpg" alt="Broken image" width="150" height="150"
                        rounded="lg" border @error="handleImageError" />
                    <p class="text-sm text-gray-600 mt-2">URL ไม่ถูกต้อง - แสดง fallback เริ่มต้น</p>
                </div>

                <!-- Custom Icon Fallback -->
                <div>
                    <h3 class="text-lg font-medium mb-3">Icon Fallback</h3>
                    <UiImage src="https://invalid-url.com/broken.jpg" alt="Product image" width="150" height="150"
                        rounded="lg" border fallback-icon="solar:bag-2-bold-duotone" fallback-text="สินค้า" />
                    <p class="text-sm text-gray-600 mt-2">Fallback icon สำหรับสินค้า</p>
                </div>

                <!-- Avatar Fallback -->
                <div>
                    <h3 class="text-lg font-medium mb-3">Avatar Fallback</h3>
                    <UiImage src="https://invalid-url.com/broken.jpg" alt="John Doe" width="150" height="150"
                        rounded="full" border fallback-icon="solar:user-circle-bold-duotone" fallback-text="JD" />
                    <p class="text-sm text-gray-600 mt-2">Fallback สำหรับ avatar</p>
                </div>

                <!-- Custom Fallback -->
                <div>
                    <h3 class="text-lg font-medium mb-3">Fallback กำหนดเอง</h3>
                    <UiImage src="https://invalid-url.com/broken.jpg" alt="Custom fallback" width="150" height="150"
                        rounded="lg" border>
                        <template #fallback>
                            <div
                                class="w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-purple-500 to-pink-600 text-white">
                                <Icon name="solar:gallery-bold-duotone" class="w-8 h-8 mb-2" />
                                <span class="text-sm font-medium">ไม่มีรูป</span>
                                <UButton size="xs" color="white" variant="ghost" class="mt-2" @click="retryLoad">
                                    ลองใหม่
                                </UButton>
                            </div>
                        </template>
                    </UiImage>
                    <p class="text-sm text-gray-600 mt-2">Fallback แบบกำหนดเองพร้อมปุ่ม</p>
                </div>
            </div>
        </section>

        <!-- Interactive Features -->
        <section>
            <h2 class="text-2xl font-semibold mb-6">🎯 Features เชิงโต้ตอบ</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">

                <!-- Clickable -->
                <div>
                    <h3 class="text-lg font-medium mb-3">คลิกได้</h3>
                    <UiImage src="https://picsum.photos/200/150?random=2" alt="Clickable image" width="200" height="150"
                        rounded="lg" clickable @click="handleClick"
                        class="hover:shadow-xl transition-shadow cursor-pointer" />
                    <p class="text-sm text-gray-600 mt-2">คลิกเพื่อดูการทำงาน</p>
                </div>

                <!-- Zoomable -->
                <div>
                    <h3 class="text-lg font-medium mb-3">ซูมได้</h3>
                    <UiImage src="https://picsum.photos/200/150?random=3" alt="Zoomable image" width="200" height="150"
                        rounded="lg" zoomable @zoom="handleZoom" />
                    <p class="text-sm text-gray-600 mt-2">คลิกเพื่อซูม</p>
                </div>

                <!-- With Overlay -->
                <div>
                    <h3 class="text-lg font-medium mb-3">มี Overlay</h3>
                    <UiImage src="https://picsum.photos/200/150?random=4" alt="Overlay example" width="200" height="150"
                        rounded="lg" clickable class="group">
                        <template #overlay>
                            <div
                                class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center rounded-lg">
                                <Icon name="solar:eye-bold"
                                    class="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                            </div>
                        </template>
                    </UiImage>
                    <p class="text-sm text-gray-600 mt-2">Hover เพื่อดู overlay</p>
                </div>
            </div>
        </section>

        <!-- Real-world Examples -->
        <section>
            <h2 class="text-2xl font-semibold mb-6">🌟 ตัวอย่างการใช้งานจริง</h2>

            <!-- Product Gallery -->
            <div class="mb-8">
                <h3 class="text-xl font-medium mb-4">แกลเลอรี่สินค้า</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <UiImage v-for="i in 8" :key="i"
                        :src="Math.random() > 0.3 ? `https://picsum.photos/300/300?random=${i + 10}` : null"
                        :alt="`Product ${i}`" width="100%" aspect-ratio="1/1" rounded="lg" clickable zoomable
                        fallback-icon="solar:bag-2-bold-duotone" :fallback-text="`P${i}`"
                        fallback-bg="bg-gradient-to-br from-blue-500 to-purple-600 text-white"
                        @click="() => handleProductClick(i)" class="hover:shadow-lg transition-shadow" />
                </div>
            </div>

            <!-- User Profiles -->
            <div class="mb-8">
                <h3 class="text-xl font-medium mb-4">โปรไฟล์ผู้ใช้</h3>
                <div class="flex flex-wrap gap-4">
                    <div v-for="user in mockUsers" :key="user.id"
                        class="flex items-center gap-3 bg-white dark:bg-gray-900 p-4 rounded-lg border">
                        <UiImage :src="user.avatar" :alt="user.name" width="48" height="48" rounded="full" border
                            fallback-icon="solar:user-circle-bold-duotone" :fallback-text="user.name.charAt(0)"
                            clickable @click="() => handleUserClick(user)" />
                        <div>
                            <h4 class="font-medium">{{ user.name }}</h4>
                            <p class="text-sm text-gray-600">{{ user.role }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Event Log -->
        <section v-if="eventLog.length > 0">
            <h2 class="text-2xl font-semibold mb-6">📝 Event Log</h2>
            <div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 max-h-40 overflow-y-auto">
                <div v-for="(event, index) in eventLog" :key="index" class="text-sm mb-1">
                    <span class="text-gray-500">{{ event.time }}</span> -
                    <span class="font-medium">{{ event.message }}</span>
                </div>
            </div>
        </section>
    </div>
</template>

<script setup>
// Page meta
definePageMeta({
    title: 'UiImage States Test',
    description: 'Testing UiImage loading states and fallbacks'
})

// State
const eventLog = ref([])
const slowLoadingImage = ref('https://httpbin.org/delay/3/image/jpeg?width=200&height=150')

// Mock data
const mockUsers = ref([
    { id: 1, name: 'สมชาย ใจดี', role: 'ผู้ดูแล', avatar: Math.random() > 0.5 ? 'https://picsum.photos/100/100?random=101' : null },
    { id: 2, name: 'สมหญิง รักงาน', role: 'บรรณาธิการ', avatar: Math.random() > 0.5 ? 'https://picsum.photos/100/100?random=102' : null },
    { id: 3, name: 'สมศักดิ์ ขยัน', role: 'ผู้ใช้', avatar: Math.random() > 0.5 ? 'https://picsum.photos/100/100?random=103' : null },
    { id: 4, name: 'สมพร สวยงาม', role: 'ผู้ใช้', avatar: Math.random() > 0.5 ? 'https://picsum.photos/100/100?random=104' : null },
])

// Methods
const addToLog = (message) => {
    eventLog.value.unshift({
        time: new Date().toLocaleTimeString(),
        message
    })

    // Keep only last 10 events
    if (eventLog.value.length > 10) {
        eventLog.value = eventLog.value.slice(0, 10)
    }
}

const handleImageLoad = (event) => {
    addToLog('✅ รูปภาพโหลดสำเร็จ')
}

const handleImageError = (event) => {
    addToLog('❌ รูปภาพโหลดไม่สำเร็จ - แสดง fallback')
}

const handleClick = (event) => {
    addToLog('👆 คลิกรูปภาพ')
}

const handleZoom = (event) => {
    addToLog('🔍 ซูมรูปภาพ')
}

const handleProductClick = (productId) => {
    addToLog(`🛒 คลิกสินค้า #${productId}`)
}

const handleUserClick = (user) => {
    addToLog(`👤 คลิกผู้ใช้: ${user.name}`)
}

const retryLoad = () => {
    addToLog('🔄 ลองโหลดรูปใหม่')
    // In real app, you would retry loading the image
}

// Auto-add some events on mount
onMounted(() => {
    addToLog('🚀 หน้าทดสอบ UiImage โหลดเสร็จแล้ว')
})
</script>

<style scoped>
.group:hover .group-hover\:opacity-100 {
    opacity: 1;
}

.group:hover .group-hover\:bg-opacity-50 {
    background-opacity: 0.5;
}
</style>