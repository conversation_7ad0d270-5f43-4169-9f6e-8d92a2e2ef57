<script setup lang="ts">
import type { FormSubmitEvent } from '@nuxt/ui';
import { z } from 'zod';
import { passwordSchema } from '~/utils/validationSchemas';

definePageMeta({
  layout: 'empty',
  middleware: ['guest'],
});

const route = useRoute();
const token = route.query.token as string;
const isValidToken = ref(false);
const tokenError = ref('');
const isLoading = ref(false);

if (!token) {
  navigateTo('/signin');
}

// เพิ่มฟังก์ชันตรวจสอบ token
const verifyToken = async () => {
  try {
    isLoading.value = true;

    const response = await $fetch<ApiResponse>('/api/v1/auth/verify-reset-token', {
      method: 'GET',
      params: { token },
    });

    if (response?.success) {
      isValidToken.value = true;
    } else {
      tokenError.value = response?.message || 'Token ไม่ถูกต้องหรือหมดอายุแล้ว';
      setTimeout(() => {
        navigateTo('/signin');
      }, 3000);
    }
  } catch (error: any) {
    console.log('Error object from Nuxt API:', error);
    // ลองเข้าถึงข้อความ error จาก property อื่นๆ ที่เป็นไปได้
    tokenError.value =
      error.data?.message ||
      error.message ||
      error._data?.message ||
      'เกิดข้อผิดพลาดในการตรวจสอบ token';
    setTimeout(() => {
      navigateTo('/signin');
    }, 3000);
  } finally {
    isLoading.value = false;
  }
};

// เรียกใช้ฟังก์ชันตรวจสอบ token เมื่อโหลดหน้า
onMounted(() => {
  verifyToken();
});

// Form Schema (ใช้ Zod)
const schema = z
  .object({
    newPassword: passwordSchema,
    confirmPassword: z.string().min(1, 'กรุณากรอกรหัสผ่านอีกครั้ง'), // Schema เบื้องต้น, refine ทีหลัง
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: 'รหัสผ่านใหม่และยืนยันรหัสผ่านไม่ตรงกัน',
    path: ['confirmPassword'], // กำหนดว่า error นี้จะผูกกับ field confirmPassword
  });

// Form State (ใช้ Reactive)
type Schema = z.output<typeof schema>;
const state = reactive<Partial<Schema>>({
  newPassword: '',
  confirmPassword: '',
});

const pending = ref(false);
const showPassword = ref(false);

// Computed property เพื่อตรวจสอบความถูกต้องของฟอร์มด้วย Zod
const isFormValid = computed(() => {
  const result = schema.safeParse(state);
  return result.success;
});

// ปรับใช้กับ UForm และ Zod
async function submitForm(event: FormSubmitEvent<Schema>) {
  try {
    pending.value = true;
    const response = await $fetch<ApiResponse>('/api/v1/auth/reset-password', {
      method: 'POST',
      body: {
        token,
        newPassword: event.data.newPassword?.trim(), // ใช้ optional chaining เผื่อ newPassword เป็น undefined
      },
    });

    if (response?.success) {
      useToast().add({
        title: 'รีเซ็ตรหัสผ่านสำเร็จ',
        description: 'คุณสามารถเข้าสู่ระบบด้วยรหัสผ่านใหม่ได้แล้ว',
        icon: 'i-heroicons-check-circle',
        color: 'success',
        duration: 3000,
      });
      navigateTo('/signin');
    }
  } catch (error: any) {
    useToast().add({
      title: 'เกิดข้อผิดพลาด',
      description: error.data?.message || error.message || 'ไม่สามารถรีเซ็ตรหัสผ่านได้ กรุณาลองใหม่อีกครั้ง',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'error',
      duration: 3000,
    });
  } finally {
    pending.value = false;
  }
}
</script>

<template>
  <UContainer class="max-w-lg">
    <div v-if="tokenError" class="text-center space-y-2">
      <h2 class="text-2xl font-bold tracking-tight text-red-500">เกิดข้อผิดพลาด</h2>
      <p class="text-sm text-red-400">
        {{ tokenError }}
      </p>
      <p class="text-sm">
        กำลังนำคุณไปยังหน้าเข้าสู่ระบบ...
      </p>
    </div>

    <UForm v-else-if="isValidToken" :schema="schema" :state="state" class="space-y-5" @submit="submitForm">
      <div class="text-center space-y-2">
        <h2 class="text-2xl font-bold tracking-tight">{{ $t('auth.resetPassword.label') }}</h2>
        <p class="text-sm">
          กรุณากรอกรหัสผ่านใหม่ของคุณ
        </p>
      </div>

      <UFormField size="xl" label="รหัสผ่านใหม่" name="newPassword" required>
        <UInput icon="solar:password-minimalistic-input-line-duotone" v-model="state.newPassword" name="newPassword"
          :type="showPassword ? 'text' : 'password'" placeholder="••••••••" class="w-full" :ui="{ trailing: 'pe-1' }">
          <template #trailing>
            <UButton color="neutral" variant="ghost" :icon="showPassword ? 'i-heroicons-eye-slash' : 'i-heroicons-eye'"
              :aria-label="showPassword ? 'ซ่อนรหัสผ่าน' : 'แสดงรหัสผ่าน'" :aria-pressed="showPassword"
              @click="showPassword = !showPassword" />
          </template>
        </UInput>
      </UFormField>

      <UFormField size="xl" label="ยืนยันรหัสผ่าน" name="confirmPassword" required>
        <UInput icon="solar:password-minimalistic-input-line-duotone" v-model="state.confirmPassword"
          name="confirmPassword" :type="showPassword ? 'text' : 'password'" placeholder="••••••••" class="w-full"
          :ui="{ trailing: 'pe-1' }">
          <template #trailing>
            <UButton color="neutral" variant="ghost" :icon="showPassword ? 'i-heroicons-eye-slash' : 'i-heroicons-eye'"
              :aria-label="showPassword ? 'ซ่อนรหัสผ่าน' : 'แสดงรหัสผ่าน'" :aria-pressed="showPassword"
              @click="showPassword = !showPassword" />
          </template>
        </UInput>
      </UFormField>

      <UButton size="xl" type="submit" color="primary" block :loading="pending" :disabled="!isFormValid">
        {{ $t('auth.resetPassword.label') }}
      </UButton>

      <!-- Navigation Buttons -->
      <div class="flex flex-row gap-3 p-1">
        <UButton block to="/signin" :label="$t('auth.signin.label')" color="info" variant="soft" />
        <UButton block to="/" :label="$t('main')" color="warning" variant="soft" />
      </div>
    </UForm>
  </UContainer>
</template>