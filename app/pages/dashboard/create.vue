<script setup lang="ts">
import { useVuelidate } from '@vuelidate/core';
import { required, minLength, maxLength, helpers } from '@vuelidate/validators';
import { usePackage } from '~/composables/usePackage';
import { useDiscount } from '~/composables/useDiscount';

definePageMeta({
  layout: 'manage',
  middleware: ['auth'],
});

const { createSite } = useSites();

// State
const loading = ref(false);

// Domain type selection
const domainType = ref<'subdomain' | 'custom'>('subdomain');

// Domain checking
const checkingDomain = ref(false);
const domainAvailable = ref<boolean | null>(null);
const selectedMainDomain = ref('is1.shop');

// Form data
const siteForm = reactive({
  name: '',
  subDomain: '',
  customDomain: '',
  domainType: 'subdomain',
  mainDomain: 'is1.shop',
  plan: 'monthly',
});

// Use composables
const {
  loadingPackages,
  packages,
  selectedPlan,
  selectedPackage,
  originalPrice,
  fetchPackages
} = useSubscription();

// Sync selectedPlan with siteForm.plan
watch(() => siteForm.plan, (newPlan) => {
  selectedPlan.value = newPlan;
});

const {
  discountCode,
  checkingDiscount,
  appliedDiscount,
  discountAmount,
  finalPrice,
  applyDiscount
} = useDiscount(originalPrice, selectedPackage);

// Main domains available
const mainDomains = [{ value: 'is1.shop', label: 'is1.shop' }];

// Reserved words for domain validation
const reservedWords = ['admin', 'api', 'www', 'mail', 'ftp', 'blog', 'shop', 'store', 'app', 'mobile', 'support', 'help', 'docs', 'dev', 'test', 'staging', 'prod', 'production'];

// Custom validators
const isNotReserved = (value: string) => {
  if (!value) return true;
  return !reservedWords.includes(value.toLowerCase());
};

const isValidDomainFormat = (value: string) => {
  if (!value) return true;
  // More comprehensive domain validation
  const domainRegex = /^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?(\.[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/;
  return domainRegex.test(value.toLowerCase());
};

const hasValidTLD = (value: string) => {
  if (!value) return true;
  const parts = value.split('.');
  if (parts.length < 2) return false;
  const tld: any = parts[parts.length - 1];
  return tld.length >= 2 && tld.length <= 6;
};

// Vuelidate validation rules
const rules = computed(() => {
  const subDomainPattern = /^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$/;
  const customDomainPattern = /^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?(\.[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)+$/;

  const baseRules: any = {
    name: {
      required: helpers.withMessage('กรุณาใส่ชื่อเว็บไซต์', required),
      minLength: helpers.withMessage('ชื่อเว็บไซต์ต้องมีอย่างน้อย 2 ตัวอักษร', minLength(2)),
      maxLength: helpers.withMessage('ชื่อเว็บไซต์ต้องไม่เกิน 100 ตัวอักษร', maxLength(100))
    },
    plan: {
      required: helpers.withMessage('กรุณาเลือกแพ็กเกจ', required)
    },
  };

  // Add domain validation based on domain type
  if (domainType.value === 'subdomain') {
    baseRules.subDomain = {
      required: helpers.withMessage('กรุณาใส่ซัพโดเมน', required),
      minLength: helpers.withMessage('ซัพโดเมนต้องมีอย่างน้อย 3 ตัวอักษร', minLength(3)),
      maxLength: helpers.withMessage('ซัพโดเมนต้องไม่เกิน 63 ตัวอักษร', maxLength(63)),
      pattern: helpers.withMessage('ใช้ได้เฉพาะ a-z, 0-9 และ - (ไม่ขึ้นต้นหรือลงท้ายด้วย -)', helpers.regex(subDomainPattern)),
      notReserved: helpers.withMessage('ชื่อนี้เป็นคำสงวน กรุณาเลือกชื่ออื่น', isNotReserved),
      domainAvailable: helpers.withMessage('โดเมนไม่ว่าง กรุณาเลือกชื่ออื่น', () => domainAvailable.value !== false),
      domainChecked: helpers.withMessage('กรุณาตรวจสอบโดเมนก่อน', () => domainAvailable.value !== null),
    };
  } else if (domainType.value === 'custom') {
    baseRules.customDomain = {
      required: helpers.withMessage('กรุณาใส่โดเมนส่วนตัว', required),
      minLength: helpers.withMessage('โดเมนต้องมีอย่างน้อย 4 ตัวอักษร', minLength(4)),
      maxLength: helpers.withMessage('โดเมนต้องไม่เกิน 253 ตัวอักษร', maxLength(253)),
      pattern: helpers.withMessage('รูปแบบโดเมนไม่ถูกต้อง (เช่น example.com)', helpers.regex(customDomainPattern)),
      validFormat: helpers.withMessage('รูปแบบโดเมนไม่ถูกต้อง', isValidDomainFormat),
      validTLD: helpers.withMessage('ส่วนท้ายของโดเมนไม่ถูกต้อง', hasValidTLD),
      domainAvailable: helpers.withMessage('โดเมนไม่ว่าง กรุณาเลือกชื่ออื่น', () => domainAvailable.value !== false),
      domainChecked: helpers.withMessage('กรุณาตรวจสอบโดเมนก่อน', () => domainAvailable.value !== null),
    };
  }

  return baseRules;
});

// Vuelidate instance
const v$ = useVuelidate(rules, siteForm);

// Check domain availability
const checkDomainAvailability = async () => {
  checkingDomain.value = true;
  domainAvailable.value = null;

  try {
    const url = `/api/v1/site/check-domain?domainType=${domainType.value}&subDomain=${siteForm.subDomain}&mainDomain=${selectedMainDomain.value}&customDomain=${siteForm.customDomain}`;

    const response: any = await $fetch<any>(url);
    domainAvailable.value = response.data?.available ?? null;

    const domainToShow =
      domainType.value === 'subdomain'
        ? `${siteForm.subDomain}.${selectedMainDomain.value}`
        : siteForm.customDomain;

    if (domainAvailable.value) {
      useToast().add({
        title: 'โดเมนว่าง',
        description: `${domainToShow} สามารถใช้งานได้`,
        color: 'success',
        duration: 1000,
      });
    } else {
      useToast().add({
        title: 'โดเมนไม่ว่าง',
        description: `${domainToShow} ถูกใช้งานแล้ว กรุณาเลือกชื่ออื่น`,
        color: 'error',
        duration: 1000,
      });
    }
  } catch (error: any) {
    useToast().add({
      title: 'ผิดพลาด',
      description: error.message || 'ไม่สามารถตรวจสอบโดเมนได้',
      color: 'error',
      duration: 3000,
    });
  } finally {
    checkingDomain.value = false;
  }
};

// Submit
const createWebsite = async () => {
  const isValid = await v$.value.$validate();
  if (!isValid) return;

  loading.value = true;
  try {
    const siteData = {
      name: siteForm.name,
      domainType: domainType.value,
      mainDomain: selectedMainDomain.value,
      subDomain: domainType.value === 'subdomain' ? siteForm.subDomain : undefined,
      customDomain: domainType.value === 'custom' ? siteForm.customDomain : undefined,
      plan: siteForm.plan,
    };

    const response: any = await createSite(siteData as any);

    useToast().add({
      title: 'สำเร็จ',
      description: 'สร้างเว็บไซต์เรียบร้อยแล้ว',
      color: 'success',
      duration: 1000,
    });

    if (response.data?.site?._id) {
      await navigateTo(`/dashboard/${response.data?.site?._id}`);
    }

  } catch (error: any) {
    useToast().add({
      title: error.data.statusMessage || 'ผิดพลาด',
      description: error.data.message || 'ไม่สามารถสร้างเว็บไซต์ได้',
      color: 'error',
      duration: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// Input handlers
const onSubDomainInput = (value: string) => {
  // Allow a-z, 0-9, and hyphens (but not at start/end)
  let cleanValue = value.toLowerCase().replace(/[^a-z0-9-]/g, '');

  // Remove hyphens at start and end
  cleanValue = cleanValue.replace(/^-+|-+$/g, '');

  // Prevent consecutive hyphens
  cleanValue = cleanValue.replace(/-{2,}/g, '-');

  siteForm.subDomain = cleanValue;
  domainAvailable.value = null;
  if (v$.value.subDomain) {
    v$.value.subDomain.$touch();
  }
};

const onCustomDomainInput = (value: string) => {
  // Allow a-z, 0-9, hyphens, and dots
  let cleanValue = value.toLowerCase().replace(/[^a-z0-9.-]/g, '');

  // Prevent consecutive dots
  cleanValue = cleanValue.replace(/\.{2,}/g, '.');

  // Prevent dots at start
  cleanValue = cleanValue.replace(/^\.+/, '');

  // Limit to reasonable domain structure (domain.tld)
  const parts = cleanValue.split('.');
  if (parts.length > 2) {
    cleanValue = parts.slice(0, 2).join('.');
  }

  siteForm.customDomain = cleanValue;
  domainAvailable.value = null;
  if (v$.value.customDomain) {
    v$.value.customDomain.$touch();
  }
};


// Computed properties for error messages
const nameError = computed(() => {
  const error = v$.value.name.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});
const subDomainError = computed(() => {
  const error = v$.value.subDomain.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});
const customDomainError = computed(() => {
  const error = v$.value.customDomain.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});
const planError = computed(() => {
  const error = v$.value.plan.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});

// Computed property สำหรับ validation ปุ่ม "สร้างเว็บไซต์"
const isFormValid = computed(() => {
  // ตรวจสอบว่า form ผ่าน validation และไม่มีการโหลดข้อมูล
  const basicValidation = !v$.value.$invalid && !checkingDomain.value && !loadingPackages.value;

  // ตรวจสอบว่าได้ตรวจสอบโดเมนแล้วและโดเมนว่าง
  const domainValidation = domainAvailable.value === true;

  // ตรวจสอบว่าเลือกแพ็กเกจแล้ว
  const packageValidation = selectedPackage.value !== null;

  return basicValidation && domainValidation && packageValidation;
});

// Watch
watch([domainAvailable, domainType], ([available, type]) => {
  if (type === 'subdomain' && available !== null) {
    if (v$.value.subDomain) {
      v$.value.subDomain.$touch();
    }
  }
  if (type === 'custom' && available !== null) {
    if (v$.value.customDomain) {
      v$.value.customDomain.$touch();
    }
  }
});

// Watch domainType เพื่อ reset domainAvailable และ touch validation
watch(domainType, () => {
  domainAvailable.value = null;
  // Touch validation for the appropriate domain field
  if (domainType.value === 'subdomain') {
    if (v$.value.subDomain) {
      v$.value.subDomain.$touch();
    }
  } else if (domainType.value === 'custom') {
    if (v$.value.customDomain) {
      v$.value.customDomain.$touch();
    }
  }
});

onMounted(() => {
  fetchPackages();
});

</script>

<template>
  <UContainer>
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
        สร้างเว็บไซต์ใหม่
      </h1>
      <p class="text-gray-600 dark:text-gray-400 mt-2">
        กรอกข้อมูลเพื่อเริ่มต้นสร้างเว็บไซต์ของคุณ
      </p>
    </div>

    <!-- Two Column Layout -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Left Column - Form -->
      <div class="lg:col-span-2">
        <UCard variant="soft">
          <div class="space-y-8">
            <!-- Basic Info Section -->
            <div class="space-y-6">
              <h2 class="text-lg font-semibold flex items-center gap-2">
                <Icon name="solar:info-circle-line-duotone" class="size-7 text-primary" />
                ข้อมูลพื้นฐาน
              </h2>

              <UFormField size="xl" label="ชื่อเว็บไซต์" name="name" required :error="nameError">
                <UInput icon="solar:letter-line-duotone" placeholder="ชื่อเว็บไซต์ของคุณ" v-model="siteForm.name"
                  class="w-full" @blur="() => { if (v$.name) v$.name.$touch(); }" />
              </UFormField>
            </div>

            <!-- Domain Section -->
            <div class="space-y-6">
              <h2 class="text-lg font-semibold flex items-center gap-2">
                <Icon name="solar:global-line-duotone" class="size-7 text-primary" />
                เลือกโดเมน
              </h2>

              <!-- Domain Type Selection -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Subdomain Option -->
                <div :class="[
                  'border-2 rounded-lg p-4 cursor-pointer transition-all',
                  domainType === 'subdomain'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                ]" @click="domainType = 'subdomain'">
                  <div class="flex items-center gap-3">
                    <div
                      class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                      <Icon name="solar:global-line-duotone" class="w-4 h-4 text-green-600" />
                    </div>
                    <div>
                      <h4 class="font-medium text-gray-900 dark:text-white">ซัพโดเมนฟรี</h4>
                      <p class="text-sm text-gray-600 dark:text-gray-400">yourname.is1.shop</p>
                    </div>
                  </div>
                </div>

                <!-- Custom Domain Option -->
                <div :class="[
                  'border-2 rounded-lg p-4 cursor-pointer transition-all',
                  domainType === 'custom'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                ]" @click="domainType = 'custom'">
                  <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                      <Icon name="solar:star-line-duotone" class="w-4 h-4 text-blue-600" />
                    </div>
                    <div>
                      <h4 class="font-medium text-gray-900 dark:text-white">โดเมนส่วนตัว</h4>
                      <p class="text-sm text-gray-600 dark:text-gray-400">yourdomain.com</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Domain Input -->
              <div v-if="domainType === 'subdomain'" class="space-y-4">
                <div class="w-full flex flex-col sm:flex-row gap-4">
                  <UFormField label="ซัพโดเมน" name="subdomain" required :error="subDomainError">
                    <UInput size="xl" :model-value="siteForm.subDomain" placeholder="yourname"
                      @update:model-value="onSubDomainInput" />
                  </UFormField>

                  <UFormField label="โดเมนหลัก" name="mainDomain">
                    <USelect size="xl" v-model="selectedMainDomain" :items="mainDomains"
                      @update:model-value="domainAvailable = null" />
                  </UFormField>

                  <UFormField label="ตรวจสอบ" name="checkDomain">
                    <UButton variant="soft" size="xl" :loading="checkingDomain"
                      :disabled="siteForm.subDomain.trim().length <= 5" @click="checkDomainAvailability" block>
                      ตรวจสอบ
                    </UButton>
                  </UFormField>
                </div>

                <!-- Domain Preview -->
                <div v-if="siteForm.subDomain.trim()" class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="text-sm text-gray-600 dark:text-gray-400">ที่อยู่เว็บไซต์ของคุณ:</p>
                      <p class="text-lg text-gray-900 dark:text-white">
                        {{ siteForm.subDomain }}.{{ selectedMainDomain }}
                      </p>
                    </div>
                    <div v-if="domainAvailable !== null">
                      <UBadge size="xl" :color="domainAvailable ? 'success' : 'error'" variant="subtle">
                        {{ domainAvailable ? 'ใช้งานได้' : 'ไม่ว่าง' }}
                      </UBadge>
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="domainType === 'custom'" class="space-y-4">
                <div class="w-full flex flex-col gap-4">
                  <div class="w-full flex flex-col sm:flex-row gap-4">
                    <UFormField label="โดเมนส่วนตัว" name="customDomain" required :error="customDomainError">
                      <UInput size="xl" :model-value="siteForm.customDomain" placeholder="yourdomain.com"
                        @update:model-value="onCustomDomainInput" />
                    </UFormField>

                    <UFormField label="ตรวจสอบ" name="checkCustomDomain">
                      <UButton variant="soft" size="xl" :loading="checkingDomain"
                        :disabled="siteForm.customDomain.trim().length <= 5" @click="checkDomainAvailability" block>
                        ตรวจสอบ
                      </UButton>
                    </UFormField>
                  </div>

                  <div>
                    <h2 class="text-lg font-semibold flex items-center gap-2">
                      <Icon name="solar:global-line-duotone" class="size-7 text-primary" />
                      ตั้งค่าโดเมน
                    </h2>
                    <UTable :data="[
                      {
                        type: 'A',
                        name: siteForm?.customDomain || '@',
                        content: '76.76.21.21',
                      },
                      {
                        type: 'CNAME',
                        name: '*',
                        content: 'cname.vercel-dns.com',
                      },
                    ]" class="flex-1" />
                  </div>
                </div>

                <!-- Domain Preview -->
                <div v-if="siteForm.customDomain.trim()" class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="text-sm text-gray-600 dark:text-gray-400">ที่อยู่เว็บไซต์ของคุณ:</p>
                      <p class="text-lg text-gray-900 dark:text-white">
                        {{ siteForm.customDomain }}
                      </p>
                    </div>
                    <div v-if="domainAvailable !== null">
                      <UBadge size="xl" :color="domainAvailable ? 'success' : 'error'" variant="subtle">
                        {{ domainAvailable ? 'ใช้งานได้' : 'ไม่ว่าง' }}
                      </UBadge>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Package Section -->
            <div class="space-y-6">
              <h2 class="text-lg font-semibold flex items-center gap-2">
                <Icon name="solar:box-line-duotone" class="size-7 text-primary" />
                เลือกแพ็กเกจ
              </h2>

              <div v-if="planError" class="text-red-500 text-sm">
                {{ planError }}
              </div>

              <div v-if="loadingPackages" class="flex justify-center items-center py-8">
                <UIcon name="eos-icons:bubble-loading" class="w-8 h-8 animate-spin text-primary-500" />
                <span class="ml-2  ">กำลังโหลดแพ็กเกจ...</span>
              </div>

              <div v-else-if="packages.length === 0" class="text-center py-8">
                <span>ไม่พบแพ็กเกจ</span>
              </div>

              <div v-else class="w-full flex flex-col md:flex-row gap-4">
                <div v-for="pkg in packages" :key="pkg.id" :class="[
                  'border-2 rounded-lg p-3 cursor-pointer transition-all relative w-full',
                  siteForm.plan === pkg.id
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                ]" @click="() => { siteForm.plan = pkg.id; if (v$.plan) v$.plan.$touch(); }">
                  <div v-if="pkg.popular" class="absolute -top-2 right-5 transform ">
                    <UBadge color="success">แนะนำ</UBadge>
                  </div>

                  <div class="text-center">
                    <h3 class="text-2xl font-semibold">
                      {{ pkg.name }}
                    </h3>

                    <div class="mb-3">
                      <span class="text-xl font-bold">
                        ฿{{ pkg.price }}
                      </span>
                      <span v-if="pkg.period">
                        /{{ pkg.period }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Right Column - Summary -->
      <div class="lg:col-span-1">
        <UCard variant="soft" class="sticky top-8">
          <template #header>
            <h2 class="text-lg font-semibold flex items-center gap-2">
              <Icon name="solar:document-text-line-duotone" class="size-7 text-primary" />
              สรุปรายการ
            </h2>
          </template>

          <div class="space-y-6">
            <!-- Website Info -->
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">ชื่อเว็บไซต์:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ siteForm.name || '-' }}
                </span>
              </div>

              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">โดเมน:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">
                  <template v-if="domainType === 'subdomain' && siteForm.subDomain">
                    {{ siteForm.subDomain }}.{{ selectedMainDomain }}
                  </template>
                  <template v-else-if="domainType === 'custom' && siteForm.customDomain">
                    {{ siteForm.customDomain }}
                  </template>
                  <template v-else>-</template>
                </span>
              </div>

              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">แพ็กเกจ:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ selectedPackage?.name || '-' }}
                </span>
              </div>
            </div>

            <hr />

            <!-- Pricing -->
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">ราคา:</span>
                <span class="text-lg font-bold text-gray-900 dark:text-white">
                  ฿{{ originalPrice }}
                  <span v-if="selectedPackage?.period" class="text-sm font-normal text-gray-600 dark:text-gray-400">
                    /{{ selectedPackage.period }}
                  </span>
                </span>
              </div>
            </div>

            <hr />

            <!-- Discount Code -->
            <div class="space-y-3">
              <div class="flex flex-col sm:flex-row sm:items-end gap-2 w-full">
                <UFormField class="flex-1" label="รหัสส่วนลด" name="discountCode">
                  <UInput class="w-full" v-model="discountCode" placeholder="กรอกรหัสส่วนลด"
                    icon="solar:ticket-line-duotone" />
                </UFormField>

                <UButton variant="soft" class="w-full sm:w-auto" :loading="checkingDiscount"
                  :disabled="!discountCode.trim()" @click="applyDiscount">
                  ใช้รหัสส่วนลด
                </UButton>
              </div>

              <!-- Discount Applied -->
              <div v-if="appliedDiscount" class="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
                <div class="flex items-center gap-2 text-green-600 dark:text-green-400">
                  <Icon name="solar:check-circle-line-duotone" class="w-4 h-4" />
                  <span class="text-sm font-medium">
                    ส่วนลด
                    <template v-if="appliedDiscount.type === 'percentage'">
                      {{ appliedDiscount?.value }}%
                    </template>
                    <template v-else>
                      ฿{{ appliedDiscount.value }}
                    </template>
                  </span>
                </div>
                <p class="text-xs text-green-600 dark:text-green-400 mt-1">
                  {{ appliedDiscount.description }}
                </p>
              </div>
            </div>

            <hr />

            <!-- Total -->
            <div class="space-y-3">
              <div v-if="appliedDiscount" class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-400">ราคาเดิม:</span>
                <span class="line-through text-gray-500">
                  ฿{{ originalPrice }}
                </span>
              </div>

              <div v-if="appliedDiscount" class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-400">ส่วนลด:</span>
                <span class="text-green-600 dark:text-green-400">
                  -฿{{ discountAmount }}
                </span>
              </div>

              <div class="flex items-center justify-between text-lg font-bold">
                <span class="text-gray-900 dark:text-white">รวมทั้งสิ้น:</span>
                <span class="text-primary-600 dark:text-primary-400">
                  ฿{{ finalPrice }}
                </span>
              </div>
            </div>
          </div>
        </UCard>
        <div class="mt-8">
          <UButton size="xl" block label="ชำระเงินและสร้างเว็บไซต์" :loading="loading" @click="createWebsite"
            :disabled="!isFormValid || loading" icon="solar:card-line-duotone" />
        </div>
      </div>
    </div>
  </UContainer>
</template>