<script setup lang="ts">
definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// State
const loading = ref(false);
const searchQuery = ref('');
const selectedStatus = ref('all');
const selectedCategory = ref('all');

// Mock blog posts data
const blogPosts = ref([
  {
    id: '1',
    title: 'วิธีสร้างร้านค้าออนไลน์ที่ประสบความสำเร็จ',
    excerpt: 'เรียนรู้เทคนิคและกลยุทธ์ในการสร้างร้านค้าออนไลน์ที่สามารถสร้างรายได้ได้อย่างยั่งยืน...',
    author: 'ทีม WebShop',
    status: 'published',
    category: 'business',
    publishedAt: '2024-01-15',
    views: 1250,
    likes: 89,
    comments: 23,
    featured: true,
    image: '/images/blog/post-1.jpg',
  },
  {
    id: '2',
    title: 'เทรนด์การตลาดดิจิทัลในปี 2024',
    excerpt: 'สำรวจเทรนด์การตลาดดิจิทัลที่กำลังมาแรงในปี 2024 และวิธีนำไปประยุกต์ใช้กับธุรกิจ...',
    author: 'นักวิเคราะห์การตลาด',
    status: 'published',
    category: 'marketing',
    publishedAt: '2024-01-12',
    views: 980,
    likes: 67,
    comments: 15,
    featured: false,
    image: '/images/blog/post-2.jpg',
  },
  {
    id: '3',
    title: 'SEO 2024: อัปเดตอัลกอริทึมใหม่ของ Google',
    excerpt: 'อัปเดตล่าสุดเกี่ยวกับการเปลี่ยนแปลงอัลกอริทึมของ Google และวิธีปรับปรุง SEO...',
    author: 'SEO Expert',
    status: 'draft',
    category: 'seo',
    publishedAt: null,
    views: 0,
    likes: 0,
    comments: 0,
    featured: false,
    image: '/images/blog/post-3.jpg',
  },
  {
    id: '4',
    title: 'การจัดการลูกค้าด้วยระบบ CRM',
    excerpt: 'แนะนำระบบ CRM ที่ช่วยจัดการลูกค้าและเพิ่มยอดขายได้อย่างมีประสิทธิภาพ...',
    author: 'CRM Specialist',
    status: 'published',
    category: 'business',
    publishedAt: '2024-01-10',
    views: 756,
    likes: 45,
    comments: 12,
    featured: false,
    image: '/images/blog/post-4.jpg',
  },
]);

// Categories
const categories = ref([
  { value: 'all', label: 'ทุกหมวดหมู่' },
  { value: 'business', label: 'ธุรกิจ' },
  { value: 'marketing', label: 'การตลาด' },
  { value: 'seo', label: 'SEO' },
  { value: 'technology', label: 'เทคโนโลยี' },
  { value: 'tips', label: 'เคล็ดลับ' },
]);

// Status options
const statuses = ref([
  { value: 'all', label: 'ทั้งหมด' },
  { value: 'published', label: 'เผยแพร่' },
  { value: 'draft', label: 'ฉบับร่าง' },
  { value: 'scheduled', label: 'กำหนดเวลา' },
]);

// Computed filtered posts
const filteredPosts = computed(() => {
  let filtered = blogPosts.value;

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(
      post =>
        post.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  // Status filter
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(post => post.status === selectedStatus.value);
  }

  // Category filter
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(post => post.category === selectedCategory.value);
  }

  return filtered;
});

// Get status badge class
const getStatusBadgeClass = (status: string) => {
  const classes = {
    published: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    draft: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
    scheduled: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
  };
  return classes[status as keyof typeof classes] || classes.draft;
};

// Get status label
const getStatusLabel = (status: string) => {
  const labels = {
    published: 'เผยแพร่',
    draft: 'ฉบับร่าง',
    scheduled: 'กำหนดเวลา',
  };
  return labels[status as keyof typeof labels] || status;
};

// Format date
const formatDate = (date: string | null) => {
  if (!date) return '-';
  return new Date(date).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

// Delete post
const deletePost = async (postId: string) => {
  try {
    loading.value = true;
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Remove from list
    blogPosts.value = blogPosts.value.filter(post => post.id !== postId);

    useToast().add({
      title: 'ลบสำเร็จ',
      description: 'ลบบทความเรียบร้อยแล้ว',
      color: 'success',
    });
  } catch (error) {
    useToast().add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถลบบทความได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">บล็อก</h1>
        <p class="text-gray-600 dark:text-gray-400">จัดการบทความและเนื้อหาบล็อก</p>
      </div>
      <UButton
        to="blog/create"
        color="primary"
        icon="i-heroicons-plus"
      >
        เขียนบทความใหม่
      </UButton>
    </div>

    <!-- Filters -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <UInput
        v-model="searchQuery"
        placeholder="ค้นหาบทความ..."
        icon="i-heroicons-magnifying-glass"
      />
      <USelect
        v-model="selectedStatus"
        :items="statuses"
        placeholder="สถานะ"
      />
      <USelect
        v-model="selectedCategory"
        :items="categories"
        placeholder="หมวดหมู่"
      />
      <UButton
        variant="outline"
        icon="i-heroicons-funnel"
      >
        กรอง
      </UButton>
    </div>

    <!-- Blog Posts Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <UCard
        v-for="post in filteredPosts"
        :key="post.id"
        class="hover:shadow-lg transition-shadow"
      >
        <template #header>
          <div class="flex items-center justify-between">
                         <UBadge
               :color="post.status === 'published' ? 'success' : 'neutral'"
               :label="getStatusLabel(post.status)"
             />
            <UDropdownMenu
              :items="[
                [
                  {
                    label: 'แก้ไข',
                    icon: 'i-heroicons-pencil-square',
                    to: `blog/${post.id}/edit`
                  },
                  {
                    label: 'ดูตัวอย่าง',
                    icon: 'i-heroicons-eye',
                    click: () => {}
                  }
                ],
                [
                  {
                    label: 'ลบ',
                    icon: 'i-heroicons-trash',
                    click: () => deletePost(post.id)
                  }
                ]
              ]"
            >
              <UButton
                variant="ghost"
                icon="i-heroicons-ellipsis-vertical"
                size="sm"
              />
            </UDropdownMenu>
          </div>
        </template>

        <div class="space-y-4">
          <!-- Featured Image -->
          <div class="aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
            <img
              :src="post.image"
              :alt="post.title"
              class="w-full h-full object-cover"
            />
          </div>

          <!-- Content -->
          <div class="space-y-2">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white line-clamp-2">
              {{ post.title }}
            </h3>
            <p class="text-gray-600 dark:text-gray-400 text-sm line-clamp-3">
              {{ post.excerpt }}
            </p>
          </div>

          <!-- Meta -->
          <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
            <div class="flex items-center gap-4">
              <span>{{ post.author }}</span>
              <span>{{ formatDate(post.publishedAt) }}</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="flex items-center gap-1">
                <Icon name="i-heroicons-eye" class="w-4 h-4" />
                {{ post.views }}
              </span>
              <span class="flex items-center gap-1">
                <Icon name="i-heroicons-heart" class="w-4 h-4" />
                {{ post.likes }}
              </span>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Empty State -->
    <div
      v-if="filteredPosts.length === 0"
      class="text-center py-12"
    >
      <Icon
        name="i-heroicons-document-text"
        class="w-12 h-12 text-gray-400 mx-auto mb-4"
      />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
        ไม่พบบทความ
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">
        ยังไม่มีบทความในระบบ หรือไม่ตรงกับเงื่อนไขการค้นหา
      </p>
      <UButton
        to="blog/create"
        color="primary"
        icon="i-heroicons-plus"
      >
        เขียนบทความแรก
      </UButton>
    </div>
  </div>
</template> 