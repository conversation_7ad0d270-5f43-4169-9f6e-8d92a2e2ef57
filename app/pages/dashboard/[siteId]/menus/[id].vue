<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">รายละเอียดเมนู</h1>
        <p class="text-gray-600 dark:text-gray-400">ดูรายละเอียดและโครงสร้างของเมนู</p>
      </div>
      <div class="flex items-center gap-3">
        <UButton @click="navigateTo(`/dashboard/${siteId}/menus`)" variant="outline">
          กลับ
        </UButton>
        <UButton @click="navigateTo(`/dashboard/${siteId}/menus/${menuId}/edit`)">
          แก้ไขเมนู
        </UButton>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="flex items-center gap-3">
        <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-blue-600" />
        <span class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</span>
      </div>
    </div>

    <!-- Content -->
    <div v-else class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Left Column - Menu Details -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">ข้อมูลพื้นฐาน</h3>
          </template>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                ชื่อเมนู
              </label>
              <p class="text-gray-900 dark:text-white">{{ menu.name }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                คำอธิบาย
              </label>
              <p class="text-gray-900 dark:text-white">{{ menu.description || '-' }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                ตำแหน่ง
              </label>
              <UBadge :color="getLocationColor(menu.location) as any" variant="soft">
                {{ getLocationLabel(menu.location) }}
              </UBadge>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                สถานะ
              </label>
              <UBadge 
                :color="menu.status === 'active' ? 'success' : 'neutral'" 
                variant="soft"
              >
                {{ menu.status === 'active' ? 'ใช้งาน' : 'ไม่ใช้งาน' }}
              </UBadge>
            </div>
          </div>
        </UCard>

        <!-- Menu Structure -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold">โครงสร้างเมนู</h3>
              <span class="text-sm text-gray-500">{{ menu.items.length }} รายการ</span>
            </div>
          </template>

          <div v-if="menu.items.length === 0" class="text-center py-8">
            <Icon name="i-heroicons-list-bullet" class="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p class="text-gray-500">ไม่มีรายการเมนู</p>
          </div>

          <div v-else class="space-y-2">
            <div
              v-for="(item, index) in menu.items"
              :key="item.id"
              class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
            >
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-2">
                    <Icon name="i-heroicons-bars-3" class="w-5 h-5 text-gray-400" />
                    <span class="font-medium text-gray-900 dark:text-white">
                      {{ item.label }}
                    </span>
                  </div>
                  
                  <div class="ml-7 space-y-1 text-sm">
                    <div class="flex items-center gap-2">
                      <span class="text-gray-600 dark:text-gray-400">ประเภท:</span>
                      <UBadge :color="getLinkTypeColor(item.type) as any" variant="soft" size="xs">
                        {{ getLinkTypeLabel(item.type) }}
                      </UBadge>
                    </div>
                    
                    <div v-if="item.type === 'page'" class="flex items-center gap-2">
                      <span class="text-gray-600 dark:text-gray-400">หน้า:</span>
                      <span class="text-gray-900 dark:text-white">{{ getPageName(item.pageId) }}</span>
                    </div>
                    
                    <div v-else-if="item.type === 'custom'" class="flex items-center gap-2">
                      <span class="text-gray-600 dark:text-gray-400">URL:</span>
                      <a :href="item.url" target="_blank" class="text-blue-600 hover:underline">
                        {{ item.url }}
                      </a>
                    </div>
                    
                    <div v-else-if="item.type === 'category'" class="flex items-center gap-2">
                      <span class="text-gray-600 dark:text-gray-400">หมวดหมู่:</span>
                      <span class="text-gray-900 dark:text-white">{{ getCategoryName(item.categoryId) }}</span>
                    </div>
                    
                    <div v-if="item.openInNewTab" class="flex items-center gap-1">
                      <Icon name="i-heroicons-arrow-top-right-on-square" class="w-4 h-4 text-gray-400" />
                      <span class="text-gray-600 dark:text-gray-400">เปิดในแท็บใหม่</span>
                    </div>
                  </div>
                </div>
                
                <span class="text-sm text-gray-500">#{{ index + 1 }}</span>
              </div>
            </div>
          </div>
        </UCard>

        <!-- Preview -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">ตัวอย่างการแสดงผล</h3>
          </template>

          <div class="space-y-4">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Desktop View
              </p>
              <div class="p-4 bg-gray-100 dark:bg-gray-800 rounded">
                <nav class="flex items-center gap-6">
                  <a
                    v-for="item in menu.items"
                    :key="item.id"
                    href="#"
                    class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                  >
                    {{ item.label }}
                  </a>
                </nav>
              </div>
            </div>

            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Mobile View
              </p>
              <div class="p-4 bg-gray-100 dark:bg-gray-800 rounded max-w-xs">
                <nav class="space-y-2">
                  <a
                    v-for="item in menu.items"
                    :key="item.id"
                    href="#"
                    class="block py-2 px-3 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
                  >
                    {{ item.label }}
                  </a>
                </nav>
              </div>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Right Column - Info & Actions -->
      <div class="space-y-6">
        <!-- Quick Actions -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">การดำเนินการ</h3>
          </template>

          <div class="space-y-3">
            <UButton 
              @click="navigateTo(`/dashboard/${siteId}/menus/${menuId}/edit`)" 
              class="w-full"
              icon="i-heroicons-pencil-square"
            >
              แก้ไขเมนู
            </UButton>
            
            <UButton 
              @click="handleDuplicate" 
              variant="outline" 
              class="w-full"
              icon="i-heroicons-document-duplicate"
            >
              คัดลอกเมนู
            </UButton>
            
            <UButton 
              @click="handleToggleStatus" 
              variant="outline" 
              class="w-full"
              :icon="menu.status === 'active' ? 'i-heroicons-pause' : 'i-heroicons-play'"
            >
              {{ menu.status === 'active' ? 'ปิดใช้งาน' : 'เปิดใช้งาน' }}
            </UButton>
            
            <div class="border-t border-gray-200 dark:border-gray-700 my-4"></div>
            
            <UButton 
              @click="handleDelete" 
              variant="outline" 
              color="error" 
              class="w-full"
              icon="i-heroicons-trash"
            >
              ลบเมนู
            </UButton>
          </div>
        </UCard>

        <!-- Settings -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">การตั้งค่า</h3>
          </template>

          <div class="space-y-3">
            <div class="flex items-center gap-2">
              <Icon 
                :name="menu.showOnMobile ? 'i-heroicons-check-circle' : 'i-heroicons-x-circle'" 
                :class="menu.showOnMobile ? 'text-green-600' : 'text-gray-400'" 
                class="w-5 h-5"
              />
              <span class="text-sm text-gray-700 dark:text-gray-300">
                แสดงบนอุปกรณ์มือถือ
              </span>
            </div>
            
            <div class="flex items-center gap-2">
              <Icon 
                :name="menu.showOnDesktop ? 'i-heroicons-check-circle' : 'i-heroicons-x-circle'" 
                :class="menu.showOnDesktop ? 'text-green-600' : 'text-gray-400'" 
                class="w-5 h-5"
              />
              <span class="text-sm text-gray-700 dark:text-gray-300">
                แสดงบนคอมพิวเตอร์
              </span>
            </div>
            
            <div class="border-t border-gray-200 dark:border-gray-700 my-4"></div>
            
            <div>
              <span class="text-sm text-gray-600 dark:text-gray-400">CSS Class:</span>
              <code class="text-sm text-gray-900 dark:text-white ml-2">
                {{ menu.cssClass || '-' }}
              </code>
            </div>
            
            <div>
              <span class="text-sm text-gray-600 dark:text-gray-400">ลำดับ:</span>
              <span class="text-sm text-gray-900 dark:text-white ml-2">
                {{ menu.order }}
              </span>
            </div>
          </div>
        </UCard>

        <!-- Info -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">ข้อมูลเมนู</h3>
          </template>

          <div class="space-y-3 text-sm">
            <div>
              <span class="text-gray-600 dark:text-gray-400">ID:</span>
              <span class="text-gray-900 dark:text-white ml-2 font-mono">{{ menuId }}</span>
            </div>
            
            <div class="border-t border-gray-200 dark:border-gray-700 my-4"></div>
            
            <div>
              <span class="text-gray-600 dark:text-gray-400">สร้างเมื่อ:</span>
              <div class="text-gray-900 dark:text-white mt-1">
                {{ formatDate(menu.createdAt) }}
              </div>
            </div>
            
            <div>
              <span class="text-gray-600 dark:text-gray-400">แก้ไขล่าสุด:</span>
              <div class="text-gray-900 dark:text-white mt-1">
                {{ formatDate(menu.updatedAt) }}
              </div>
            </div>
            
            <div class="border-t border-gray-200 dark:border-gray-700 my-4"></div>
            
            <div>
              <span class="text-gray-600 dark:text-gray-400">จำนวนรายการ:</span>
              <span class="text-gray-900 dark:text-white ml-2">{{ menu.items.length }}</span>
            </div>
          </div>
        </UCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Types
interface MenuItem {
  id: string;
  label: string;
  type: 'page' | 'custom' | 'category' | 'none';
  pageId?: string;
  url?: string;
  categoryId?: string;
  openInNewTab: boolean;
}

interface Menu {
  id: string;
  name: string;
  description: string;
  location: string;
  status: string;
  showOnMobile: boolean;
  showOnDesktop: boolean;
  cssClass: string;
  order: number;
  items: MenuItem[];
  createdAt: string;
  updatedAt: string;
}

// Route
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);
const menuId = computed(() => route.params.id as string);

// State
const loading = ref(true);
const menu = ref<Menu>({
  id: '',
  name: '',
  description: '',
  location: '',
  status: '',
  showOnMobile: true,
  showOnDesktop: true,
  cssClass: '',
  order: 0,
  items: [],
  createdAt: '',
  updatedAt: '',
});

// Methods
const loadMenu = async () => {
  loading.value = true;
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock data
    menu.value = {
      id: menuId.value,
      name: 'เมนูหลัก',
      description: 'เมนูหลักของเว็บไซต์สำหรับการนำทางหลัก',
      location: 'header',
      status: 'active',
      showOnMobile: true,
      showOnDesktop: true,
      cssClass: 'main-menu',
      order: 0,
      items: [
        {
          id: '1',
          label: 'หน้าแรก',
          type: 'page',
          pageId: '1',
          openInNewTab: false,
        },
        {
          id: '2',
          label: 'สินค้า',
          type: 'category',
          categoryId: '1',
          openInNewTab: false,
        },
        {
          id: '3',
          label: 'เกี่ยวกับเรา',
          type: 'page',
          pageId: '2',
          openInNewTab: false,
        },
        {
          id: '4',
          label: 'บล็อก',
          type: 'custom',
          url: 'https://blog.example.com',
          openInNewTab: true,
        },
        {
          id: '5',
          label: 'ติดต่อเรา',
          type: 'page',
          pageId: '5',
          openInNewTab: false,
        },
      ],
      createdAt: '2024-01-01T10:00:00Z',
      updatedAt: '2024-01-15T14:30:00Z',
    };
  } catch (error) {
    console.error('Error loading menu:', error);
    const toast = useToast();
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถโหลดข้อมูลเมนูได้',
      icon: 'i-heroicons-x-circle',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

const handleDuplicate = async () => {
  if (confirm(`คุณต้องการคัดลอกเมนู "${menu.value.name}" ใช่หรือไม่?`)) {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));

      const toast = useToast();
      toast.add({
        title: 'สำเร็จ',
        description: 'คัดลอกเมนูเรียบร้อยแล้ว',
        icon: 'i-heroicons-check-circle',
        color: 'success',
      });

      navigateTo(`/dashboard/${siteId.value}/menus`);
    } catch (error) {
      console.error('Error duplicating menu:', error);
    }
  }
};

const handleToggleStatus = async () => {
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 500));

    menu.value.status = menu.value.status === 'active' ? 'inactive' : 'active';

    const toast = useToast();
    toast.add({
      title: 'สำเร็จ',
      description: `${menu.value.status === 'active' ? 'เปิด' : 'ปิด'}ใช้งานเมนูแล้ว`,
      icon: 'i-heroicons-check-circle',
      color: 'success',
    });
  } catch (error) {
    console.error('Error toggling status:', error);
  }
};

const handleDelete = async () => {
  if (confirm(`คุณต้องการลบเมนู "${menu.value.name}" ใช่หรือไม่? การดำเนินการนี้ไม่สามารถย้อนกลับได้`)) {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));

      const toast = useToast();
      toast.add({
        title: 'สำเร็จ',
        description: 'ลบเมนูเรียบร้อยแล้ว',
        icon: 'i-heroicons-check-circle',
        color: 'success',
      });

      navigateTo(`/dashboard/${siteId.value}/menus`);
    } catch (error) {
      console.error('Error deleting menu:', error);
    }
  }
};

// Utility functions
const getLocationLabel = (location: string) => {
  const labels: Record<string, string> = {
    header: 'ส่วนหัว',
    footer: 'ส่วนท้าย',
    sidebar: 'แถบข้าง',
    mobile: 'มือถือ',
  };
  return labels[location] || location;
};

const getLocationColor = (location: string) => {
  const colors: Record<string, string> = {
    header: 'primary',
    footer: 'secondary',
    sidebar: 'warning',
    mobile: 'info',
  };
  return colors[location] || 'neutral';
};

const getLinkTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    page: 'หน้าเพจ',
    custom: 'ลิงก์ภายนอก',
    category: 'หมวดหมู่',
    none: 'ไม่มีลิงก์',
  };
  return labels[type] || type;
};

const getLinkTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    page: 'primary',
    custom: 'secondary',
    category: 'success',
    none: 'neutral',
  };
  return colors[type] || 'neutral';
};

const getPageName = (pageId?: string) => {
  const pages: Record<string, string> = {
    '1': 'หน้าแรก',
    '2': 'เกี่ยวกับเรา',
    '3': 'สินค้า',
    '4': 'บริการ',
    '5': 'ติดต่อเรา',
  };
  return pageId ? pages[pageId] || 'ไม่ระบุ' : 'ไม่ระบุ';
};

const getCategoryName = (categoryId?: string) => {
  const categories: Record<string, string> = {
    '1': 'เสื้อผ้า',
    '2': 'รองเท้า',
    '3': 'กระเป๋า',
    '4': 'เครื่องประดับ',
  };
  return categoryId ? categories[categoryId] || 'ไม่ระบุ' : 'ไม่ระบุ';
};

const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Load data on mount
onMounted(() => {
  loadMenu();
});

// Page meta
definePageMeta({
  layout: 'dashboard',
  middleware: 'auth',
});
</script> 