
<script setup lang="ts">
// Types
interface MenuItem {
  id: string;
  label: string;
  type: 'page' | 'custom' | 'category' | 'none';
  pageId?: string;
  url?: string;
  categoryId?: string;
  openInNewTab: boolean;
  children?: MenuItem[];
}

interface MenuForm {
  name: string;
  description: string;
  location: string;
  status: string;
  showOnMobile: boolean;
  showOnDesktop: boolean;
  cssClass: string;
  order: number;
  items: MenuItem[];
}

// Route
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// State
const saving = ref(false);
const menuForm = ref<MenuForm>({
  name: '',
  description: '',
  location: '',
  status: 'active',
  showOnMobile: true,
  showOnDesktop: true,
  cssClass: '',
  order: 0,
  items: [],
});

// Options
const locationOptions = [
  { value: 'header', label: 'ส่วนหัว' },
  { value: 'footer', label: 'ส่วนท้าย' },
  { value: 'sidebar', label: 'แถบข้าง' },
  { value: 'mobile', label: 'มือถือ' },
];

const statusOptions = [
  { value: 'active', label: 'ใช้งาน' },
  { value: 'inactive', label: 'ไม่ใช้งาน' },
];

const linkTypeOptions = [
  { value: 'page', label: 'หน้าเพจ' },
  { value: 'custom', label: 'ลิงก์ภายนอก' },
  { value: 'category', label: 'หมวดหมู่' },
  { value: 'none', label: 'ไม่มีลิงก์' },
];

const pageOptions = [
  { value: '1', label: 'หน้าแรก' },
  { value: '2', label: 'เกี่ยวกับเรา' },
  { value: '3', label: 'สินค้า' },
  { value: '4', label: 'บริการ' },
  { value: '5', label: 'ติดต่อเรา' },
];

const categoryOptions = [
  { value: '1', label: 'เสื้อผ้า' },
  { value: '2', label: 'รองเท้า' },
  { value: '3', label: 'กระเป๋า' },
  { value: '4', label: 'เครื่องประดับ' },
];

// Computed
const isFormValid = computed(() => {
  return (
    menuForm.value.name &&
    menuForm.value.location &&
    menuForm.value.items.every(item => item.label && item.type !== 'none')
  );
});

// Methods
const addMenuItem = () => {
  menuForm.value.items.push({
    id: Date.now().toString(),
    label: '',
    type: 'page',
    openInNewTab: false,
  });
};

const removeMenuItem = (index: number) => {
  menuForm.value.items.splice(index, 1);
};

const handleLinkTypeChange = (index: number) => {
  const item = menuForm.value.items[index];
  if (!item) return;

  // Reset fields when type changes
  delete item.pageId;
  delete item.url;
  delete item.categoryId;
};

const handleSave = async () => {
  if (!isFormValid.value) return;

  saving.value = true;
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    const toast = useToast();
    toast.add({
      title: 'สำเร็จ',
      description: 'สร้างเมนูเรียบร้อยแล้ว',
      icon: 'i-heroicons-check-circle',
      color: 'success',
    });

    navigateTo(`/dashboard/${siteId.value}/menus`);
  } catch (error) {
    console.error('Error saving menu:', error);
    const toast = useToast();
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถบันทึกเมนูได้',
      icon: 'i-heroicons-x-circle',
      color: 'error',
    });
  } finally {
    saving.value = false;
  }
};

// Page meta
definePageMeta({
  layout: 'dashboard',
  middleware: 'auth',
});
</script> 

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">สร้างเมนูใหม่</h1>
        <p class="text-gray-600 dark:text-gray-400">สร้างเมนูและโครงสร้างการนำทางใหม่สำหรับเว็บไซต์</p>
      </div>
      <div class="flex items-center gap-3">
        <UButton @click="navigateTo(`/dashboard/${siteId.value}/menus`)" variant="outline">
          ยกเลิก
        </UButton>
        <UButton @click="handleSave" :loading="saving" :disabled="!isFormValid">
          บันทึกเมนู
        </UButton>
      </div>
    </div>

    <!-- Form -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Left Column - Menu Details -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">ข้อมูลพื้นฐาน</h3>
          </template>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ชื่อเมนู <span class="text-red-500">*</span>
              </label>
              <UInput
                v-model="menuForm.name"
                placeholder="เช่น เมนูหลัก, เมนูส่วนท้าย"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                คำอธิบาย
              </label>
              <UTextarea
                v-model="menuForm.description"
                placeholder="อธิบายวัตถุประสงค์ของเมนูนี้..."
                :rows="3"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ตำแหน่ง <span class="text-red-500">*</span>
              </label>
              <USelect
                v-model="menuForm.location"
                :items="locationOptions"
                placeholder="เลือกตำแหน่งเมนู"
              />
            </div>
          </div>
        </UCard>

        <!-- Menu Items -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold">รายการเมนู</h3>
              <UButton @click="addMenuItem" variant="soft" size="sm">
                <Icon name="i-heroicons-plus" class="w-4 h-4 mr-1" />
                เพิ่มรายการ
              </UButton>
            </div>
          </template>

          <div v-if="menuForm.items.length === 0" class="text-center py-8">
            <Icon name="i-heroicons-list-bullet" class="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p class="text-gray-500">ยังไม่มีรายการเมนู</p>
            <UButton @click="addMenuItem" variant="soft" size="sm" class="mt-2">
              เพิ่มรายการแรก
            </UButton>
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="(item, index) in menuForm.items"
              :key="item.id"
              class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
            >
              <div class="flex items-start justify-between mb-3">
                <div class="flex items-center gap-2">
                  <Icon name="i-heroicons-bars-3" class="w-5 h-5 text-gray-400 cursor-move" />
                  <span class="font-medium">รายการที่ {{ index + 1 }}</span>
                </div>
                <UButton
                  @click="removeMenuItem(index)"
                  variant="ghost"
                  color="error"
                  size="sm"
                  icon="i-heroicons-trash"
                />
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    ชื่อรายการ <span class="text-red-500">*</span>
                  </label>
                  <UInput
                    v-model="item.label"
                    placeholder="เช่น หน้าแรก, เกี่ยวกับเรา"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    ประเภท <span class="text-red-500">*</span>
                  </label>
                  <USelect
                    v-model="item.type"
                    :items="linkTypeOptions"
                    placeholder="เลือกประเภท"
                    @change="handleLinkTypeChange(index)"
                  />
                </div>

                <div v-if="item.type === 'page'" class="md:col-span-2">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    เลือกหน้า <span class="text-red-500">*</span>
                  </label>
                  <USelect
                    v-model="item.pageId"
                    :items="pageOptions"
                    placeholder="เลือกหน้า"
                  />
                </div>

                <div v-else-if="item.type === 'custom'" class="md:col-span-2">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    URL <span class="text-red-500">*</span>
                  </label>
                  <UInput
                    v-model="item.url"
                    placeholder="https://example.com"
                  />
                </div>

                <div v-else-if="item.type === 'category'" class="md:col-span-2">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    หมวดหมู่ <span class="text-red-500">*</span>
                  </label>
                  <USelect
                    v-model="item.categoryId"
                    :items="categoryOptions"
                    placeholder="เลือกหมวดหมู่"
                  />
                </div>

                <div class="md:col-span-2">
                  <UCheckbox v-model="item.openInNewTab" label="เปิดในแท็บใหม่" />
                </div>
              </div>

              <!-- Sub-items -->
              <div v-if="item.children && item.children.length > 0" class="mt-4 ml-8">
                <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">รายการย่อย</div>
                <!-- Render sub-items here -->
              </div>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Right Column - Settings -->
      <div class="space-y-6">
        <!-- Status -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">สถานะ</h3>
          </template>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                สถานะการใช้งาน
              </label>
              <USelect
                v-model="menuForm.status"
                :items="statusOptions"
                placeholder="เลือกสถานะ"
              />
            </div>

            <div>
              <UCheckbox
                v-model="menuForm.showOnMobile"
                label="แสดงบนอุปกรณ์มือถือ"
              />
            </div>

            <div>
              <UCheckbox
                v-model="menuForm.showOnDesktop"
                label="แสดงบนคอมพิวเตอร์"
              />
            </div>
          </div>
        </UCard>

        <!-- Advanced Settings -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">ตั้งค่าขั้นสูง</h3>
          </template>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                CSS Class
              </label>
              <UInput
                v-model="menuForm.cssClass"
                placeholder="custom-menu-class"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ลำดับการแสดงผล
              </label>
              <UInput
                v-model="menuForm.order"
                type="number"
                placeholder="0"
              />
            </div>
          </div>
        </UCard>

        <!-- Preview -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">ตัวอย่าง</h3>
          </template>

          <div class="space-y-2">
            <div class="text-sm text-gray-600 dark:text-gray-400 mb-3">
              ตัวอย่างการแสดงผลเมนู
            </div>
            <div class="p-4 bg-gray-100 dark:bg-gray-800 rounded">
              <nav class="flex items-center gap-4">
                <a
                  v-for="item in menuForm.items"
                  :key="item.id"
                  href="#"
                  class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
                >
                  {{ item.label || 'รายการเมนู' }}
                </a>
              </nav>
            </div>
          </div>
        </UCard>
      </div>
    </div>
  </div>
</template>
