<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: 'auth',
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// Use API composable
const {
  getApi<PERSON>eys,
  getApi<PERSON>ey,
  createApi<PERSON><PERSON>,
  update<PERSON>pi<PERSON><PERSON>,
  deleteApi<PERSON><PERSON>,
  regenerate<PERSON>pi<PERSON><PERSON>,
  toggleApi<PERSON>ey,
  getApiUsage,
  getApiAnalytics,
} = useApi();

// Types
interface ApiKey {
  id: string;
  name: string;
  description: string;
  key: string;
  maskedKey: string;
  permissions: string[];
  requestCount: number;
  status: 'active' | 'inactive';
  createdAt: string;
  expiresAt?: string;
  rateLimit: number;
}

interface ApiEndpoint {
  path: string;
  method: string;
  description: string;
  requestCount: number;
  avgResponseTime: number;
  parameters: any;
}

interface ApiTestResponse {
  status: number;
  statusText: string;
  headers: Record<string, any>;
  data: any;
  responseTime: number;
  size: number;
}

// State
const activeTab = ref(0);
const searchQuery = ref('');
const selectedStatus = ref('');
const showCreateKeyModal = ref(false);
const showDocumentation = ref(false);
const isCreatingKey = ref(false);
const isSavingRateLimit = ref(false);
const isTestingApi = ref(false);
const loading = ref(false);

// API Keys
const apiKeys = ref<ApiKey[]>([]);
const analytics = ref<any>({});

// New API Key form
const newApiKey = ref({
  name: '',
  description: '',
  permissions: [] as string[],
  expiresAt: '',
  rateLimit: 1000,
});

// Rate limiting settings
const rateLimitSettings = ref({
  enabled: true,
  requestsPerMinute: 100,
  requestsPerHour: 1000,
  requestsPerDay: 10000,
  burstLimit: 200,
  errorMessage: 'Rate limit exceeded. Please try again later.',
});

// Test request
const testRequest = ref({
  method: 'GET',
  url: '',
  headers: '{}',
  body: '{}',
  apiKey: '',
});

const testResponse = ref<ApiTestResponse | null>(null);

// Load API keys
const loadApiKeys = async () => {
  loading.value = true;
  try {
    const response = await getApiKeys(siteId.value, {
      search: searchQuery.value,
      status: selectedStatus.value,
    });
    apiKeys.value = (response as any)?.data || [];
  } catch (error: any) {
    useToast().add({
      title: 'เกิดข้อผิดพลาด',
      description: error.message || 'ไม่สามารถโหลด API Keys ได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// Load analytics
const loadAnalytics = async () => {
  try {
    const response = await getApiAnalytics(siteId.value);
    analytics.value = (response as any)?.data || {};
  } catch (error: any) {
    console.error('Error loading analytics:', error);
  }
};

// Load data on mount
onMounted(async () => {
  await Promise.all([loadApiKeys(), loadAnalytics()]);
});

// Watch for filter changes
watch([searchQuery, selectedStatus], async () => {
  await loadApiKeys();
});

// Watch for route changes
watch(
  () => siteId.value,
  async newSiteId => {
    if (newSiteId) {
      await Promise.all([loadApiKeys(), loadAnalytics()]);
    }
  }
);

// Computed
const filteredApiKeys = computed(() => {
  let filtered = apiKeys.value;

  if (searchQuery.value) {
    filtered = filtered.filter(
      (key: any) =>
        key.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        key.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  if (selectedStatus.value && selectedStatus.value !== 'all') {
    filtered = filtered.filter((key: any) => key.status === selectedStatus.value);
  }

  return filtered;
});

const apiStats = computed(() => {
  return {
    totalKeys: apiKeys.value.length,
    totalRequests: apiKeys.value.reduce((sum: number, key: any) => sum + key.requestCount, 0),
    avgResponseTime: analytics.value.avgResponseTime || 245,
    errorRate: analytics.value.errorRate || 2.1,
  };
});

// Mock data for endpoints (since this might not be available in the API composable)
const apiEndpoints = ref<ApiEndpoint[]>([
  {
    path: '/api/v1/products',
    method: 'GET',
    description: 'ดึงรายการสินค้าทั้งหมด',
    requestCount: 15420,
    avgResponseTime: 180,
    parameters: {
      page: 'number',
      limit: 'number',
      category: 'string',
    },
  },
  {
    path: '/api/v1/products',
    method: 'POST',
    description: 'สร้างสินค้าใหม่',
    requestCount: 2340,
    avgResponseTime: 320,
    parameters: {
      name: 'string',
      price: 'number',
      description: 'string',
    },
  },
  {
    path: '/api/v1/orders',
    method: 'GET',
    description: 'ดึงรายการคำสั่งซื้อ',
    requestCount: 8900,
    avgResponseTime: 210,
    parameters: {
      status: 'string',
      date_from: 'date',
      date_to: 'date',
    },
  },
]);

const rateLimitStats = ref({
  blocked: 1250,
  warnings: 340,
  allowed: 45680,
});

// Options
const tabItems = [
  { key: 'api-keys', label: 'API Keys', icon: 'i-heroicons-key' },
  { key: 'endpoints', label: 'Endpoints', icon: 'i-heroicons-globe-alt' },
  {
    key: 'rate-limiting',
    label: 'Rate Limiting',
    icon: 'i-heroicons-shield-check',
  },
  { key: 'testing', label: 'ทดสอบ', icon: 'i-heroicons-beaker' },
];

const statusOptions = [
  { label: 'ทั้งหมด', value: 'all' },
  { label: 'ใช้งาน', value: 'active' },
  { label: 'ปิดใช้งาน', value: 'inactive' },
];

const availablePermissions = [
  { label: 'อ่านสินค้า', value: 'products:read' },
  { label: 'เขียนสินค้า', value: 'products:write' },
  { label: 'อ่านคำสั่งซื้อ', value: 'orders:read' },
  { label: 'เขียนคำสั่งซื้อ', value: 'orders:write' },
  { label: 'อ่านลูกค้า', value: 'customers:read' },
  { label: 'เขียนลูกค้า', value: 'customers:write' },
];

const methodOptions = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
];

const apiKeyOptions = computed(() =>
  apiKeys.value.map((key: any) => ({
    label: key.name,
    value: key.key,
  }))
);

// Methods
const handleCreateApiKey = async () => {
  isCreatingKey.value = true;
  try {
    const keyData = {
      name: newApiKey.value.name,
      description: newApiKey.value.description,
      permissions: newApiKey.value.permissions,
      expiresAt: newApiKey.value.expiresAt || undefined,
      rateLimit: newApiKey.value.rateLimit,
    };

    await createApiKey(siteId.value, keyData);
    await loadApiKeys();

    useToast().add({
      title: 'สร้าง API Key สำเร็จ',
      description: 'API Key ใหม่ถูกสร้างแล้ว',
      color: 'success',
    });

    // Reset form
    newApiKey.value = {
      name: '',
      description: '',
      permissions: [],
      expiresAt: '',
      rateLimit: 1000,
    };

    showCreateKeyModal.value = false;
  } catch (error: any) {
    useToast().add({
      title: 'เกิดข้อผิดพลาด',
      description: error.message || 'ไม่สามารถสร้าง API Key ได้',
      color: 'error',
    });
  } finally {
    isCreatingKey.value = false;
  }
};

const copyApiKey = async (key: string) => {
  try {
    await navigator.clipboard.writeText(key);
    useToast().add({
      title: 'คัดลอกแล้ว',
      description: 'API Key ถูกคัดลอกไปยังคลิปบอร์ด',
      color: 'info',
    });
  } catch (error) {
    useToast().add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถคัดลอก API Key ได้',
      color: 'error',
    });
  }
};

const viewApiKeyDetails = (key: ApiKey) => {
  // Navigate to API key details page
  navigateTo(`/dashboard/${siteId.value}/api/${key.id}`);
};

const editApiKey = (key: ApiKey) => {
  // Navigate to API key edit page
  navigateTo(`/dashboard/${siteId.value}/api/${key.id}/edit`);
};

const handleRegenerateApiKey = async (key: ApiKey) => {
  if (!confirm(`คุณต้องการสร้าง API Key ใหม่สำหรับ "${key.name}" หรือไม่?`)) {
    return;
  }

  try {
    await regenerateApiKey(siteId.value, key.id);
    await loadApiKeys();

    useToast().add({
      title: 'สร้าง API Key ใหม่สำเร็จ',
      description: `สร้าง API Key ใหม่สำหรับ "${key.name}" แล้ว`,
      color: 'success',
    });
  } catch (error: any) {
    useToast().add({
      title: 'เกิดข้อผิดพลาด',
      description: error.message || 'ไม่สามารถสร้าง API Key ใหม่ได้',
      color: 'error',
    });
  }
};

const handleToggleApiKey = async (key: ApiKey) => {
  try {
    const newStatus = key.status === 'active' ? 'inactive' : 'active';
    await toggleApiKey(siteId.value, key.id, newStatus);
    await loadApiKeys();

    const action = key.status === 'active' ? 'ปิดใช้งาน' : 'เปิดใช้งาน';
    useToast().add({
      title: `${action}สำเร็จ`,
      description: `${action} API Key "${key.name}" แล้ว`,
      color: 'success',
    });
  } catch (error: any) {
    useToast().add({
      title: 'เกิดข้อผิดพลาด',
      description: error.message || 'ไม่สามารถเปลี่ยนสถานะ API Key ได้',
      color: 'error',
    });
  }
};

const handleDeleteApiKey = async (key: ApiKey) => {
  if (!confirm(`คุณต้องการลบ API Key "${key.name}" หรือไม่?`)) {
    return;
  }

  try {
    await deleteApiKey(siteId.value, key.id);
    await loadApiKeys();

    useToast().add({
      title: 'ลบสำเร็จ',
      description: `ลบ API Key "${key.name}" สำเร็จ`,
      color: 'success',
    });
  } catch (error: any) {
    useToast().add({
      title: 'เกิดข้อผิดพลาด',
      description: error.message || 'ไม่สามารถลบ API Key ได้',
      color: 'error',
    });
  }
};

const testEndpoint = (endpoint: ApiEndpoint) => {
  testRequest.value.method = endpoint.method;
  testRequest.value.url = `https://api.example.com${endpoint.path}`;
  activeTab.value = 3; // Switch to testing tab
};

const sendTestRequest = async () => {
  isTestingApi.value = true;
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Mock response
    testResponse.value = {
      status: 200,
      statusText: 'OK',
      headers: {
        'content-type': 'application/json',
        'x-ratelimit-remaining': '99',
        'x-ratelimit-reset': '1642678800',
      },
      data: {
        success: true,
        data: {
          id: 1,
          name: 'Sample Product',
          price: 299.99,
        },
        message: 'Success',
      },
      responseTime: Math.floor(Math.random() * 500) + 100,
      size: 1024,
    };

    useToast().add({
      title: 'ทดสอบสำเร็จ',
      description: 'ได้รับผลลัพธ์จาก API แล้ว',
      color: 'success',
    });
  } catch (error) {
    testResponse.value = {
      status: 500,
      statusText: 'Internal Server Error',
      headers: {},
      data: {
        success: false,
        error: 'Internal server error',
      },
      responseTime: 0,
      size: 0,
    };

    useToast().add({
      title: 'ทดสอบล้มเหลว',
      description: 'เกิดข้อผิดพลาดในการเรียก API',
      color: 'error',
    });
  } finally {
    isTestingApi.value = false;
  }
};

const saveRateLimitSettings = async () => {
  isSavingRateLimit.value = true;
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    useToast().add({
      title: 'บันทึกสำเร็จ',
      description: 'การตั้งค่า Rate Limiting ได้รับการบันทึกแล้ว',
      color: 'success',
    });
  } catch (error) {
    useToast().add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถบันทึกการตั้งค่าได้',
      color: 'error',
    });
  } finally {
    isSavingRateLimit.value = false;
  }
};

// Utility functions
const formatDateTime = (dateString: string): string => {
  return new Date(dateString).toLocaleString('th-TH');
};

const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('th-TH').format(num);
};

const getMethodColor = (
  method: string
): 'success' | 'primary' | 'secondary' | 'info' | 'warning' | 'error' | 'neutral' => {
  const colors = {
    GET: 'success' as const,
    POST: 'primary' as const,
    PUT: 'warning' as const,
    DELETE: 'error' as const,
  };
  return colors[method as keyof typeof colors] || 'neutral';
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / k ** i).toFixed(2)) + ' ' + sizes[i];
};

// SEO
useSeoMeta({
  title: 'API Management - ระบบจัดการร้านค้า',
  description: 'จัดการ API Keys และการเข้าถึง API',
  keywords: 'API, API Keys, การจัดการ API, ความปลอดภัย',
  ogTitle: 'API Management - ระบบจัดการร้านค้า',
  ogDescription: 'จัดการ API Keys และการเข้าถึง API',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">การจัดการ API</h1>
        <p class="text-gray-600 dark:text-gray-400">จัดการ API Keys, เอกสาร และการตั้งค่า API</p>
      </div>
      <div class="flex items-center gap-3">
        <UButton
          variant="outline"
          icon="i-heroicons-document-text"
          @click="showDocumentation = true"
        >
          เอกสาร API
        </UButton>
        <UButton
          icon="i-heroicons-plus"
          @click="showCreateKeyModal = true"
        >
          สร้าง API Key
        </UButton>
      </div>
    </div>

    <!-- API Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Icon name="i-heroicons-key" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ apiStats.totalKeys }}</div>
            <div class="text-sm text-gray-500">API Keys</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <Icon name="i-heroicons-arrow-trending-up" class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ apiStats.totalRequests.toLocaleString() }}</div>
            <div class="text-sm text-gray-500">คำขอทั้งหมด</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
            <Icon name="i-heroicons-clock" class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ apiStats.avgResponseTime }}ms</div>
            <div class="text-sm text-gray-500">เวลาตอบสนองเฉลี่ย</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-red-100 dark:bg-red-900/20 rounded-lg">
            <Icon name="i-heroicons-exclamation-triangle" class="w-6 h-6 text-red-600 dark:text-red-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ apiStats.errorRate }}%</div>
            <div class="text-sm text-gray-500">อัตราข้อผิดพลาด</div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Tabs -->
    <UTabs :items="tabItems" v-model="activeTab">
      <template v-slot="slotProps">
        <div v-if="slotProps.item.key === 'api-keys'">
          <!-- API Keys Tab -->
          <div class="space-y-4">
            <!-- Search and Filter -->
            <div class="flex items-center gap-4">
              <UInput
                v-model="searchQuery"
                placeholder="ค้นหา API Keys..."
                icon="i-heroicons-magnifying-glass"
                class="flex-1"
              />
              <USelect
                v-model="selectedStatus"
                :items="statusOptions"
                placeholder="สถานะ"
              />
            </div>

            <!-- API Keys Table -->
            <UCard>
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        ชื่อ
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        API Key
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        สิทธิ์
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        คำขอ
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        สถานะ
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        วันที่สร้าง
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        การดำเนินการ
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    <tr
                      v-for="key in filteredApiKeys"
                      :key="key.id"
                      class="hover:bg-gray-50 dark:hover:bg-gray-800"
                    >
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                          {{ key.name }}
                        </div>
                        <div class="text-sm text-gray-500">
                          {{ key.description }}
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center gap-2">
                          <code class="text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                            {{ key.maskedKey }}
                          </code>
                          <UButton
                            variant="ghost"
                            size="sm"
                            icon="i-heroicons-clipboard"
                            @click="copyApiKey(key.key)"
                          />
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex flex-wrap gap-1">
                          <UBadge
                            v-for="permission in key.permissions"
                            :key="permission"
                            :label="permission"
                            size="sm"
                            variant="soft"
                          />
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {{ key.requestCount.toLocaleString() }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <UBadge
                          :color="key.status === 'active' ? 'success' : 'error'"
                          :label="key.status === 'active' ? 'ใช้งาน' : 'ปิดใช้งาน'"
                        />
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {{ formatDateTime(key.createdAt) }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex items-center gap-2">
                          <UButton
                            variant="ghost"
                            size="sm"
                            icon="i-heroicons-eye"
                            @click="viewApiKeyDetails(key)"
                          />
                          <UButton
                            variant="ghost"
                            size="sm"
                            icon="i-heroicons-pencil"
                            @click="editApiKey(key)"
                          />
                          <UButton
                            variant="ghost"
                            size="sm"
                            icon="i-heroicons-trash"
                            color="error"
                            @click="handleDeleteApiKey(key)"
                          />
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </UCard>
          </div>
        </div>
        <div v-else-if="slotProps.item.key === 'endpoints'">
          <!-- Endpoints Tab -->
          <div class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div
                v-for="endpoint in apiEndpoints"
                :key="endpoint.path"
                class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center gap-2">
                    <UBadge
                      :color="getMethodColor(endpoint.method)"
                      :label="endpoint.method"
                    />
                    <span class="font-medium text-gray-900 dark:text-white">
                      {{ endpoint.path }}
                    </span>
                  </div>
                  <UButton
                    variant="ghost"
                    size="sm"
                    icon="i-heroicons-play"
                    @click="testEndpoint(endpoint)"
                  />
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {{ endpoint.description }}
                </p>
                <div class="flex items-center justify-between text-sm">
                  <span class="text-gray-500">
                    {{ endpoint.requestCount.toLocaleString() }} คำขอ
                  </span>
                  <span class="text-gray-500">
                    {{ endpoint.avgResponseTime }}ms
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else-if="slotProps.item.key === 'rate-limiting'">
          <!-- Rate Limiting Tab -->
          <div class="space-y-6">
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold">การจำกัดอัตราการใช้งาน</h3>
              </template>

              <div class="space-y-4">
                <UFormField label="เปิดใช้งาน Rate Limiting">
                  <UToggle v-model="rateLimitSettings.enabled" />
                </UFormField>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <UFormField label="จำนวนคำขอต่อนาที">
                    <UInput
                      v-model="rateLimitSettings.requestsPerMinute"
                      type="number"
                      :disabled="!rateLimitSettings.enabled"
                    />
                  </UFormField>

                  <UFormField label="จำนวนคำขอต่อชั่วโมง">
                    <UInput
                      v-model="rateLimitSettings.requestsPerHour"
                      type="number"
                      :disabled="!rateLimitSettings.enabled"
                    />
                  </UFormField>

                  <UFormField label="จำนวนคำขอต่อวัน">
                    <UInput
                      v-model="rateLimitSettings.requestsPerDay"
                      type="number"
                      :disabled="!rateLimitSettings.enabled"
                    />
                  </UFormField>

                  <UFormField label="Burst Limit">
                    <UInput
                      v-model="rateLimitSettings.burstLimit"
                      type="number"
                      :disabled="!rateLimitSettings.enabled"
                    />
                  </UFormField>
                </div>

                <UFormField label="ข้อความเมื่อเกินขีดจำกัด">
                  <UTextarea
                    v-model="rateLimitSettings.errorMessage"
                    :disabled="!rateLimitSettings.enabled"
                    :rows="3"
                  />
                </UFormField>

                <UButton
                  :loading="isSavingRateLimit"
                  @click="saveRateLimitSettings"
                >
                  บันทึกการตั้งค่า
                </UButton>
              </div>
            </UCard>

            <!-- Rate Limit Statistics -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold">สถิติการจำกัดอัตรา</h3>
              </template>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                    {{ rateLimitStats.blocked }}
                  </div>
                  <div class="text-sm text-red-600 dark:text-red-400">
                    คำขอที่ถูกบล็อก
                  </div>
                </div>

                <div class="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                    {{ rateLimitStats.warnings }}
                  </div>
                  <div class="text-sm text-yellow-600 dark:text-yellow-400">
                    คำเตือน
                  </div>
                </div>

                <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                    {{ rateLimitStats.allowed }}
                  </div>
                  <div class="text-sm text-green-600 dark:text-green-400">
                    คำขอที่อนุญาต
                  </div>
                </div>
              </div>
            </UCard>
          </div>
        </div>
        <div v-else-if="slotProps.item.key === 'testing'">
          <!-- Testing Tab -->
          <div class="space-y-6">
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold">ทดสอบ API</h3>
              </template>

              <div class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <UFormField label="HTTP Method">
                    <USelect
                      v-model="testRequest.method"
                      :items="methodOptions"
                    />
                  </UFormField>

                  <UFormField label="API Key">
                    <USelect
                      v-model="testRequest.apiKey"
                      :items="apiKeyOptions"
                      placeholder="เลือก API Key"
                    />
                  </UFormField>
                </div>

                <UFormField label="Endpoint URL">
                  <UInput
                    v-model="testRequest.url"
                    placeholder="https://api.example.com/v1/..."
                  />
                </UFormField>

                <UFormField label="Headers">
                  <UTextarea
                    v-model="testRequest.headers"
                    placeholder='{"Content-Type": "application/json"}'
                    :rows="3"
                  />
                </UFormField>

                <UFormField label="Request Body">
                  <UTextarea
                    v-model="testRequest.body"
                    placeholder='{"key": "value"}'
                    :rows="5"
                  />
                </UFormField>

                <UButton
                  :loading="isTestingApi"
                  @click="sendTestRequest"
                >
                  ส่งคำขอ
                </UButton>
              </div>
            </UCard>

            <!-- Test Response -->
            <UCard v-if="testResponse">
              <template #header>
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold">ผลลัพธ์</h3>
                  <UBadge
                    :color="testResponse.status < 400 ? 'success' : 'error'"
                    :label="`${testResponse.status} ${testResponse.statusText}`"
                  />
                </div>
              </template>

              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Response Headers
                  </label>
                  <pre class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg text-sm overflow-x-auto">{{ JSON.stringify(testResponse.headers, null, 2) }}</pre>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Response Body
                  </label>
                  <pre class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg text-sm overflow-x-auto">{{ JSON.stringify(testResponse.data, null, 2) }}</pre>
                </div>

                <div class="flex items-center gap-4 text-sm text-gray-500">
                  <span>เวลาตอบสนอง: {{ testResponse.responseTime }}ms</span>
                  <span>ขนาด: {{ testResponse.size }} bytes</span>
                </div>
              </div>
            </UCard>
          </div>
        </div>
      </template>
    </UTabs>

    <!-- Create API Key Modal -->
    <UModal 
      v-model:open="showCreateKeyModal"
      title="สร้าง API Key ใหม่"
      description="สร้าง API Key ใหม่สำหรับการเข้าถึง API ของเว็บไซต์"
    >
      <template #body>
        <div class="space-y-4">
          <UFormField label="ชื่อ" required>
            <UInput
              v-model="newApiKey.name"
              placeholder="ชื่อ API Key"
            />
          </UFormField>

          <UFormField label="คำอธิบาย">
            <UTextarea
              v-model="newApiKey.description"
              placeholder="คำอธิบายสำหรับ API Key นี้"
              :rows="3"
            />
          </UFormField>

          <UFormField label="สิทธิ์การเข้าถึง">
            <UCheckboxGroup v-model="newApiKey.permissions">
              <UCheckbox
                v-for="permission in availablePermissions"
                :key="permission.value"
                :value="permission.value"
                :label="permission.label"
              />
            </UCheckboxGroup>
          </UFormField>

          <UFormField label="วันหมดอายุ">
            <UInput
              v-model="newApiKey.expiresAt"
              type="date"
            />
          </UFormField>

          <UFormField label="Rate Limit (คำขอต่อนาที)">
            <UInput
              v-model="newApiKey.rateLimit"
              type="number"
              min="1"
            />
          </UFormField>
        </div>
      </template>

      <template #footer>
        <div class="flex justify-end gap-2">
          <UButton
            variant="outline"
            @click="showCreateKeyModal = false"
          >
            ยกเลิก
          </UButton>
          <UButton
            :loading="isCreatingKey"
            @click="handleCreateApiKey"
          >
            สร้าง API Key
          </UButton>
        </div>
      </template>
    </UModal>

    <!-- API Documentation Modal -->
    <UModal 
      v-model:open="showDocumentation"
      title="เอกสาร API"
      description="คู่มือการใช้งาน API และตัวอย่างการเรียกใช้"
    >
      <template #body>
        <div class="space-y-6">
          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-white mb-2">
              การยืนยันตัวตน
            </h4>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
              ใช้ API Key ในส่วนหัว Authorization:
            </p>
            <pre class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg text-sm">Authorization: Bearer YOUR_API_KEY</pre>
          </div>

          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-white mb-2">
              Endpoints
            </h4>
            <div class="space-y-4">
              <div
                v-for="endpoint in apiEndpoints"
                :key="endpoint.path"
                class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div class="flex items-center gap-2 mb-2">
                  <UBadge
                    :color="getMethodColor(endpoint.method)"
                    :label="endpoint.method"
                  />
                  <code class="text-sm font-mono">{{ endpoint.path }}</code>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {{ endpoint.description }}
                </p>
                <div class="text-sm">
                  <strong>Parameters:</strong>
                  <pre class="bg-gray-50 dark:bg-gray-800 p-2 rounded mt-1">{{ JSON.stringify(endpoint.parameters, null, 2) }}</pre>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-white mb-2">
              Response Format
            </h4>
            <pre class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg text-sm">{
  "success": true,
  "data": {...},
  "message": "Success",
  "timestamp": "2024-01-15T10:30:00Z"
}</pre>
          </div>

          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-white mb-2">
              Error Codes
            </h4>
            <div class="space-y-2">
              <div class="flex items-center gap-3">
                <UBadge color="error" label="400" />
                <span class="text-sm">Bad Request - คำขอไม่ถูกต้อง</span>
              </div>
              <div class="flex items-center gap-3">
                <UBadge color="error" label="401" />
                <span class="text-sm">Unauthorized - API Key ไม่ถูกต้อง</span>
              </div>
              <div class="flex items-center gap-3">
                <UBadge color="error" label="403" />
                <span class="text-sm">Forbidden - ไม่มีสิทธิ์เข้าถึง</span>
              </div>
              <div class="flex items-center gap-3">
                <UBadge color="error" label="429" />
                <span class="text-sm">Too Many Requests - เกินขีดจำกัด</span>
              </div>
              <div class="flex items-center gap-3">
                <UBadge color="error" label="500" />
                <span class="text-sm">Internal Server Error - ข้อผิดพลาดเซิร์ฟเวอร์</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </UModal>
  </div>
</template>
