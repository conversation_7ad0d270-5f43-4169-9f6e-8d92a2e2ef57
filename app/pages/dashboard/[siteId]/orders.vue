<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route:any = useRoute();
const siteId = computed(() => route.params.siteId);

// ใช้ SEO meta สำหรับหน้า orders
const { t } = useI18n();
useDashboardSeo(
  t('orders.title', 'Orders'),
  t('orders.description', 'Manage and track all your customer orders')
);

// State
const loading = ref(false);
const searchQuery = ref('');
const selectedStatus = ref('all');
const selectedPaymentStatus = ref('all');
const sortBy = ref('created_at');
const sortOrder = ref('desc');
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Status options
const statuses = ref([
  { value: 'all', label: 'ทุกสถานะ' },
  { value: 'pending', label: 'รอดำเนินการ' },
  { value: 'processing', label: 'กำลังดำเนินการ' },
  { value: 'shipped', label: 'จัดส่งแล้ว' },
  { value: 'delivered', label: 'จัดส่งสำเร็จ' },
  { value: 'cancelled', label: 'ยกเลิก' },
  { value: 'refunded', label: 'คืนเงิน' },
]);

const paymentStatuses = ref([
  { value: 'all', label: 'ทุกสถานะการชำระเงิน' },
  { value: 'pending', label: 'รอชำระเงิน' },
  { value: 'paid', label: 'ชำระเงินแล้ว' },
  { value: 'failed', label: 'ชำระเงินล้มเหลว' },
  { value: 'refunded', label: 'คืนเงินแล้ว' },
]);

// Mock orders data
const orders = ref([
  {
    id: 'ORD-001',
    customer: {
      name: 'สมชาย ใจดี',
      email: '<EMAIL>',
      phone: '************',
    },
    items: [
      { name: 'iPhone 15 Pro', quantity: 1, price: 45000 },
      { name: 'เคส iPhone 15 Pro', quantity: 2, price: 1200 },
    ],
    total: 47400,
    status: 'delivered',
    paymentStatus: 'paid',
    paymentMethod: 'credit_card',
    shippingAddress: '123 ถนนสุขุมวิท แขวงคลองเตย เขตคลองเตย กรุงเทพฯ 10110',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-17T14:20:00Z',
    trackingNumber: 'TH123456789',
  },
  {
    id: 'ORD-002',
    customer: {
      name: 'สมหญิง รักดี',
      email: '<EMAIL>',
      phone: '************',
    },
    items: [
      { name: 'MacBook Air M2', quantity: 1, price: 45000 },
      { name: 'เมาส์ไร้สาย', quantity: 1, price: 2500 },
    ],
    total: 47500,
    status: 'processing',
    paymentStatus: 'paid',
    paymentMethod: 'bank_transfer',
    shippingAddress: '456 ถนนรัชดาภิเษก แขวงดินแดง เขตดินแดง กรุงเทพฯ 10400',
    createdAt: '2024-01-16T09:15:00Z',
    updatedAt: '2024-01-16T16:45:00Z',
    trackingNumber: null,
  },
  {
    id: 'ORD-003',
    customer: {
      name: 'สมศักดิ์ มั่นคง',
      email: '<EMAIL>',
      phone: '************',
    },
    items: [
      { name: 'iPad Air', quantity: 1, price: 25000 },
      { name: 'Apple Pencil', quantity: 1, price: 3500 },
    ],
    total: 28500,
    status: 'pending',
    paymentStatus: 'pending',
    paymentMethod: 'mobile_banking',
    shippingAddress: '789 ถนนลาดพร้าว แขวงจันทรเกษม เขตจตุจักร กรุงเทพฯ 10900',
    createdAt: '2024-01-17T11:20:00Z',
    updatedAt: '2024-01-17T11:20:00Z',
    trackingNumber: null,
  },
  {
    id: 'ORD-004',
    customer: {
      name: 'สมปอง ใจเย็น',
      email: '<EMAIL>',
      phone: '************',
    },
    items: [{ name: 'AirPods Pro', quantity: 1, price: 8500 }],
    total: 8500,
    status: 'cancelled',
    paymentStatus: 'refunded',
    paymentMethod: 'credit_card',
    shippingAddress: '321 ถนนวิภาวดี แขวงดินแดง เขตดินแดง กรุงเทพฯ 10400',
    createdAt: '2024-01-14T15:45:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    trackingNumber: null,
  },
  {
    id: 'ORD-005',
    customer: {
      name: 'สมศรี สวยงาม',
      email: '<EMAIL>',
      phone: '************',
    },
    items: [
      { name: 'Apple Watch Series 9', quantity: 1, price: 18000 },
      { name: 'สายนาฬิกา', quantity: 2, price: 1500 },
    ],
    total: 21000,
    status: 'shipped',
    paymentStatus: 'paid',
    paymentMethod: 'e_wallet',
    shippingAddress: '654 ถนนพระราม 9 แขวงห้วยขวาง เขตห้วยขวาง กรุงเทพฯ 10310',
    createdAt: '2024-01-16T14:30:00Z',
    updatedAt: '2024-01-17T09:15:00Z',
    trackingNumber: 'TH987654321',
  },
]);

// Analytics data
const analytics = ref({
  totalOrders: 156,
  totalRevenue: 2845000,
  averageOrderValue: 18237,
  pendingOrders: 23,
  processingOrders: 45,
  deliveredOrders: 88,
  cancelledOrders: 12,
  refundedOrders: 8,
});

// Computed filtered orders
const filteredOrders = computed(() => {
  let filtered = orders.value;

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(
      order =>
        order.id.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        order.customer.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        order.customer.email.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  // Status filter
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(order => order.status === selectedStatus.value);
  }

  // Payment status filter
  if (selectedPaymentStatus.value !== 'all') {
    filtered = filtered.filter(order => order.paymentStatus === selectedPaymentStatus.value);
  }

  // Sort
  filtered.sort((a, b) => {
    let aVal = a[sortBy.value as keyof typeof a];
    let bVal = b[sortBy.value as keyof typeof b];

    if (typeof aVal === 'string' && typeof bVal === 'string') {
      aVal = aVal.toLowerCase();
      bVal = bVal.toLowerCase();
    }

    if (sortOrder.value === 'asc') {
      return (aVal ?? '') > (bVal ?? '') ? 1 : -1;
    } else {
      return (aVal ?? '') < (bVal ?? '') ? 1 : -1;
    }
  });

  return filtered;
});

// Paginated orders
const paginatedOrders = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredOrders.value.slice(start, end);
});

// Total pages
const totalPages = computed(() => {
  return Math.ceil(filteredOrders.value.length / itemsPerPage.value);
});

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
  }).format(amount);
};

// Format date
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Get status badge class
const getStatusBadgeClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    processing: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    shipped: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
    delivered: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    cancelled: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
    refunded: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
  };
  return classes[status as keyof typeof classes] || classes.pending;
};

// Get status label
const getStatusLabel = (status: string) => {
  const labels = {
    pending: 'รอดำเนินการ',
    processing: 'กำลังดำเนินการ',
    shipped: 'จัดส่งแล้ว',
    delivered: 'จัดส่งสำเร็จ',
    cancelled: 'ยกเลิก',
    refunded: 'คืนเงิน',
  };
  return labels[status as keyof typeof labels] || status;
};

// Get payment status badge class
const getPaymentStatusBadgeClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    paid: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    failed: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
    refunded: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
  };
  return classes[status as keyof typeof classes] || classes.pending;
};

// Get payment status label
const getPaymentStatusLabel = (status: string) => {
  const labels = {
    pending: 'รอชำระเงิน',
    paid: 'ชำระเงินแล้ว',
    failed: 'ชำระเงินล้มเหลว',
    refunded: 'คืนเงินแล้ว',
  };
  return labels[status as keyof typeof labels] || status;
};

// Get payment method name
const getPaymentMethodName = (method: string) => {
  const names = {
    credit_card: 'บัตรเครดิต',
    bank_transfer: 'โอนเงินธนาคาร',
    mobile_banking: 'Mobile Banking',
    e_wallet: 'E-Wallet',
  };
  return names[method as keyof typeof names] || method;
};

// Methods
const handleSort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = field;
    sortOrder.value = 'desc';
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handleViewOrder = (order: any) => {
  // Navigate to order detail page
  navigateTo(`/dashboard/${siteId.value}/orders/${order.id}`);
};

const handleUpdateStatus = async (order: any, newStatus: string) => {
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    order.status = newStatus;
    // Update timestamp only on client side
    order.updatedAt = new Date().toISOString();

    useToast().add({
      title: 'สำเร็จ',
      description: `อัปเดตสถานะคำสั่งซื้อ ${order.id} เรียบร้อยแล้ว`,
      color: 'success',
    });
  } catch (error) {
    useToast().add({
      title: 'ผิดพลาด',
      description: 'ไม่สามารถอัปเดตสถานะได้',
      color: 'error',
    });
  }
};

const handlePrintInvoice = (order: any) => {
  // Mock print functionality
  window.open(`/dashboard/${siteId.value}/orders/${order.id}/invoice`, '_blank');
};

const handleExportOrders = () => {
  // Mock export functionality
  useToast().add({
    title: 'สำเร็จ',
    description: 'ส่งออกข้อมูลคำสั่งซื้อเรียบร้อยแล้ว',
    color: 'success',
  });
};

// SEO
useDashboardSeo('จัดการคำสั่งซื้อ', 'จัดการคำสั่งซื้อ ตรวจสอบสถานะ การชำระเงิน และการจัดส่งสินค้า');
</script>

<template>
  <div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          คำสั่งซื้อ
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          จัดการคำสั่งซื้อและติดตามสถานะการจัดส่ง
        </p>
      </div>
      <div class="flex items-center gap-2">
        <UButton 
          @click="handleExportOrders"
          variant="outline"
          icon="i-heroicons-arrow-down-tray"
        >
          ส่งออก
        </UButton>
        <UButton 
          variant="outline"
          icon="i-heroicons-cog-6-tooth"
        >
          ตั้งค่า
        </UButton>
      </div>
    </div>

    <!-- Analytics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">คำสั่งซื้อทั้งหมด</p>
            <ClientOnly>
              <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalOrders }}</p>
              <template #fallback>
                <p class="text-3xl font-bold text-gray-400 dark:text-gray-500">-</p>
              </template>
            </ClientOnly>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">+12% จากเดือนที่แล้ว</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-shopping-cart" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">รายได้รวม</p>
            <ClientOnly>
              <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatCurrency(analytics.totalRevenue) }}</p>
              <template #fallback>
                <p class="text-3xl font-bold text-gray-400 dark:text-gray-500">-</p>
              </template>
            </ClientOnly>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">+8% จากเดือนที่แล้ว</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-currency-dollar" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">มูลค่าสั่งซื้อเฉลี่ย</p>
                        <ClientOnly>
              <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatCurrency(analytics.averageOrderValue) }}</p>
              <template #fallback>
                <p class="text-3xl font-bold text-gray-400 dark:text-gray-500">-</p>
              </template>
            </ClientOnly>
            <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">+5% จากเดือนที่แล้ว</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-chart-bar" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">รอดำเนินการ</p>
            <ClientOnly>
              <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.pendingOrders }}</p>
              <template #fallback>
                <p class="text-3xl font-bold text-gray-400 dark:text-gray-500">-</p>
              </template>
            </ClientOnly>
            <p class="text-xs text-orange-600 dark:text-orange-400 mt-1">ต้องดำเนินการ</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-clock" class="w-8 h-8" />
          </div>
        </div>
      </UCard>
    </div>

    <!-- Filters -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-funnel" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวกรองและค้นหา</h2>
        </div>
      </template>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหาคำสั่งซื้อ</label>
          <UInput
            v-model="searchQuery"
            placeholder="ค้นหาตามรหัสหรือชื่อลูกค้า..."
            icon="i-heroicons-magnifying-glass"
            size="xl"
            class="w-full"
          />
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สถานะ</label>
          <USelect
            v-model="selectedStatus"
            :items="statuses"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกสถานะ"
            size="xl"
            class="w-full"
          />
        </div>

        <!-- Payment Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สถานะการชำระเงิน</label>
          <USelect
            v-model="selectedPaymentStatus"
            :items="paymentStatuses"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกสถานะการชำระเงิน"
            size="xl"
            class="w-full"
          />
        </div>

        <!-- Sort -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เรียงลำดับ</label>
          <div class="flex gap-2">
            <USelect
              v-model="sortBy"
              :items="[
                { value: 'created_at', label: 'วันที่สร้าง' },
                { value: 'total', label: 'มูลค่า' },
                { value: 'status', label: 'สถานะ' }
              ]"
              option-attribute="label"
              value-attribute="value"
              size="xl"
              class="flex-1"
            />
            <UButton
              @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'"
              variant="outline"
              size="sm"
              class="px-3"
            >
              <Icon 
                :name="sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down'" 
                class="w-4 h-4" 
              />
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Orders Table -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <Icon name="i-heroicons-shopping-cart" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รายการคำสั่งซื้อ</h2>
            <ClientOnly>
              <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-400 rounded-full">
                {{ filteredOrders.length }} รายการ
              </span>
              <template #fallback>
                                  <ClientOnly>
                    <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-400 dark:text-gray-500 rounded-full">
                      กำลังนับ...
                    </span>
                    <template #fallback>
                      <ClientOnly>
                        <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-400 dark:text-gray-500 rounded-full">
                          กำลังโหลด...
                        </span>
                        <template #fallback>
                          <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-400 dark:text-gray-500 rounded-full">
                            ...
                          </span>
                        </template>
                      </ClientOnly>
                    </template>
                  </ClientOnly>
              </template>
            </ClientOnly>
          </div>
        </div>
      </template>
      
      <div v-if="loading" class="flex items-center justify-center py-12">
        <ClientOnly>
          <div class="flex items-center gap-3">
            <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-blue-600" />
            <ClientOnly>
              <span class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</span>
              <template #fallback>
                <ClientOnly>
                  <span class="text-gray-400 dark:text-gray-500">กำลังเตรียมข้อมูล...</span>
                  <template #fallback>
                    <ClientOnly>
                      <span class="text-gray-400 dark:text-gray-500">กำลังโหลด...</span>
                      <template #fallback>
                        <span class="text-gray-400 dark:text-gray-500">...</span>
                      </template>
                    </ClientOnly>
                  </template>
                </ClientOnly>
              </template>
            </ClientOnly>
          </div>
          <template #fallback>
            <div class="flex items-center gap-3">
              <div class="w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <ClientOnly>
                <span class="text-gray-400 dark:text-gray-500">กำลังเตรียมข้อมูล...</span>
                <template #fallback>
                  <ClientOnly>
                    <span class="text-gray-400 dark:text-gray-500">กำลังโหลด...</span>
                    <template #fallback>
                      <span class="text-gray-400 dark:text-gray-500">...</span>
                    </template>
                  </ClientOnly>
                </template>
              </ClientOnly>
            </div>
          </template>
        </ClientOnly>
      </div>

      <div v-else-if="paginatedOrders.length === 0" class="text-center py-12">
        <ClientOnly>
          <div class="text-gray-400 dark:text-gray-500 mb-4">
            <Icon name="i-heroicons-shopping-cart" class="w-12 h-12 mx-auto" />
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">ไม่พบคำสั่งซื้อ</h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            ยังไม่มีคำสั่งซื้อในระบบ หรือไม่ตรงกับเงื่อนไขการค้นหา
          </p>
          <template #fallback>
            <div class="text-gray-400 dark:text-gray-500 mb-4">
              <div class="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            </div>
            <h3 class="text-lg font-semibold text-gray-400 dark:text-gray-500 mb-2">กำลังตรวจสอบ...</h3>
                          <ClientOnly>
                <p class="text-gray-400 dark:text-gray-500 mb-4">
                  กำลังตรวจสอบข้อมูล...
                </p>
                <template #fallback>
                  <ClientOnly>
                    <p class="text-gray-400 dark:text-gray-500 mb-4">
                      กำลังโหลด...
                    </p>
                    <template #fallback>
                      <p class="text-gray-400 dark:text-gray-500 mb-4">
                        ...
                      </p>
                    </template>
                  </ClientOnly>
                </template>
              </ClientOnly>
          </template>
        </ClientOnly>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>รหัสคำสั่งซื้อ</span>
                  <UButton
                    @click="handleSort('id')"
                    variant="ghost"
                    size="xs"
                    class="p-1"
                  >
                    <Icon 
                      :name="sortBy === 'id' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'" 
                      class="w-3 h-3" 
                    />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">ลูกค้า</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">สินค้า</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>มูลค่า</span>
                  <UButton
                    @click="handleSort('total')"
                    variant="ghost"
                    size="xs"
                    class="p-1"
                  >
                    <Icon 
                      :name="sortBy === 'total' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'" 
                      class="w-3 h-3" 
                    />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">สถานะ</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">การชำระเงิน</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>วันที่สร้าง</span>
                  <UButton
                    @click="handleSort('created_at')"
                    variant="ghost"
                    size="xs"
                    class="p-1"
                  >
                    <Icon 
                      :name="sortBy === 'created_at' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'" 
                      class="w-3 h-3" 
                    />
                  </UButton>
                </div>
              </th>
              <th class="text-right py-4 px-6 font-semibold text-gray-900 dark:text-white">การดำเนินการ</th>
            </tr>
          </thead>
          <tbody>
            <tr 
              v-for="order in paginatedOrders" 
              :key="order.id"
              class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
            >
              <td class="py-4 px-6">
                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-semibold text-sm">
                    {{ order.id.slice(-3) }}
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">{{ order.id }}</h3>
                    <ClientOnly>
                      <p class="text-sm text-gray-600 dark:text-gray-400">{{ order.items.length }} รายการ</p>
                      <template #fallback>
                        <p class="text-sm text-gray-400 dark:text-gray-500">กำลังโหลด...</p>
                      </template>
                    </ClientOnly>
                  </div>
                </div>
              </td>
              <td class="py-4 px-6">
                <div>
                  <h3 class="font-semibold text-gray-900 dark:text-white">{{ order.customer.name }}</h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400">{{ order.customer.email }}</p>
                  <p class="text-xs text-gray-500 dark:text-gray-500">{{ order.customer.phone }}</p>
                </div>
              </td>
              <td class="py-4 px-6">
                <div class="space-y-1">
                  <div 
                    v-for="item in order.items.slice(0, 2)" 
                    :key="item.name"
                    class="text-sm text-gray-600 dark:text-gray-400"
                  >
                    {{ item.name }} x{{ item.quantity }}
                  </div>
                  <ClientOnly>
                    <div v-if="order.items.length > 2" class="text-xs text-gray-500 dark:text-gray-500">
                      +{{ order.items.length - 2 }} รายการเพิ่มเติม
                    </div>
                    <template #fallback>
                      <div class="text-xs text-gray-400 dark:text-gray-500">
                        กำลังโหลด...
                      </div>
                    </template>
                  </ClientOnly>
                </div>
              </td>
              <td class="py-4 px-6">
                <ClientOnly>
                  <span class="font-semibold text-gray-900 dark:text-white">
                    {{ formatCurrency(order.total) }}
                  </span>
                  <template #fallback>
                    <span class="font-semibold text-gray-400 dark:text-gray-500">
                      กำลังโหลด...
                    </span>
                  </template>
                </ClientOnly>
              </td>
              <td class="py-4 px-6">
                <span 
                  class="px-3 py-1 text-xs rounded-full font-medium"
                  :class="getStatusBadgeClass(order.status)"
                >
                  {{ getStatusLabel(order.status) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="space-y-1">
                  <span 
                    class="px-2 py-1 text-xs rounded-full font-medium"
                    :class="getPaymentStatusBadgeClass(order.paymentStatus)"
                  >
                    {{ getPaymentStatusLabel(order.paymentStatus) }}
                  </span>
                  <p class="text-xs text-gray-500 dark:text-gray-500">
                    {{ getPaymentMethodName(order.paymentMethod) }}
                  </p>
                </div>
              </td>
              <td class="py-4 px-6">
                <ClientOnly>
                  <span class="text-sm text-gray-600 dark:text-gray-400">
                    {{ formatDate(order.createdAt) }}
                  </span>
                  <template #fallback>
                    <span class="text-sm text-gray-400 dark:text-gray-500">กำลังโหลด...</span>
                  </template>
                </ClientOnly>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center justify-end gap-2">
                  <UButton
                    @click="handleViewOrder(order)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                  >
                    <Icon name="i-heroicons-eye" class="w-4 h-4" />
                  </UButton>
                  <UButton
                    @click="handlePrintInvoice(order)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-green-50 dark:hover:bg-green-900/20 text-green-600 dark:text-green-400"
                  >
                    <Icon name="i-heroicons-printer" class="w-4 h-4" />
                  </UButton>
                  <UDropdownMenu :items="[
                    { label: 'อัปเดตสถานะ', icon: 'i-heroicons-pencil-square', click: () => handleUpdateStatus(order, 'processing') },
                    { label: 'ยกเลิก', icon: 'i-heroicons-x-mark', click: () => handleUpdateStatus(order, 'cancelled') }
                  ]">
                    <UButton
                      variant="ghost"
                      size="sm"
                      class="hover:bg-gray-50 dark:hover:bg-gray-900/20"
                    >
                      <Icon name="i-heroicons-ellipsis-vertical" class="w-4 h-4" />
                    </UButton>
                  </UDropdownMenu>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex items-center justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <ClientOnly>
          <div class="text-sm text-gray-600 dark:text-gray-400">
            แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} ถึง {{ Math.min(currentPage * itemsPerPage, filteredOrders.length) }} 
            จาก {{ filteredOrders.length }} รายการ
          </div>
          <template #fallback>
            <div class="text-sm text-gray-400 dark:text-gray-500">กำลังโหลดข้อมูล...</div>
          </template>
        </ClientOnly>
        <div class="flex items-center gap-2">
          <UButton
            @click="handlePageChange(currentPage - 1)"
            :disabled="currentPage === 1"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-left" class="w-4 h-4" />
          </UButton>
          
          <div class="flex items-center gap-1">
                      <ClientOnly>
            <UButton
              v-for="page in Math.min(5, totalPages)"
              :key="page"
              @click="handlePageChange(page)"
              :variant="currentPage === page ? 'solid' : 'outline'"
              size="sm"
              class="w-8 h-8 p-0"
            >
              {{ page }}
            </UButton>
            <template #fallback>
              <div class="flex items-center gap-1">
                <div class="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div class="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div class="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </div>
            </template>
          </ClientOnly>
          </div>
          
          <UButton
            @click="handlePageChange(currentPage + 1)"
            :disabled="currentPage === totalPages"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-right" class="w-4 h-4" />
          </UButton>
        </div>
      </div>
    </UCard>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style> 