<script setup lang="ts">

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route: any = useRoute();
const siteId = computed(() => route.params.siteId);

// Reactive data
const customers = ref<any[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);

// Filters and search
const searchQuery = ref('');
const selectedStatus = ref('all');
const selectedSource = ref('all');
const selectedDateRange = ref('all');
const sortBy = ref('created_at');
const sortOrder = ref('desc');
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Filter options
const statusOptions = ref([
  { value: 'all', label: 'ทุกสถานะ' },
  { value: 'active', label: 'ใช้งาน' },
  { value: 'inactive', label: 'ไม่ใช้งาน' },
  { value: 'vip', label: 'VIP' },
  { value: 'blocked', label: 'ถูกบล็อก' },
]);

const sourceOptions = ref([
  { value: 'all', label: 'ทุกแหล่ง' },
  { value: 'website', label: 'เว็บไซต์' },
  { value: 'mobile', label: 'มือถือ' },
  { value: 'social', label: 'โซเชียล' },
  { value: 'referral', label: 'แนะนำ' },
]);

const dateRangeOptions = ref([
  { value: 'all', label: 'ทั้งหมด' },
  { value: 'today', label: 'วันนี้' },
  { value: 'week', label: 'สัปดาห์นี้' },
  { value: 'month', label: 'เดือนนี้' },
  { value: 'year', label: 'ปีนี้' },
]);

const sortOptions = ref([
  { value: 'created_at', label: 'วันที่สมัคร' },
  { value: 'name', label: 'ชื่อ' },
  { value: 'email', label: 'อีเมล' },
  { value: 'orders', label: 'จำนวนคำสั่งซื้อ' },
  { value: 'total_spent', label: 'ยอดซื้อ' },
]);

// Analytics data
const analytics = ref({
  totalCustomers: 1247,
  activeCustomers: 892,
  newCustomers: 45,
  vipCustomers: 23,
  averageOrderValue: 2850,
  customerRetention: 78.5,
  topSource: 'เว็บไซต์',
  growthRate: 12.3,
});

// Mock customers data
const mockCustomers = [
  {
    id: 1,
    name: 'สมชาย ใจดี',
    email: '<EMAIL>',
    phone: '************',
    status: 'active',
    source: 'website',
    orders: 12,
    totalSpent: 45600,
    lastOrder: '2024-01-15',
    createdAt: '2023-06-15',
    avatar: null,
  },
  {
    id: 2,
    name: 'สมหญิง รักดี',
    email: '<EMAIL>',
    phone: '************',
    status: 'vip',
    source: 'mobile',
    orders: 28,
    totalSpent: 125000,
    lastOrder: '2024-01-20',
    createdAt: '2023-03-10',
    avatar: null,
  },
  {
    id: 3,
    name: 'สมศักดิ์ มั่นคง',
    email: '<EMAIL>',
    phone: '************',
    status: 'active',
    source: 'social',
    orders: 8,
    totalSpent: 23400,
    lastOrder: '2024-01-18',
    createdAt: '2023-08-22',
    avatar: null,
  },
  {
    id: 4,
    name: 'สมปอง ใจเย็น',
    email: '<EMAIL>',
    phone: '************',
    status: 'inactive',
    source: 'website',
    orders: 3,
    totalSpent: 8900,
    lastOrder: '2023-12-05',
    createdAt: '2023-09-15',
    avatar: null,
  },
  {
    id: 5,
    name: 'สมศรี สวยงาม',
    email: '<EMAIL>',
    phone: '************',
    status: 'active',
    source: 'referral',
    orders: 15,
    totalSpent: 67800,
    lastOrder: '2024-01-22',
    createdAt: '2023-05-20',
    avatar: null,
  },
];

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
  }).format(amount);
};

// Format number
const formatNumber = (num: number) => {
  return new Intl.NumberFormat('th-TH').format(num);
};

// Get status badge class
const getStatusBadgeClass = (status: string) => {
  const classes = {
    active: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
    vip: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
    blocked: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
  };
  return classes[status as keyof typeof classes] || classes.inactive;
};

// Get status label
const getStatusLabel = (status: string) => {
  const labels = {
    active: 'ใช้งาน',
    inactive: 'ไม่ใช้งาน',
    vip: 'VIP',
    blocked: 'ถูกบล็อก',
  };
  return labels[status as keyof typeof labels] || status;
};

// Get source label
const getSourceLabel = (source: string) => {
  const labels = {
    website: 'เว็บไซต์',
    mobile: 'มือถือ',
    social: 'โซเชียล',
    referral: 'แนะนำ',
  };
  return labels[source as keyof typeof labels] || source;
};

// Computed filtered customers
const filteredCustomers = computed(() => {
  let filtered = customers.value || [];

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(
      customer =>
        customer.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        customer.email.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        customer.phone.includes(searchQuery.value)
    );
  }

  // Status filter
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(customer => customer.status === selectedStatus.value);
  }

  // Source filter
  if (selectedSource.value !== 'all') {
    filtered = filtered.filter(customer => customer.source === selectedSource.value);
  }

  // Sort
  filtered.sort((a, b) => {
    let aVal = a[sortBy.value as keyof typeof a];
    let bVal = b[sortBy.value as keyof typeof b];

    if (typeof aVal === 'string') {
      aVal = aVal.toLowerCase();
      bVal = bVal.toLowerCase();
    }

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });

  return filtered;
});

// Paginated customers
const paginatedCustomers = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredCustomers.value.slice(start, end);
});

// Total pages
const totalPages = computed(() => {
  return Math.ceil(filteredCustomers.value.length / itemsPerPage.value);
});

// Load customers
const loadCustomers = async () => {
  loading.value = true;
  error.value = null;
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    customers.value = mockCustomers;
  } catch (err) {
    error.value = 'เกิดข้อผิดพลาดในการโหลดข้อมูล';
    console.error('Error loading customers:', err);
  } finally {
    loading.value = false;
  }
};

// Load data on mount
onMounted(async () => {
  await loadCustomers();
});

// Methods
const handleSort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = field;
    sortOrder.value = 'desc';
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handleEditCustomer = (customer: any) => {
  // Navigate to edit page or open modal
  navigateTo(`/dashboard/${siteId.value}/customers/${customer.id}/edit`);
};

const handleViewCustomer = (customer: any) => {
  // Navigate to customer detail page
  navigateTo(`/dashboard/${siteId.value}/customers/${customer.id}`);
};

const handleBlockCustomer = async (customer: any) => {
  if (confirm(`คุณต้องการบล็อกลูกค้า "${customer.name}" ใช่หรือไม่?`)) {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      customer.status = 'blocked';
      // Note: useToast() might not be available, using console.log instead
      console.log(`บล็อกลูกค้า ${customer.name} เรียบร้อยแล้ว`);
    } catch (err) {
      console.error('Error blocking customer:', err);
    }
  }
};

const handleUnblockCustomer = async (customer: any) => {
  if (confirm(`คุณต้องการปลดบล็อกลูกค้า "${customer.name}" ใช่หรือไม่?`)) {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      customer.status = 'active';
      // Note: useToast() might not be available, using console.log instead
      console.log(`ปลดบล็อกลูกค้า ${customer.name} เรียบร้อยแล้ว`);
    } catch (err) {
      console.error('Error unblocking customer:', err);
    }
  }
};

// SEO meta สำหรับหน้า customers
useSeoMeta({
  title: 'จัดการลูกค้า - ระบบจัดการร้านค้า',
  description: 'จัดการข้อมูลลูกค้าและความสัมพันธ์กับลูกค้าในระบบ',
  keywords: 'จัดการลูกค้า, ฐานข้อมูลลูกค้า, CRM',
  ogTitle: 'จัดการลูกค้า - ระบบจัดการร้านค้า',
  ogDescription: 'จัดการข้อมูลลูกค้าและความสัมพันธ์กับลูกค้าในระบบ',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div class="space-y-8">
    <!-- Enhanced Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          จัดการลูกค้า
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          จัดการข้อมูลลูกค้าทั้งหมดในระบบ
        </p>
      </div>
      <UButton
        class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
        <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
        เพิ่มลูกค้าใหม่
      </UButton>
    </div>

    <!-- Enhanced Analytics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ลูกค้าทั้งหมด</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalCustomers }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">+{{ analytics.growthRate }}% จากเดือนที่แล้ว</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-users" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ลูกค้าที่ใช้งาน</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.activeCustomers }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">{{ Math.round((analytics.activeCustomers /
              analytics.totalCustomers) * 100) }}% ของทั้งหมด</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-user-group" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ลูกค้าใหม่</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.newCustomers }}</p>
            <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">เดือนนี้</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-user-plus" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ลูกค้า VIP</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.vipCustomers }}</p>
            <p class="text-xs text-purple-600 dark:text-purple-400 mt-1">เฉลี่ย {{
              formatCurrency(analytics.averageOrderValue) }}/คน</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-star" class="w-8 h-8" />
          </div>
        </div>
      </UCard>
    </div>

    <!-- Enhanced Filters -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-funnel" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวกรองและค้นหา</h2>
        </div>
      </template>

      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหาลูกค้า</label>
          <UInput v-model="searchQuery" placeholder="ค้นหาตามชื่อ, อีเมล, เบอร์โทร..."
            icon="i-heroicons-magnifying-glass" class="w-full" />
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สถานะ</label>
          <USelect v-model="selectedStatus" :items="statusOptions" option-attribute="label" value-attribute="value"
            placeholder="เลือกสถานะ" class="w-full" />
        </div>

        <!-- Source Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">แหล่งที่มา</label>
          <USelect v-model="selectedSource" :items="sourceOptions" option-attribute="label" value-attribute="value"
            placeholder="เลือกแหล่งที่มา" class="w-full" />
        </div>

        <!-- Date Range Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ช่วงเวลา</label>
          <USelect v-model="selectedDateRange" :items="dateRangeOptions" option-attribute="label"
            value-attribute="value" placeholder="เลือกช่วงเวลา" class="w-full" />
        </div>

        <!-- Sort -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เรียงลำดับ</label>
          <div class="flex gap-2">
            <USelect v-model="sortBy" :items="sortOptions" option-attribute="label" value-attribute="value"
              class="flex-1" />
            <UButton @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'" variant="outline" size="sm" class="px-3">
              <Icon :name="sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down'" class="w-4 h-4" />
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Enhanced Customers Table -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <Icon name="i-heroicons-users" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รายการลูกค้า</h2>
            <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-400 rounded-full">
              {{ filteredCustomers.length }} รายการ
            </span>
          </div>
          <div class="flex items-center gap-2">
            <UButton variant="ghost" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
              <Icon name="i-heroicons-arrow-down-tray" class="w-4 h-4 mr-1" />
              ส่งออก
            </UButton>
            <UButton variant="ghost" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
              <Icon name="i-heroicons-cog-6-tooth" class="w-4 h-4" />
            </UButton>
          </div>
        </div>
      </template>

      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="flex items-center gap-3">
          <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-blue-600" />
          <span class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</span>
        </div>
      </div>

      <div v-else-if="error" class="text-center py-12">
        <div class="text-red-600 dark:text-red-400 mb-4">
          <Icon name="i-heroicons-exclamation-triangle" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">เกิดข้อผิดพลาด</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">{{ error }}</p>
        <UButton @click="loadCustomers" variant="outline">
          ลองใหม่
        </UButton>
      </div>

      <div v-else-if="paginatedCustomers.length === 0" class="text-center py-12">
        <div class="text-gray-400 dark:text-gray-500 mb-4">
          <Icon name="i-heroicons-users" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">ไม่พบลูกค้า</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          ยังไม่มีลูกค้าในระบบ หรือไม่ตรงกับเงื่อนไขการค้นหา
        </p>
        <UButton class="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
          เพิ่มลูกค้าแรก
        </UButton>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>ลูกค้า</span>
                  <UButton @click="handleSort('name')" variant="ghost" size="xs" class="p-1">
                    <Icon
                      :name="sortBy === 'name' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'"
                      class="w-3 h-3" />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>ข้อมูลติดต่อ</span>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>สถานะ</span>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>คำสั่งซื้อ</span>
                  <UButton @click="handleSort('orders')" variant="ghost" size="xs" class="p-1">
                    <Icon
                      :name="sortBy === 'orders' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'"
                      class="w-3 h-3" />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>ยอดซื้อ</span>
                  <UButton @click="handleSort('total_spent')" variant="ghost" size="xs" class="p-1">
                    <Icon
                      :name="sortBy === 'total_spent' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'"
                      class="w-3 h-3" />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>วันที่สมัคร</span>
                  <UButton @click="handleSort('created_at')" variant="ghost" size="xs" class="p-1">
                    <Icon
                      :name="sortBy === 'created_at' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'"
                      class="w-3 h-3" />
                  </UButton>
                </div>
              </th>
              <th class="text-right py-4 px-6 font-semibold text-gray-900 dark:text-white">การดำเนินการ</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="customer in paginatedCustomers" :key="customer.id"
              class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
              <td class="py-4 px-6">
                <div class="flex items-center gap-4">
                  <div
                    class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-semibold">
                    {{ customer.name.charAt(0).toUpperCase() }}
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">{{ customer.name }}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ getSourceLabel(customer.source) }}</p>
                  </div>
                </div>
              </td>
              <td class="py-4 px-6">
                <div class="space-y-1">
                  <p class="text-sm text-gray-900 dark:text-white">{{ customer.email }}</p>
                  <p class="text-sm text-gray-600 dark:text-gray-400">{{ customer.phone }}</p>
                </div>
              </td>
              <td class="py-4 px-6">
                <span class="px-3 py-1 text-xs rounded-full font-medium" :class="getStatusBadgeClass(customer.status)">
                  {{ getStatusLabel(customer.status) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span class="font-semibold text-gray-900 dark:text-white">
                  {{ formatNumber(customer.orders) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span class="font-semibold text-gray-900 dark:text-white">
                  {{ formatCurrency(customer.totalSpent) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {{ new Date(customer.createdAt).toLocaleDateString('th-TH') }}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center justify-end gap-2">
                  <UButton @click="handleViewCustomer(customer)" variant="ghost" size="sm"
                    class="hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400">
                    <Icon name="i-heroicons-eye" class="w-4 h-4" />
                  </UButton>
                  <UButton @click="handleEditCustomer(customer)" variant="ghost" size="sm"
                    class="hover:bg-green-50 dark:hover:bg-green-900/20 text-green-600 dark:text-green-400">
                    <Icon name="i-heroicons-pencil-square" class="w-4 h-4" />
                  </UButton>
                  <UButton v-if="customer.status !== 'blocked'" @click="handleBlockCustomer(customer)" variant="ghost"
                    size="sm" class="hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400">
                    <Icon name="i-heroicons-no-symbol" class="w-4 h-4" />
                  </UButton>
                  <UButton v-else @click="handleUnblockCustomer(customer)" variant="ghost" size="sm"
                    class="hover:bg-green-50 dark:hover:bg-green-900/20 text-green-600 dark:text-green-400">
                    <Icon name="i-heroicons-check" class="w-4 h-4" />
                  </UButton>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Enhanced Pagination -->
      <div v-if="totalPages > 1"
        class="flex items-center justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} ถึง {{ Math.min(currentPage * itemsPerPage,
            filteredCustomers.length) }}
          จาก {{ filteredCustomers.length }} รายการ
        </div>
        <div class="flex items-center gap-2">
          <UButton @click="handlePageChange(currentPage - 1)" :disabled="currentPage === 1" variant="outline" size="sm">
            <Icon name="i-heroicons-chevron-left" class="w-4 h-4" />
          </UButton>

          <div class="flex items-center gap-1">
            <UButton v-for="page in Math.min(5, totalPages)" :key="page" @click="handlePageChange(page)"
              :variant="currentPage === page ? 'solid' : 'outline'" size="sm" class="w-8 h-8 p-0">
              {{ page }}
            </UButton>
          </div>

          <UButton @click="handlePageChange(currentPage + 1)" :disabled="currentPage === totalPages" variant="outline"
            size="sm">
            <Icon name="i-heroicons-chevron-right" class="w-4 h-4" />
          </UButton>
        </div>
      </div>
    </UCard>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8>* {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>