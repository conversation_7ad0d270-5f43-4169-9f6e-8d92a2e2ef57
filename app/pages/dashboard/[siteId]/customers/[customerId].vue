<script setup lang="ts">
import { useMyImage } from '~/composables/useMyImage'
const { imageUrl } = useMyImage()

definePageMeta({
    layout: 'dashboard',
    middleware: ['auth'],
});

// Note: UiImage is used in template but doesn't need explicit import (auto-imported)

// Get route params
const route: any = useRoute();
const siteId = computed(() => route.params.siteId);
const customerId = computed(() => route.params.customerId);

// State
const loading = ref(false);
const customer = ref<any>(null);
const orders = ref<any[]>([]);
const activities = ref<any[]>([]);
const error = ref<string | null>(null);

// Tabs
const activeTab = ref('overview');
const tabs = [
    { key: 'overview', label: 'ภาพรวม', icon: 'i-heroicons-user' },
    { key: 'orders', label: 'คำสั่งซื้อ', icon: 'i-heroicons-shopping-bag' },
    { key: 'activities', label: 'กิจกรรม', icon: 'i-heroicons-clock' },
    { key: 'settings', label: 'การตั้งค่า', icon: 'i-heroicons-cog-6-tooth' }
];

// Mock customer data
const mockCustomer = {
    id: 1,
    name: 'สมชาย ใจดี',
    email: '<EMAIL>',
    phone: '************',
    status: 'active',
    source: 'website',
    orders: 12,
    totalSpent: 45600,
    lastOrder: '2024-01-15',
    createdAt: '2023-06-15',
    avatar: null,
    address: {
        street: '123 ถนนสุขุมวิท',
        district: 'คลองเตย',
        province: 'กรุงเทพมหานคร',
        postalCode: '10110',
        country: 'ประเทศไทย'
    },
    preferences: {
        newsletter: true,
        sms: false,
        promotions: true,
        language: 'th'
    },
    tags: ['VIP', 'ลูกค้าประจำ', 'ซื้อบ่อย'],
    notes: 'ลูกค้าที่ดี ชอบสินค้าคุณภาพดี มักจะซื้อในช่วงเทศกาล',
    socialMedia: {
        facebook: 'somchai.jaidee',
        line: '@somchai123',
        instagram: null
    },
    loyaltyPoints: 2450,
    customershipLevel: 'Gold',
    referralCode: 'SC2023',
    lastLogin: '2024-01-20T10:30:00Z'
};

// Mock orders data
const mockOrders = [
    {
        id: 'ORD-001',
        date: '2024-01-15',
        status: 'completed',
        total: 2850,
        items: 3,
        paymentMethod: 'credit_card'
    },
    {
        id: 'ORD-002',
        date: '2024-01-10',
        status: 'shipped',
        total: 1200,
        items: 1,
        paymentMethod: 'bank_transfer'
    },
    {
        id: 'ORD-003',
        date: '2024-01-05',
        status: 'pending',
        total: 3400,
        items: 2,
        paymentMethod: 'cash'
    }
];

// Mock activities data
const mockActivities = [
    {
        id: 1,
        type: 'order',
        title: 'สั่งซื้อสินค้า',
        description: 'สั่งซื้อสินค้า 3 รายการ มูลค่า ฿2,850',
        timestamp: '2024-01-15T14:30:00Z',
        icon: 'i-heroicons-shopping-bag',
        color: 'green'
    },
    {
        id: 2,
        type: 'login',
        title: 'เข้าสู่ระบบ',
        description: 'เข้าสู่ระบบผ่านเว็บไซต์',
        timestamp: '2024-01-15T10:15:00Z',
        icon: 'i-heroicons-arrow-right-on-rectangle',
        color: 'blue'
    },
    {
        id: 3,
        type: 'profile',
        title: 'อัปเดตโปรไฟล์',
        description: 'เปลี่ยนเบอร์โทรศัพท์',
        timestamp: '2024-01-10T16:45:00Z',
        icon: 'i-heroicons-user',
        color: 'purple'
    }
];

// Format currency
const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('th-TH', {
        style: 'currency',
        currency: 'THB',
    }).format(amount);
};

// Format date
const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('th-TH', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
};

// Format datetime
const formatDateTime = (date: string) => {
    return new Date(date).toLocaleString('th-TH', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// Get status badge class
const getStatusBadgeClass = (status: string) => {
    const classes = {
        active: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
        inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
        vip: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
        blocked: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
    };
    return classes[status as keyof typeof classes] || classes.inactive;
};

// Get status label
const getStatusLabel = (status: string) => {
    const labels = {
        active: 'ใช้งาน',
        inactive: 'ไม่ใช้งาน',
        vip: 'VIP',
        blocked: 'ถูกบล็อก',
    };
    return labels[status as keyof typeof labels] || status;
};

// Get order status class
const getOrderStatusClass = (status: string) => {
    const classes = {
        completed: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
        shipped: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
        pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
        cancelled: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
    };
    return classes[status as keyof typeof classes] || classes.pending;
};

// Get order status label
const getOrderStatusLabel = (status: string) => {
    const labels = {
        completed: 'สำเร็จ',
        shipped: 'จัดส่งแล้ว',
        pending: 'รอดำเนินการ',
        cancelled: 'ยกเลิก',
    };
    return labels[status as keyof typeof labels] || status;
};

// Load customer data
const loadCustomer = async () => {
    loading.value = true;
    error.value = null;
    try {
        // Mock API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        customer.value = mockCustomer;
        orders.value = mockOrders;
        activities.value = mockActivities;
    } catch (err) {
        error.value = 'เกิดข้อผิดพลาดในการโหลดข้อมูล';
        console.error('Error loading customer:', err);
    } finally {
        loading.value = false;
    }
};

// Actions
const handleEditCustomer = () => {
    navigateTo(`/dashboard/${siteId.value}/customers/${customerId.value}/edit`);
};

const handleBlockCustomer = async () => {
    if (!customer.value) return;

    if (confirm(`คุณต้องการบล็อกลูกค้า "${customer.value.name}" ใช่หรือไม่?`)) {
        try {
            // Mock API call
            await new Promise(resolve => setTimeout(resolve, 500));
            customer.value.status = 'blocked';
            console.log(`บล็อกลูกค้า ${customer.value.name} เรียบร้อยแล้ว`);
        } catch (err) {
            console.error('Error blocking customer:', err);
        }
    }
};

const handleUnblockCustomer = async () => {
    if (!customer.value) return;

    if (confirm(`คุณต้องการปลดบล็อกลูกค้า "${customer.value.name}" ใช่หรือไม่?`)) {
        try {
            // Mock API call
            await new Promise(resolve => setTimeout(resolve, 500));
            customer.value.status = 'active';
            console.log(`ปลดบล็อกลูกค้า ${customer.value.name} เรียบร้อยแล้ว`);
        } catch (err) {
            console.error('Error unblocking customer:', err);
        }
    }
};

const handleDeleteCustomer = async () => {
    if (!customer.value) return;

    if (confirm(`คุณต้องการลบลูกค้า "${customer.value.name}" ใช่หรือไม่? การดำเนินการนี้ไม่สามารถย้อนกลับได้`)) {
        try {
            // Mock API call
            await new Promise(resolve => setTimeout(resolve, 500));
            console.log(`ลบลูกค้า ${customer.value.name} เรียบร้อยแล้ว`);
            navigateTo(`/dashboard/${siteId.value}/customers`);
        } catch (err) {
            console.error('Error deleting customer:', err);
        }
    }
};

const handleSendEmail = () => {
    if (!customer.value) return;
    // TODO: Implement email sending
    console.log(`ส่งอีเมลถึง ${customer.value.email}`);
};

const handleAddNote = () => {
    // TODO: Implement add note modal
    console.log('เพิ่มหมายเหตุ');
};

const handleViewOrder = (orderId: string) => {
    navigateTo(`/dashboard/${siteId.value}/orders/${orderId}`);
};

// Load data on mount
onMounted(() => {
    loadCustomer();
});

// SEO
useSeoMeta({
    title: 'รายละเอียดลูกค้า - ระบบจัดการร้านค้า',
    description: 'ดูและจัดการข้อมูลลูกค้ารายบุคคล',
    keywords: 'รายละเอียดลูกค้า, จัดการลูกค้า, CRM',
    ogTitle: 'รายละเอียดลูกค้า - ระบบจัดการร้านค้า',
    ogDescription: 'ดูและจัดการข้อมูลลูกค้ารายบุคคล',
    ogType: 'website',
    twitterCard: 'summary_large_image',
});
</script>

<template>
    <div class="space-y-6">
        <!-- Loading State -->
        <div v-if="loading" class="flex items-center justify-center py-12">
            <div class="flex items-center gap-3">
                <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-blue-600" />
                <span class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</span>
            </div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="text-center py-12">
            <div class="text-red-600 dark:text-red-400 mb-4">
                <Icon name="i-heroicons-exclamation-triangle" class="w-12 h-12 mx-auto" />
            </div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">เกิดข้อผิดพลาด</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">{{ error }}</p>
            <UButton @click="loadCustomer" variant="outline">
                ลองใหม่
            </UButton>
        </div>

        <!-- Customer Data -->
        <div v-else-if="customer" class="space-y-6">
            <!-- Header -->
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="flex items-center gap-4">
                    <UButton :to="`/dashboard/${siteId}/customers`" variant="ghost" icon="i-heroicons-arrow-left"
                        size="sm">
                        กลับ
                    </UButton>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                            รายละเอียดลูกค้า
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400">
                            จัดการข้อมูลและกิจกรรมของลูกค้า
                        </p>
                    </div>
                </div>

                <div class="flex items-center gap-2">
                    <UButton @click="handleSendEmail" variant="outline" icon="i-heroicons-envelope" size="sm">
                        ส่งอีเมล
                    </UButton>
                    <UButton @click="handleEditCustomer" color="primary" icon="i-heroicons-pencil-square" size="sm">
                        แก้ไข
                    </UButton>
                    <UDropdownMenu :items="[
                        [{
                            label: customer.status === 'blocked' ? 'ปลดบล็อก' : 'บล็อกลูกค้า',
                            icon: customer.status === 'blocked' ? 'i-heroicons-check' : 'i-heroicons-no-symbol',
                            click: customer.status === 'blocked' ? handleUnblockCustomer : handleBlockCustomer
                        }],
                        [{
                            label: 'ลบลูกค้า',
                            icon: 'i-heroicons-trash',
                            click: handleDeleteCustomer
                        }]
                    ]">
                        <UButton variant="ghost" icon="i-heroicons-ellipsis-vertical" size="sm" />
                    </UDropdownMenu>
                </div>
            </div>

            <!-- Customer Profile Card -->
            <UCard class="overflow-hidden">
                <div class="flex flex-col sm:flex-row gap-6">
                    <!-- Avatar -->
                    <div class="flex-shrink-0">
                        <UiImage :src="customer.avatar" :alt="customer.name" width="96" height="96" :process-width="192"
                            :process-height="192" crop="thumb" rounded="xl" object-fit="cover"
                            fallback-icon="solar:user-circle-bold-duotone"
                            :fallback-text="customer.name.charAt(0).toUpperCase()"
                            fallback-bg="bg-gradient-to-br from-blue-500 to-purple-600 text-white" />
                    </div>

                    <!-- Basic Info -->
                    <div class="flex-1 space-y-4">
                        <div>
                            <div class="flex items-center gap-3 mb-2">
                                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                                    {{ customer.name }}
                                </h2>
                                <span class="px-3 py-1 text-xs rounded-full font-medium"
                                    :class="getStatusBadgeClass(customer.status)">
                                    {{ getStatusLabel(customer.status) }}
                                </span>
                                <span
                                    class="px-3 py-1 text-xs rounded-full font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                                    {{ customer.customershipLevel }}
                                </span>
                            </div>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                                <div class="flex items-center gap-2">
                                    <Icon name="i-heroicons-envelope" class="w-4 h-4 text-gray-400" />
                                    <span class="text-gray-600 dark:text-gray-400">{{ customer.email }}</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <Icon name="i-heroicons-phone" class="w-4 h-4 text-gray-400" />
                                    <span class="text-gray-600 dark:text-gray-400">{{ customer.phone }}</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <Icon name="i-heroicons-calendar" class="w-4 h-4 text-gray-400" />
                                    <span class="text-gray-600 dark:text-gray-400">สมัครเมื่อ {{
                                        formatDate(customer.createdAt) }}</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <Icon name="i-heroicons-clock" class="w-4 h-4 text-gray-400" />
                                    <span class="text-gray-600 dark:text-gray-400">เข้าสู่ระบบล่าสุด {{
                                        formatDateTime(customer.lastLogin) }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Tags -->
                        <div v-if="customer.tags && customer.tags.length > 0" class="flex flex-wrap gap-2">
                            <span v-for="tag in customer.tags" :key="tag"
                                class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-md">
                                {{ tag }}
                            </span>
                        </div>
                    </div>

                    <!-- Stats -->
                    <div class="flex-shrink-0 grid grid-cols-3 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ customer.orders }}</div>
                            <div class="text-xs text-gray-600 dark:text-gray-400">คำสั่งซื้อ</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{
                                formatCurrency(customer.totalSpent) }}</div>
                            <div class="text-xs text-gray-600 dark:text-gray-400">ยอดซื้อ</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{
                                customer.loyaltyPoints.toLocaleString() }}</div>
                            <div class="text-xs text-gray-600 dark:text-gray-400">แต้มสะสม</div>
                        </div>
                    </div>
                </div>
            </UCard>

            <!-- Tabs -->
            <div class="border-b border-gray-200 dark:border-gray-700">
                <nav class="flex space-x-8">
                    <button v-for="tab in tabs" :key="tab.key" @click="activeTab = tab.key" :class="[
                        'flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors',
                        activeTab === tab.key
                            ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                            : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                    ]">
                        <Icon :name="tab.icon" class="w-4 h-4" />
                        {{ tab.label }}
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <div class="space-y-6">
                <!-- Overview Tab -->
                <div v-if="activeTab === 'overview'" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Contact Information -->
                    <UCard>
                        <template #header>
                            <div class="flex items-center gap-2">
                                <Icon name="i-heroicons-user" class="w-5 h-5 text-blue-600" />
                                <h3 class="text-lg font-semibold">ข้อมูลติดต่อ</h3>
                            </div>
                        </template>

                        <div class="space-y-4">
                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ที่อยู่</label>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    {{ customer.address.street }}<br>
                                    {{ customer.address.district }} {{ customer.address.province }}<br>
                                    {{ customer.address.postalCode }} {{ customer.address.country }}
                                </p>
                            </div>

                            <div v-if="customer.socialMedia">
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">โซเชียลมีเดีย</label>
                                <div class="space-y-2">
                                    <div v-if="customer.socialMedia.facebook" class="flex items-center gap-2">
                                        <Icon name="i-simple-icons-facebook" class="w-4 h-4 text-blue-600" />
                                        <span class="text-sm text-gray-600 dark:text-gray-400">{{
                                            customer.socialMedia.facebook }}</span>
                                    </div>
                                    <div v-if="customer.socialMedia.line" class="flex items-center gap-2">
                                        <Icon name="i-simple-icons-line" class="w-4 h-4 text-green-600" />
                                        <span class="text-sm text-gray-600 dark:text-gray-400">{{
                                            customer.socialMedia.line }}</span>
                                    </div>
                                    <div v-if="customer.socialMedia.instagram" class="flex items-center gap-2">
                                        <Icon name="i-simple-icons-instagram" class="w-4 h-4 text-pink-600" />
                                        <span class="text-sm text-gray-600 dark:text-gray-400">{{
                                            customer.socialMedia.instagram }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </UCard>

                    <!-- Preferences -->
                    <UCard>
                        <template #header>
                            <div class="flex items-center gap-2">
                                <Icon name="i-heroicons-cog-6-tooth" class="w-5 h-5 text-green-600" />
                                <h3 class="text-lg font-semibold">การตั้งค่า</h3>
                            </div>
                        </template>

                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-700 dark:text-gray-300">รับจดหมายข่าว</span>
                                <span :class="customer.preferences.newsletter ? 'text-green-600' : 'text-gray-400'">
                                    {{ customer.preferences.newsletter ? 'เปิด' : 'ปิด' }}
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-700 dark:text-gray-300">รับ SMS</span>
                                <span :class="customer.preferences.sms ? 'text-green-600' : 'text-gray-400'">
                                    {{ customer.preferences.sms ? 'เปิด' : 'ปิด' }}
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-700 dark:text-gray-300">รับโปรโมชั่น</span>
                                <span :class="customer.preferences.promotions ? 'text-green-600' : 'text-gray-400'">
                                    {{ customer.preferences.promotions ? 'เปิด' : 'ปิด' }}
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-700 dark:text-gray-300">ภาษา</span>
                                <span class="text-sm text-gray-600 dark:text-gray-400">
                                    {{ customer.preferences.language === 'th' ? 'ไทย' : 'English' }}
                                </span>
                            </div>
                        </div>
                    </UCard>

                    <!-- Notes -->
                    <UCard class="lg:col-span-2">
                        <template #header>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-2">
                                    <Icon name="i-heroicons-document-text" class="w-5 h-5 text-purple-600" />
                                    <h3 class="text-lg font-semibold">หมายเหตุ</h3>
                                </div>
                                <UButton @click="handleAddNote" variant="outline" size="sm" icon="i-heroicons-plus">
                                    เพิ่มหมายเหตุ
                                </UButton>
                            </div>
                        </template>

                        <div v-if="customer.notes" class="text-sm text-gray-600 dark:text-gray-400">
                            {{ customer.notes }}
                        </div>
                        <div v-else class="text-sm text-gray-400 italic">
                            ยังไม่มีหมายเหตุ
                        </div>
                    </UCard>
                </div>

                <!-- Orders Tab -->
                <div v-else-if="activeTab === 'orders'">
                    <UCard>
                        <template #header>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-2">
                                    <Icon name="i-heroicons-shopping-bag" class="w-5 h-5 text-green-600" />
                                    <h3 class="text-lg font-semibold">คำสั่งซื้อ</h3>
                                    <span
                                        class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-400 rounded-full">
                                        {{ orders.length }} รายการ
                                    </span>
                                </div>
                            </div>
                        </template>

                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">
                                            รหัสคำสั่งซื้อ</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">
                                            วันที่</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">
                                            สถานะ</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">
                                            จำนวนสินค้า</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">
                                            ยอดรวม</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">
                                            การดำเนินการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="order in orders" :key="order.id"
                                        class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50">
                                        <td class="py-3 px-4">
                                            <span class="font-medium text-gray-900 dark:text-white">{{ order.id
                                            }}</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="text-gray-600 dark:text-gray-400">{{ formatDate(order.date)
                                            }}</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="px-2 py-1 text-xs rounded-full font-medium"
                                                :class="getOrderStatusClass(order.status)">
                                                {{ getOrderStatusLabel(order.status) }}
                                            </span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="text-gray-600 dark:text-gray-400">{{ order.items }}
                                                รายการ</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="font-semibold text-gray-900 dark:text-white">{{
                                                formatCurrency(order.total)
                                            }}</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <UButton @click="handleViewOrder(order.id)" variant="ghost" size="sm"
                                                icon="i-heroicons-eye">
                                                ดู
                                            </UButton>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </UCard>
                </div>

                <!-- Activities Tab -->
                <div v-else-if="activeTab === 'activities'">
                    <UCard>
                        <template #header>
                            <div class="flex items-center gap-2">
                                <Icon name="i-heroicons-clock" class="w-5 h-5 text-orange-600" />
                                <h3 class="text-lg font-semibold">กิจกรรมล่าสุด</h3>
                            </div>
                        </template>

                        <div class="space-y-4">
                            <div v-for="activity in activities" :key="activity.id"
                                class="flex items-start gap-4 p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50">
                                <div :class="[
                                    'p-2 rounded-lg',
                                    activity.color === 'green' ? 'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400' :
                                        activity.color === 'blue' ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400' :
                                            activity.color === 'purple' ? 'bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400' :
                                                'bg-gray-100 text-gray-600 dark:bg-gray-900/20 dark:text-gray-400'
                                ]">
                                    <Icon :name="activity.icon" class="w-4 h-4" />
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-900 dark:text-white">{{ activity.title }}</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ activity.description }}
                                    </p>
                                    <p class="text-xs text-gray-500 dark:text-gray-500 mt-2">{{
                                        formatDateTime(activity.timestamp) }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </UCard>
                </div>

                <!-- Settings Tab -->
                <div v-else-if="activeTab === 'settings'">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Account Settings -->
                        <UCard>
                            <template #header>
                                <div class="flex items-center gap-2">
                                    <Icon name="i-heroicons-user-circle" class="w-5 h-5 text-blue-600" />
                                    <h3 class="text-lg font-semibold">การตั้งค่าบัญชี</h3>
                                </div>
                            </template>

                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="font-medium text-gray-900 dark:text-white">สถานะบัญชี</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">เปลี่ยนสถานะการใช้งานบัญชี
                                        </p>
                                    </div>
                                    <UButton v-if="customer.status === 'blocked'" @click="handleUnblockCustomer"
                                        color="success" variant="soft" size="sm">
                                        ปลดบล็อก
                                    </UButton>
                                    <UButton v-else @click="handleBlockCustomer" color="error" variant="soft" size="sm">
                                        บล็อก
                                    </UButton>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="font-medium text-gray-900 dark:text-white">รหัสแนะนำ</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ customer.referralCode }}
                                        </p>
                                    </div>
                                    <UButton variant="outline" size="sm">
                                        คัดลอก
                                    </UButton>
                                </div>
                            </div>
                        </UCard>

                        <!-- Danger Zone -->
                        <UCard>
                            <template #header>
                                <div class="flex items-center gap-2">
                                    <Icon name="i-heroicons-exclamation-triangle" class="w-5 h-5 text-red-600" />
                                    <h3 class="text-lg font-semibold text-red-600">พื้นที่อันตราย</h3>
                                </div>
                            </template>

                            <div class="space-y-4">
                                <div>
                                    <h4 class="font-medium text-gray-900 dark:text-white mb-2">ลบบัญชีลูกค้า</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                        การลบบัญชีลูกค้าจะไม่สามารถย้อนกลับได้ ข้อมูลทั้งหมดจะถูกลบอย่างถาวร
                                    </p>
                                    <UButton @click="handleDeleteCustomer" color="error" variant="outline" size="sm">
                                        ลบบัญชีลูกค้า
                                    </UButton>
                                </div>
                            </div>
                        </UCard>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.space-y-6>* {
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Tab transition */
.tab-content {
    transition: all 0.3s ease;
}

/* Hover effects */
.hover\:scale-105:hover {
    transform: scale(1.05);
}
</style>