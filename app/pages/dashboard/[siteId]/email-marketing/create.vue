<script setup lang="ts">
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);
const form = ref({
  name: '',
  subject: '',
  content: '',
  recipientList: '',
  scheduleDate: '',
});
const isSubmitting = ref(false);
const toast = useToast();

const handleSubmit = async () => {
  isSubmitting.value = true;
  // TODO: เรียก API เพื่อสร้าง email campaign จริง
  setTimeout(() => {
    toast.add({
      title: 'สร้างแคมเปญอีเมลสำเร็จ',
      description: `สร้างแคมเปญ ${form.value.name} สำหรับเว็บไซต์ ${siteId.value} เรียบร้อยแล้ว`,
      color: 'success',
    });
    isSubmitting.value = false;
  }, 1000);
};
</script>
<template>
  <div class="p-6 max-w-2xl mx-auto">
    <h1 class="text-2xl font-bold mb-4">สร้างแคมเปญอีเมลใหม่</h1>
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ชื่อแคมเปญ <span class="text-red-500">*</span></label>
        <UInput
          v-model="form.name"
          placeholder="ชื่อแคมเปญอีเมล"
        />
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">หัวข้ออีเมล <span class="text-red-500">*</span></label>
        <UInput
          v-model="form.subject"
          placeholder="หัวข้ออีเมล"
        />
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เนื้อหาอีเมล <span class="text-red-500">*</span></label>
        <UTextarea
          v-model="form.content"
          placeholder="เนื้อหาอีเมล..."
          :rows="8"
        />
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">รายชื่อผู้รับ <span class="text-red-500">*</span></label>
          <USelect
            v-model="form.recipientList"
            :items="[
              { label: 'ลูกค้าทั้งหมด', value: 'all' },
              { label: 'ลูกค้าใหม่', value: 'new' },
              { label: 'ลูกค้า VIP', value: 'vip' }
            ]"
            placeholder="เลือกรายชื่อผู้รับ"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">วันที่ส่ง</label>
          <UInput
            v-model="form.scheduleDate"
            type="datetime-local"
            placeholder="เลือกวันที่ส่ง"
          />
        </div>
      </div>

      <div class="flex justify-end gap-3">
        <UButton to="../email-marketing" variant="outline">
          ยกเลิก
        </UButton>
        <UButton type="submit" :loading="isSubmitting">
          สร้างแคมเปญ
        </UButton>
      </div>
    </form>
  </div>
</template> 