<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

const loading = ref(false);
const searchQuery = ref('');
const selectedType = ref('all');
const sortBy = ref('date');
const sortOrder = ref('desc');
const currentPage = ref(1);
const itemsPerPage = ref(10);

const reportTypes = ref([
  { value: 'all', label: 'ทุกประเภท' },
  { value: 'sales', label: 'ยอดขาย' },
  { value: 'orders', label: 'คำสั่งซื้อ' },
  { value: 'customers', label: 'ลูกค้า' },
  { value: 'products', label: 'สินค้า' },
  { value: 'traffic', label: 'ทราฟฟิก' },
  { value: 'finance', label: 'การเงิน' },
]);

const reports = ref([
  {
    id: '1',
    type: 'sales',
    title: 'ยอดขายประจำวัน',
    value: 125000,
    date: '2024-06-01',
    growth: 12.5,
    unit: 'บาท',
    trend: 'up',
  },
  {
    id: '2',
    type: 'orders',
    title: 'จำนวนคำสั่งซื้อ',
    value: 320,
    date: '2024-06-01',
    growth: 8.2,
    unit: 'ออเดอร์',
    trend: 'up',
  },
  {
    id: '3',
    type: 'customers',
    title: 'ลูกค้าใหม่',
    value: 45,
    date: '2024-06-01',
    growth: 5.0,
    unit: 'คน',
    trend: 'up',
  },
  {
    id: '4',
    type: 'products',
    title: 'สินค้าขายดี',
    value: 12,
    date: '2024-06-01',
    growth: 2.1,
    unit: 'รายการ',
    trend: 'up',
  },
  {
    id: '5',
    type: 'traffic',
    title: 'ผู้เข้าชมเว็บไซต์',
    value: 5400,
    date: '2024-06-01',
    growth: 15.3,
    unit: 'ครั้ง',
    trend: 'up',
  },
  {
    id: '6',
    type: 'finance',
    title: 'รายรับสุทธิ',
    value: 98000,
    date: '2024-06-01',
    growth: 10.7,
    unit: 'บาท',
    trend: 'up',
  },
]);

const analytics = ref({
  totalSales: 125000,
  totalOrders: 320,
  newCustomers: 45,
  bestSeller: 'สินค้า A',
  totalVisitors: 5400,
  netRevenue: 98000,
});

const filteredReports = computed(() => {
  let filtered = reports.value;
  if (searchQuery.value) {
    filtered = filtered.filter(r => r.title.includes(searchQuery.value));
  }
  if (selectedType.value !== 'all') {
    filtered = filtered.filter(r => r.type === selectedType.value);
  }
  filtered.sort((a, b) => {
    if (sortOrder.value === 'asc') {
      return a[sortBy.value] > b[sortBy.value] ? 1 : -1;
    } else {
      return a[sortBy.value] < b[sortBy.value] ? 1 : -1;
    }
  });
  return filtered;
});

const paginatedReports = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredReports.value.slice(start, end);
});

const totalPages = computed(() => {
  return Math.ceil(filteredReports.value.length / itemsPerPage.value);
});

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
  }).format(amount);
};

const handleSort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = field;
    sortOrder.value = 'desc';
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handleExport = () => {
  useToast().add({
    title: 'ส่งออก',
    description: 'ส่งออกรายงานสำเร็จ',
    color: 'success',
  });
};
</script>

<template>
  <div class="space-y-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">รายงาน</h1>
        <p class="text-gray-600 dark:text-gray-400">ดูสถิติและรายงานสำคัญของร้านค้า</p>
      </div>
      <UButton @click="handleExport" variant="outline" icon="i-heroicons-arrow-down-tray">ส่งออก</UButton>
    </div>
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      <UCard>
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ยอดขายวันนี้</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ formatCurrency(analytics.totalSales) }}</p>
          </div>
          <Icon name="i-heroicons-currency-dollar" class="w-8 h-8 text-green-500" />
        </div>
      </UCard>
      <UCard>
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">คำสั่งซื้อ</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ analytics.totalOrders }}</p>
          </div>
          <Icon name="i-heroicons-shopping-cart" class="w-8 h-8 text-blue-500" />
        </div>
      </UCard>
      <UCard>
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ลูกค้าใหม่</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ analytics.newCustomers }}</p>
          </div>
          <Icon name="i-heroicons-user-plus" class="w-8 h-8 text-purple-500" />
        </div>
      </UCard>
      <UCard>
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">สินค้าขายดี</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ analytics.bestSeller }}</p>
          </div>
          <Icon name="i-heroicons-star" class="w-8 h-8 text-yellow-500" />
        </div>
      </UCard>
      <UCard>
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ผู้เข้าชม</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ analytics.totalVisitors }}</p>
          </div>
          <Icon name="i-heroicons-eye" class="w-8 h-8 text-orange-500" />
        </div>
      </UCard>
      <UCard>
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">รายรับสุทธิ</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ formatCurrency(analytics.netRevenue) }}</p>
          </div>
          <Icon name="i-heroicons-banknotes" class="w-8 h-8 text-green-700" />
        </div>
      </UCard>
    </div>
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-funnel" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวกรองและค้นหา</h2>
        </div>
      </template>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหารายงาน</label>
          <UInput v-model="searchQuery" placeholder="ค้นหาตามชื่อรายงาน..." icon="i-heroicons-magnifying-glass" class="w-full" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ประเภท</label>
          <USelect v-model="selectedType" :items="reportTypes" option-attribute="label" value-attribute="value" placeholder="เลือกประเภท" class="w-full" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เรียงลำดับ</label>
          <div class="flex gap-2">
            <USelect v-model="sortBy" :items="[
              { value: 'date', label: 'วันที่' },
              { value: 'value', label: 'มูลค่า' }
            ]" option-attribute="label" value-attribute="value" class="flex-1" />
            <UButton @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'" variant="outline" size="sm" class="px-3">
              <Icon :name="sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down'" class="w-4 h-4" />
            </UButton>
          </div>
        </div>
      </div>
    </UCard>
    <UCard class="overflow-x-auto">
      <template #header>
        <div class="flex items-center gap-3">
          <Icon name="i-heroicons-chart-bar" class="w-5 h-5 text-blue-500" />
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตารางรายงาน</h2>
        </div>
      </template>
      <table class="w-full">
        <thead>
          <tr class="border-b border-gray-200 dark:border-gray-700">
            <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">ชื่อรายงาน</th>
            <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">ประเภท</th>
            <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">มูลค่า</th>
            <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">วันที่</th>
            <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">แนวโน้ม</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="report in paginatedReports" :key="report.id" class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
            <td class="py-4 px-6">{{ report.title }}</td>
            <td class="py-4 px-6">{{ reportTypes.value.find(t => t.value === report.type)?.label || report.type }}</td>
            <td class="py-4 px-6">{{ report.unit === 'บาท' ? formatCurrency(report.value) : report.value + ' ' + report.unit }}</td>
            <td class="py-4 px-6">{{ report.date }}</td>
            <td class="py-4 px-6">
              <span :class="report.trend === 'up' ? 'text-green-600' : 'text-red-600'">
                <Icon :name="report.trend === 'up' ? 'i-heroicons-arrow-trending-up' : 'i-heroicons-arrow-trending-down'" class="w-4 h-4 inline" />
                {{ report.growth }}%
              </span>
            </td>
          </tr>
        </tbody>
      </table>
      <div v-if="totalPages > 1" class="flex items-center justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} ถึง {{ Math.min(currentPage * itemsPerPage, filteredReports.length) }} จาก {{ filteredReports.length }} รายการ
        </div>
        <div class="flex items-center gap-2">
          <UButton @click="handlePageChange(currentPage - 1)" :disabled="currentPage === 1" variant="outline" size="sm">
            <Icon name="i-heroicons-chevron-left" class="w-4 h-4" />
          </UButton>
          <div class="flex items-center gap-1">
            <UButton v-for="page in Math.min(5, totalPages)" :key="page" @click="handlePageChange(page)" :variant="currentPage === page ? 'solid' : 'outline'" size="sm" class="w-8 h-8 p-0">
              {{ page }}
            </UButton>
          </div>
          <UButton @click="handlePageChange(currentPage + 1)" :disabled="currentPage === totalPages" variant="outline" size="sm">
            <Icon name="i-heroicons-chevron-right" class="w-4 h-4" />
          </UButton>
        </div>
      </div>
    </UCard>
  </div>
</template>

<style scoped>
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}
.space-y-8 > * { animation: fadeInUp 0.6s ease-out forwards; }
.group:hover { transform: translateY(-2px); }
.bg-gradient-to-r { background-size: 200% 200%; animation: gradient 3s ease infinite; }
@keyframes gradient { 0% { background-position: 0% 50%; } 50% { background-position: 100% 50%; } 100% { background-position: 0% 50%; } }
tbody tr { transition: all 0.2s ease; }
tbody tr:hover { transform: scale(1.01); box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
</style> 