<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route:any = useRoute();
const siteId = computed(() => route.params.siteId);

// State
const searchQuery = ref('');
const selectedCategory = ref('all');
const selectedStatus = ref('all');

// Categories
const categories = ref([
  { value: 'all', label: 'ทุกหมวดหมู่' },
  { value: 'ecommerce', label: 'อีคอมเมิร์ซ' },
  { value: 'marketing', label: 'การตลาด' },
  { value: 'analytics', label: 'วิเคราะห์' },
  { value: 'communication', label: 'การสื่อสาร' },
  { value: 'security', label: 'ความปลอดภัย' },
  { value: 'performance', label: 'ประสิทธิภาพ' },
  { value: 'social', label: 'โซเชียล' },
]);

// Status options
const statuses = ref([
  { value: 'all', label: 'ทั้งหมด' },
  { value: 'installed', label: 'ติดตั้งแล้ว' },
  { value: 'available', label: 'พร้อมติดตั้ง' },
  { value: 'updates', label: 'อัปเดต' },
]);

// Addons data from backend
interface Addon {
  id: string;
  name: string;
  description: string;
  category: string;
  status: 'installed' | 'available' | 'updates';
  version: string;
  price: number;
  rating: number;
  downloads: number;
  author: string;
  icon: string;
  features: string[];
  screenshots: string[];
  lastUpdated: string;
}

const addons = ref<Addon[]>([]);
const loading = ref(false);

// Load addons from backend
const loadAddons = async () => {
  try {
    loading.value = true;
    const response = (await $fetch('/api/v1/addons')) as any;
    if (response.success) {
      addons.value = response.data.map((addon: any) => ({
        id: addon._id,
        name: addon.name,
        description: addon.description || '',
        category: addon.category,
        status: addon.features?.isInstalled ? 'installed' : 'available',
        version: addon.version,
        price: addon.pricing?.price || 0,
        rating: addon.stats?.rating || 0,
        downloads: addon.stats?.downloads || 0,
        author: addon.author,
        icon: 'solar:widget-line-duotone',
        features: addon.content?.requirements || [],
        screenshots: addon.ui?.screenshots || [],
        lastUpdated: addon.updatedAt,
      }));
    }
  } catch (error) {
    console.error('Error loading addons:', error);
    // Fallback to mock data if API fails
    addons.value = [
      {
        id: 'mct3wpw7TEk31Euw',
        name: 'Test Addon 4',
        description: 'Test addon for testing',
        category: 'utility',
        status: 'available',
        version: '1.0.0',
        price: 0,
        rating: 0,
        downloads: 0,
        author: 'Test Author',
        icon: 'solar:widget-line-duotone',
        features: ['Test feature 1', 'Test feature 2'],
        screenshots: [],
        lastUpdated: '2025-07-07T12:58:06.273Z',
      },
    ];
  } finally {
    loading.value = false;
  }
};

// Load addons on mount
onMounted(() => {
  loadAddons();
});

// Computed filtered addons
const filteredAddons = computed(() => {
  let filtered = addons.value;

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(
      addon =>
        addon.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        addon.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  // Category filter
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(addon => addon.category === selectedCategory.value);
  }

  // Status filter
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(addon => addon.status === selectedStatus.value);
  }

  return filtered;
});

// Format currency
const formatCurrency = (amount: number) => {
  if (amount === 0) return 'ฟรี';
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
  }).format(amount);
};

// Get status badge class
const getStatusBadgeClass = (status: string) => {
  const classes = {
    installed: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    available: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    updates: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
  };
  return classes[status as keyof typeof classes] || classes.available;
};

// Get status label
const getStatusLabel = (status: string) => {
  const labels = {
    installed: 'ติดตั้งแล้ว',
    available: 'พร้อมติดตั้ง',
    updates: 'อัปเดต',
  };
  return labels[status as keyof typeof labels] || status;
};

// Use composables
const {
  installAddon: apiInstallAddon,
  uninstallAddon: apiUninstallAddon,
  updateAddon: apiUpdateAddon,
} = useAddons();

// Install addon
const installAddon = async (addon: any) => {
  try {
    await apiInstallAddon(siteId.value, addon.id);

    // Update addon status
    addon.status = 'installed';

    useToast().add({
      title: 'สำเร็จ',
      description: `ติดตั้ง ${addon.name} เรียบร้อยแล้ว`,
      color: 'success',
    });
  } catch (error: any) {
    useToast().add({
      title: 'ผิดพลาด',
      description: error.message || 'ไม่สามารถติดตั้งแอปเสริมได้',
      color: 'error',
    });
  }
};

// Uninstall addon
const uninstallAddon = async (addon: any) => {
  if (confirm(`คุณต้องการถอนการติดตั้ง ${addon.name} ใช่หรือไม่?`)) {
    try {
      await apiUninstallAddon(siteId.value, addon.id);

      // Update addon status
      addon.status = 'available';

      useToast().add({
        title: 'สำเร็จ',
        description: `ถอนการติดตั้ง ${addon.name} เรียบร้อยแล้ว`,
        color: 'success',
      });
    } catch (error: any) {
      useToast().add({
        title: 'ผิดพลาด',
        description: error.message || 'ไม่สามารถถอนการติดตั้งแอปเสริมได้',
        color: 'error',
      });
    }
  }
};

// Update addon
const updateAddon = async (addon: any) => {
  try {
    await apiUpdateAddon(siteId.value, addon.id);

    useToast().add({
      title: 'สำเร็จ',
      description: `อัปเดต ${addon.name} เรียบร้อยแล้ว`,
      color: 'success',
    });
  } catch (error: any) {
    useToast().add({
      title: 'ผิดพลาด',
      description: error.message || 'ไม่สามารถอัปเดตแอปเสริมได้',
      color: 'error',
    });
  }
};
</script>

<template>
  <div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          แอปเสริม
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          ติดตั้งและจัดการแอปเสริมเพื่อเพิ่มฟีเจอร์ให้เว็บไซต์
        </p>
      </div>
      <div class="flex items-center gap-2">
        <UButton 
          variant="outline"
          icon="i-heroicons-arrow-path"
        >
          รีเฟรช
        </UButton>
        <UButton 
          variant="outline"
          icon="i-heroicons-cog-6-tooth"
        >
          ตั้งค่า
        </UButton>
      </div>
    </div>

    <!-- Filters -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-funnel" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวกรองและค้นหา</h2>
        </div>
      </template>
      
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหาแอปเสริม</label>
          <UInput
            v-model="searchQuery"
            placeholder="ค้นหาตามชื่อหรือคำอธิบาย..."
            icon="i-heroicons-magnifying-glass"
            size="xl"
            class="w-full"
          />
        </div>

        <!-- Category Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">หมวดหมู่</label>
          <USelect
            v-model="selectedCategory"
            :items="categories"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกหมวดหมู่"
            size="xl"
            class="w-full"
          />
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สถานะ</label>
          <USelect
            v-model="selectedStatus"
            :items="statuses"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกสถานะ"
            size="xl"
            class="w-full"
          />
        </div>
      </div>
    </UCard>

    <!-- Addons Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <UCard 
        v-for="addon in filteredAddons" 
        :key="addon.id"
        class="group hover:shadow-lg transition-all duration-300 hover:scale-105"
      >
        <!-- Addon Header -->
        <div class="flex items-start justify-between mb-4">
          <div class="flex items-center gap-3">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white">
              <Icon :name="addon.icon" class="w-6 h-6" />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900 dark:text-white">{{ addon.name }}</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">{{ addon.author }}</p>
            </div>
          </div>
          <UBadge 
            :color="addon.status === 'installed' ? 'success' : addon.status === 'updates' ? 'warning' : 'primary'"
            variant="subtle"
            size="sm"
          >
            {{ getStatusLabel(addon.status) }}
          </UBadge>
        </div>

        <!-- Addon Description -->
        <p class="text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
          {{ addon.description }}
        </p>

        <!-- Addon Stats -->
        <div class="flex items-center justify-between mb-4 text-sm">
          <div class="flex items-center gap-4">
            <div class="flex items-center gap-1">
              <Icon name="i-heroicons-star" class="w-4 h-4 text-yellow-500" />
              <span class="text-gray-600 dark:text-gray-400">{{ addon.rating }}</span>
            </div>
            <div class="flex items-center gap-1">
              <Icon name="i-heroicons-arrow-down-tray" class="w-4 h-4 text-gray-400" />
              <span class="text-gray-600 dark:text-gray-400">{{ addon.downloads.toLocaleString() }}</span>
            </div>
          </div>
          <div class="text-right">
            <p class="font-semibold text-gray-900 dark:text-white">{{ formatCurrency(addon.price) }}</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">v{{ addon.version }}</p>
          </div>
        </div>

        <!-- Addon Features -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">ฟีเจอร์หลัก:</h4>
          <div class="space-y-1">
            <div 
              v-for="feature in addon.features.slice(0, 2)" 
              :key="feature"
              class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400"
            >
              <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500" />
              <span>{{ feature }}</span>
            </div>
            <div v-if="addon.features.length > 2" class="text-xs text-gray-500 dark:text-gray-400">
              +{{ addon.features.length - 2 }} ฟีเจอร์เพิ่มเติม
            </div>
          </div>
        </div>

        <!-- Addon Actions -->
        <div class="flex items-center gap-2">
          <UButton
            v-if="addon.status === 'available'"
            @click="installAddon(addon)"
            size="sm"
            class="flex-1"
          >
            <Icon name="i-heroicons-plus" class="w-4 h-4 mr-1" />
            ติดตั้ง
          </UButton>
          
          <UButton
            v-else-if="addon.status === 'installed'"
            @click="uninstallAddon(addon)"
            variant="outline"
            size="sm"
            class="flex-1"
          >
            <Icon name="i-heroicons-trash" class="w-4 h-4 mr-1" />
            ถอนการติดตั้ง
          </UButton>
          
          <UButton
            v-else-if="addon.status === 'updates'"
            @click="updateAddon(addon)"
            size="sm"
            class="flex-1"
          >
            <Icon name="i-heroicons-arrow-up" class="w-4 h-4 mr-1" />
            อัปเดต
          </UButton>
          
          <UButton
            variant="ghost"
            size="sm"
            icon="i-heroicons-information-circle"
          >
            รายละเอียด
          </UButton>
        </div>
      </UCard>
    </div>

    <!-- Empty State -->
    <div v-if="filteredAddons.length === 0" class="text-center py-12">
      <div class="text-gray-400 dark:text-gray-500 mb-4">
        <Icon name="solar:widget-line-duotone" class="w-12 h-12 mx-auto" />
      </div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">ไม่พบแอปเสริม</h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">
        ลองเปลี่ยนเงื่อนไขการค้นหาหรือหมวดหมู่
      </p>
      <UButton @click="searchQuery = ''; selectedCategory = 'all'; selectedStatus = 'all'" variant="outline">
        ล้างตัวกรอง
      </UButton>
    </div>
  </div>
</template>

<style scoped>
/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Hover effects */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient animation */
.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
</style> 