
<script setup lang="ts"> 

// Types
interface SystemService {
  name: string
  description: string
  status: 'healthy' | 'warning' | 'error'
  responseTime: number
}

interface MaintenanceTask {
  id: string
  title: string
  description: string
  status: 'pending' | 'running' | 'completed'
  lastRun?: string
}

interface MaintenanceLog {
  id: string
  message: string
  type: 'success' | 'error' | 'info'
  timestamp: string
}

// Route and utilities
const route = useRoute()
const toast = useToast()

// State
const maintenanceMode = ref(false)
const isRefreshing = ref(false)
const optimizingDatabase = ref(false)
const repairingDatabase = ref(false)
const analyzingDatabase = ref(false)

// System status
const systemStatus = ref({
  cpu: 45,
  memory: 68,
  disk: 32,
  uptime: '15d 8h 32m'
})

// System services
const systemServices = ref<SystemService[]>([
  {
    name: 'Web Server',
    description: 'Apache/Nginx HTTP Server',
    status: 'healthy',
    responseTime: 45
  },
  {
    name: 'Database',
    description: 'MySQL/PostgreSQL Database',
    status: 'healthy',
    responseTime: 12
  },
  {
    name: 'Cache Server',
    description: 'Redis Cache Server',
    status: 'warning',
    responseTime: 89
  },
  {
    name: 'Email Service',
    description: 'SMTP Email Service',
    status: 'healthy',
    responseTime: 156
  }
])

// Maintenance tasks
const maintenanceTasks = ref<MaintenanceTask[]>([
  {
    id: '1',
    title: 'ล้างไฟล์ชั่วคราว',
    description: 'ลบไฟล์ชั่วคราวและไฟล์ที่ไม่จำเป็น',
    status: 'pending',
    lastRun: '2024-01-14T10:30:00Z'
  },
  {
    id: '2',
    title: 'เพิ่มประสิทธิภาพรูปภาพ',
    description: 'บีบอัดและเพิ่มประสิทธิภาพรูปภาพ',
    status: 'pending',
    lastRun: '2024-01-13T15:45:00Z'
  },
  {
    id: '3',
    title: 'ตรวจสอบลิงก์เสีย',
    description: 'ตรวจสอบและรายงานลิงก์ที่เสีย',
    status: 'pending',
    lastRun: '2024-01-12T09:15:00Z'
  },
  {
    id: '4',
    title: 'อัพเดทดัชนีการค้นหา',
    description: 'อัพเดทดัชนีสำหรับการค้นหา',
    status: 'pending',
    lastRun: '2024-01-11T14:20:00Z'
  }
])

// Cache info
const cacheInfo = ref({
  application: {
    size: 1024 * 1024 * 25, // 25MB
    files: 1250
  },
  database: {
    size: 1024 * 1024 * 15, // 15MB
    entries: 5680
  },
  static: {
    size: 1024 * 1024 * 45, // 45MB
    files: 2890
  }
})

// Cache clearing states
const clearingCache = ref({
  application: false,
  database: false,
  static: false,
  all: false
})

// Database info
const databaseInfo = ref({
  size: 1024 * 1024 * 1024 * 2.5, // 2.5GB
  tables: 45,
  connections: 12
})

// Maintenance logs
const maintenanceLogs = ref<MaintenanceLog[]>([
  {
    id: '1',
    message: 'ล้างไฟล์ชั่วคราวเสร็จสิ้น',
    type: 'success',
    timestamp: new Date().toISOString()
  },
  {
    id: '2',
    message: 'เริ่มต้นการเพิ่มประสิทธิภาพฐานข้อมูล',
    type: 'info',
    timestamp: new Date(Date.now() - 60000).toISOString()
  }
])

// Auto refresh interval
let refreshInterval: NodeJS.Timeout | null = null

// Methods
const refreshSystemStatus = async () => {
  isRefreshing.value = true
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Update system status with random values
    systemStatus.value = {
      cpu: Math.floor(Math.random() * 30) + 30,
      memory: Math.floor(Math.random() * 40) + 40,
      disk: Math.floor(Math.random() * 20) + 20,
      uptime: systemStatus.value.uptime
    }
    
    // Update service response times
    systemServices.value.forEach(service => {
      service.responseTime = Math.floor(Math.random() * 100) + 10
    })
    
    toast.add({
      title: 'อัพเดทสถานะ',
      description: 'สถานะระบบได้รับการอัพเดทแล้ว',
      icon: 'i-heroicons-check-circle',
      color: 'success'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถอัพเดทสถานะระบบได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  } finally {
    isRefreshing.value = false
  }
}

const toggleMaintenanceMode = async () => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))
    
    maintenanceMode.value = !maintenanceMode.value
    
    const log: MaintenanceLog = {
      id: Date.now().toString(),
      message: `${maintenanceMode.value ? 'เปิด' : 'ปิด'}โหมดบำรุงรักษา`,
      type: 'info',
      timestamp: new Date().toISOString()
    }
    maintenanceLogs.value.unshift(log)
    
    toast.add({
      title: maintenanceMode.value ? 'เปิดโหมดบำรุงรักษา' : 'ปิดโหมดบำรุงรักษา',
      description: maintenanceMode.value ? 
        'เว็บไซต์อยู่ในโหมดบำรุงรักษา' : 
        'เว็บไซต์กลับมาใช้งานได้ปกติ',
      icon: maintenanceMode.value ? 'i-heroicons-pause' : 'i-heroicons-play',
      color: maintenanceMode.value ? 'error' : 'success'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถเปลี่ยนโหมดบำรุงรักษาได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  }
}

const runMaintenanceTask = async (task: MaintenanceTask) => {
  task.status = 'running'
  
  try {
    // Simulate task execution
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    task.status = 'completed'
    task.lastRun = new Date().toISOString()
    
    const log: MaintenanceLog = {
      id: Date.now().toString(),
      message: `งาน "${task.title}" เสร็จสิ้น`,
      type: 'success',
      timestamp: new Date().toISOString()
    }
    maintenanceLogs.value.unshift(log)
    
    toast.add({
      title: 'งานเสร็จสิ้น',
      description: `งาน "${task.title}" ดำเนินการเสร็จสิ้น`,
      icon: 'i-heroicons-check-circle',
      color: 'success'
    })
  } catch (error) {
    task.status = 'pending'
    
    const log: MaintenanceLog = {
      id: Date.now().toString(),
      message: `งาน "${task.title}" ล้มเหลว`,
      type: 'error',
      timestamp: new Date().toISOString()
    }
    maintenanceLogs.value.unshift(log)
    
    toast.add({
      title: 'งานล้มเหลว',
      description: `งาน "${task.title}" ดำเนินการล้มเหลว`,
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  }
}

const clearCache = async (type: string) => {
  clearingCache.value[type as keyof typeof clearingCache.value] = true
  
  try {
    // Simulate cache clearing
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Reset cache info
    if (type === 'application') {
      cacheInfo.value.application = { size: 0, files: 0 }
    } else if (type === 'database') {
      cacheInfo.value.database = { size: 0, entries: 0 }
    } else if (type === 'static') {
      cacheInfo.value.static = { size: 0, files: 0 }
    }
    
    const log: MaintenanceLog = {
      id: Date.now().toString(),
      message: `ล้าง ${type} cache เสร็จสิ้น`,
      type: 'success',
      timestamp: new Date().toISOString()
    }
    maintenanceLogs.value.unshift(log)
    
    toast.add({
      title: 'ล้าง Cache สำเร็จ',
      description: `ล้าง ${type} cache เสร็จสิ้น`,
      icon: 'i-heroicons-check-circle',
      color: 'success'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถล้าง cache ได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  } finally {
    clearingCache.value[type as keyof typeof clearingCache.value] = false
  }
}

const clearAllCache = async () => {
  clearingCache.value.all = true
  
  try {
    // Simulate clearing all cache
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Reset all cache info
    cacheInfo.value = {
      application: { size: 0, files: 0 },
      database: { size: 0, entries: 0 },
      static: { size: 0, files: 0 }
    }
    
    const log: MaintenanceLog = {
      id: Date.now().toString(),
      message: 'ล้าง cache ทั้งหมดเสร็จสิ้น',
      type: 'success',
      timestamp: new Date().toISOString()
    }
    maintenanceLogs.value.unshift(log)
    
    toast.add({
      title: 'ล้าง Cache ทั้งหมดสำเร็จ',
      description: 'ล้าง cache ทั้งหมดเสร็จสิ้น',
      icon: 'i-heroicons-check-circle',
      color: 'success'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถล้าง cache ทั้งหมดได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  } finally {
    clearingCache.value.all = false
  }
}

const optimizeDatabase = async () => {
  optimizingDatabase.value = true
  
  try {
    // Simulate database optimization
    await new Promise(resolve => setTimeout(resolve, 4000))
    
    const log: MaintenanceLog = {
      id: Date.now().toString(),
      message: 'เพิ่มประสิทธิภาพฐานข้อมูลเสร็จสิ้น',
      type: 'success',
      timestamp: new Date().toISOString()
    }
    maintenanceLogs.value.unshift(log)
    
    toast.add({
      title: 'เพิ่มประสิทธิภาพสำเร็จ',
      description: 'เพิ่มประสิทธิภาพฐานข้อมูลเสร็จสิ้น',
      icon: 'i-heroicons-check-circle',
        color: 'success'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถเพิ่มประสิทธิภาพฐานข้อมูลได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  } finally {
    optimizingDatabase.value = false
  }
}

const repairDatabase = async () => {
  repairingDatabase.value = true
  
  try {
    // Simulate database repair
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    const log: MaintenanceLog = {
      id: Date.now().toString(),
      message: 'ซ่อมแซมฐานข้อมูลเสร็จสิ้น',
      type: 'success',
      timestamp: new Date().toISOString()
    }
    maintenanceLogs.value.unshift(log)
    
    toast.add({
      title: 'ซ่อมแซมสำเร็จ',
      description: 'ซ่อมแซมฐานข้อมูลเสร็จสิ้น',
      icon: 'i-heroicons-check-circle',
      color: 'success'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถซ่อมแซมฐานข้อมูลได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  } finally {
    repairingDatabase.value = false
  }
}

const analyzeDatabase = async () => {
  analyzingDatabase.value = true
  
  try {
    // Simulate database analysis
    await new Promise(resolve => setTimeout(resolve, 2500))
    
    const log: MaintenanceLog = {
      id: Date.now().toString(),
      message: 'วิเคราะห์ฐานข้อมูลเสร็จสิ้น',
      type: 'success',
      timestamp: new Date().toISOString()
    }
    maintenanceLogs.value.unshift(log)
    
    toast.add({
      title: 'วิเคราะห์สำเร็จ',
      description: 'วิเคราะห์ฐานข้อมูลเสร็จสิ้น',
      icon: 'i-heroicons-check-circle',
      color: 'success'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถวิเคราะห์ฐานข้อมูลได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  } finally {
    analyzingDatabase.value = false
  }
}

// Utility functions
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDateTime = (dateString: string): string => {
  return new Date(dateString).toLocaleString('th-TH')
}

const getStatusLabel = (status: string): string => {
  const labels = {
    healthy: 'ปกติ',
    warning: 'คำเตือน',
    error: 'ข้อผิดพลาด'
  }
  return labels[status as keyof typeof labels] || status
}

const getTaskStatusLabel = (status: string): string => {
  const labels = {
    pending: 'รอดำเนินการ',
    running: 'กำลังรัน',
    completed: 'เสร็จสิ้น'
  }
  return labels[status as keyof typeof labels] || status
}

// Lifecycle
onMounted(() => {
  // Start auto refresh
  refreshInterval = setInterval(refreshSystemStatus, 30000) // Every 30 seconds
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})

// Page meta
definePageMeta({
  layout: 'dashboard',
  middleware: 'auth'
})
</script> 
<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">การบำรุงรักษาระบบ</h1>
        <p class="text-gray-600 dark:text-gray-400">จัดการการบำรุงรักษาและตรวจสอบสุขภาพของระบบ</p>
      </div>
      <div class="flex items-center gap-3">
        <UButton
          variant="outline"
          icon="i-heroicons-arrow-path"
          :loading="isRefreshing"
          @click="refreshSystemStatus"
        >
          รีเฟรช
        </UButton>
        <UButton
          :variant="maintenanceMode ? 'solid' : 'outline'"
          :color="maintenanceMode ? 'error' : 'neutral'"
          :icon="maintenanceMode ? 'i-heroicons-pause' : 'i-heroicons-play'"
          @click="toggleMaintenanceMode"
        >
          {{ maintenanceMode ? 'ปิด' : 'เปิด' }} Maintenance Mode
        </UButton>
      </div>
    </div>

    <!-- Maintenance Mode Alert -->
    <UAlert
      v-if="maintenanceMode"
      color="error"
      variant="soft"
      icon="i-heroicons-exclamation-triangle"
      title="โหมดบำรุงรักษา"
      description="เว็บไซต์อยู่ในโหมดบำรุงรักษา ผู้เยี่ยมชมจะเห็นหน้าแจ้งเตือน"
    />

    <!-- System Status -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <Icon name="i-heroicons-cpu-chip" class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ systemStatus.cpu }}%</div>
            <div class="text-sm text-gray-500">CPU Usage</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Icon name="i-heroicons-server" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ systemStatus.memory }}%</div>
            <div class="text-sm text-gray-500">Memory Usage</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
            <Icon name="i-heroicons-circle-stack" class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ systemStatus.disk }}%</div>
            <div class="text-sm text-gray-500">Disk Usage</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Icon name="i-heroicons-clock" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ systemStatus.uptime }}</div>
            <div class="text-sm text-gray-500">Uptime</div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- System Health -->
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">สุขภาพระบบ</h3>
      </template>

      <div class="space-y-4">
        <div
          v-for="service in systemServices"
          :key="service.name"
          class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          <div class="flex items-center gap-3">
            <div
              :class="[
                'w-3 h-3 rounded-full',
                service.status === 'healthy' ? 'bg-green-500' :
                service.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
              ]"
            />
            <div>
              <div class="font-medium text-gray-900 dark:text-white">{{ service.name }}</div>
              <div class="text-sm text-gray-500">{{ service.description }}</div>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <UBadge
                :color="service.status === 'healthy' ? 'success' : service.status === 'warning' ? 'warning' : 'error'"
              :label="getStatusLabel(service.status)"
            />
            <span class="text-sm text-gray-500">{{ service.responseTime }}ms</span>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Maintenance Tasks -->
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">งานบำรุงรักษา</h3>
      </template>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div
          v-for="task in maintenanceTasks"
          :key="task.id"
          class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
        >
          <div class="flex items-center justify-between mb-3">
            <h4 class="font-medium text-gray-900 dark:text-white">{{ task.title }}</h4>
            <UBadge
              :color="task.status === 'completed' ? 'success' : task.status === 'running' ? 'info' : 'neutral'"
              :label="getTaskStatusLabel(task.status)"
            />
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ task.description }}</p>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-500">
              ล่าสุด: {{ task.lastRun ? formatDateTime(task.lastRun) : 'ยังไม่เคยรัน' }}
            </span>
            <UButton
              size="sm"
              :variant="task.status === 'running' ? 'solid' : 'outline'"
              :loading="task.status === 'running'"
              :disabled="task.status === 'running'"
              @click="runMaintenanceTask(task)"
            >
              {{ task.status === 'running' ? 'กำลังรัน' : 'เรียกใช้' }}
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Cache Management -->
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">จัดการ Cache</h3>
      </template>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="flex items-center justify-between mb-2">
            <h4 class="font-medium text-gray-900 dark:text-white">Application Cache</h4>
            <UBadge :label="formatFileSize(cacheInfo.application.size)" />
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
            {{ cacheInfo.application.files }} ไฟล์
          </p>
          <UButton
            size="sm"
            variant="outline"
            :loading="clearingCache.application"
            @click="clearCache('application')"
          >
            ล้าง Cache
          </UButton>
        </div>

        <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="flex items-center justify-between mb-2">
            <h4 class="font-medium text-gray-900 dark:text-white">Database Cache</h4>
            <UBadge :label="formatFileSize(cacheInfo.database.size)" />
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
            {{ cacheInfo.database.entries }} รายการ
          </p>
          <UButton
            size="sm"
            variant="outline"
            :loading="clearingCache.database"
            @click="clearCache('database')"
          >
            ล้าง Cache
          </UButton>
        </div>

        <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="flex items-center justify-between mb-2">
            <h4 class="font-medium text-gray-900 dark:text-white">Static Files</h4>
            <UBadge :label="formatFileSize(cacheInfo.static.size)" />
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
            {{ cacheInfo.static.files }} ไฟล์
          </p>
          <UButton
            size="sm"
            variant="outline"
            :loading="clearingCache.static"
            @click="clearCache('static')"
          >
            ล้าง Cache
          </UButton>
        </div>
      </div>

      <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <UButton
          color="error"
          variant="outline"
          :loading="clearingCache.all"
          @click="clearAllCache"
        >
          ล้าง Cache ทั้งหมด
        </UButton>
      </div>
    </UCard>

    <!-- Database Maintenance -->
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">บำรุงรักษาฐานข้อมูล</h3>
      </template>

      <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h4 class="font-medium text-gray-900 dark:text-white mb-2">ขนาดฐานข้อมูล</h4>
            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {{ formatFileSize(databaseInfo.size) }}
            </div>
            <p class="text-sm text-gray-500">{{ databaseInfo.tables }} ตาราง</p>
          </div>

          <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h4 class="font-medium text-gray-900 dark:text-white mb-2">การเชื่อมต่อ</h4>
            <div class="text-2xl font-bold text-green-600 dark:text-green-400">
              {{ databaseInfo.connections }}
            </div>
            <p class="text-sm text-gray-500">การเชื่อมต่อที่ใช้งาน</p>
          </div>
        </div>

        <div class="flex items-center gap-3">
          <UButton
            variant="outline"
            :loading="optimizingDatabase"
            @click="optimizeDatabase"
          >
            เพิ่มประสิทธิภาพฐานข้อมูล
          </UButton>
          <UButton
            variant="outline"
            :loading="repairingDatabase"
            @click="repairDatabase"
          >
            ซ่อมแซมฐานข้อมูล
          </UButton>
          <UButton
            variant="outline"
            :loading="analyzingDatabase"
            @click="analyzeDatabase"
          >
            วิเคราะห์ฐานข้อมูล
          </UButton>
        </div>
      </div>
    </UCard>

    <!-- Logs -->
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">บันทึกการบำรุงรักษา</h3>
      </template>

      <div class="space-y-2 max-h-96 overflow-y-auto">
        <div
          v-for="log in maintenanceLogs"
          :key="log.id"
          class="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          <Icon
            :name="log.type === 'success' ? 'i-heroicons-check-circle' : 
                   log.type === 'error' ? 'i-heroicons-x-circle' : 
                   'i-heroicons-information-circle'"
            :class="log.type === 'success' ? 'text-green-500' : 
                    log.type === 'error' ? 'text-red-500' : 
                    'text-blue-500'"
            class="w-5 h-5"
          />
          <div class="flex-1">
            <div class="font-medium text-gray-900 dark:text-white">{{ log.message }}</div>
            <div class="text-sm text-gray-500">{{ formatDateTime(log.timestamp) }}</div>
          </div>
        </div>
      </div>
    </UCard>
  </div>
</template>
