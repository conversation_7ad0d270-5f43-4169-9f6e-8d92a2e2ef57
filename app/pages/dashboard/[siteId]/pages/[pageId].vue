<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);
const pageId = computed(() => route.params.pageId as string);

// Use pages composable
const { getPage, updatePage, deletePage, getPageAnalytics } = usePages();

// State
const page = ref<any>(null);
const analytics = ref<any>({});
const loading = ref(false);
const deleting = ref(false);
const showDeleteModal = ref(false);

// Load page data
const loadPage = async () => {
  loading.value = true;
  try {
    const response = await getPage(siteId.value, pageId.value);
    page.value = (response as any)?.data;
  } catch (error: any) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: error.message || 'ไม่สามารถโหลดข้อมูลเพจได้',
      color: 'error',
    });
    // Redirect to pages list if page not found
    navigateTo(`/dashboard/${siteId.value}/pages`);
  } finally {
    loading.value = false;
  }
};

// Load analytics
const loadAnalytics = async () => {
  try {
    const response = await getPageAnalytics(siteId.value, pageId.value);
    analytics.value = (response as any)?.data || {};
  } catch (error: any) {
    console.error('Error loading analytics:', error);
  }
};

// Load data on mount
onMounted(async () => {
  await Promise.all([loadPage(), loadAnalytics()]);
});

// Watch for route changes
watch(
  () => pageId.value,
  async newPageId => {
    if (newPageId) {
      await Promise.all([loadPage(), loadAnalytics()]);
    }
  }
);

// Toggle page status
const handleToggleStatus = async () => {
  if (!page.value) return;

  try {
    const newStatus = page.value.status === 'published' ? 'draft' : 'published';
    await updatePage(siteId.value, pageId.value, { status: newStatus } as any);
    page.value.status = newStatus;

    useToast().add({
      title: 'อัพเดตสถานะสำเร็จ',
      description: `เปลี่ยนสถานะเป็น ${newStatus === 'published' ? 'เผยแพร่' : 'แบบร่าง'} แล้ว`,
      color: 'success',
    });
  } catch (error: any) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: error.message || 'ไม่สามารถอัพเดตสถานะได้',
      color: 'error',
    });
  }
};

// Handle delete
const handleDelete = async () => {
  if (!page.value) return;

  deleting.value = true;
  try {
    await deletePage(siteId.value, pageId.value);

    useToast().add({
      title: 'ลบเพจสำเร็จ',
      description: `ลบเพจ "${page.value.title}" เรียบร้อยแล้ว`,
      color: 'success',
    });

    // Navigate back to pages list
    navigateTo(`/dashboard/${siteId.value}/pages`);
  } catch (error: any) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: error.message || 'ไม่สามารถลบเพจได้',
      color: 'error',
    });
  } finally {
    deleting.value = false;
    showDeleteModal.value = false;
  }
};

// Utility functions
const formatDateTime = (dateString: string): string => {
  return new Date(dateString).toLocaleString('th-TH');
};

const getStatusColor = (status: string): 'success' | 'warning' | 'error' | 'neutral' => {
  switch (status) {
    case 'published':
      return 'success';
    case 'draft':
      return 'warning';
    case 'archived':
      return 'neutral';
    default:
      return 'neutral';
  }
};

const getStatusText = (status: string): string => {
  switch (status) {
    case 'published':
      return 'เผยแพร่';
    case 'draft':
      return 'แบบร่าง';
    case 'archived':
      return 'เก็บถาวร';
    default:
      return status;
  }
};

// SEO
useSeoMeta({
  title: computed(() => (page.value ? `${page.value.title} - รายละเอียดเพจ` : 'รายละเอียดเพจ')),
  description: computed(() => page.value?.excerpt || 'ดูรายละเอียดเพจและจัดการเนื้อหา'),
  keywords: 'เพจ, รายละเอียด, จัดการเนื้อหา, CMS',
  ogTitle: computed(() => (page.value ? `${page.value.title} - รายละเอียดเพจ` : 'รายละเอียดเพจ')),
  ogDescription: computed(() => page.value?.excerpt || 'ดูรายละเอียดเพจและจัดการเนื้อหา'),
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div class="space-y-6">
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center py-12">
      <Icon name="i-heroicons-arrow-path" class="w-8 h-8 animate-spin text-primary" />
    </div>

    <!-- Page Content -->
    <template v-else-if="page">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <UButton
            @click="$router.back()"
            variant="ghost"
            icon="i-heroicons-arrow-left"
            size="sm"
          />
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ page.title }}
            </h1>
            <p class="text-gray-600 dark:text-gray-400">
              รายละเอียดเพจและการจัดการ
            </p>
          </div>
        </div>
        
        <div class="flex items-center gap-3">
          <!-- Status Badge -->
          <UBadge
            :color="getStatusColor(page.status)"
            :label="getStatusText(page.status)"
            size="lg"
          />
          
          <!-- Actions -->
          <UButton
            @click="handleToggleStatus"
            :variant="page.status === 'published' ? 'outline' : 'solid'"
            :color="page.status === 'published' ? 'warning' : 'success'"
            :icon="page.status === 'published' ? 'i-heroicons-eye-slash' : 'i-heroicons-eye'"
          >
            {{ page.status === 'published' ? 'ยกเลิกเผยแพร่' : 'เผยแพร่' }}
          </UButton>
          
          <UButton
            :to="`/dashboard/${siteId}/pages/${pageId}/edit`"
            icon="i-heroicons-pencil-square"
          >
            แก้ไข
          </UButton>
          
          <UDropdownMenu
            :items="[
              [
                {
                  label: 'ดูเพจ',
                  icon: 'i-heroicons-eye',
                  click: () => (window as any).open(page.url, '_blank')
                },
                {
                  label: 'คัดลอกลิงก์',
                  icon: 'i-heroicons-link',
                  click: () => navigator.clipboard.writeText(page.url)
                }
              ],
              [
                {
                  label: 'ลบเพจ',
                  icon: 'i-heroicons-trash',
                  click: () => showDeleteModal = true
                }
              ]
            ]"
          >
            <UButton variant="ghost" icon="i-heroicons-ellipsis-vertical" />
          </UDropdownMenu>
        </div>
      </div>

      <!-- Analytics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <UCard>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Icon name="i-heroicons-eye" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <div class="text-2xl font-bold text-gray-900 dark:text-white">
                {{ analytics.views || 0 }}
              </div>
              <div class="text-sm text-gray-500">การดู</div>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <Icon name="i-heroicons-users" class="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <div class="text-2xl font-bold text-gray-900 dark:text-white">
                {{ analytics.uniqueVisitors || 0 }}
              </div>
              <div class="text-sm text-gray-500">ผู้เยี่ยมชม</div>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
              <Icon name="i-heroicons-clock" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <div class="text-2xl font-bold text-gray-900 dark:text-white">
                {{ analytics.avgTimeOnPage || '0:00' }}
              </div>
              <div class="text-sm text-gray-500">เวลาเฉลี่ย</div>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
              <Icon name="i-heroicons-arrow-trending-up" class="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <div class="text-2xl font-bold text-gray-900 dark:text-white">
                {{ analytics.bounceRate || '0%' }}
              </div>
              <div class="text-sm text-gray-500">อัตราการออก</div>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Page Details -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Content Preview -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">เนื้อหา</h3>
            </template>
            
            <div class="prose prose-gray dark:prose-invert max-w-none">
              <div v-if="page.excerpt" class="text-lg text-gray-600 dark:text-gray-400 mb-4">
                {{ page.excerpt }}
              </div>
              <div v-html="page.content" class="break-words"></div>
            </div>
          </UCard>

          <!-- SEO Information -->
          <UCard v-if="page.seo">
            <template #header>
              <div class="flex items-center gap-2">
                <Icon name="i-heroicons-magnifying-glass" class="w-5 h-5" />
                <h3 class="text-lg font-semibold">SEO</h3>
              </div>
            </template>
            
            <div class="space-y-4">
              <div v-if="page.seo.title">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Meta Title
                </label>
                <p class="text-gray-900 dark:text-white">{{ page.seo.title }}</p>
              </div>
              
              <div v-if="page.seo.description">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Meta Description
                </label>
                <p class="text-gray-900 dark:text-white">{{ page.seo.description }}</p>
              </div>
              
              <div v-if="page.seo.keywords?.length">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Keywords
                </label>
                <div class="flex flex-wrap gap-2">
                  <UBadge
                    v-for="keyword in page.seo.keywords"
                    :key="keyword"
                    :label="keyword"
                    variant="soft"
                  />
                </div>
              </div>
            </div>
          </UCard>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Page Info -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">ข้อมูลเพจ</h3>
            </template>
            
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  URL
                </label>
                <div class="flex items-center gap-2">
                  <code class="flex-1 text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                    {{ page.slug }}
                  </code>
                  <UButton
                    @click="navigator.clipboard.writeText(page.url)"
                    variant="ghost"
                    size="sm"
                    icon="i-heroicons-clipboard"
                  />
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  สถานะ
                </label>
                <UBadge
                  :color="getStatusColor(page.status)"
                  :label="getStatusText(page.status)"
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  ผู้เขียน
                </label>
                <div class="flex items-center gap-2">
                  <UAvatar size="xs" :src="page.author?.avatar" />
                  <span class="text-gray-900 dark:text-white">
                    {{ page.author?.name || 'ไม่ระบุ' }}
                  </span>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  วันที่สร้าง
                </label>
                <p class="text-gray-900 dark:text-white">
                  {{ formatDateTime(page.createdAt) }}
                </p>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  อัพเดตล่าสุด
                </label>
                <p class="text-gray-900 dark:text-white">
                  {{ formatDateTime(page.updatedAt) }}
                </p>
              </div>
            </div>
          </UCard>

          <!-- Categories & Tags -->
          <UCard v-if="page.categories?.length || page.tags?.length">
            <template #header>
              <h3 class="text-lg font-semibold">หมวดหมู่และแท็ก</h3>
            </template>
            
            <div class="space-y-4">
              <div v-if="page.categories?.length">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  หมวดหมู่
                </label>
                <div class="flex flex-wrap gap-2">
                  <UBadge
                    v-for="category in page.categories"
                    :key="category.id"
                    :label="category.name"
                    color="primary"
                    variant="soft"
                  />
                </div>
              </div>
              
              <div v-if="page.tags?.length">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  แท็ก
                </label>
                <div class="flex flex-wrap gap-2">
                  <UBadge
                    v-for="tag in page.tags"
                    :key="tag.id"
                    :label="tag.name"
                    variant="outline"
                  />
                </div>
              </div>
            </div>
          </UCard>

          <!-- Featured Image -->
          <UCard v-if="page.featuredImage">
            <template #header>
              <h3 class="text-lg font-semibold">รูปภาพหลัก</h3>
            </template>
            
            <div class="aspect-video rounded-lg overflow-hidden">
              <img
                :src="page.featuredImage"
                :alt="page.title"
                class="w-full h-full object-cover"
              />
            </div>
          </UCard>
        </div>
      </div>
    </template>

    <!-- Delete Confirmation Modal -->
    <UModal 
      v-model:open="showDeleteModal"
      title="ยืนยันการลบ"
    >
      <template #header>
        <div class="flex items-center gap-2">
          <Icon name="i-heroicons-exclamation-triangle" class="w-5 h-5 text-red-500" />
          <span>ยืนยันการลบ</span>
        </div>
      </template>

      <template #body>
        <div class="space-y-4">
          <p class="text-gray-600 dark:text-gray-400">
            คุณต้องการลบเพจ <strong>"{{ page?.title }}"</strong> ใช่หรือไม่?
          </p>
          <p class="text-sm text-red-600 dark:text-red-400">
            การดำเนินการนี้ไม่สามารถยกเลิกได้
          </p>
        </div>
      </template>

      <template #footer>
        <div class="flex justify-end gap-3">
          <UButton
            @click="showDeleteModal = false"
            variant="outline"
            :disabled="deleting"
          >
            ยกเลิก
          </UButton>
          <UButton
            @click="handleDelete"
            color="error"
            :loading="deleting"
          >
            ลบเพจ
          </UButton>
        </div>
      </template>
    </UModal>
  </div>
</template> 