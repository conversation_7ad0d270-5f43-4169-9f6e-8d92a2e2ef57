<script setup lang="ts">
// Types
interface Page {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  status: 'draft' | 'published' | 'archived';
  type: 'page' | 'post' | 'landing' | 'custom';
  metaTitle: string;
  metaDescription: string;
  featuredImage: string;
  seoKeywords: string[];
  isHomepage: boolean;
  allowComments: boolean;
  passwordProtected: boolean;
  views: number;
  createdAt: string;
  updatedAt: string;
  author: string;
  tags: string[];
  comments: Comment[];
}

interface Comment {
  id: string;
  author: string;
  content: string;
  createdAt: string;
  status: 'approved' | 'pending' | 'spam';
}

// Route and utilities
const route = useRoute();
const toast = useToast();

// Get siteId and pageId from route
const siteId = computed(() => route.params.siteId as string);
const pageId = computed(() => route.params.id as string);

// Page data
const page = ref<Page | null>(null);
const loading = ref(true);
const showEditModal = ref(false);
const showDeleteModal = ref(false);
const isDeleting = ref(false);

// Mock page data
const mockPageData: Page = {
  id: '1',
  title: 'หน้าแรก',
  slug: 'home',
  content: 'เนื้อหาของหน้าแรกของเว็บไซต์\n\nนี่คือตัวอย่างเนื้อหาที่จะแสดงในหน้าเพจ\n\nคุณสามารถเพิ่มเนื้อหาได้ที่นี่',
  excerpt: 'หน้าแรกของเว็บไซต์ที่มีข้อมูลสำคัญและลิงก์ไปยังหน้าต่างๆ',
  status: 'published',
  type: 'page',
  metaTitle: 'หน้าแรก - เว็บไซต์ของเรา',
  metaDescription: 'หน้าแรกของเว็บไซต์ที่มีข้อมูลสำคัญและลิงก์ไปยังหน้าต่างๆ',
  featuredImage: 'https://via.placeholder.com/800x400',
  seoKeywords: ['หน้าแรก', 'เว็บไซต์', 'ข้อมูล'],
  isHomepage: true,
  allowComments: false,
  passwordProtected: false,
  views: 3240,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-15T10:30:00Z',
  author: 'ผู้ดูแลระบบ',
  tags: ['หน้าแรก', 'สำคัญ'],
  comments: [],
};

// Methods
const loadPage = async () => {
  loading.value = true;
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Load mock data based on pageId
    if (pageId.value === '1') {
      page.value = { ...mockPageData };
    } else {
      // For other IDs, create a modified version
      page.value = {
        ...mockPageData,
        id: pageId.value,
        title: `หน้าเพจ ${pageId.value}`,
        slug: `page-${pageId.value}`,
        status: 'draft',
      };
    }
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถโหลดข้อมูลหน้าเพจได้',
      icon: 'i-heroicons-x-circle',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

const editPage = () => {
  navigateTo(`/dashboard/${siteId.value}/pages/${pageId.value}/edit`);
};

const deletePage = async () => {
  isDeleting.value = true;
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    toast.add({
      title: 'ลบสำเร็จ',
      description: 'หน้าเพจถูกลบเรียบร้อยแล้ว',
      icon: 'i-heroicons-check-circle',
      color: 'success',
    });

    // Navigate back to pages list
    navigateTo(`/dashboard/${siteId.value}/pages`);
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถลบหน้าเพจได้',
      icon: 'i-heroicons-x-circle',
      color: 'error',
    });
  } finally {
    isDeleting.value = false;
    showDeleteModal.value = false;
  }
};

const viewPage = () => {
  if (page.value) {
    window.open(`/${page.value.slug}`, '_blank');
  }
};

const duplicatePage = async () => {
  if (!page.value) return;

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    toast.add({
      title: 'คัดลอกสำเร็จ',
      description: `คัดลอกหน้า ${page.value.title} เรียบร้อยแล้ว`,
      icon: 'i-heroicons-check-circle',
      color: 'success',
    });
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถคัดลอกหน้าเพจได้',
      icon: 'i-heroicons-x-circle',
      color: 'error',
    });
  }
};

const getStatusColor = (status: string) => {
  const colors = {
    published: 'success',
    draft: 'warning',
    archived: 'neutral',
  };
  return colors[status as keyof typeof colors] || 'neutral';
};

const getStatusLabel = (status: string) => {
  const labels = {
    published: 'เผยแพร่',
    draft: 'ร่าง',
    archived: 'เก็บถาวร',
  };
  return labels[status as keyof typeof labels] || status;
};

const getTypeLabel = (type: string) => {
  const labels = {
    page: 'หน้าเพจ',
    post: 'บทความ',
    landing: 'หน้า Landing',
    custom: 'หน้าแบบกำหนดเอง',
  };
  return labels[type as keyof typeof labels] || type;
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('th-TH');
};

// Load page data on mount
onMounted(() => {
  loadPage();
});

// Page meta
definePageMeta({
  layout: 'dashboard',
  middleware: 'auth',
});

// SEO
useSeoMeta({
  title: 'รายละเอียดหน้าเพจ - ระบบจัดการร้านค้า',
  description: 'ดูรายละเอียดหน้าเพจ สถิติ และการจัดการหน้าเพจ',
  keywords: 'รายละเอียดหน้าเพจ, สถิติหน้าเพจ, จัดการหน้าเพจ',
  ogTitle: 'รายละเอียดหน้าเพจ - ระบบจัดการร้านค้า',
  ogDescription: 'ดูรายละเอียดหน้าเพจ สถิติ และการจัดการหน้าเพจ',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div v-if="loading" class="flex items-center justify-center h-64">
    <UIcon name="i-heroicons-arrow-path" class="w-8 h-8 animate-spin" />
  </div>

  <div v-else-if="page" class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ page.title }}</h1>
        <p class="text-gray-600 dark:text-gray-400">
          รายละเอียดหน้าเพจ
        </p>
      </div>
      <div class="flex items-center gap-3">
        <UButton
          variant="outline"
          icon="i-heroicons-eye"
          @click="viewPage"
        >
          ดูหน้าเพจ
        </UButton>
        <UButton
          variant="outline"
          icon="i-heroicons-document-duplicate"
          @click="duplicatePage"
        >
          คัดลอก
        </UButton>
        <UButton
          icon="i-heroicons-pencil-square"
          @click="editPage"
        >
          แก้ไข
        </UButton>
      </div>
    </div>

    <!-- Page Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Icon name="i-heroicons-eye" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ page.views }}</div>
            <div class="text-sm text-gray-500">จำนวนผู้เข้าชม</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <Icon name="i-heroicons-check-circle" class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">
              <UBadge
                :color="getStatusColor(page.status)"
                :label="getStatusLabel(page.status)"
              />
            </div>
            <div class="text-sm text-gray-500">สถานะ</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
            <Icon name="i-heroicons-calendar" class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ formatDate(page.createdAt) }}
            </div>
            <div class="text-sm text-gray-500">วันที่สร้าง</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Icon name="i-heroicons-user" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ page.author }}</div>
            <div class="text-sm text-gray-500">ผู้เขียน</div>
          </div>
        </div>
      </UCard>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Main Content -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Page Content -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">เนื้อหา</h3>
          </template>

          <div class="space-y-4">
            <div v-if="page.excerpt" class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <h4 class="font-medium text-gray-900 dark:text-white mb-2">บทคัดย่อ</h4>
              <p class="text-gray-600 dark:text-gray-400">{{ page.excerpt }}</p>
            </div>

            <div class="prose dark:prose-invert max-w-none">
              <div class="whitespace-pre-wrap">{{ page.content }}</div>
            </div>
          </div>
        </UCard>

        <!-- SEO Information -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">ข้อมูล SEO</h3>
          </template>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Meta Title
              </label>
              <p class="text-sm text-gray-900 dark:text-white">{{ page.metaTitle }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Meta Description
              </label>
              <p class="text-sm text-gray-900 dark:text-white">{{ page.metaDescription }}</p>
            </div>

            <div v-if="page.seoKeywords.length > 0">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                คำค้นหา
              </label>
              <div class="flex flex-wrap gap-2">
                <UBadge
                  v-for="keyword in page.seoKeywords"
                  :key="keyword"
                  :label="keyword"
                  color="info"
                />
              </div>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Page Information -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">ข้อมูลหน้าเพจ</h3>
          </template>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                URL Slug
              </label>
              <p class="text-sm text-gray-900 dark:text-white font-mono">{{ page.slug }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                ประเภท
              </label>
              <p class="text-sm text-gray-900 dark:text-white">{{ getTypeLabel(page.type) }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                วันที่แก้ไขล่าสุด
              </label>
              <p class="text-sm text-gray-900 dark:text-white">{{ formatDate(page.updatedAt) }}</p>
            </div>

            <div v-if="page.tags.length > 0">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                แท็ก
              </label>
              <div class="flex flex-wrap gap-2">
                <UBadge
                  v-for="tag in page.tags"
                  :key="tag"
                  :label="tag"
                  color="primary"
                />
              </div>
            </div>
          </div>
        </UCard>

        <!-- Page Settings -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">การตั้งค่า</h3>
          </template>

          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-700 dark:text-gray-300">หน้าแรก</span>
              <UBadge
                :color="page.isHomepage ? 'success' : 'neutral'"
                :label="page.isHomepage ? 'ใช่' : 'ไม่'"
              />
            </div>

            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-700 dark:text-gray-300">อนุญาตความคิดเห็น</span>
              <UBadge
                :color="page.allowComments ? 'success' : 'neutral'"
                :label="page.allowComments ? 'ใช่' : 'ไม่'"
              />
            </div>

            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-700 dark:text-gray-300">ป้องกันรหัสผ่าน</span>
              <UBadge
                :color="page.passwordProtected ? 'warning' : 'neutral'"
                :label="page.passwordProtected ? 'ใช่' : 'ไม่'"
              />
            </div>
          </div>
        </UCard>

        <!-- Featured Image -->
        <UCard v-if="page.featuredImage">
          <template #header>
            <h3 class="text-lg font-semibold">รูปภาพหลัก</h3>
          </template>

          <div>
            <img
              :src="page.featuredImage"
              :alt="page.title"
              class="w-full h-32 object-cover rounded-lg"
            />
          </div>
        </UCard>

        <!-- Danger Zone -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold text-red-600">เขตอันตราย</h3>
          </template>

          <div class="space-y-4">
            <UButton
              variant="outline"
              color="error"
              icon="i-heroicons-trash"
              class="w-full"
              @click="showDeleteModal = true"
            >
              ลบหน้าเพจ
            </UButton>
          </div>
        </UCard>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <UModal
      v-model:open="showDeleteModal"
      title="ยืนยันการลบหน้าเพจ"
      description="การดำเนินการนี้ไม่สามารถยกเลิกได้"
    >
      <template #body>
        <div class="space-y-4">
          <div class="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <div class="flex items-center gap-2">
              <Icon name="i-heroicons-exclamation-triangle" class="w-5 h-5 text-red-600" />
              <span class="text-red-800 dark:text-red-200 font-medium">
                คุณกำลังจะลบหน้าเพจ "{{ page.title }}"
              </span>
            </div>
            <p class="mt-2 text-sm text-red-700 dark:text-red-300">
              หน้าเพจนี้จะถูกลบอย่างถาวรและไม่สามารถกู้คืนได้
            </p>
          </div>
        </div>
      </template>

      <template #footer>
        <div class="flex justify-end gap-2">
          <UButton
            variant="outline"
            @click="showDeleteModal = false"
          >
            ยกเลิก
          </UButton>
          <UButton
            color="error"
            :loading="isDeleting"
            @click="deletePage"
          >
            ลบหน้าเพจ
          </UButton>
        </div>
      </template>
    </UModal>
  </div>

  <div v-else class="flex items-center justify-center h-64">
    <div class="text-center">
      <Icon name="i-heroicons-exclamation-triangle" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">ไม่พบหน้าเพจ</h3>
      <p class="text-gray-500 dark:text-gray-400">หน้าเพจที่คุณค้นหาอาจถูกลบหรือไม่เคยมีอยู่</p>
      <UButton
        variant="outline"
        class="mt-4"
        @click="navigateTo(`/dashboard/${siteId.value}/pages`)"
      >
        กลับไปหน้าจัดการหน้าเพจ
      </UButton>
    </div>
  </div>
</template> 