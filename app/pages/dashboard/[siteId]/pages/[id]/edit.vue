<script setup lang="ts">
// Types
interface PageForm {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  status: 'draft' | 'published' | 'archived';
  type: 'page' | 'post' | 'landing' | 'custom';
  metaTitle: string;
  metaDescription: string;
  featuredImage: string;
  seoKeywords: string[];
  isHomepage: boolean;
  allowComments: boolean;
  passwordProtected: boolean;
  password: string;
  publishDate: string;
  author: string;
  tags: string[];
  views: number;
  createdAt: string;
  updatedAt: string;
}

// Route and utilities
const route = useRoute();
const toast = useToast();

// Get siteId and pageId from route
const siteId = computed(() => route.params.siteId as string);
const pageId = computed(() => route.params.id as string);

// Form state
const form = ref<PageForm>({
  id: '',
  title: '',
  slug: '',
  content: '',
  excerpt: '',
  status: 'draft',
  type: 'page',
  metaTitle: '',
  metaDescription: '',
  featuredImage: '',
  seoKeywords: [],
  isHomepage: false,
  allowComments: false,
  passwordProtected: false,
  password: '',
  publishDate: '',
  author: 'ผู้ดูแลระบบ',
  tags: [],
  views: 0,
  createdAt: '',
  updatedAt: '',
});

// UI state
const loading = ref(true);
const isSaving = ref(false);
const isPublishing = ref(false);
const showPreview = ref(false);
const showSeoSettings = ref(false);
const showAdvancedSettings = ref(false);
const newTag = ref('');
const showDeleteModal = ref(false);

// Options
const statusOptions = [
  { label: 'ร่าง', value: 'draft' },
  { label: 'เผยแพร่', value: 'published' },
  { label: 'เก็บถาวร', value: 'archived' },
];

const typeOptions = [
  { label: 'หน้าเพจ', value: 'page' },
  { label: 'บทความ', value: 'post' },
  { label: 'หน้า Landing', value: 'landing' },
  { label: 'หน้าแบบกำหนดเอง', value: 'custom' },
];

// Mock page data
const mockPageData: PageForm = {
  id: '1',
  title: 'หน้าแรก',
  slug: 'home',
  content: 'เนื้อหาของหน้าแรกของเว็บไซต์\n\nนี่คือตัวอย่างเนื้อหาที่จะแสดงในหน้าเพจ',
  excerpt: 'หน้าแรกของเว็บไซต์',
  status: 'published',
  type: 'page',
  metaTitle: 'หน้าแรก - เว็บไซต์ของเรา',
  metaDescription: 'หน้าแรกของเว็บไซต์ที่มีข้อมูลสำคัญ',
  featuredImage: 'https://via.placeholder.com/800x400',
  seoKeywords: ['หน้าแรก', 'เว็บไซต์', 'ข้อมูล'],
  isHomepage: true,
  allowComments: false,
  passwordProtected: false,
  password: '',
  publishDate: '2024-01-01T00:00',
  author: 'ผู้ดูแลระบบ',
  tags: ['หน้าแรก', 'สำคัญ'],
  views: 3240,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-15T10:30:00Z',
};

// Methods
const loadPage = async () => {
  loading.value = true;
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Load mock data based on pageId
    if (pageId.value === '1') {
      form.value = { ...mockPageData };
    } else {
      // For other IDs, create a modified version
      form.value = {
        ...mockPageData,
        id: pageId.value,
        title: `หน้าเพจ ${pageId.value}`,
        slug: `page-${pageId.value}`,
        status: 'draft',
      };
    }
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถโหลดข้อมูลหน้าเพจได้',
      icon: 'i-heroicons-x-circle',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

const generateSlug = () => {
  if (form.value.title) {
    form.value.slug = form.value.title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }
};

const addTag = () => {
  if (newTag.value.trim() && !form.value.tags.includes(newTag.value.trim())) {
    form.value.tags.push(newTag.value.trim());
    newTag.value = '';
  }
};

const removeTag = (tag: string) => {
  const index = form.value.tags.indexOf(tag);
  if (index > -1) {
    form.value.tags.splice(index, 1);
  }
};

const addKeyword = (keyword: string) => {
  if (keyword.trim() && !form.value.seoKeywords.includes(keyword.trim())) {
    form.value.seoKeywords.push(keyword.trim());
  }
};

const removeKeyword = (keyword: string) => {
  const index = form.value.seoKeywords.indexOf(keyword);
  if (index > -1) {
    form.value.seoKeywords.splice(index, 1);
  }
};

const saveAsDraft = async () => {
  isSaving.value = true;
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    form.value.updatedAt = new Date().toISOString();

    toast.add({
      title: 'บันทึกสำเร็จ',
      description: 'บันทึกการแก้ไขหน้าเพจเรียบร้อยแล้ว',
      icon: 'i-heroicons-check-circle',
      color: 'success',
    });
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถบันทึกการแก้ไขได้',
      icon: 'i-heroicons-x-circle',
      color: 'error',
    });
  } finally {
    isSaving.value = false;
  }
};

const publishPage = async () => {
  isPublishing.value = true;
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));

    form.value.status = 'published';
    form.value.updatedAt = new Date().toISOString();

    toast.add({
      title: 'เผยแพร่สำเร็จ',
      description: 'หน้าเพจได้รับการเผยแพร่แล้ว',
      icon: 'i-heroicons-check-circle',
      color: 'success',
    });
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถเผยแพร่หน้าได้',
      icon: 'i-heroicons-x-circle',
      color: 'error',
    });
  } finally {
    isPublishing.value = false;
  }
};

const deletePage = async () => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    toast.add({
      title: 'ลบสำเร็จ',
      description: 'หน้าเพจถูกลบเรียบร้อยแล้ว',
      icon: 'i-heroicons-check-circle',
      color: 'success',
    });

    // Navigate back to pages list
    navigateTo(`/dashboard/${siteId.value}/pages`);
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถลบหน้าเพจได้',
      icon: 'i-heroicons-x-circle',
      color: 'error',
    });
  }
};

const previewPage = () => {
  showPreview.value = true;
};

const viewPage = () => {
  window.open(`/${form.value.slug}`, '_blank');
};

// Auto-generate meta title and description
watch(
  () => form.value.title,
  newTitle => {
    if (!form.value.metaTitle) {
      form.value.metaTitle = newTitle;
    }
  }
);

watch(
  () => form.value.excerpt,
  newExcerpt => {
    if (!form.value.metaDescription) {
      form.value.metaDescription = newExcerpt;
    }
  }
);

// Load page data on mount
onMounted(() => {
  loadPage();
});

// Page meta
definePageMeta({
  layout: 'dashboard',
  middleware: 'auth',
});

// SEO
useSeoMeta({
  title: 'แก้ไขหน้าเพจ - ระบบจัดการร้านค้า',
  description: 'แก้ไขหน้าเพจ จัดการเนื้อหา การตั้งค่า SEO และการเผยแพร่',
  keywords: 'แก้ไขหน้าเพจ, จัดการเนื้อหา, SEO, การเผยแพร่',
  ogTitle: 'แก้ไขหน้าเพจ - ระบบจัดการร้านค้า',
  ogDescription: 'แก้ไขหน้าเพจ จัดการเนื้อหา การตั้งค่า SEO และการเผยแพร่',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div v-if="loading" class="flex items-center justify-center h-64">
    <UIcon name="i-heroicons-arrow-path" class="w-8 h-8 animate-spin" />
  </div>

  <div v-else class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">แก้ไขหน้าเพจ</h1>
        <p class="text-gray-600 dark:text-gray-400">
          แก้ไขหน้าเพจ: {{ form.title }}
        </p>
      </div>
      <div class="flex items-center gap-3">
        <UButton
          variant="outline"
          icon="i-heroicons-eye"
          @click="viewPage"
        >
          ดูหน้าเพจ
        </UButton>
        <UButton
          variant="outline"
          icon="i-heroicons-eye"
          @click="previewPage"
        >
          ดูตัวอย่าง
        </UButton>
        <UButton
          variant="outline"
          :loading="isSaving"
          @click="saveAsDraft"
        >
          บันทึก
        </UButton>
        <UButton
          :loading="isPublishing"
          @click="publishPage"
        >
          เผยแพร่
        </UButton>
      </div>
    </div>

    <!-- Page Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Icon name="i-heroicons-eye" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ form.views }}</div>
            <div class="text-sm text-gray-500">จำนวนผู้เข้าชม</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <Icon name="i-heroicons-calendar" class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ new Date(form.createdAt).toLocaleDateString('th-TH') }}
            </div>
            <div class="text-sm text-gray-500">วันที่สร้าง</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
            <Icon name="i-heroicons-clock" class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ new Date(form.updatedAt).toLocaleDateString('th-TH') }}
            </div>
            <div class="text-sm text-gray-500">แก้ไขล่าสุด</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Icon name="i-heroicons-user" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ form.author }}</div>
            <div class="text-sm text-gray-500">ผู้เขียน</div>
          </div>
        </div>
      </UCard>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Main Content -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">ข้อมูลพื้นฐาน</h3>
          </template>

          <div class="space-y-4">
            <UFormField label="ชื่อหน้า" required>
              <UInput
                v-model="form.title"
                placeholder="ใส่ชื่อหน้าเพจ"
                @input="generateSlug"
              />
            </UFormField>

            <UFormField label="URL Slug" required>
              <UInput
                v-model="form.slug"
                placeholder="url-slug"
                icon="i-heroicons-link"
              />
            </UFormField>

            <UFormField label="ประเภทหน้า">
              <USelect
                v-model="form.type"
                :items="typeOptions"
                placeholder="เลือกประเภทหน้า"
              />
            </UFormField>

            <UFormField label="สถานะ">
              <USelect
                v-model="form.status"
                :items="statusOptions"
                placeholder="เลือกสถานะ"
              />
            </UFormField>

            <UFormField label="บทคัดย่อ">
              <UTextarea
                v-model="form.excerpt"
                placeholder="สรุปเนื้อหาของหน้าเพจ"
                rows="3"
              />
            </UFormField>
          </div>
        </UCard>

        <!-- Content Editor -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">เนื้อหา</h3>
          </template>

          <div class="space-y-4">
            <UFormField label="เนื้อหาหน้าเพจ" required>
              <UTextarea
                v-model="form.content"
                placeholder="เขียนเนื้อหาของหน้าเพจที่นี่..."
                rows="15"
                class="font-mono"
              />
            </UFormField>

            <div class="flex items-center gap-4">
              <UCheckbox
                v-model="form.isHomepage"
                label="ตั้งเป็นหน้าแรก"
              />
              <UCheckbox
                v-model="form.allowComments"
                label="อนุญาตให้แสดงความคิดเห็น"
              />
            </div>
          </div>
        </UCard>

        <!-- SEO Settings -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold">การตั้งค่า SEO</h3>
              <UButton
                variant="ghost"
                size="sm"
                @click="showSeoSettings = !showSeoSettings"
              >
                <Icon :name="showSeoSettings ? 'i-heroicons-chevron-up' : 'i-heroicons-chevron-down'" />
              </UButton>
            </div>
          </template>

          <div v-if="showSeoSettings" class="space-y-4">
            <UFormField label="Meta Title">
              <UInput
                v-model="form.metaTitle"
                placeholder="ชื่อหน้าในผลการค้นหา"
              />
            </UFormField>

            <UFormField label="Meta Description">
              <UTextarea
                v-model="form.metaDescription"
                placeholder="คำอธิบายหน้าในผลการค้นหา"
                rows="3"
              />
            </UFormField>

            <UFormField label="คำค้นหา">
              <div class="space-y-2">
                <div class="flex gap-2">
                  <UInput
                    v-model="newTag"
                    placeholder="เพิ่มคำค้นหา"
                    @keyup.enter="addKeyword"
                  />
                  <UButton
                    variant="outline"
                    size="sm"
                    @click="addKeyword(newTag)"
                  >
                    เพิ่ม
                  </UButton>
                </div>
                <div class="flex flex-wrap gap-2">
                  <UBadge
                    v-for="keyword in form.seoKeywords"
                    :key="keyword"
                    :label="keyword"
                    color="info"
                    class="cursor-pointer"
                    @click="removeKeyword(keyword)"
                  />
                </div>
              </div>
            </UFormField>
          </div>
        </UCard>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Publishing -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">การเผยแพร่</h3>
          </template>

          <div class="space-y-4">
            <UFormField label="วันที่เผยแพร่">
              <UInput
                v-model="form.publishDate"
                type="datetime-local"
              />
            </UFormField>

            <UFormField label="ผู้เขียน">
              <UInput
                v-model="form.author"
                placeholder="ชื่อผู้เขียน"
              />
            </UFormField>

            <UFormField label="แท็ก">
              <div class="space-y-2">
                <div class="flex gap-2">
                  <UInput
                    v-model="newTag"
                    placeholder="เพิ่มแท็ก"
                    @keyup.enter="addTag"
                  />
                  <UButton
                    variant="outline"
                    size="sm"
                    @click="addTag"
                  >
                    เพิ่ม
                  </UButton>
                </div>
                <div class="flex flex-wrap gap-2">
                  <UBadge
                    v-for="tag in form.tags"
                    :key="tag"
                    :label="tag"
                    color="primary"
                    class="cursor-pointer"
                    @click="removeTag(tag)"
                  />
                </div>
              </div>
            </UFormField>
          </div>
        </UCard>

        <!-- Featured Image -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">รูปภาพหลัก</h3>
          </template>

          <div class="space-y-4">
            <div v-if="form.featuredImage" class="relative">
              <img
                :src="form.featuredImage"
                alt="Featured image"
                class="w-full h-32 object-cover rounded-lg"
              />
              <UButton
                variant="ghost"
                size="sm"
                color="error"
                class="absolute top-2 right-2"
                @click="form.featuredImage = ''"
              >
                <Icon name="i-heroicons-x-mark" />
              </UButton>
            </div>
            <UButton
              v-else
              variant="outline"
              icon="i-heroicons-photo"
              class="w-full"
            >
              เลือกรูปภาพ
            </UButton>
          </div>
        </UCard>

        <!-- Advanced Settings -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold">การตั้งค่าขั้นสูง</h3>
              <UButton
                variant="ghost"
                size="sm"
                @click="showAdvancedSettings = !showAdvancedSettings"
              >
                <Icon :name="showAdvancedSettings ? 'i-heroicons-chevron-up' : 'i-heroicons-chevron-down'" />
              </UButton>
            </div>
          </template>

          <div v-if="showAdvancedSettings" class="space-y-4">
            <UCheckbox
              v-model="form.passwordProtected"
              label="ป้องกันด้วยรหัสผ่าน"
            />

            <UFormField
              v-if="form.passwordProtected"
              label="รหัสผ่าน"
            >
              <UInput
                v-model="form.password"
                type="password"
                placeholder="ใส่รหัสผ่าน"
              />
            </UFormField>
          </div>
        </UCard>

        <!-- Danger Zone -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold text-red-600">เขตอันตราย</h3>
          </template>

          <div class="space-y-4">
            <UButton
              variant="outline"
              color="error"
              icon="i-heroicons-trash"
              class="w-full"
              @click="showDeleteModal = true"
            >
              ลบหน้าเพจ
            </UButton>
          </div>
        </UCard>
      </div>
    </div>

    <!-- Preview Modal -->
    <UModal
      v-model:open="showPreview"
      title="ดูตัวอย่างหน้าเพจ"
      description="ตัวอย่างการแสดงผลหน้าเพจ"
    >
      <template #body>
        <div class="space-y-4">
          <div class="border-b pb-4">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ form.title }}
            </h2>
            <div class="flex items-center gap-4 mt-2 text-sm text-gray-500">
              <span>โดย {{ form.author }}</span>
              <span>{{ new Date().toLocaleDateString('th-TH') }}</span>
            </div>
          </div>
          
          <div class="prose dark:prose-invert max-w-none">
            <p v-if="form.excerpt" class="text-lg text-gray-600 dark:text-gray-400 italic">
              {{ form.excerpt }}
            </p>
            <div class="mt-4">
              {{ form.content }}
            </div>
          </div>
        </div>
      </template>

      <template #footer>
        <div class="flex justify-end gap-2">
          <UButton
            variant="outline"
            @click="showPreview = false"
          >
            ปิด
          </UButton>
          <UButton
            @click="publishPage"
          >
            เผยแพร่
          </UButton>
        </div>
      </template>
    </UModal>

    <!-- Delete Confirmation Modal -->
    <UModal
      v-model:open="showDeleteModal"
      title="ยืนยันการลบหน้าเพจ"
      description="การดำเนินการนี้ไม่สามารถยกเลิกได้"
    >
      <template #body>
        <div class="space-y-4">
          <div class="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <div class="flex items-center gap-2">
              <Icon name="i-heroicons-exclamation-triangle" class="w-5 h-5 text-red-600" />
              <span class="text-red-800 dark:text-red-200 font-medium">
                คุณกำลังจะลบหน้าเพจ "{{ form.title }}"
              </span>
            </div>
            <p class="mt-2 text-sm text-red-700 dark:text-red-300">
              หน้าเพจนี้จะถูกลบอย่างถาวรและไม่สามารถกู้คืนได้
            </p>
          </div>
        </div>
      </template>

      <template #footer>
        <div class="flex justify-end gap-2">
          <UButton
            variant="outline"
            @click="showDeleteModal = false"
          >
            ยกเลิก
          </UButton>
          <UButton
            color="error"
            @click="deletePage"
          >
            ลบหน้าเพจ
          </UButton>
        </div>
      </template>
    </UModal>
  </div>
</template> 