<script setup lang="ts">
import { useRoute } from 'vue-router';
import { z } from 'zod';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);
const pageId = computed(() => route.params.pageId as string);

// Use pages composable
const { getPage, updatePage, getCategories, getTags } = usePages();

// Form schema
const pageSchema = z.object({
  title: z.string().min(1, 'กรุณากรอกชื่อเพจ').max(200, 'ชื่อเพจต้องไม่เกิน 200 ตัวอักษร'),
  slug: z
    .string()
    .min(1, 'กรุณากรอก URL slug')
    .regex(/^[a-z0-9-]+$/, 'URL slug ต้องเป็นตัวอักษรเล็ก ตัวเลข และ - เท่านั้น'),
  excerpt: z.string().max(500, 'บทสรุปต้องไม่เกิน 500 ตัวอักษร').optional(),
  content: z.string().min(1, 'กรุณากรอกเนื้อหา'),
  status: z.enum(['draft', 'published', 'archived']).default('draft'),
  featuredImage: z.string().optional(),
  categories: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  seo: z
    .object({
      title: z.string().optional(),
      description: z.string().optional(),
      keywords: z.array(z.string()).default([]),
    })
    .optional(),
});

type PageForm = z.infer<typeof pageSchema>;

// State
const page = ref<any>(null);
const categories = ref<any[]>([]);
const tags = ref<any[]>([]);
const loading = ref(false);
const saving = ref(false);

// Form state
const state = reactive<PageForm>({
  title: '',
  slug: '',
  excerpt: '',
  content: '',
  status: 'draft',
  featuredImage: '',
  categories: [],
  tags: [],
  seo: {
    title: '',
    description: '',
    keywords: [],
  },
});

// Status options
const statusOptions = [
  { value: 'draft', label: 'แบบร่าง' },
  { value: 'published', label: 'เผยแพร่' },
  { value: 'archived', label: 'เก็บถาวร' },
];

// Load page data
const loadPage = async () => {
  loading.value = true;
  try {
    const response = await getPage(siteId.value, pageId.value);
    page.value = (response as any)?.data;

    if (page.value) {
      // Populate form with existing data
      Object.assign(state, {
        title: page.value.title || '',
        slug: page.value.slug || '',
        excerpt: page.value.excerpt || '',
        content: page.value.content || '',
        status: page.value.status || 'draft',
        featuredImage: page.value.featuredImage || '',
        categories: page.value.categories?.map((c: any) => c.id) || [],
        tags: page.value.tags?.map((t: any) => t.id) || [],
        seo: {
          title: page.value.seo?.title || '',
          description: page.value.seo?.description || '',
          keywords: page.value.seo?.keywords || [],
        },
      });
    }
  } catch (error: any) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: error.message || 'ไม่สามารถโหลดข้อมูลเพจได้',
      color: 'error',
    });
    navigateTo(`/dashboard/${siteId.value}/pages`);
  } finally {
    loading.value = false;
  }
};

// Load categories and tags
const loadCategoriesAndTags = async () => {
  try {
    const [categoriesResponse, tagsResponse] = await Promise.all([
      getCategories(siteId.value),
      getTags(siteId.value),
    ]);

    categories.value = (categoriesResponse as any)?.data || [];
    tags.value = (tagsResponse as any)?.data || [];
  } catch (error: any) {
    console.error('Error loading categories/tags:', error);
  }
};

// Load data on mount
onMounted(async () => {
  await Promise.all([loadPage(), loadCategoriesAndTags()]);
});

// Generate slug from title
const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
};

// Watch title changes to auto-generate slug
watch(
  () => state.title,
  newTitle => {
    if (newTitle && !state.slug) {
      state.slug = generateSlug(newTitle);
    }
  }
);

// Category options
const categoryOptions = computed(() =>
  categories.value.map(category => ({
    value: category.id,
    label: category.name,
  }))
);

// Tag options
const tagOptions = computed(() =>
  tags.value.map(tag => ({
    value: tag.id,
    label: tag.name,
  }))
);

// Handle form submission
const handleSubmit = async (event: any) => {
  saving.value = true;
  try {
    const formData = {
      ...event.data,
      categories: event.data.categories || [],
      tags: event.data.tags || [],
      seo: {
        title: event.data.seo?.title || '',
        description: event.data.seo?.description || '',
        keywords: event.data.seo?.keywords || [],
      },
    };

    await updatePage(siteId.value, pageId.value, formData);

    useToast().add({
      title: 'อัพเดตเพจสำเร็จ',
      description: 'บันทึกการเปลี่ยนแปลงเรียบร้อยแล้ว',
      color: 'success',
    });

    // Navigate back to page detail
    navigateTo(`/dashboard/${siteId.value}/pages/${pageId.value}`);
  } catch (error: any) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: error.message || 'ไม่สามารถอัพเดตเพจได้',
      color: 'error',
    });
  } finally {
    saving.value = false;
  }
};

// Handle cancel
const handleCancel = () => {
  navigateTo(`/dashboard/${siteId.value}/pages/${pageId.value}`);
};

// Handle image upload
const handleImageUpload = (url: string) => {
  state.featuredImage = url;
};

// Add new keyword
const newKeyword = ref('');
const addKeyword = () => {
  if (newKeyword.value.trim() && !state.seo?.keywords?.includes(newKeyword.value.trim())) {
    if (!state.seo) state.seo = { title: '', description: '', keywords: [] };
    state.seo.keywords = [...(state.seo.keywords || []), newKeyword.value.trim()];
    newKeyword.value = '';
  }
};

// Remove keyword
const removeKeyword = (keyword: string) => {
  if (state.seo?.keywords) {
    state.seo.keywords = state.seo.keywords.filter(k => k !== keyword);
  }
};

// SEO
useSeoMeta({
  title: computed(() => (page.value ? `แก้ไข ${page.value.title}` : 'แก้ไขเพจ')),
  description: 'แก้ไขเนื้อหาเพจ จัดการข้อมูล SEO และการตั้งค่าต่างๆ',
  keywords: 'แก้ไขเพจ, จัดการเนื้อหา, CMS, SEO',
  ogTitle: computed(() => (page.value ? `แก้ไข ${page.value.title}` : 'แก้ไขเพจ')),
  ogDescription: 'แก้ไขเนื้อหาเพจ จัดการข้อมูล SEO และการตั้งค่าต่างๆ',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div class="max-w-6xl mx-auto space-y-6">
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center py-12">
      <Icon name="i-heroicons-arrow-path" class="w-8 h-8 animate-spin text-primary" />
    </div>

    <!-- Page Content -->
    <template v-else-if="page">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <UButton
            @click="handleCancel"
            variant="ghost"
            icon="i-heroicons-arrow-left"
            size="sm"
          />
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
              แก้ไขเพจ
            </h1>
            <p class="text-gray-600 dark:text-gray-400">
              {{ page.title }}
            </p>
          </div>
        </div>
        
        <div class="flex items-center gap-3">
          <UButton
            @click="handleCancel"
            variant="outline"
            :disabled="saving"
          >
            ยกเลิก
          </UButton>
          <UButton
            @click="handleSubmit"
            :loading="saving"
            form="page-form"
            type="submit"
          >
            บันทึก
          </UButton>
        </div>
      </div>

      <!-- Form -->
      <UForm
        id="page-form"
        :schema="pageSchema"
        :state="state"
        @submit="handleSubmit"
        class="space-y-6"
      >
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Main Content -->
          <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold">ข้อมูลพื้นฐาน</h3>
              </template>
              
              <div class="space-y-4">
                <UFormField label="ชื่อเพจ" name="title" required>
                  <UInput
                    v-model="state.title"
                    placeholder="กรอกชื่อเพจ"
                    icon="i-heroicons-document-text"
                  />
                </UFormField>

                <UFormField label="URL Slug" name="slug" required>
                  <UInput
                    v-model="state.slug"
                    placeholder="url-slug"
                    icon="i-heroicons-link"
                  />
                  <template #hint>
                    <span class="text-xs text-gray-500">
                      URL: {{ `${window.location.origin}/pages/${state.slug}` }}
                    </span>
                  </template>
                </UFormField>

                <UFormField label="บทสรุป" name="excerpt">
                  <UTextarea
                    v-model="state.excerpt"
                    placeholder="บทสรุปสั้นๆ ของเพจ..."
                    :rows="3"
                  />
                </UFormField>
              </div>
            </UCard>

            <!-- Content -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold">เนื้อหา</h3>
              </template>
              
              <UFormField label="เนื้อหาเพจ" name="content" required>
                <UTextarea
                  v-model="state.content"
                  placeholder="เขียนเนื้อหาเพจที่นี่..."
                  :rows="15"
                />
              </UFormField>
            </UCard>

            <!-- SEO Settings -->
            <UCard>
              <template #header>
                <div class="flex items-center gap-2">
                  <Icon name="i-heroicons-magnifying-glass" class="w-5 h-5" />
                  <h3 class="text-lg font-semibold">การตั้งค่า SEO</h3>
                </div>
              </template>
              
              <div class="space-y-4">
                <UFormField label="Meta Title" name="seo.title">
                  <UInput
                    v-model="state.seo!.title"
                    placeholder="ชื่อที่แสดงใน search results"
                    icon="i-heroicons-tag"
                  />
                </UFormField>

                <UFormField label="Meta Description" name="seo.description">
                  <UTextarea
                    v-model="state.seo!.description"
                    placeholder="คำอธิบายที่แสดงใน search results"
                    :rows="3"
                  />
                </UFormField>

                <UFormField label="Keywords" name="seo.keywords">
                  <div class="space-y-3">
                    <div class="flex gap-2">
                      <UInput
                        v-model="newKeyword"
                        placeholder="เพิ่ม keyword"
                        @keydown.enter.prevent="addKeyword"
                        class="flex-1"
                      />
                      <UButton
                        @click="addKeyword"
                        :disabled="!newKeyword.trim()"
                        icon="i-heroicons-plus"
                      >
                        เพิ่ม
                      </UButton>
                    </div>
                    
                    <div v-if="state.seo?.keywords?.length" class="flex flex-wrap gap-2">
                      <UBadge
                        v-for="keyword in state.seo.keywords"
                        :key="keyword"
                        :label="keyword"
                        variant="soft"
                        class="cursor-pointer"
                        @click="removeKeyword(keyword)"
                      >
                        <template #trailing>
                          <Icon name="i-heroicons-x-mark" class="w-3 h-3" />
                        </template>
                      </UBadge>
                    </div>
                  </div>
                </UFormField>
              </div>
            </UCard>
          </div>

          <!-- Sidebar -->
          <div class="space-y-6">
            <!-- Publish Settings -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold">การเผยแพร่</h3>
              </template>
              
              <div class="space-y-4">
                <UFormField label="สถานะ" name="status">
                  <USelect
                    v-model="state.status"
                    :items="statusOptions"
                    placeholder="เลือกสถานะ"
                  />
                </UFormField>
              </div>
            </UCard>

            <!-- Featured Image -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold">รูปภาพหลัก</h3>
              </template>
              
              <div class="space-y-4">
                <div v-if="state.featuredImage" class="aspect-video rounded-lg overflow-hidden">
                  <img
                    :src="state.featuredImage"
                    :alt="state.title"
                    class="w-full h-full object-cover"
                  />
                </div>
                
                <ImageUpload
                  @upload="handleImageUpload"
                  :current-image="state.featuredImage"
                  accept="image/*"
                  class="w-full"
                />
                
                <UFormField label="URL รูปภาพ" name="featuredImage">
                  <UInput
                    v-model="state.featuredImage"
                    placeholder="https://example.com/image.jpg"
                    icon="i-heroicons-photo"
                  />
                </UFormField>
              </div>
            </UCard>

            <!-- Categories -->
            <UCard v-if="categories.length">
              <template #header>
                <h3 class="text-lg font-semibold">หมวดหมู่</h3>
              </template>
              
              <UFormField label="เลือกหมวดหมู่" name="categories">
                <USelect
                  v-model="state.categories"
                  :items="categoryOptions"
                  multiple
                  placeholder="เลือกหมวดหมู่"
                />
              </UFormField>
            </UCard>

            <!-- Tags -->
            <UCard v-if="tags.length">
              <template #header>
                <h3 class="text-lg font-semibold">แท็ก</h3>
              </template>
              
              <UFormField label="เลือกแท็ก" name="tags">
                <USelect
                  v-model="state.tags"
                  :items="tagOptions"
                  multiple
                  placeholder="เลือกแท็ก"
                />
              </UFormField>
            </UCard>

            <!-- Page Preview -->
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold">ตัวอย่าง</h3>
              </template>
              
              <div class="space-y-3">
                <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <h4 class="font-semibold text-gray-900 dark:text-white">
                    {{ state.title || 'ชื่อเพจ' }}
                  </h4>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {{ state.excerpt || 'บทสรุปของเพจ' }}
                  </p>
                  <div class="flex items-center gap-2 mt-2">
                    <UBadge
                      :label="statusOptions.find(s => s.value === state.status)?.label || 'แบบร่าง'"
                      :color="state.status === 'published' ? 'success' : 'warning'"
                      size="xs"
                    />
                  </div>
                </div>
              </div>
            </UCard>
          </div>
        </div>
      </UForm>
    </template>
  </div>
</template> 