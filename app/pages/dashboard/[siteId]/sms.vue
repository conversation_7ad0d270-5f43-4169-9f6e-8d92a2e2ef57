<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">การตลาดผ่าน SMS</h1>
        <p class="text-gray-600 dark:text-gray-400">จัดการและส่งข้อความ SMS ไปยังลูกค้าของคุณ</p>
      </div>
      <div class="flex items-center gap-3">
        <UButton @click="loadSMS" variant="outline" icon="i-heroicons-arrow-path">
          รีเฟรช
        </UButton>
        <UButton @click="showCreateModal = true" icon="i-heroicons-plus">
          ส่ง SMS ใหม่
        </UButton>
      </div>
    </div>

    <!-- Analytics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Icon name="i-heroicons-chat-bubble-left-ellipsis" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ analytics.totalSent }}</div>
            <div class="text-sm text-gray-500">ส่งแล้วทั้งหมด</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <Icon name="i-heroicons-eye" class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ analytics.openRate }}%</div>
            <div class="text-sm text-gray-500">อัตราการเปิดอ่าน</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Icon name="i-heroicons-users" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ analytics.totalRecipients }}</div>
            <div class="text-sm text-gray-500">ผู้รับทั้งหมด</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
            <Icon name="i-heroicons-megaphone" class="w-6 h-6 text-orange-600 dark:text-orange-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ analytics.activeCampaigns }}</div>
            <div class="text-sm text-gray-500">แคมเปญที่ใช้งาน</div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Filters -->
    <UCard>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหา</label>
          <UInput
            v-model="searchQuery"
            placeholder="ค้นหา SMS..."
            icon="i-heroicons-magnifying-glass"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สถานะ</label>
          <USelect
            v-model="statusFilter"
            :items="statusOptions"
            placeholder="เลือกสถานะ"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ประเภท</label>
          <USelect
            v-model="typeFilter"
            :items="typeOptions"
            placeholder="เลือกประเภท"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">วันที่</label>
          <UInput
            v-model="dateFilter"
            type="date"
            placeholder="เลือกวันที่"
          />
        </div>
      </div>
    </UCard>

    <!-- SMS List -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold">SMS ทั้งหมด</h3>
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-500">
              แสดง {{ filteredSMS.length }} รายการ
            </span>
          </div>
        </div>
      </template>

      <div v-if="loading" class="flex justify-center py-8">
        <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin" />
      </div>
      
      <div v-else-if="filteredSMS.length === 0" class="text-center py-12">
        <Icon name="i-heroicons-chat-bubble-left-ellipsis" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">ไม่พบ SMS</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">ยังไม่มี SMS ที่ส่งในระบบ</p>
        <UButton @click="showCreateModal = true" icon="i-heroicons-plus">
          ส่ง SMS ใหม่
        </UButton>
      </div>
      
      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                เนื้อหา
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                ผู้รับ
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                สถานะ
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                ประเภท
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                วันที่ส่ง
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                การเปิดอ่าน
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                การดำเนินการ
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="sms in paginatedSMS" :key="sms.id" class="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="max-w-xs">
                  <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {{ sms.content }}
                  </p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    {{ sms.content.length }} ตัวอักษร
                  </p>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">
                  {{ sms.recipients.length }} คน
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  {{ sms.recipients.slice(0, 2).join(', ') }}{{ sms.recipients.length > 2 ? '...' : '' }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <UBadge :color="getStatusColor(sms.status)" :variant="getStatusVariant(sms.status)">
                  {{ getStatusText(sms.status) }}
                </UBadge>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <UBadge :color="getTypeColor(sms.type)" :variant="getTypeVariant(sms.type)">
                  {{ getTypeText(sms.type) }}
                </UBadge>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ formatDate(sms.sentAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                    <div 
                      class="bg-green-600 h-2 rounded-full" 
                      :style="{ width: `${sms.openRate}%` }"
                    ></div>
                  </div>
                  <span class="text-sm text-gray-900 dark:text-white">{{ sms.openRate }}%</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <UDropdownMenu :items="getActionItems(sms)">
                  <UButton variant="ghost" icon="i-heroicons-ellipsis-vertical" />
                </UDropdownMenu>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex items-center justify-between mt-4">
        <div class="text-sm text-gray-700 dark:text-gray-300">
          แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} ถึง {{ Math.min(currentPage * itemsPerPage, filteredSMS.length) }}
          จาก {{ filteredSMS.length }} รายการ
        </div>
        <UPagination
          v-model="currentPage"
          :total="totalPages"
        />
      </div>
    </UCard>

    <!-- Create SMS Modal -->
    <UModal 
      v-model:open="showCreateModal"
      title="ส่ง SMS ใหม่"
      description="สร้างและส่งข้อความ SMS ไปยังลูกค้าของคุณ"
    >
      <template #body>
        <form @submit.prevent="handleCreateSMS" class="space-y-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ประเภท SMS <span class="text-red-500">*</span></label>
            <USelect
              v-model="newSMS.type"
              :items="typeOptions.filter(t => t.value)"
              placeholder="เลือกประเภท"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ผู้รับ <span class="text-red-500">*</span></label>
            <USelect
              v-model="newSMS.recipients"
              :items="recipientOptions"
              multiple
              placeholder="เลือกผู้รับ"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เนื้อหา <span class="text-red-500">*</span></label>
            <UTextarea
              v-model="newSMS.content"
              placeholder="พิมพ์เนื้อหา SMS..."
              :rows="4"
              maxlength="160"
            />
            <div class="text-xs text-gray-500 mt-1">{{ newSMS.content.length }}/160 ตัวอักษร</div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">กำหนดเวลาส่ง</label>
            <UInput
              v-model="newSMS.scheduledAt"
              type="datetime-local"
              placeholder="เลือกเวลาส่ง"
            />
          </div>
        </form>
      </template>

      <template #footer>
        <div class="flex justify-end gap-3">
          <UButton @click="showCreateModal = false" variant="outline">
            ยกเลิก
          </UButton>
          <UButton @click="handleCreateSMS" :loading="creating">
            ส่ง SMS
          </UButton>
        </div>
      </template>
    </UModal>
  </div>
</template>

<script setup lang="ts">
// Types
interface SMS {
  id: string;
  content: string;
  recipients: string[];
  status: 'sent' | 'delivered' | 'failed' | 'pending';
  type: 'promotional' | 'transactional' | 'notification' | 'marketing';
  sentAt: string;
  openRate: number;
}

interface Analytics {
  totalSent: number;
  openRate: number;
  totalRecipients: number;
  activeCampaigns: number;
}

// Props
interface Props {
  siteId?: string;
}

const props = defineProps<Props>();

// State
const sms = ref<SMS[]>([]);
const loading = ref(false);
const creating = ref(false);
const searchQuery = ref('');
const statusFilter = ref('');
const typeFilter = ref('');
const dateFilter = ref('');
const currentPage = ref(1);
const itemsPerPage = ref(10);
const showCreateModal = ref(false);

// New SMS form
const newSMS = ref({
  type: '',
  recipients: [] as string[],
  content: '',
  scheduledAt: '',
});

// Analytics
const analytics = ref<Analytics>({
  totalSent: 1247,
  openRate: 78,
  totalRecipients: 892,
  activeCampaigns: 3,
});

// Mock SMS data
const mockSMS = [
  {
    id: '1',
    content: 'ขอบคุณที่สั่งซื้อสินค้า! คำสั่งซื้อ #12345 ของคุณจะจัดส่งใน 2-3 วัน',
    recipients: ['0812345678', '0898765432'],
    status: 'delivered' as const,
    type: 'transactional' as const,
    sentAt: '2024-01-15T10:30:00Z',
    openRate: 95,
  },
  {
    id: '2',
    content: '🎉 เปิดตัวสินค้าใหม่! ลดพิเศษ 20% สำหรับลูกค้าทุกคน หมดเขต 31 ม.ค.',
    recipients: ['0812345678', '0898765432', '0654321098'],
    status: 'sent' as const,
    type: 'promotional' as const,
    sentAt: '2024-01-14T15:45:00Z',
    openRate: 82,
  },
  {
    id: '3',
    content: 'ระบบจะปิดปรับปรุงในวันที่ 20 ม.ค. เวลา 02:00-04:00 น. ขออภัยในความไม่สะดวก',
    recipients: ['0812345678'],
    status: 'pending' as const,
    type: 'notification' as const,
    sentAt: '2024-01-13T09:15:00Z',
    openRate: 0,
  },
];

// Options
const statusOptions = [
  { value: 'all', label: 'ทั้งหมด' },
  { value: 'sent', label: 'ส่งแล้ว' },
  { value: 'delivered', label: 'ส่งถึงแล้ว' },
  { value: 'failed', label: 'ล้มเหลว' },
  { value: 'pending', label: 'รอส่ง' },
];

const typeOptions = [
  { value: 'all', label: 'ทั้งหมด' },
  { value: 'promotional', label: 'โปรโมชัน' },
  { value: 'transactional', label: 'ธุรกรรม' },
  { value: 'notification', label: 'แจ้งเตือน' },
  { value: 'marketing', label: 'การตลาด' },
];

const recipientOptions = [
  { label: '0812345678', value: '0812345678' },
  { label: '0898765432', value: '0898765432' },
  { label: '0654321098', value: '0654321098' },
  { label: '0923456789', value: '0923456789' },
];

// Computed filtered SMS
const filteredSMS = computed(() => {
  let filtered = sms.value || [];

  if (searchQuery.value) {
    filtered = filtered.filter(s =>
      s.content.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  if (statusFilter.value) {
    filtered = filtered.filter(s => s.status === statusFilter.value);
  }

  if (typeFilter.value) {
    filtered = filtered.filter(s => s.type === typeFilter.value);
  }

  if (dateFilter.value) {
    filtered = filtered.filter(s => s.sentAt.startsWith(dateFilter.value));
  }

  return filtered;
});

// Paginated SMS
const paginatedSMS = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredSMS.value.slice(start, end);
});

const totalPages = computed(() => {
  return Math.ceil(filteredSMS.value.length / itemsPerPage.value);
});

// Load SMS
const loadSMS = async () => {
  loading.value = true;
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    sms.value = mockSMS;
  } catch (err) {
    console.error('Error loading SMS:', err);
  } finally {
    loading.value = false;
  }
};

// Create SMS
const handleCreateSMS = async () => {
  creating.value = true;
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    const newSMSItem: SMS = {
      id: Date.now().toString(),
      content: newSMS.value.content,
      recipients: newSMS.value.recipients,
      status: 'pending',
      type: newSMS.value.type as any,
      sentAt: new Date().toISOString(),
      openRate: 0,
    };

    sms.value.unshift(newSMSItem);
    showCreateModal.value = false;

    // Reset form
    newSMS.value = {
      type: '',
      recipients: [],
      content: '',
      scheduledAt: '',
    };

    // Show success message
    const toast = useToast();
    toast.add({
      title: 'ส่ง SMS สำเร็จ',
      description: 'SMS จะถูกส่งในไม่ช้า',
      icon: 'i-heroicons-check-circle',
      color: 'success',
    });
  } catch (err) {
    console.error('Error creating SMS:', err);
  } finally {
    creating.value = false;
  }
};

// Utility functions
const getStatusColor = (
  status: string
): 'success' | 'primary' | 'secondary' | 'info' | 'warning' | 'error' | 'neutral' => {
  switch (status) {
    case 'delivered':
      return 'success';
    case 'sent':
      return 'primary';
    case 'failed':
      return 'error';
    case 'pending':
      return 'warning';
    default:
      return 'neutral';
  }
};

const getStatusVariant = (status: string): 'outline' | 'solid' | 'soft' | 'subtle' => {
  return status === 'pending' ? 'soft' : 'solid';
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'delivered':
      return 'ส่งถึงแล้ว';
    case 'sent':
      return 'ส่งแล้ว';
    case 'failed':
      return 'ล้มเหลว';
    case 'pending':
      return 'รอส่ง';
    default:
      return status;
  }
};

const getTypeColor = (
  type: string
): 'success' | 'primary' | 'secondary' | 'info' | 'warning' | 'error' | 'neutral' => {
  switch (type) {
    case 'promotional':
      return 'secondary';
    case 'transactional':
      return 'primary';
    case 'notification':
      return 'warning';
    case 'marketing':
      return 'success';
    default:
      return 'neutral';
  }
};

const getTypeVariant = (type: string): 'outline' | 'solid' | 'soft' | 'subtle' => {
  return 'soft';
};

const getTypeText = (type: string) => {
  switch (type) {
    case 'promotional':
      return 'โปรโมชัน';
    case 'transactional':
      return 'ธุรกรรม';
    case 'notification':
      return 'แจ้งเตือน';
    case 'marketing':
      return 'การตลาด';
    default:
      return type;
  }
};

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

const getActionItems = (sms: SMS) => [
  [
    {
      label: 'ดูรายละเอียด',
      icon: 'i-heroicons-eye',
      click: () => console.log('View SMS:', sms.id),
    },
    {
      label: 'ส่งซ้ำ',
      icon: 'i-heroicons-arrow-path',
      click: () => console.log('Resend SMS:', sms.id),
    },
  ],
  [
    {
      label: 'ลบ',
      icon: 'i-heroicons-trash',
      click: () => console.log('Delete SMS:', sms.id),
    },
  ],
];

// Load data on mount
onMounted(() => {
  loadSMS();
});

// Watch for filter changes
watch([searchQuery, statusFilter, typeFilter, dateFilter], () => {
  currentPage.value = 1;
});

// Page meta
definePageMeta({
  layout: 'dashboard',
  middleware: 'auth',
});
</script> 