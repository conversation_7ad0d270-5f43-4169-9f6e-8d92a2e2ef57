<script setup lang="ts">
definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route: any = useRoute();
const siteId = computed(() => route.params.siteId);

// Use composables
const { getTeams } = useTeam();

// State
const loading = ref(false);
const invitationsLoading = ref(false);
const roles = ref<any[]>([]);
const invitations = ref<any[]>([]);
const selectedRows = ref<any[]>([]);

// Tab state
const activeTab = ref('members');

// Search and Filter State
const searchQuery = ref('');
const invitationSearchQuery = ref('');
const selectedRole = ref('all');
const selectedInvitationStatus = ref('all');
const currentPage = ref(1);
const invitationCurrentPage = ref(1);
const itemsPerPage = ref(10);
const invitationItemsPerPage = ref(10);

// Modal state
const showInviteModal = ref(false);
const inviteForm = reactive({
  email: '',
  role: 'viewer',
  message: ''
});
const inviteLoading = ref(false);

// Role options for filter
const roleOptions = [
  { label: 'ทั้งหมด', value: 'all' },
  { label: 'เจ้าของ', value: 'owner' },
  { label: 'ผู้ดูแล', value: 'admin' },
  { label: 'บรรณาธิการ', value: 'editor' },
  { label: 'ผู้ดู', value: 'viewer' }
];

// Invitation status options
const invitationStatusOptions = [
  { label: 'ทั้งหมด', value: 'all' },
  { label: 'รอการตอบรับ', value: 'pending' },
  { label: 'ยอมรับแล้ว', value: 'accepted' },
  { label: 'ปฏิเสธ', value: 'rejected' },
  { label: 'หมดอายุ', value: 'expired' }
];

// Role options for invite form
const inviteRoleOptions = [
  { label: 'ผู้ดู', value: 'viewer' },
  { label: 'บรรณาธิการ', value: 'editor' },
  { label: 'ผู้ดูแล', value: 'admin' }
];

// Load teams
const loadRoles = async () => {
  try {
    loading.value = true;
    const response: any = await getTeams(siteId.value);

    let rawRoles = [];
    // Handle different possible data structures
    if (response.data?.roles) {
      rawRoles = response.data.roles;
    } else if (response.roles) {
      rawRoles = response.roles;
    } else if (Array.isArray(response.data)) {
      rawRoles = response.data;
    } else if (Array.isArray(response)) {
      rawRoles = response;
    }

    // Transform data to match UTable columns
    roles.value = rawRoles.map((role: any) => ({
      ...role,
      user: role.userInfo?.name || role.userInfo?.email || 'ไม่มีชื่อ',
      joinedAt: role.createdAt,
      status: 'active'
    }));
  } catch (error) {
    console.error('Error loading roles:', error);
    roles.value = [];
  } finally {
    loading.value = false;
  }
};

// Load invitations
const loadInvitations = async () => {
  try {
    invitationsLoading.value = true;
    // TODO: Implement actual API call
    // const response = await getInvitations(siteId.value);

    // Mock data for now
    invitations.value = [
      {
        _id: '1',
        email: '<EMAIL>',
        role: 'editor',
        status: 'pending',
        invitedBy: '<EMAIL>',
        createdAt: '2024-01-20T10:30:00Z',
        expiresAt: '2024-01-27T10:30:00Z',
        message: 'ขอเชิญเข้าร่วมทีมงาน'
      },
      {
        _id: '2',
        email: '<EMAIL>',
        role: 'viewer',
        status: 'accepted',
        invitedBy: '<EMAIL>',
        createdAt: '2024-01-18T14:20:00Z',
        acceptedAt: '2024-01-19T09:15:00Z'
      },
      {
        _id: '3',
        email: '<EMAIL>',
        role: 'admin',
        status: 'expired',
        invitedBy: '<EMAIL>',
        createdAt: '2024-01-10T08:00:00Z',
        expiresAt: '2024-01-17T08:00:00Z'
      }
    ];
  } catch (error) {
    console.error('Error loading invitations:', error);
    invitations.value = [];
  } finally {
    invitationsLoading.value = false;
  }
};

// Send invitation
const sendInvitation = async () => {
  try {
    inviteLoading.value = true;

    // TODO: Implement actual API call
    // await sendTeamInvitation(siteId.value, inviteForm);

    // Mock success
    console.log('Sending invitation:', inviteForm);

    useToast().add({
      title: 'สำเร็จ',
      description: `ส่งคำเชิญไปยัง ${inviteForm.email} เรียบร้อยแล้ว`,
      color: 'success'
    });

    // Reset form and close modal
    Object.assign(inviteForm, {
      email: '',
      role: 'viewer',
      message: ''
    });
    showInviteModal.value = false;

    // Reload invitations
    await loadInvitations();
  } catch (error: any) {
    useToast().add({
      title: 'ผิดพลาด',
      description: error.message || 'ไม่สามารถส่งคำเชิญได้',
      color: 'error'
    });
  } finally {
    inviteLoading.value = false;
  }
};

// Resend invitation
const resendInvitation = async (invitationId: string) => {
  try {
    // TODO: Implement actual API call
    console.log('Resending invitation:', invitationId);

    useToast().add({
      title: 'สำเร็จ',
      description: 'ส่งคำเชิญใหม่เรียบร้อยแล้ว',
      color: 'success'
    });

    await loadInvitations();
  } catch (error: any) {
    useToast().add({
      title: 'ผิดพลาด',
      description: 'ไม่สามารถส่งคำเชิญใหม่ได้',
      color: 'error'
    });
  }
};

// Cancel invitation
const cancelInvitation = async (invitationId: string) => {
  if (!confirm('คุณแน่ใจหรือไม่ที่จะยกเลิกคำเชิญนี้?')) return;

  try {
    // TODO: Implement actual API call
    console.log('Canceling invitation:', invitationId);

    useToast().add({
      title: 'สำเร็จ',
      description: 'ยกเลิกคำเชิญเรียบร้อยแล้ว',
      color: 'success'
    });

    await loadInvitations();
  } catch (error: any) {
    useToast().add({
      title: 'ผิดพลาด',
      description: 'ไม่สามารถยกเลิกคำเชิญได้',
      color: 'error'
    });
  }
};

// Computed properties for filtering and pagination - Members
const filteredRoles = computed(() => {
  let filtered = roles.value;

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter((role: any) =>
      role.userInfo?.name?.toLowerCase().includes(query) ||
      role.userInfo?.email?.toLowerCase().includes(query) ||
      role.role?.toLowerCase().includes(query)
    );
  }

  // Role filter
  if (selectedRole.value && selectedRole.value !== 'all') {
    filtered = filtered.filter((role: any) => role.role === selectedRole.value);
  }

  return filtered;
});

const paginatedRoles = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredRoles.value.slice(start, end);
});

const totalPages = computed(() => {
  return Math.ceil(filteredRoles.value.length / itemsPerPage.value);
});

// Computed properties for filtering and pagination - Invitations
const filteredInvitations = computed(() => {
  let filtered = invitations.value;

  // Search filter
  if (invitationSearchQuery.value) {
    const query = invitationSearchQuery.value.toLowerCase();
    filtered = filtered.filter((invitation: any) =>
      invitation.email?.toLowerCase().includes(query) ||
      invitation.role?.toLowerCase().includes(query) ||
      invitation.invitedBy?.toLowerCase().includes(query)
    );
  }

  // Status filter
  if (selectedInvitationStatus.value && selectedInvitationStatus.value !== 'all') {
    filtered = filtered.filter((invitation: any) => invitation.status === selectedInvitationStatus.value);
  }

  return filtered;
});

const paginatedInvitations = computed(() => {
  const start = (invitationCurrentPage.value - 1) * invitationItemsPerPage.value;
  const end = start + invitationItemsPerPage.value;
  return filteredInvitations.value.slice(start, end);
});

const totalInvitationPages = computed(() => {
  return Math.ceil(filteredInvitations.value.length / invitationItemsPerPage.value);
});

// Clear filters
const clearFilters = () => {
  searchQuery.value = '';
  selectedRole.value = 'all';
  currentPage.value = 1;
};

// Clear invitation filters
const clearInvitationFilters = () => {
  invitationSearchQuery.value = '';
  selectedInvitationStatus.value = 'all';
  invitationCurrentPage.value = 1;
};

// Get invitation status color
const getInvitationStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'pending': return 'warning';
    case 'accepted': return 'success';
    case 'rejected': return 'error';
    case 'expired': return 'neutral';
    default: return 'neutral';
  }
};

// Get invitation status label
const getInvitationStatusLabel = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'pending': return 'รอการตอบรับ';
    case 'accepted': return 'ยอมรับแล้ว';
    case 'rejected': return 'ปฏิเสธ';
    case 'expired': return 'หมดอายุ';
    default: return status || '-';
  }
};

// Get role badge color
const getRoleColor = (role: string) => {
  switch (role?.toLowerCase()) {
    case 'owner': return 'primary';
    case 'admin': return 'info';
    case 'editor': return 'success';
    case 'viewer': return 'neutral';
    default: return 'neutral';
  }
};

// Get role label in Thai
const getRoleLabel = (role: string) => {
  switch (role?.toLowerCase()) {
    case 'owner': return 'เจ้าของ';
    case 'admin': return 'ผู้ดูแล';
    case 'editor': return 'บรรณาธิการ';
    case 'viewer': return 'ผู้ดู';
    default: return role || '-';
  }
};

// Format date
const formatDate = (date: string) => {
  if (!date) return '-';
  return new Date(date).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Bulk actions
const bulkRemoveMembers = async () => {
  if (selectedRows.value.length === 0) return;

  const userIds = selectedRows.value.map((row: any) => row._id);
  const memberNames = selectedRows.value.map((row: any) => row.userInfo?.name || row.userInfo?.email).join(', ');

  if (!confirm(`คุณแน่ใจหรือไม่ที่จะลบสมาชิก ${selectedRows.value.length} คน?\n${memberNames}`)) return;

  try {
    // TODO: Implement bulk remove API call
    console.log('Bulk removing members:', userIds);
    selectedRows.value = [];
    await loadRoles();
    // Show success message
  } catch (error) {
    console.error('Error bulk removing members:', error);
    // Show error message
  }
};

const bulkChangeRole = async (newRole: string) => {
  if (selectedRows.value.length === 0) return;

  const memberNames = selectedRows.value.map((row: any) => row.userInfo?.name || row.userInfo?.email).join(', ');

  if (!confirm(`คุณแน่ใจหรือไม่ที่จะเปลี่ยนบทบาทของสมาชิก ${selectedRows.value.length} คน เป็น "${getRoleLabel(newRole)}"?\n${memberNames}`)) return;

  try {
    // TODO: Implement bulk role change API call
    console.log('Bulk changing role to:', newRole, 'for members:', selectedRows.value.map((r: any) => r._id));
    selectedRows.value = [];
    await loadRoles();
    // Show success message
  } catch (error) {
    console.error('Error bulk changing roles:', error);
    // Show error message
  }
};

// Transform data for table display
const transformedRoles = computed(() => {
  try {
    const transformed = paginatedRoles.value.map((role: any) => {
      const roleLabel = role?.role === 'owner' ? 'เจ้าของ' :
        role?.role === 'admin' ? 'ผู้ดูแล' :
          role?.role === 'editor' ? 'บรรณาธิการ' :
            role?.role === 'viewer' ? 'ผู้ดู' :
              role?.role || '-';

      return {
        ...role,
        avatar: role?.userInfo?.email?.charAt(0)?.toUpperCase() || '?',
        name: role?.userInfo?.name || role?.userInfo?.email || 'ไม่มีชื่อ',
        email: role?.userInfo?.email || '-',
        role: roleLabel,
        roleValue: role?.role || 'viewer',
        joined: role?.updatedAt ? new Date(role.updatedAt).toLocaleDateString('th-TH', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        }) : '-',
        status: 'active'
      };
    });
    return transformed;
  } catch (error) {
    console.error('Error transforming roles:', error);
    return paginatedRoles.value;
  }
});

// Context menu items for row actions
const contextMenuItems = (row: any) => [
  [{
    label: 'แก้ไขบทบาท',
    icon: 'i-heroicons-pencil-square',
    click: () => handleEditRole(row)
  }],
  [{
    label: 'ส่งข้อความ',
    icon: 'i-heroicons-chat-bubble-left-ellipsis',
    click: () => handleSendMessage(row)
  }],
  [{
    label: 'ลบออกจากทีม',
    icon: 'i-heroicons-trash',
    click: () => handleRemoveMember(row)
  }]
];

// Handle edit role
const handleEditRole = (row: any) => {
  console.log('Edit role for:', row);
  // TODO: Implement role editing modal
  useToast().add({
    title: 'แก้ไขบทบาท',
    description: `กำลังแก้ไขบทบาทของ ${row.userInfo?.name || row.userInfo?.email}`,
    color: 'info'
  });
};

// Handle send message
const handleSendMessage = (row: any) => {
  console.log('Send message to:', row);
  // TODO: Implement message sending modal
  useToast().add({
    title: 'ส่งข้อความ',
    description: `กำลังส่งข้อความถึง ${row.userInfo?.name || row.userInfo?.email}`,
    color: 'info'
  });
};

// Handle remove member
const handleRemoveMember = (row: any) => {
  const memberName = row.userInfo?.name || row.userInfo?.email;
  if (!confirm(`คุณแน่ใจหรือไม่ที่จะลบ ${memberName} ออกจากทีม?`)) return;

  console.log('Remove member:', row);
  // TODO: Implement member removal
  useToast().add({
    title: 'ลบสมาชิก',
    description: `ลบ ${memberName} ออกจากทีมเรียบร้อยแล้ว`,
    color: 'success'
  });
};

// Add test data for development
const addTestData = () => {
  roles.value = [
    {
      _id: '1',
      role: 'admin',
      userInfo: {
        name: 'สมชาย ใจดี',
        email: '<EMAIL>',
        avatar: null
      },
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-15T10:30:00Z'
    },
    {
      _id: '2',
      role: 'editor',
      userInfo: {
        name: 'สมหญิง รักงาน',
        email: '<EMAIL>',
        avatar: null
      },
      createdAt: '2024-01-10T08:15:00Z',
      updatedAt: '2024-01-10T08:15:00Z'
    },
    {
      _id: '3',
      role: 'viewer',
      userInfo: {
        name: null,
        email: '<EMAIL>',
        avatar: null
      },
      createdAt: '2024-01-05T14:20:00Z',
      updatedAt: '2024-01-05T14:20:00Z'
    }
  ];
};

// Load data when component mounts
onMounted(() => {
  loadRoles();
  // Add test data if no real data is available
  setTimeout(() => {
    if (roles.value.length === 0) {
      console.log('No data loaded, adding test data...');
      addTestData();
    }
  }, 2000);
});

// Watch for tab changes to load invitations
watch(activeTab, (newTab) => {
  if (newTab === 'invitations' && invitations.value.length === 0) {
    loadInvitations();
  }
});

// SEO
useSeoMeta({
  title: 'จัดการทีม - ระบบจัดการร้านค้า',
  description: 'จัดการทีมและสมาชิกในระบบ',
  keywords: 'จัดการทีม, สมาชิก, การทำงานร่วมกัน',
  ogTitle: 'จัดการทีม - ระบบจัดการร้านค้า',
  ogDescription: 'จัดการทีมและสมาชิกในระบบ',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div>
    <!-- Header -->
    <div class="flex items-center justify-between p-3">
      <div>
        <h1 class="text-xl font-bold text-gray-900 dark:text-white">
          จัดการทีมงาน
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          จัดการทีมงานและคำเชิญในระบบ
        </p>
      </div>
      <div class="flex items-center gap-2">
        <UButton @click="showInviteModal = true" color="primary" icon="i-heroicons-plus">
          สร้างคำเชิญ
        </UButton>
        <UButton @click="activeTab === 'members' ? loadRoles() : loadInvitations()"
          :loading="loading || invitationsLoading" color="secondary" icon="i-heroicons-arrow-path" variant="soft"
          :disabled="loading || invitationsLoading" title="รีเฟรชข้อมูล" />
      </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="-mb-px flex space-x-8">
          <button @click="activeTab = 'members'" :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'members'
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
          ]">
            <div class="flex items-center gap-2">
              <Icon name="i-heroicons-users" class="size-4" />
              สมาชิกทีม
              <UBadge v-if="roles.length > 0" :label="roles.length" color="primary" variant="soft" size="xs" />
            </div>
          </button>
          <button @click="activeTab = 'invitations'" :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'invitations'
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
          ]">
            <div class="flex items-center gap-2">
              <Icon name="i-heroicons-envelope" class="size-4" />
              คำเชิญ
              <UBadge v-if="invitations.length > 0" :label="invitations.length" color="warning" variant="soft"
                size="xs" />
            </div>
          </button>
        </nav>
      </div>
    </div>

    <!-- Members Tab Content -->
    <div v-if="activeTab === 'members'">
      <!-- Search and Filter Bar -->
      <div class="mb-6 bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800 p-4">
        <div class="flex flex-col sm:flex-row gap-4">
          <!-- Search -->
          <div class="flex-1">
            <UInput v-model="searchQuery" placeholder="ค้นหาสมาชิก (ชื่อ, อีเมล, บทบาท)"
              icon="i-heroicons-magnifying-glass" size="md" />
          </div>

          <!-- Role Filter -->
          <div class="w-full sm:w-48">
            <USelect v-model="selectedRole" :items="roleOptions" placeholder="กรองตามบทบาท" size="md"
              icon="i-heroicons-funnel" />
          </div>

          <!-- Clear Filters -->
          <UButton v-if="searchQuery || selectedRole !== 'all'" @click="clearFilters" variant="outline" color="neutral"
            icon="i-heroicons-x-mark" size="md">
            ล้างตัวกรอง
          </UButton>
        </div>

        <!-- Results Info -->
        <div class="mt-3 flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
          <div>
            แสดง {{ paginatedRoles.length }} จาก {{ filteredRoles.length }} รายการ
            <span v-if="filteredRoles.length !== roles.length">
              (กรองจากทั้งหมด {{ roles.length }} รายการ)
            </span>
          </div>

          <!-- Items per page -->
          <div class="flex items-center gap-2">
            <span>แสดงต่อหน้า:</span>
            <USelect v-model="itemsPerPage" :items="[
              { label: '5', value: 5 },
              { label: '10', value: 10 },
              { label: '20', value: 20 },
              { label: '50', value: 50 }
            ]" size="xs" class="w-20" @change="currentPage = 1" />
          </div>
        </div>
      </div>

      <!-- Bulk Actions Bar -->
      <div v-if="selectedRows.length > 0"
        class="mb-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="p-1 bg-blue-100 dark:bg-blue-800 rounded-full">
              <Icon name="i-heroicons-check-circle" class="size-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <span class="font-medium text-blue-900 dark:text-blue-100">
                เลือกแล้ว {{ selectedRows.length }} รายการ
              </span>
              <p class="text-xs text-blue-700 dark:text-blue-300">
                {{selectedRows.map((row: any) => row.userInfo?.name || row.userInfo?.email).join(', ')}}
              </p>
            </div>
          </div>

          <div class="flex items-center gap-2">
            <!-- Bulk Role Change -->
            <UDropdownMenu :items="[
              [
                {
                  label: 'เปลี่ยนเป็นผู้ดูแล',
                  icon: 'i-heroicons-shield-check',
                  click: () => bulkChangeRole('admin')
                },
                {
                  label: 'เปลี่ยนเป็นบรรณาธิการ',
                  icon: 'i-heroicons-pencil-square',
                  click: () => bulkChangeRole('editor')
                },
                {
                  label: 'เปลี่ยนเป็นผู้ดู',
                  icon: 'i-heroicons-eye',
                  click: () => bulkChangeRole('viewer')
                }
              ]
            ]" :popper="{ placement: 'bottom-start' }">
              <UButton color="info" variant="soft" icon="i-heroicons-user-group" size="sm"
                trailing-icon="i-heroicons-chevron-down" title="เปลี่ยนบทบาทของสมาชิกที่เลือก">
                เปลี่ยนบทบาท
              </UButton>
            </UDropdownMenu>

            <!-- Bulk Remove -->
            <UButton @click="bulkRemoveMembers" color="error" variant="soft" icon="i-heroicons-trash" size="sm"
              title="ลบสมาชิกที่เลือกออกจากทีม">
              ลบที่เลือก
            </UButton>

            <!-- Clear Selection -->
            <UButton @click="selectedRows = []" variant="ghost" color="neutral" icon="i-heroicons-x-mark" size="sm"
              title="ยกเลิกการเลือก">
              ยกเลิก
            </UButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Invitations Tab Content -->
    <div v-if="activeTab === 'invitations'">
      <!-- Search and Filter Bar for Invitations -->
      <div class="mb-6 bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800 p-4">
        <div class="flex flex-col sm:flex-row gap-4">
          <!-- Search -->
          <div class="flex-1">
            <UInput v-model="invitationSearchQuery" placeholder="ค้นหาคำเชิญ (อีเมล, บทบาท, ผู้เชิญ)"
              icon="i-heroicons-magnifying-glass" size="md" />
          </div>

          <!-- Status Filter -->
          <div class="w-full sm:w-48">
            <USelect v-model="selectedInvitationStatus" :items="invitationStatusOptions" placeholder="กรองตามสถานะ"
              size="md" />
          </div>

          <!-- Clear Filters -->
          <UButton v-if="invitationSearchQuery || selectedInvitationStatus !== 'all'" @click="clearInvitationFilters"
            variant="outline" color="neutral" icon="i-heroicons-x-mark" size="md">
            ล้างตัวกรอง
          </UButton>
        </div>

        <!-- Results Info -->
        <div class="mt-3 flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
          <div>
            แสดง {{ paginatedInvitations.length }} จาก {{ filteredInvitations.length }} รายการ
            <span v-if="filteredInvitations.length !== invitations.length">
              (กรองจากทั้งหมด {{ invitations.length }} รายการ)
            </span>
          </div>

          <!-- Items per page -->
          <div class="flex items-center gap-2">
            <span>แสดงต่อหน้า:</span>
            <USelect v-model="invitationItemsPerPage" :items="[
              { label: '5', value: 5 },
              { label: '10', value: 10 },
              { label: '20', value: 20 },
              { label: '50', value: 50 }
            ]" size="xs" class="w-20" @change="invitationCurrentPage = 1" />
          </div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <UCard variant="soft">
      <!-- Members Table -->
      <div v-if="activeTab === 'members'" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead>
            <tr>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Avatar
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                ชื่อ
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                อีเมล
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                บทบาท
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                เข้าร่วมเมื่อ
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                การดำเนินการ
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-if="loading" class="animate-pulse">
              <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                <div class="flex items-center justify-center gap-2">
                  <Icon name="i-heroicons-arrow-path" class="w-4 h-4 animate-spin" />
                  กำลังโหลด...
                </div>
              </td>
            </tr>
            <tr v-else-if="transformedRoles.length === 0">
              <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                <div class="flex flex-col items-center gap-2">
                  <Icon name="i-heroicons-users" class="w-8 h-8 text-gray-400" />
                  ไม่มีข้อมูลทีมงาน
                </div>
              </td>
            </tr>
            <tr v-else v-for="role in transformedRoles" :key="role._id"
              class="bg-[var(--ui-bg-2)] hover:bg-[var(--ui-bg-1)] transition-colors">
              <!-- Avatar -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center justify-center">
                  <div class="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                      {{ role.avatar }}
                    </span>
                  </div>
                </div>
              </td>

              <!-- Name -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex flex-col">
                  <span class="font-medium text-gray-900 dark:text-white">
                    {{ role.name }}
                  </span>
                  <span v-if="role?.userInfo?.name" class="text-xs text-gray-500 dark:text-gray-400">
                    {{ role?.userInfo?.email }}
                  </span>
                </div>
              </td>

              <!-- Email -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-gray-700 dark:text-gray-300">
                  {{ role.email }}
                </span>
              </td>

              <!-- Role -->
              <td class="px-6 py-4 whitespace-nowrap">
                <UBadge :color="getRoleColor(role.roleValue)" variant="soft" class="font-medium">
                  {{ role.role }}
                </UBadge>
              </td>

              <!-- Joined Date -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-gray-600 dark:text-gray-400 text-sm">
                  {{ role.joined }}
                </span>
              </td>

              <!-- Actions -->
              <td class="px-6 py-4 whitespace-nowrap">
                <UDropdownMenu :items="contextMenuItems(role)" :popper="{ placement: 'bottom-end' }">
                  <UButton icon="i-heroicons-ellipsis-vertical" color="neutral" variant="ghost" size="sm"
                    class="hover:bg-gray-100 dark:hover:bg-gray-800" title="ตัวเลือกเพิ่มเติม" />
                </UDropdownMenu>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Invitations Table -->
      <div v-if="activeTab === 'invitations'" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead>
            <tr>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                อีเมล
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                บทบาท
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                สถานะ
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                ผู้เชิญ
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                วันที่เชิญ
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                การดำเนินการ
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-if="invitationsLoading" class="animate-pulse">
              <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                <div class="flex items-center justify-center gap-2">
                  <Icon name="i-heroicons-arrow-path" class="w-4 h-4 animate-spin" />
                  กำลังโหลด...
                </div>
              </td>
            </tr>
            <tr v-else-if="paginatedInvitations.length === 0">
              <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                <div class="flex flex-col items-center gap-2">
                  <Icon name="i-heroicons-envelope" class="w-8 h-8 text-gray-400" />
                  ไม่มีคำเชิญ
                </div>
              </td>
            </tr>
            <tr v-else v-for="invitation in paginatedInvitations" :key="invitation._id"
              class="bg-[var(--ui-bg-2)] hover:bg-[var(--ui-bg-1)] transition-colors">
              <!-- Email -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center gap-3">
                  <div class="w-8 h-8 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                    <Icon name="i-heroicons-envelope" class="size-4 text-gray-500" />
                  </div>
                  <div>
                    <div class="font-medium text-gray-900 dark:text-white">
                      {{ invitation.email }}
                    </div>
                    <div v-if="invitation.message" class="text-xs text-gray-500 dark:text-gray-400">
                      {{ invitation.message }}
                    </div>
                  </div>
                </div>
              </td>

              <!-- Role -->
              <td class="px-6 py-4 whitespace-nowrap">
                <UBadge :color="getRoleColor(invitation.role)" variant="soft">
                  {{ getRoleLabel(invitation.role) }}
                </UBadge>
              </td>

              <!-- Status -->
              <td class="px-6 py-4 whitespace-nowrap">
                <UBadge :color="getInvitationStatusColor(invitation.status)" variant="soft">
                  {{ getInvitationStatusLabel(invitation.status) }}
                </UBadge>
              </td>

              <!-- Invited By -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-gray-700 dark:text-gray-300 text-sm">
                  {{ invitation.invitedBy }}
                </span>
              </td>

              <!-- Created Date -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-gray-600 dark:text-gray-400 text-sm">
                  {{ formatDate(invitation.createdAt) }}
                </span>
              </td>

              <!-- Actions -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center gap-2">
                  <UButton v-if="invitation.status === 'pending'" @click="resendInvitation(invitation._id)"
                    color="primary" variant="soft" size="xs" icon="i-heroicons-arrow-path" title="ส่งคำเชิญใหม่">
                    ส่งใหม่
                  </UButton>
                  <UButton v-if="invitation.status === 'pending'" @click="cancelInvitation(invitation._id)"
                    color="error" variant="soft" size="xs" icon="i-heroicons-x-mark" title="ยกเลิกคำเชิญ">
                    ยกเลิก
                  </UButton>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State for Members -->
      <div v-if="activeTab === 'members' && !loading && roles.length === 0" class="text-center py-16">
        <div class="mx-auto size-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
          <Icon name="i-heroicons-user-group" class="size-8 text-gray-400" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          ยังไม่มีทีมงาน
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-6 max-w-sm mx-auto">
          เริ่มต้นสร้างทีมงานของคุณด้วยการเชิญสมาชิกใหม่เข้าร่วม
        </p>
        <UButton @click="showInviteModal = true" color="primary" icon="i-heroicons-plus" size="lg">
          เชิญสมาชิกใหม่
        </UButton>
      </div>

      <!-- Empty State for Invitations -->
      <div v-if="activeTab === 'invitations' && !invitationsLoading && invitations.length === 0"
        class="text-center py-16">
        <div class="mx-auto size-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
          <Icon name="i-heroicons-envelope" class="size-8 text-gray-400" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          ยังไม่มีคำเชิญ
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-6 max-w-sm mx-auto">
          ส่งคำเชิญให้สมาชิกใหม่เพื่อเข้าร่วมทีมงานของคุณ
        </p>
        <UButton @click="showInviteModal = true" color="primary" icon="i-heroicons-plus" size="lg">
          สร้างคำเชิญ
        </UButton>
      </div>
    </UCard>

    <!-- Team Stats -->
    <div v-if="roles.length > 0" class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800 p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Icon name="i-heroicons-users" class="size-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ roles.length }}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              สมาชิกทั้งหมด
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800 p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <Icon name="i-heroicons-shield-check" class="size-5 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">
              {{roles.filter((r: any) => r?.role === 'admin').length}}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              ผู้ดูแล
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800 p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Icon name="i-heroicons-pencil-square" class="size-5 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">
              {{roles.filter((r: any) => r?.role === 'editor').length}}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              บรรณาธิการ
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination for Members -->
    <div v-if="activeTab === 'members' && totalPages > 1" class="mt-6 flex justify-center">
      <UPagination v-model="currentPage" :page-count="totalPages" :total="filteredRoles.length" show-last show-first
        class="flex items-center gap-1" />
    </div>

    <!-- Pagination for Invitations -->
    <div v-if="activeTab === 'invitations' && totalInvitationPages > 1" class="mt-6 flex justify-center">
      <UPagination v-model="invitationCurrentPage" :page-count="totalInvitationPages"
        :total="filteredInvitations.length" show-last show-first class="flex items-center gap-1" />
    </div>

    <!-- Invite Modal -->
    <UModal v-model="showInviteModal">
      <UCard class="max-w-md mx-auto">
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Icon name="i-heroicons-user-plus" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                เชิญสมาชิกใหม่
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                ส่งคำเชิญให้สมาชิกใหม่เข้าร่วมทีมงาน
              </p>
            </div>
          </div>
        </template>

        <form @submit.prevent="sendInvitation" class="space-y-4">
          <!-- Email -->
          <UFormField label="อีเมล" name="email" required>
            <UInput v-model="inviteForm.email" type="email" placeholder="<EMAIL>" icon="i-heroicons-envelope"
              required />
          </UFormField>

          <!-- Role -->
          <UFormField label="บทบาท" name="role" required>
            <USelect v-model="inviteForm.role" :items="inviteRoleOptions" icon="i-heroicons-shield-check" />
          </UFormField>

          <!-- Message -->
          <UFormField label="ข้อความ (ไม่บังคับ)" name="message">
            <UTextarea v-model="inviteForm.message" placeholder="ข้อความเชิญ..." :rows="3"
              icon="i-heroicons-chat-bubble-left-ellipsis" />
          </UFormField>

          <!-- Role Description -->
          <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div class="text-sm text-gray-600 dark:text-gray-400">
              <div class="font-medium mb-2">คำอธิบายบทบาท:</div>
              <div v-if="inviteForm.role === 'viewer'" class="text-xs">
                <span class="font-medium text-blue-600">ผู้ดู:</span> ดูข้อมูลในทีมได้เท่านั้น
              </div>
              <div v-else-if="inviteForm.role === 'editor'" class="text-xs">
                <span class="font-medium text-green-600">บรรณาธิการ:</span> แก้ไขเนื้อหาและข้อมูลได้
              </div>
              <div v-else-if="inviteForm.role === 'admin'" class="text-xs">
                <span class="font-medium text-orange-600">ผู้ดูแล:</span> จัดการสมาชิกและตั้งค่าทีมได้
              </div>
            </div>
          </div>
        </form>

        <template #footer>
          <div class="flex items-center justify-end gap-3">
            <UButton @click="showInviteModal = false" color="neutral" variant="soft">
              ยกเลิก
            </UButton>
            <UButton type="submit" :loading="inviteLoading" :disabled="!inviteForm.email" color="primary"
              @click="sendInvitation">
              ส่งคำเชิญ
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>