<script setup lang="ts">
// import { UCard } from '#components'; 

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route: any = useRoute();
const siteId = computed(() => route.params.siteId);

// Use composables
const { createInvitation } = useTeam();


// Loading state
const loading = ref(false);

// Role options
const roleOptions = [
  { value: 'owner', label: 'เจ้าของ' },
  { value: 'admin', label: 'ผู้ดูแล' },
  { value: 'editor', label: 'บรรณาธิการ' },
  { value: 'viewer', label: 'ผู้ใช้งานทั่วไป' },
];

// เพิ่ม state สำหรับ invitation
const invitationState = reactive({
  toEmail: '',
  role: 'viewer',
  message: '',
  limit: 1,
});

// ฟังก์ชันส่งคำเชิญ
const handleInvite = async () => {
  loading.value = true;
  try {
    const payload = {
      toEmail: invitationState.toEmail,
      role: invitationState.role,
      message: invitationState.message,
      limit: invitationState.limit,
    };
    const response = await createInvitation(payload);
    if (response?.success) {
      useToast().add({
        title: 'ส่งคำเชิญสำเร็จ',
        color: 'success',
      });
      // reset form
      invitationState.toEmail = '';
      invitationState.role = 'viewer';
      invitationState.message = '';
      invitationState.limit = 1;
    }
  } catch (error: any) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: error.message || 'ไม่สามารถส่งคำเชิญได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};


// SEO
useSeoMeta({
  title: 'สร้างคำเชิญ - ระบบจัดการร้านค้า',
  description: 'สร้างคำเชิญเข้าร่วมทีมงานเว็บไซต์',
  keywords: 'สร้างคำเชิญ, จัดการสมาชิก, การทำงานร่วมกัน, ทีมงาน',
  ogTitle: 'สร้างคำเชิญ - ระบบจัดการร้านค้า',
  ogDescription: 'สร้างคำเชิญเข้าร่วมทีมงานเว็บไซต์',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-3">
        <UButton :to="`/dashboard/${siteId}/teams`" variant="ghost" icon="i-heroicons-arrow-left" />
        <div>
          <h1 class="text-xl font-bold text-gray-900 dark:text-white">
            สร้างคำเชิญ
          </h1>
          <p class="text-gray-600 dark:text-gray-400">
            สร้างคำเชิญเข้าร่วมทีมงานเว็บไซต์
          </p>
        </div>
      </div>
    </div>

    <!-- Invitation Form -->
    <div class="max-w-2xl mx-auto">
      <UForm :state="invitationState" @submit.prevent="handleInvite" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Email Field -->
          <UFormField label="อีเมลผู้รับ" name="toEmail" required>
            <UInput 
              size="lg" 
              class="w-full" 
              v-model="invitationState.toEmail" 
              placeholder="<EMAIL>"
              icon="i-heroicons-envelope" 
            />
          </UFormField>

          <!-- Role Field -->
          <UFormField label="บทบาท" name="role" required>
            <USelect 
              icon="i-heroicons-shield-check" 
              class="w-full" 
              size="lg" 
              v-model="invitationState.role"
              :items="roleOptions" 
              option-attribute="label" 
              value-attribute="value" 
            />
          </UFormField>
        </div>

        <!-- Message Field -->
        <UFormField label="ข้อความ (ไม่บังคับ)" name="message">
          <UTextarea 
            size="lg" 
            class="w-full" 
            v-model="invitationState.message" 
            placeholder="ข้อความถึงผู้รับ..."
            :rows="3"
            icon="i-heroicons-chat-bubble-left-ellipsis"
          />
        </UFormField>

        <!-- Limit Field -->
        <UFormField label="จำนวนครั้งที่ใช้ได้" name="limit">
          <UInputNumber 
            size="lg" 
            class="w-full" 
            v-model.number="invitationState.limit" 
            :min="1"
            icon="i-heroicons-clock"
          />
        </UFormField>

        <!-- Submit Button -->
        <div class="flex justify-center pt-4">
          <UButton 
            type="submit" 
            icon="i-heroicons-paper-airplane" 
            color="primary" 
            size="lg" 
            :loading="loading"
            :disabled="loading || !invitationState.toEmail" 
            class="min-w-48"
          >
            {{ loading ? 'กำลังส่ง...' : 'ส่งคำเชิญ' }}
          </UButton>
        </div>
      </UForm>
    </div>

    <UCard class="max-w-2xl mx-auto">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
            <Icon name="i-heroicons-light-bulb" class="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              เคล็ดลับและคำอธิบาย
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              ข้อมูลเพิ่มเติมเกี่ยวกับการจัดการทีมงาน
            </p>
          </div>
        </div>
      </template>
      
      <div class="space-y-6">
        <!-- Tips Section -->
        <div class="space-y-3">
          <h4 class="font-medium text-gray-900 dark:text-white">เคล็ดลับการใช้งาน:</h4>
          <div class="space-y-2">
            <div class="flex items-start gap-3">
              <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span class="text-sm text-gray-600 dark:text-gray-400">กำหนดจำนวนสมาชิกสูงสุดให้เหมาะสมกับขนาดโปรเจกต์</span>
            </div>
            <div class="flex items-start gap-3">
              <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span class="text-sm text-gray-600 dark:text-gray-400">เลือกบทบาทเริ่มต้นที่เหมาะสมกับลักษณะงาน</span>
            </div>
            <div class="flex items-start gap-3">
              <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
              <span class="text-sm text-gray-600 dark:text-gray-400">ส่งข้อความที่ชัดเจนเพื่อให้ผู้รับเข้าใจวัตถุประสงค์</span>
            </div>
          </div>
        </div>

        <!-- Role Descriptions -->
        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
          <h4 class="font-medium text-gray-900 dark:text-white mb-3">คำอธิบายบทบาท:</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div class="flex items-center gap-2 mb-2">
                <Icon name="i-heroicons-crown" class="w-4 h-4 text-blue-600" />
                <span class="font-medium text-blue-700 dark:text-blue-300">เจ้าของ (Owner)</span>
              </div>
              <p class="text-xs text-blue-600 dark:text-blue-400">มีสิทธิ์สูงสุดในทีม จัดการทุกอย่างได้ รวมถึงการลบทีมและจัดการบทบาทอื่น ๆ</p>
            </div>
            
            <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div class="flex items-center gap-2 mb-2">
                <Icon name="i-heroicons-shield-check" class="w-4 h-4 text-green-600" />
                <span class="font-medium text-green-700 dark:text-green-300">ผู้ดูแล (Admin)</span>
              </div>
              <p class="text-xs text-green-600 dark:text-green-400">จัดการสมาชิกและตั้งค่าทีมได้ แต่ไม่สามารถลบทีมได้</p>
            </div>
            
            <div class="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <div class="flex items-center gap-2 mb-2">
                <Icon name="i-heroicons-pencil-square" class="w-4 h-4 text-yellow-600" />
                <span class="font-medium text-yellow-700 dark:text-yellow-300">บรรณาธิการ (Editor)</span>
              </div>
              <p class="text-xs text-yellow-600 dark:text-yellow-400">แก้ไขเนื้อหาและข้อมูลในทีมได้ แต่ไม่สามารถจัดการสมาชิกหรือการตั้งค่าทีม</p>
            </div>
            
            <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="flex items-center gap-2 mb-2">
                <Icon name="i-heroicons-eye" class="w-4 h-4 text-gray-600" />
                <span class="font-medium text-gray-700 dark:text-gray-300">ผู้ใช้งานทั่วไป (Viewer)</span>
              </div>
              <p class="text-xs text-gray-600 dark:text-gray-400">ดูข้อมูลในทีมได้เท่านั้น ไม่มีสิทธิ์แก้ไขหรือจัดการใด ๆ</p>
            </div>
          </div>
        </div>
      </div>
    </UCard>




  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-6>* {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Card hover effects */
.card:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease;
}
</style>