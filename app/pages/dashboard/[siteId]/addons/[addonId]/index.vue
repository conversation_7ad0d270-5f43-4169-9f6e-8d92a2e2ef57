<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);
const addonId = computed(() => route.params.addonId as string);

// Use composables

// Reactive data
const addon = ref<any>(null);
const loading = ref(false);
const error = ref<string | null>(null);
const isInstalled = ref(false);
const installing = ref(false);

// Mock addon data (จะแทนที่ด้วย API จริง)
const mockAddon = {
  id: '6',
  name: 'Novel System',
  description: 'ระบบจัดการนิยายแบบตอน พร้อมระบบการชำระเงินและการจัดการเนื้อหา',
  version: '1.0.0',
  author: 'WebShop Team',
  price: 299,
  currency: 'THB',
  category: 'Content Management',
  tags: ['นิยาย', 'การจัดการเนื้อหา', 'การชำระเงิน'],
  features: [
    'สร้างและจัดการนิยายแบบตอน',
    'ระบบการชำระเงินสำหรับแต่ละตอน',
    'การจัดการผู้เขียนและผู้ใช้',
    'ระบบการแสดงผลที่สวยงาม',
    'การจัดการแท็กและหมวดหมู่',
    'ระบบการค้นหาและกรอง',
    'สถิติการอ่านและการขาย',
    'การตั้งค่า SEO สำหรับแต่ละตอน',
  ],
  requirements: ['WebShop Platform 1.0+', 'Node.js 18+', 'MongoDB 5.0+'],
  changelog: [
    {
      version: '1.0.0',
      date: '2024-01-15',
      changes: [
        'เปิดตัวระบบ Novel System',
        'รองรับการสร้างนิยายแบบตอน',
        'ระบบการชำระเงินพื้นฐาน',
        'การจัดการผู้เขียนและผู้ใช้',
      ],
    },
  ],
  screenshots: [
    '/images/addons/novel-1.png',
    '/images/addons/novel-2.png',
    '/images/addons/novel-3.png',
  ],
  rating: 4.8,
  reviews: 156,
  downloads: 1234,
  lastUpdated: '2024-01-15',
  size: '2.5 MB',
  license: 'Commercial',
  support: 'Email, Live Chat',
  documentation: 'https://docs.webshop.com/novel-system',
};

// Fetch addon details
const fetchAddonDetails = async () => {
  loading.value = true;
  error.value = null;
  try {
    // Mock API call - แทนที่ด้วย API จริง
    await new Promise(resolve => setTimeout(resolve, 1000));
    addon.value = mockAddon;

    // Check if installed
    isInstalled.value = false; // แทนที่ด้วยการตรวจสอบจาก API
  } catch (err) {
    error.value = 'เกิดข้อผิดพลาดในการโหลดข้อมูล';
    console.error('Error fetching addon details:', err);
  } finally {
    loading.value = false;
  }
};

// Install addon
const installAddon = async () => {
  installing.value = true;
  try {
    const response = await $fetch(`/api/v1/site/${siteId.value}/addons/${addonId.value}/install`, {
      method: 'POST',
      body: {
        siteId: siteId.value,
        addonId: addonId.value,
      },
    });

    if (response?.success) {
      isInstalled.value = true;
      // Show success message
      console.log('Addon installed successfully');
    }
  } catch (err) {
    console.error('Error installing addon:', err);
    error.value = 'เกิดข้อผิดพลาดในการติดตั้ง addon';
  } finally {
    installing.value = false;
  }
};

// Uninstall addon
const uninstallAddon = async () => {
  if (confirm('คุณต้องการถอนการติดตั้ง addon นี้ใช่หรือไม่?')) {
    try {
      const response = await $fetch(
        `/api/v1/site/${siteId.value}/addons/${addonId.value}/uninstall`,
        {
          method: 'POST',
          body: {
            siteId: siteId.value,
            addonId: addonId.value,
            userId: 'test-user',
          },
        }
      );

      if (response?.success) {
        isInstalled.value = false;
        console.log('Addon uninstalled successfully');
      }
    } catch (err) {
      console.error('Error uninstalling addon:', err);
      error.value = 'เกิดข้อผิดพลาดในการถอนการติดตั้ง addon';
    }
  }
};

// Format price
const formatPrice = (price: number, currency: string) => {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: currency,
  }).format(price);
};

// Format date
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

// Get rating stars
const getRatingStars = (rating: number) => {
  const stars = [];
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;

  for (let i = 0; i < fullStars; i++) {
    stars.push('full');
  }

  if (hasHalfStar) {
    stars.push('half');
  }

  const emptyStars = 5 - stars.length;
  for (let i = 0; i < emptyStars; i++) {
    stars.push('empty');
  }

  return stars;
};

// Fetch data on mount
onMounted(async () => {
  await fetchAddonDetails();
});

// SEO
useSeoMeta({
  title: () => `${addon.value?.name || 'Addon'} - รายละเอียด Addon`,
  description: () => addon.value?.description || 'รายละเอียด addon สำหรับระบบจัดการร้านค้า',
  keywords: 'addon, plugin, extension, webshop',
  ogTitle: () => `${addon.value?.name || 'Addon'} - รายละเอียด Addon`,
  ogDescription: () => addon.value?.description || 'รายละเอียด addon สำหรับระบบจัดการร้านค้า',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div class="space-y-8">
    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="flex items-center gap-3">
        <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-blue-600" />
        <span class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</span>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-12">
      <div class="text-red-600 dark:text-red-400 mb-4">
        <Icon name="i-heroicons-exclamation-triangle" class="w-12 h-12 mx-auto" />
      </div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">เกิดข้อผิดพลาด</h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">{{ error }}</p>
      <UButton @click="fetchAddonDetails()" variant="outline">
        ลองใหม่
      </UButton>
    </div>

    <!-- Addon Details -->
    <div v-else-if="addon" class="space-y-8">
      <!-- Header -->
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div class="flex items-start gap-6">
          <!-- Addon Icon -->
          <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center text-white text-2xl font-bold shadow-lg">
            {{ addon.name.charAt(0) }}
          </div>
          
          <!-- Addon Info -->
          <div class="flex-1">
            <div class="flex items-center gap-3 mb-2">
              <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ addon.name }}</h1>
              <UBadge color="primary" variant="subtle">{{ addon.version }}</UBadge>
              <UBadge v-if="isInstalled" color="success" variant="subtle">ติดตั้งแล้ว</UBadge>
            </div>
            <p class="text-lg text-gray-600 dark:text-gray-400 mb-3">{{ addon.description }}</p>
            <div class="flex items-center gap-6 text-sm text-gray-500 dark:text-gray-400">
              <span class="flex items-center gap-1">
                <Icon name="i-heroicons-star" class="w-4 h-4 text-yellow-500" />
                {{ addon.rating }} ({{ addon.reviews }} รีวิว)
              </span>
              <span class="flex items-center gap-1">
                <Icon name="i-heroicons-arrow-down-tray" class="w-4 h-4" />
                {{ addon.downloads }} ดาวน์โหลด
              </span>
              <span class="flex items-center gap-1">
                <Icon name="i-heroicons-calendar" class="w-4 h-4" />
                อัปเดตล่าสุด {{ formatDate(addon.lastUpdated) }}
              </span>
            </div>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex items-center gap-3">
          <UButton
            v-if="!isInstalled"
            @click="installAddon"
            :loading="installing"
            class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
            ติดตั้ง Addon
          </UButton>
          <UButton
            v-else
            @click="uninstallAddon"
            variant="outline"
            color="error"
            class="hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <Icon name="i-heroicons-trash" class="w-5 h-5 mr-2" />
            ถอนการติดตั้ง
          </UButton>
          <UButton
            variant="outline"
            class="hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <Icon name="i-heroicons-heart" class="w-5 h-5 mr-2" />
            เพิ่มในรายการโปรด
          </UButton>
        </div>
      </div>

      <!-- Price Card -->
      <UCard class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-blue-200 dark:border-blue-700">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">ราคา</h3>
            <p class="text-3xl font-bold text-blue-600 dark:text-blue-400">
              {{ formatPrice(addon.price, addon.currency) }}
            </p>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">ราคาเดียว ไม่มีค่าใช้จ่ายเพิ่มเติม</p>
          </div>
          <div class="text-right">
            <p class="text-sm text-gray-600 dark:text-gray-400">ขนาดไฟล์</p>
            <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ addon.size }}</p>
          </div>
        </div>
      </UCard>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Column -->
        <div class="lg:col-span-2 space-y-8">
          <!-- Features -->
          <UCard class="overflow-hidden">
            <template #header>
              <div class="flex items-center gap-3">
                <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                  <Icon name="i-heroicons-sparkles" class="w-5 h-5 text-white" />
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ฟีเจอร์หลัก</h2>
              </div>
            </template>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div 
                v-for="feature in addon.features" 
                :key="feature"
                class="flex items-start gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <Icon name="i-heroicons-check-circle" class="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span class="text-gray-700 dark:text-gray-300">{{ feature }}</span>
              </div>
            </div>
          </UCard>

          <!-- Screenshots -->
          <UCard class="overflow-hidden">
            <template #header>
              <div class="flex items-center gap-3">
                <div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
                  <Icon name="i-heroicons-photo" class="w-5 h-5 text-white" />
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ภาพตัวอย่าง</h2>
              </div>
            </template>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div 
                v-for="(screenshot, index) in addon.screenshots" 
                :key="index"
                class="aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center hover:scale-105 transition-transform cursor-pointer"
              >
                <Icon name="i-heroicons-photo" class="w-12 h-12 text-gray-400" />
              </div>
            </div>
          </UCard>

          <!-- Changelog -->
          <UCard class="overflow-hidden">
            <template #header>
              <div class="flex items-center gap-3">
                <div class="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
                  <Icon name="i-heroicons-document-text" class="w-5 h-5 text-white" />
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ประวัติการอัปเดต</h2>
              </div>
            </template>
            
            <div class="space-y-6">
              <div 
                v-for="version in addon.changelog" 
                :key="version.version"
                class="border-l-4 border-blue-500 pl-6"
              >
                <div class="flex items-center gap-3 mb-3">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">เวอร์ชัน {{ version.version }}</h3>
                  <UBadge color="primary" variant="subtle">{{ formatDate(version.date) }}</UBadge>
                </div>
                <ul class="space-y-2">
                  <li 
                    v-for="change in version.changes" 
                    :key="change"
                    class="flex items-start gap-2 text-gray-600 dark:text-gray-400"
                  >
                    <Icon name="i-heroicons-plus-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>{{ change }}</span>
                  </li>
                </ul>
              </div>
            </div>
          </UCard>
        </div>

        <!-- Right Sidebar -->
        <div class="space-y-6">
          <!-- System Requirements -->
          <UCard class="overflow-hidden">
            <template #header>
              <div class="flex items-center gap-3">
                <div class="p-2 bg-gradient-to-r from-red-500 to-red-600 rounded-lg">
                  <Icon name="i-heroicons-cog-6-tooth" class="w-5 h-5 text-white" />
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ความต้องการของระบบ</h2>
              </div>
            </template>
            
            <div class="space-y-3">
              <div 
                v-for="requirement in addon.requirements" 
                :key="requirement"
                class="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
              >
                <Icon name="i-heroicons-check-circle" class="w-5 h-5 text-green-500" />
                <span class="text-gray-700 dark:text-gray-300">{{ requirement }}</span>
              </div>
            </div>
          </UCard>

          <!-- Addon Info -->
          <UCard class="overflow-hidden">
            <template #header>
              <div class="flex items-center gap-3">
                <div class="p-2 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg">
                  <Icon name="i-heroicons-information-circle" class="w-5 h-5 text-white" />
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ข้อมูล Addon</h2>
              </div>
            </template>
            
            <div class="space-y-4">
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">ผู้พัฒนา</span>
                <span class="font-medium text-gray-900 dark:text-white">{{ addon.author }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">หมวดหมู่</span>
                <span class="font-medium text-gray-900 dark:text-white">{{ addon.category }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">ลิขสิทธิ์</span>
                <span class="font-medium text-gray-900 dark:text-white">{{ addon.license }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">การสนับสนุน</span>
                <span class="font-medium text-gray-900 dark:text-white">{{ addon.support }}</span>
              </div>
            </div>
          </UCard>

          <!-- Tags -->
          <UCard class="overflow-hidden">
            <template #header>
              <div class="flex items-center gap-3">
                <div class="p-2 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg">
                  <Icon name="i-heroicons-tag" class="w-5 h-5 text-white" />
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">แท็ก</h2>
              </div>
            </template>
            
            <div class="flex flex-wrap gap-2">
              <UBadge
                v-for="tag in addon.tags"
                :key="tag"
                color="primary"
                variant="subtle"
                class="cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900/20"
              >
                {{ tag }}
              </UBadge>
            </div>
          </UCard>

          <!-- Quick Actions -->
          <UCard class="overflow-hidden">
            <template #header>
              <div class="flex items-center gap-3">
                <div class="p-2 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg">
                  <Icon name="i-heroicons-bolt" class="w-5 h-5 text-white" />
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">การดำเนินการด่วน</h2>
              </div>
            </template>
            
            <div class="space-y-3">
              <UButton
                variant="outline"
                size="sm"
                class="w-full justify-start"
              >
                <Icon name="i-heroicons-document-text" class="w-4 h-4 mr-2" />
                ดูเอกสาร
              </UButton>
              
              <UButton
                variant="outline"
                size="sm"
                class="w-full justify-start"
              >
                <Icon name="i-heroicons-question-mark-circle" class="w-4 h-4 mr-2" />
                ขอความช่วยเหลือ
              </UButton>
              
              <UButton
                variant="outline"
                size="sm"
                class="w-full justify-start"
              >
                <Icon name="i-heroicons-chat-bubble-left-right" class="w-4 h-4 mr-2" />
                รายงานปัญหา
              </UButton>
              
              <UButton
                variant="outline"
                size="sm"
                class="w-full justify-start"
              >
                <Icon name="i-heroicons-star" class="w-4 h-4 mr-2" />
                ให้คะแนน
              </UButton>
            </div>
          </UCard>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
</style> 