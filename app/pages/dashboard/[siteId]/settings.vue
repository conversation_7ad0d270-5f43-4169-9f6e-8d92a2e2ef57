<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route: any = useRoute();
const siteId = computed(() => route.params.siteId);

// ใช้ useSites สำหรับจัดการข้อมูลเว็บไซต์
const { getSite, updateSite, updateSEOSettings, updateSettings } = useSites();
const { updateMaintenanceMode } = useSite();

// ดึงข้อมูลเว็บไซต์
const { data: siteData, refresh: refreshSite } = await useLazyAsyncData(
  `site-${siteId.value}`,
  () => getSite(siteId.value as string)
);

const currentSite = computed(() => siteData.value?.data);
const isPremium = computed(() => currentSite.value?.isPremium || false);
const siteFeatures = computed(() => currentSite.value?.settings?.features);

// Form data
const siteInfo = ref({
  name: '',
  description: '',
  logo: '',
  favicon: '',
});

const seoSettings = ref({
  title: '',
  description: '',
  keywords: [] as string[],
  ogImage: '',
});

// Keywords input helper
const keywordsInput = ref('');

// Update keywords from input
const updateKeywords = () => {
  if (keywordsInput.value.trim()) {
    seoSettings.value.keywords = keywordsInput.value
      .split(',')
      .map(keyword => keyword.trim())
      .filter(keyword => keyword.length > 0);
  } else {
    seoSettings.value.keywords = [];
  }
};

// Watch for keywords changes to update input
watch(() => seoSettings.value.keywords, (newKeywords) => {
  keywordsInput.value = newKeywords.join(', ');
}, { immediate: true });

// Maintenance Mode
const maintenanceMode = ref({
  isEnabled: false,
  message: 'เว็บไซต์กำลังอยู่ระหว่างการบำรุงรักษา กรุณาลองใหม่อีกครั้งในภายหลัง',
  allowedIPs: [] as string[],
  scheduledStart: null,
  scheduledEnd: null,
  showMaintenancePage: true,
});

// Initialize form data from site data
watch(currentSite, (site) => {
  if (site) {
    siteInfo.value = {
      name: site.name || '',
      description: site.description || '',
      logo: site.logo || '',
      favicon: site.favicon || '',
    };

    seoSettings.value = {
      title: site.settings?.seo?.title || '',
      description: site.settings?.seo?.description || '',
      keywords: site.settings?.seo?.keywords || [],
      ogImage: site.settings?.seo?.ogImage || '',
    };

    maintenanceMode.value = {
      isEnabled: site.settings?.maintenance?.isEnabled || false,
      message: site.settings?.maintenance?.message || 'เว็บไซต์กำลังอยู่ระหว่างการบำรุงรักษา กรุณาลองใหม่อีกครั้งในภายหลัง',
      allowedIPs: site.settings?.maintenance?.allowedIPs || [],
      scheduledStart: site.settings?.maintenance?.scheduledStart || null as any,
      scheduledEnd: site.settings?.maintenance?.scheduledEnd || null as any,
      showMaintenancePage: site.settings?.maintenance?.showMaintenancePage || true,
    };
  }
}, { immediate: true });

// Loading states
const savingSiteInfo = ref(false);
const savingSEO = ref(false);
const savingMaintenance = ref(false);

// Save site info
async function saveSiteInfo() {
  savingSiteInfo.value = true;
  try {
    await updateSite(siteId.value as string, siteInfo.value);
    await refreshSite();

    useToast().add({
      title: 'สำเร็จ',
      description: 'บันทึกข้อมูลเว็บไซต์เรียบร้อยแล้ว',
      color: 'success',
    });
  } catch (error: any) {
    useToast().add({
      title: 'ผิดพลาด',
      description: error.message || 'ไม่สามารถบันทึกข้อมูลเว็บไซต์ได้',
      color: 'error',
    });
  } finally {
    savingSiteInfo.value = false;
  }
}

// Save SEO settings
async function saveSEOSettings() {
  savingSEO.value = true;
  try {
    await updateSEOSettings(siteId.value as string, seoSettings.value);
    await refreshSite();

    useToast().add({
      title: 'สำเร็จ',
      description: 'บันทึกการตั้งค่า SEO เรียบร้อยแล้ว',
      color: 'success',
    });
  } catch (error: any) {
    useToast().add({
      title: 'ผิดพลาด',
      description: error.message || 'ไม่สามารถบันทึกการตั้งค่า SEO ได้',
      color: 'error',
    });
  } finally {
    savingSEO.value = false;
  }
}

// Toggle feature
async function handleToggleFeature(feature: string, enabled: boolean) {
  try {
    const currentFeatures = currentSite.value?.settings?.features || {
      blog: false,
      ecommerce: false,
      membership: false,
      analytics: false,
    };
    const updatedFeatures = {
      ...currentFeatures,
      [feature]: enabled,
    };

    await updateSettings(siteId.value as string, {
      features: updatedFeatures,
    });
    await refreshSite();

    useToast().add({
      title: 'สำเร็จ',
      description: `${enabled ? 'เปิด' : 'ปิด'}ใช้งาน${getFeatureLabel(feature)}เรียบร้อยแล้ว`,
      color: 'success',
    });
  } catch (error: any) {
    useToast().add({
      title: 'ผิดพลาด',
      description: error.message || 'ไม่สามารถเปลี่ยนการตั้งค่าฟีเจอร์ได้',
      color: 'error',
    });
  }
}

// Get feature label
function getFeatureLabel(feature: string) {
  const labels: Record<string, string> = {
    blog: 'บล็อก',
    ecommerce: 'ร้านค้าออนไลน์',
    membership: 'ระบบสมาชิก',
    analytics: 'การวิเคราะห์',
  };
  return labels[feature] || feature;
}

// Save maintenance mode
async function saveMaintenanceMode() {
  savingMaintenance.value = true;
  try {
    await updateMaintenanceMode(siteId.value, maintenanceMode.value);

    useToast().add({
      title: 'สำเร็จ',
      description: 'บันทึกโหมดบำรุงรักษาเรียบร้อยแล้ว',
      color: 'success',
    });
  } catch (error: any) {
    useToast().add({
      title: 'ผิดพลาด',
      description: error.message || 'ไม่สามารถบันทึกโหมดบำรุงรักษาได้',
      color: 'error',
    });
  } finally {
    savingMaintenance.value = false;
  }
}

// SEO
useSeoMeta({
  title: 'การตั้งค่า - ระบบจัดการร้านค้า',
  description: 'จัดการการตั้งค่าเว็บไซต์ สถานะ และโหมดบำรุงรักษา',
  keywords: 'การตั้งค่า, สถานะเว็บไซต์, โหมดบำรุงรักษา, ระบบจัดการ',
  ogTitle: 'การตั้งค่า - ระบบจัดการร้านค้า',
  ogDescription: 'จัดการการตั้งค่าเว็บไซต์ สถานะ และโหมดบำรุงรักษา',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div class="max-w-6xl mx-auto space-y-8">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          การตั้งค่า
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          จัดการสถานะเว็บไซต์และโหมดบำรุงรักษา
        </p>
      </div>
    </div>

    <!-- Settings Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

      <!-- Site Information -->
      <UCard variant="soft">
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
              <Icon name="solar:global-line-duotone" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ข้อมูลเว็บไซต์</h2>
          </div>
        </template>

        <div class="space-y-6">
          <!-- Site Name -->
          <UFormField label="ชื่อเว็บไซต์" required>
            <UInput v-model="siteInfo.name" placeholder="ชื่อเว็บไซต์ของคุณ" icon="solar:global-line-duotone" />
          </UFormField>

          <!-- Site Description -->
          <UFormField label="คำอธิบายเว็บไซต์">
            <UTextarea v-model="siteInfo.description" placeholder="คำอธิบายสั้นๆ เกี่ยวกับเว็บไซต์ของคุณ" :rows="3" />
          </UFormField>

          <!-- Logo URL -->
          <UFormField label="โลโก้เว็บไซต์">
            <UInput v-model="siteInfo.logo" placeholder="https://example.com/logo.png"
              icon="solar:gallery-line-duotone" />
          </UFormField>

          <!-- Favicon URL -->
          <UFormField label="Favicon">
            <UInput v-model="siteInfo.favicon" placeholder="https://example.com/favicon.ico"
              icon="solar:star-line-duotone" />
          </UFormField>

          <!-- Site Status -->
          <div v-if="currentSite" class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div class="flex items-center justify-between mb-2">
              <span class="font-medium text-gray-900 dark:text-white">สถานะเว็บไซต์</span>
              <UBadge :color="currentSite.isActive ? 'success' : 'error'" variant="subtle">
                {{ currentSite.isActive ? 'เปิดใช้งาน' : 'ปิดใช้งาน' }}
              </UBadge>
            </div>
            <div class="flex items-center justify-between">
              <span class="font-medium text-gray-900 dark:text-white">แพ็กเกจ</span>
              <UBadge :color="isPremium ? 'warning' : 'neutral'" variant="subtle">
                {{ isPremium ? 'Premium' : 'Free' }}
              </UBadge>
            </div>
          </div>

          <!-- Save Button -->
          <UButton @click="saveSiteInfo" :loading="savingSiteInfo" icon="solar:diskette-line-duotone" size="xl"
            color="primary" :label="savingSiteInfo ? 'กำลังบันทึก...' : 'บันทึกข้อมูลเว็บไซต์'" />
        </div>
      </UCard>

      <!-- SEO Settings -->
      <UCard variant="soft">
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <Icon name="solar:magnifer-line-duotone" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">การตั้งค่า SEO</h2>
          </div>
        </template>

        <div class="space-y-6">
          <!-- SEO Title -->
          <UFormField label="หัวข้อ SEO" required>
            <UInput v-model="seoSettings.title" placeholder="หัวข้อที่จะแสดงใน Google Search"
              icon="solar:text-field-line-duotone" />
          </UFormField>

          <!-- SEO Description -->
          <UFormField label="คำอธิบาย SEO" required>
            <UTextarea v-model="seoSettings.description"
              placeholder="คำอธิบายที่จะแสดงใน Google Search (แนะนำ 150-160 ตัวอักษร)" :rows="3" />
          </UFormField>

          <!-- Keywords -->
          <UFormField label="คำสำคัญ (Keywords)">
            <UInput v-model="keywordsInput" placeholder="คำสำคัญ1, คำสำคัญ2, คำสำคัญ3" icon="solar:hashtag-line-duotone"
              @blur="updateKeywords" />
            <template #help>
              <span class="text-xs text-gray-500">แยกคำสำคัญด้วยเครื่องหมายจุลภาค (,)</span>
            </template>
          </UFormField>

          <!-- OG Image -->
          <UFormField label="รูปภาพ Open Graph">
            <UInput v-model="seoSettings.ogImage" placeholder="https://example.com/og-image.jpg"
              icon="solar:gallery-line-duotone" />
            <template #help>
              <span class="text-xs text-gray-500">รูปภาพที่จะแสดงเมื่อแชร์ใน Social Media (แนะนำ 1200x630px)</span>
            </template>
          </UFormField>

          <!-- Save Button -->
          <UButton @click="saveSEOSettings" :loading="savingSEO" icon="solar:diskette-line-duotone" size="xl"
            color="success" :label="savingSEO ? 'กำลังบันทึก...' : 'บันทึกการตั้งค่า SEO'" />
        </div>
      </UCard>

      <!-- Features -->
      <UCard variant="soft">
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
              <Icon name="solar:widget-line-duotone" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ฟีเจอร์เว็บไซต์</h2>
          </div>
        </template>

        <div class="space-y-4">
          <!-- Blog Feature -->
          <div
            class="flex items-center justify-between p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
            <div>
              <h3 class="font-semibold text-purple-900 dark:text-purple-100">ระบบบล็อก</h3>
              <p class="text-sm text-purple-800 dark:text-purple-200">
                เปิดใช้งานระบบเขียนบล็อกและจัดการเนื้อหา
              </p>
            </div>
            <USwitch :checked="siteFeatures?.blog ?? false"
              @update:model-value="(value:any) => handleToggleFeature('blog', value)" label="เปิดใช้งานระบบบล็อก" />
          </div>

          <!-- E-commerce Feature -->
          <div
            class="flex items-center justify-between p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
            <div>
              <h3 class="font-semibold text-purple-900 dark:text-purple-100">ร้านค้าออนไลน์</h3>
              <p class="text-sm text-purple-800 dark:text-purple-200">
                เปิดใช้งานระบบขายสินค้าออนไลน์
              </p>
            </div>
            <USwitch :checked="siteFeatures?.ecommerce ?? false"
              @update:model-value="(value:any) => handleToggleFeature('ecommerce', value)"
              label="เปิดใช้งานระบบขายสินค้าออนไลน์" />
          </div>

          <!-- Membership Feature -->
          <div
            class="flex items-center justify-between p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
            <div>
              <h3 class="font-semibold text-purple-900 dark:text-purple-100">ระบบสมาชิก</h3>
              <p class="text-sm text-purple-800 dark:text-purple-200">
                เปิดใช้งานระบบสมาชิกและการจัดการผู้ใช้
              </p>
            </div>
            <USwitch :checked="siteFeatures?.membership ?? false"
              @update:model-value="(value:any) => handleToggleFeature('membership', value)" label="เปิดใช้งานระบบสมาชิก" />
          </div>

          <!-- Analytics Feature -->
          <div
            class="flex items-center justify-between p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
            <div>
              <h3 class="font-semibold text-purple-900 dark:text-purple-100">การวิเคราะห์</h3>
              <p class="text-sm text-purple-800 dark:text-purple-200">
                เปิดใช้งานระบบวิเคราะห์และรายงาน
              </p>
            </div>
            <USwitch :checked="siteFeatures?.analytics ?? false"
              @update:model-value="(value:any) => handleToggleFeature('analytics', value)"
              label="เปิดใช้งานระบบวิเคราะห์" />
          </div>
        </div>
      </UCard>

      <!-- Maintenance Mode -->
      <UCard variant="soft">
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
              <Icon name="i-heroicons-wrench-screwdriver" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">โหมดบำรุงรักษา</h2>
          </div>
        </template>

        <div class="space-y-6">
          <!-- Enable/Disable Maintenance -->
          <div
            class="flex items-center justify-between p-4 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
            <div>
              <h3 class="font-semibold text-orange-900 dark:text-orange-100">เปิดโหมดบำรุงรักษา</h3>
              <p class="text-sm text-orange-800 dark:text-orange-200">
                แสดงหน้าบำรุงรักษาให้ผู้เยี่ยมชม
              </p>
            </div>
            <UCheckbox v-model="maintenanceMode.isEnabled" />
          </div>

          <!-- Maintenance Message -->
          <UFormField label="ข้อความบำรุงรักษา">
            <UTextarea v-model="maintenanceMode.message" placeholder="ข้อความที่จะแสดงให้ผู้เยี่ยมชมทราบ" :rows="4"
              class="w-full" />
          </UFormField>

          <!-- Save Button -->
          <UButton @click="saveMaintenanceMode" :loading="savingMaintenance" icon="solar:diskette-line-duotone"
            size="xl" color="primary" :label="savingMaintenance ? 'กำลังบันทึก...' : 'บันทึก'" />

        </div>
      </UCard>
    </div>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8>* {
  animation: fadeInUp 0.6s ease-out forwards;
}
</style>