<script setup lang="ts">
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);
const id = route.params.id;
const form = ref({
  name: 'ผู้จัดการ',
  description: 'จัดการสินค้าและคำสั่งซื้อ',
  permissions: [],
});
const isSubmitting = ref(false);
const toast = useToast();

const handleSubmit = async () => {
  isSubmitting.value = true;
  // TODO: เรียก API เพื่อบันทึกการแก้ไข role จริง
  setTimeout(() => {
    toast.add({
      title: 'บันทึกสำเร็จ',
      description: `บันทึกบทบาท ${form.value.name} สำหรับเว็บไซต์ ${siteId.value} เรียบร้อยแล้ว`,
      color: 'success',
    });
    isSubmitting.value = false;
  }, 1000);
};
</script>
<template>
  <div class="p-6 max-w-xl mx-auto">
    <h1 class="text-2xl font-bold mb-4">แก้ไขบทบาท</h1>
    <form @submit.prevent="handleSubmit" class="space-y-4">
      <UFormField label="ชื่อบทบาท" required>
        <UInput v-model="form.name" required />
      </UFormField>
      <UFormField label="รายละเอียด">
        <UTextarea v-model="form.description" />
      </UFormField>
      <div class="mt-4 flex gap-2">
        <UButton type="submit" :loading="isSubmitting" color="primary">บันทึก</UButton>
        <UButton to="../../roles" variant="outline">ยกเลิก</UButton>
      </div>
    </form>
  </div>
</template> 