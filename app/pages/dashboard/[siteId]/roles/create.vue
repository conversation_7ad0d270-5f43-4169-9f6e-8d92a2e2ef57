<script setup lang="ts">
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);
const form = ref({
  name: '',
  description: '',
  permissions: [],
});
const isSubmitting = ref(false);
const toast = useToast();

const handleSubmit = async () => {
  isSubmitting.value = true;
  // TODO: เรียก API เพื่อสร้าง role จริง
  setTimeout(() => {
    toast.add({
      title: 'สร้างบทบาทสำเร็จ',
      description: `สร้างบทบาท ${form.value.name} สำหรับเว็บไซต์ ${siteId.value} เรียบร้อยแล้ว`,
      color: 'success',
    });
    isSubmitting.value = false;
  }, 1000);
};
</script>
<template>
  <div class="p-6 max-w-xl mx-auto">
    <h1 class="text-2xl font-bold mb-4">สร้างบทบาทใหม่</h1>
    <form @submit.prevent="handleSubmit" class="space-y-4">
      <UFormField label="ชื่อบทบาท" required>
        <UInput v-model="form.name" placeholder="เช่น ผู้จัดการ" required />
      </UFormField>
      <UFormField label="รายละเอียด">
        <UTextarea v-model="form.description" placeholder="รายละเอียดเพิ่มเติม (ถ้ามี)" />
      </UFormField>
      <div class="mt-4 flex gap-2">
        <UButton type="submit" :loading="isSubmitting" color="primary">สร้างบทบาท</UButton>
        <UButton to="../roles" variant="outline">ยกเลิก</UButton>
      </div>
    </form>
  </div>
</template> 