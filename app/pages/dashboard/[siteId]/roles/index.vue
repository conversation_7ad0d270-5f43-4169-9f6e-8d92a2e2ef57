<script setup lang="ts">
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);
const roles = ref([
  {
    id: '1',
    name: 'ผู้ดูแลระบบ',
    description: 'สิทธิ์เต็มในการจัดการระบบ',
    permissions: 15,
    users: 2,
  },
  {
    id: '2',
    name: 'ผู้จัดการ',
    description: 'จัดการสินค้าและคำสั่งซื้อ',
    permissions: 8,
    users: 5,
  },
  {
    id: '3',
    name: 'พนักงานขาย',
    description: 'ดูข้อมูลและจัดการลูกค้า',
    permissions: 5,
    users: 12,
  },
]);
</script>
<template>
  <div class="p-6 max-w-2xl mx-auto">
    <div class="flex items-center justify-between mb-4">
      <h1 class="text-2xl font-bold">บทบาทและสิทธิ์</h1>
      <UButton :to="`/dashboard/${siteId.value}/roles/create`" color="primary">สร้างบทบาทใหม่</UButton>
    </div>
    <UTable :rows="roles" :columns="[
      { key: 'name', label: 'ชื่อบทบาท' },
      { key: 'description', label: 'รายละเอียด' },
      { key: 'permissions', label: 'สิทธิ์' },
      { key: 'users', label: 'ผู้ใช้' },
      { key: 'actions', label: 'การจัดการ' }
    ]">
      <template #actions="{ row }">
        <div class="flex items-center gap-2">
          <UButton :to="`/dashboard/${siteId.value}/roles/${row.id}/edit`" size="sm" variant="outline">แก้ไข</UButton>
          <UButton size="sm" variant="outline" color="error">ลบ</UButton>
        </div>
      </template>
    </UTable>
  </div>
</template> 