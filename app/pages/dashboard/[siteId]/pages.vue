<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// Reactive data
const pages = ref<any[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);

// Filters and search
const searchQuery = ref('');
const selectedStatus = ref('all');
const selectedType = ref('all');
const sortBy = ref('created_at');
const sortOrder = ref('desc');
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Filter options
const statusOptions = ref([
  { value: 'all', label: 'ทุกสถานะ' },
  { value: 'published', label: 'เผยแพร่' },
  { value: 'draft', label: 'ร่าง' },
  { value: 'archived', label: 'เก็บถาวร' },
]);

const typeOptions = ref([
  { value: 'all', label: 'ทุกประเภท' },
  { value: 'page', label: 'หน้าเพจ' },
  { value: 'post', label: 'บทความ' },
  { value: 'landing', label: 'หน้า Landing' },
  { value: 'custom', label: 'หน้าแบบกำหนดเอง' },
]);

const sortOptions = ref([
  { value: 'created_at', label: 'วันที่สร้าง' },
  { value: 'title', label: 'ชื่อหน้า' },
  { value: 'views', label: 'จำนวนผู้เข้าชม' },
  { value: 'updated_at', label: 'วันที่แก้ไข' },
]);

// Analytics data
const analytics = ref({
  totalPages: 24,
  publishedPages: 18,
  draftPages: 6,
  totalViews: 15420,
  averageViews: 642,
  topPage: 'หน้าแรก',
  growthRate: 8.5,
});

// Mock pages data
const mockPages = [
  {
    id: 1,
    title: 'หน้าแรก',
    slug: 'home',
    type: 'page',
    status: 'published',
    views: 3240,
    author: 'ผู้ดูแลระบบ',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-15',
    excerpt: 'หน้าแรกของเว็บไซต์',
  },
  {
    id: 2,
    title: 'เกี่ยวกับเรา',
    slug: 'about',
    type: 'page',
    status: 'published',
    views: 1250,
    author: 'ผู้ดูแลระบบ',
    createdAt: '2024-01-02',
    updatedAt: '2024-01-10',
    excerpt: 'ข้อมูลเกี่ยวกับบริษัทและทีมงาน',
  },
  {
    id: 3,
    title: 'ติดต่อเรา',
    slug: 'contact',
    type: 'page',
    status: 'published',
    views: 980,
    author: 'ผู้ดูแลระบบ',
    createdAt: '2024-01-03',
    updatedAt: '2024-01-12',
    excerpt: 'ข้อมูลการติดต่อและแผนที่',
  },
  {
    id: 4,
    title: 'นโยบายความเป็นส่วนตัว',
    slug: 'privacy-policy',
    type: 'page',
    status: 'published',
    views: 450,
    author: 'ผู้ดูแลระบบ',
    createdAt: '2024-01-05',
    updatedAt: '2024-01-08',
    excerpt: 'นโยบายการคุ้มครองข้อมูลส่วนบุคคล',
  },
  {
    id: 5,
    title: 'ข้อกำหนดการใช้งาน',
    slug: 'terms-of-service',
    type: 'page',
    status: 'draft',
    views: 0,
    author: 'ผู้ดูแลระบบ',
    createdAt: '2024-01-10',
    updatedAt: '2024-01-10',
    excerpt: 'ข้อกำหนดและเงื่อนไขการใช้งานเว็บไซต์',
  },
];

// Format number
const formatNumber = (num: number) => {
  return new Intl.NumberFormat('th-TH').format(num);
};

// Get status badge class
const getStatusBadgeClass = (status: string) => {
  const classes = {
    published: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    draft: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    archived: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
  };
  return classes[status as keyof typeof classes] || classes.draft;
};

// Get status label
const getStatusLabel = (status: string) => {
  const labels = {
    published: 'เผยแพร่',
    draft: 'ร่าง',
    archived: 'เก็บถาวร',
  };
  return labels[status as keyof typeof labels] || status;
};

// Get type label
const getTypeLabel = (type: string) => {
  const labels = {
    page: 'หน้าเพจ',
    post: 'บทความ',
    landing: 'หน้า Landing',
    custom: 'หน้าแบบกำหนดเอง',
  };
  return labels[type as keyof typeof labels] || type;
};

// Computed filtered pages
const filteredPages = computed(() => {
  let filtered = pages.value || [];

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(
      page =>
        page.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        page.excerpt.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  // Status filter
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(page => page.status === selectedStatus.value);
  }

  // Type filter
  if (selectedType.value !== 'all') {
    filtered = filtered.filter(page => page.type === selectedType.value);
  }

  // Sort
  filtered.sort((a, b) => {
    let aVal = a[sortBy.value as keyof typeof a];
    let bVal = b[sortBy.value as keyof typeof b];

    if (typeof aVal === 'string') {
      aVal = aVal.toLowerCase();
      bVal = bVal.toLowerCase();
    }

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });

  return filtered;
});

// Paginated pages
const paginatedPages = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredPages.value.slice(start, end);
});

// Total pages
const totalPages = computed(() => {
  return Math.ceil(filteredPages.value.length / itemsPerPage.value);
});

// Use composables
const { getPages, getPageAnalytics } = usePages();

// Load pages
const loadPages = async () => {
  loading.value = true;
  error.value = null;
  try {
    const filters = {
      search: searchQuery.value,
      status: selectedStatus.value,
      type: selectedType.value,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value as 'asc' | 'desc',
      page: currentPage.value,
      limit: itemsPerPage.value,
    };

    const response = (await getPages(siteId.value, filters)) as any;
    pages.value = response.data || [];

    // Load analytics
    const analyticsResponse = (await getPageAnalytics(siteId.value)) as any;
    analytics.value = analyticsResponse.data || analytics.value;
  } catch (err: any) {
    error.value = err.message;
    console.error('Error loading pages:', err);
  } finally {
    loading.value = false;
  }
};

// Load data on mount
onMounted(async () => {
  await loadPages();
});

// Methods
const handleSort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = field;
    sortOrder.value = 'desc';
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handleEditPage = (page: any) => {
  // Navigate to edit page
  navigateTo(`/dashboard/${siteId.value}/pages/${page.id}/edit`);
};

const handleViewPage = (page: any) => {
  // Open page in new tab
  window.open(`/${page.slug}`, '_blank');
};

const handleDuplicatePage = async (page: any) => {
  if (confirm(`คุณต้องการคัดลอกหน้า "${page.title}" ใช่หรือไม่?`)) {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      useToast().add({
        title: 'สำเร็จ',
        description: `คัดลอกหน้า ${page.title} เรียบร้อยแล้ว`,
        color: 'success',
      });
    } catch (err) {
      console.error('Error duplicating page:', err);
    }
  }
};

const handleDeletePage = async (page: any) => {
  if (confirm(`คุณต้องการลบหน้า "${page.title}" ใช่หรือไม่?`)) {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      pages.value = pages.value.filter(p => p.id !== page.id);
      useToast().add({
        title: 'สำเร็จ',
        description: `ลบหน้า ${page.title} เรียบร้อยแล้ว`,
        color: 'success',
      });
    } catch (err) {
      console.error('Error deleting page:', err);
    }
  }
};

const handleCreatePage = () => {
  navigateTo(`/dashboard/${siteId.value}/pages/create`);
};

// SEO
useSeoMeta({
  title: 'จัดการหน้าเพจ - ระบบจัดการร้านค้า',
  description: 'จัดการหน้าเพจ สร้าง แก้ไข ลบ และจัดระเบียบหน้าเพจในเว็บไซต์',
  keywords: 'จัดการหน้าเพจ, สร้างหน้าเพจ, แก้ไขหน้าเพจ, จัดระเบียบหน้าเพจ',
  ogTitle: 'จัดการหน้าเพจ - ระบบจัดการร้านค้า',
  ogDescription: 'จัดการหน้าเพจ สร้าง แก้ไข ลบ และจัดระเบียบหน้าเพจในเว็บไซต์',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div class="space-y-8">
    <!-- Enhanced Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          จัดการหน้าเพจ
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          จัดการหน้าเพจและเนื้อหาของเว็บไซต์
        </p>
      </div>
      <UButton 
        @click="handleCreatePage"
        class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
      >
        <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
        สร้างหน้าใหม่
      </UButton>
    </div>

    <!-- Enhanced Analytics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">หน้าทั้งหมด</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalPages }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">+{{ analytics.growthRate }}% จากเดือนที่แล้ว</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-document-text" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">หน้าเผยแพร่</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.publishedPages }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">{{ Math.round((analytics.publishedPages / analytics.totalPages) * 100) }}% ของทั้งหมด</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-check-circle" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">จำนวนผู้เข้าชม</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(analytics.totalViews) }}</p>
            <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">เฉลี่ย {{ formatNumber(analytics.averageViews) }}/หน้า</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-eye" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">หน้าในร่าง</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.draftPages }}</p>
            <p class="text-xs text-yellow-600 dark:text-yellow-400 mt-1">รอการเผยแพร่</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-pencil-square" class="w-8 h-8" />
          </div>
        </div>
      </UCard>
    </div>

    <!-- Enhanced Filters -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-funnel" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวกรองและค้นหา</h2>
        </div>
      </template>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหาหน้า</label>
          <UInput
            v-model="searchQuery"
            placeholder="ค้นหาตามชื่อหรือเนื้อหา..."
            icon="i-heroicons-magnifying-glass"
            class="w-full"
          />
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สถานะ</label>
          <USelect
            v-model="selectedStatus"
            :items="statusOptions"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกสถานะ"
            class="w-full"
          />
        </div>

        <!-- Type Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ประเภท</label>
          <USelect
            v-model="selectedType"
            :items="typeOptions"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกประเภท"
            class="w-full"
          />
        </div>

        <!-- Sort -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เรียงลำดับ</label>
          <div class="flex gap-2">
            <USelect
              v-model="sortBy"
              :items="sortOptions"
              option-attribute="label"
              value-attribute="value"
              class="flex-1"
            />
            <UButton
              @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'"
              variant="outline"
              size="sm"
              class="px-3"
            >
              <Icon 
                :name="sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down'" 
                class="w-4 h-4" 
              />
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Enhanced Pages Table -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <Icon name="i-heroicons-document-text" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รายการหน้าเพจ</h2>
            <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-400 rounded-full">
              {{ filteredPages.length }} รายการ
            </span>
          </div>
          <div class="flex items-center gap-2">
            <UButton variant="ghost" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
              <Icon name="i-heroicons-arrow-down-tray" class="w-4 h-4 mr-1" />
              ส่งออก
            </UButton>
            <UButton variant="ghost" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
              <Icon name="i-heroicons-cog-6-tooth" class="w-4 h-4" />
            </UButton>
          </div>
        </div>
      </template>
      
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="flex items-center gap-3">
          <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-blue-600" />
          <span class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</span>
        </div>
      </div>

      <div v-else-if="error" class="text-center py-12">
        <div class="text-red-600 dark:text-red-400 mb-4">
          <Icon name="i-heroicons-exclamation-triangle" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">เกิดข้อผิดพลาด</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">{{ error }}</p>
        <UButton @click="loadPages" variant="outline">
          ลองใหม่
        </UButton>
      </div>

      <div v-else-if="paginatedPages.length === 0" class="text-center py-12">
        <div class="text-gray-400 dark:text-gray-500 mb-4">
          <Icon name="i-heroicons-document-text" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">ไม่พบหน้าเพจ</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          ยังไม่มีหน้าเพจในระบบ หรือไม่ตรงกับเงื่อนไขการค้นหา
        </p>
        <UButton @click="handleCreatePage" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
          สร้างหน้าแรก
        </UButton>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>หน้าเพจ</span>
                  <UButton
                    @click="handleSort('title')"
                    variant="ghost"
                    size="xs"
                    class="p-1"
                  >
                    <Icon 
                      :name="sortBy === 'title' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'" 
                      class="w-3 h-3" 
                    />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>ประเภท</span>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>สถานะ</span>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>ผู้เข้าชม</span>
                  <UButton
                    @click="handleSort('views')"
                    variant="ghost"
                    size="xs"
                    class="p-1"
                  >
                    <Icon 
                      :name="sortBy === 'views' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'" 
                      class="w-3 h-3" 
                    />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>ผู้สร้าง</span>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>วันที่สร้าง</span>
                  <UButton
                    @click="handleSort('created_at')"
                    variant="ghost"
                    size="xs"
                    class="p-1"
                  >
                    <Icon 
                      :name="sortBy === 'created_at' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'" 
                      class="w-3 h-3" 
                    />
                  </UButton>
                </div>
              </th>
              <th class="text-right py-4 px-6 font-semibold text-gray-900 dark:text-white">การดำเนินการ</th>
            </tr>
          </thead>
          <tbody>
            <tr 
              v-for="page in paginatedPages" 
              :key="page.id"
              class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
            >
              <td class="py-4 px-6">
                <div class="flex items-center gap-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-semibold">
                    {{ page.title.charAt(0).toUpperCase() }}
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">{{ page.title }}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ page.excerpt }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-500">/{{ page.slug }}</p>
                  </div>
                </div>
              </td>
              <td class="py-4 px-6">
                <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-700 dark:text-gray-300 rounded-full">
                  {{ getTypeLabel(page.type) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span 
                  class="px-3 py-1 text-xs rounded-full font-medium"
                  :class="getStatusBadgeClass(page.status)"
                >
                  {{ getStatusLabel(page.status) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span class="font-semibold text-gray-900 dark:text-white">
                  {{ formatNumber(page.views) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {{ page.author }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {{ new Date(page.createdAt).toLocaleDateString('th-TH') }}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center justify-end gap-2">
                  <UButton
                    @click="handleViewPage(page)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                  >
                    <Icon name="i-heroicons-eye" class="w-4 h-4" />
                  </UButton>
                  <UButton
                    @click="handleEditPage(page)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-green-50 dark:hover:bg-green-900/20 text-green-600 dark:text-green-400"
                  >
                    <Icon name="i-heroicons-pencil-square" class="w-4 h-4" />
                  </UButton>
                  <UButton
                    @click="handleDuplicatePage(page)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-purple-50 dark:hover:bg-purple-900/20 text-purple-600 dark:text-purple-400"
                  >
                    <Icon name="i-heroicons-document-duplicate" class="w-4 h-4" />
                  </UButton>
                  <UButton
                    @click="handleDeletePage(page)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400"
                  >
                    <Icon name="i-heroicons-trash" class="w-4 h-4" />
                  </UButton>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Enhanced Pagination -->
      <div v-if="totalPages > 1" class="flex items-center justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} ถึง {{ Math.min(currentPage * itemsPerPage, filteredPages.length) }} 
          จาก {{ filteredPages.length }} รายการ
        </div>
        <div class="flex items-center gap-2">
          <UButton
            @click="handlePageChange(currentPage - 1)"
            :disabled="currentPage === 1"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-left" class="w-4 h-4" />
          </UButton>
          
          <div class="flex items-center gap-1">
            <UButton
              v-for="page in Math.min(5, totalPages)"
              :key="page"
              @click="handlePageChange(page)"
              :variant="currentPage === page ? 'solid' : 'outline'"
              size="sm"
              class="w-8 h-8 p-0"
            >
              {{ page }}
            </UButton>
          </div>
          
          <UButton
            @click="handlePageChange(currentPage + 1)"
            :disabled="currentPage === totalPages"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-right" class="w-4 h-4" />
          </UButton>
        </div>
      </div>
    </UCard>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style> 