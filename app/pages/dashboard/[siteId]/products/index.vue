<script setup lang="ts">
import { useRoute } from 'vue-router';
import { useMyImage } from '~/composables/useMyImage';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

const { t } = useI18n();

// ใช้ SEO meta สำหรับหน้าผลิตภัณฑ์
useProductsSeo();

// Get siteId from route params
const route: any = useRoute();
const siteId = computed(() => route.params.siteId);

// Use composables
const { $apiFetch } = useNuxtApp();
const { getProducts, createProduct, updateProduct, deleteProduct } = useProducts();

// Import useMyImage for product images
const { imageUrl } = useMyImage();

// Reactive data
const products = ref<any[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);

// Reactive data
const searchQuery = ref('');
const selectedCategory = ref('all');
const selectedStatus = ref('all');
const sortBy = ref('created_at');
const sortOrder = ref('desc');
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Filters
const categories = ref([
  { value: 'all', label: 'ทุกหมวดหมู่' },
  { value: 'electronics', label: 'อิเล็กทรอนิกส์' },
  { value: 'clothing', label: 'เสื้อผ้า' },
  { value: 'books', label: 'หนังสือ' },
  { value: 'home', label: 'บ้านและสวน' },
  { value: 'sports', label: 'กีฬา' },
]);

const statuses = ref([
  { value: 'all', label: 'ทุกสถานะ' },
  { value: 'active', label: 'เปิดใช้งาน' },
  { value: 'inactive', label: 'ปิดใช้งาน' },
  { value: 'draft', label: 'ร่าง' },
  { value: 'archived', label: 'เก็บถาวร' },
]);

const sortOptions = ref([
  { value: 'created_at', label: 'วันที่สร้าง' },
  { value: 'name', label: 'ชื่อสินค้า' },
  { value: 'price', label: 'ราคา' },
  { value: 'stock', label: 'จำนวนคงเหลือ' },
  { value: 'sales', label: 'ยอดขาย' },
]);

// Analytics data - จะอัปเดตจากข้อมูลจริง
const analytics = ref({
  totalProducts: 0,
  activeProducts: 0,
  lowStockProducts: 0,
  totalSales: 0,
  averagePrice: 0,
  topCategory: '',
  growthRate: 0,
});

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
  }).format(amount);
};

// Format number
const formatNumber = (num: number) => {
  return new Intl.NumberFormat('th-TH').format(num);
};

// Get status badge class
const getStatusBadgeClass = (status: string) => {
  const classes = {
    active: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
    draft: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    archived: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
  };
  return classes[status as keyof typeof classes] || classes.inactive;
};

// Get status label
const getStatusLabel = (status: string) => {
  const labels = {
    active: 'เปิดใช้งาน',
    inactive: 'ปิดใช้งาน',
    draft: 'ร่าง',
    archived: 'เก็บถาวร',
  };
  return labels[status as keyof typeof labels] || status;
};

// Computed filtered products
const filteredProducts = computed(() => {
  let filtered = products.value || [];

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(
      product =>
        product.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        product.description?.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  // Category filter
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(
      product => product.categories && product.categories.includes(selectedCategory.value)
    );
  }

  // Status filter
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(product => product.status === selectedStatus.value);
  }

  // Sort
  filtered.sort((a, b) => {
    let aVal = a[sortBy.value as keyof typeof a];
    let bVal = b[sortBy.value as keyof typeof b];

    // Handle nested properties
    if (sortBy.value === 'stock') {
      aVal = a.inventory?.quantity || 0;
      bVal = b.inventory?.quantity || 0;
    } else if (sortBy.value === 'sales') {
      aVal = a.analytics?.sales || 0;
      bVal = b.analytics?.sales || 0;
    } else if (sortBy.value === 'created_at') {
      aVal = new Date(a.createdAt).getTime();
      bVal = new Date(b.createdAt).getTime();
    }

    if (typeof aVal === 'string') {
      aVal = aVal.toLowerCase();
      bVal = bVal.toLowerCase();
    }

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });

  return filtered;
});

// Paginated products
const paginatedProducts = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredProducts.value.slice(start, end);
});

// Total pages
const totalPages = computed(() => {
  return Math.ceil(filteredProducts.value.length / itemsPerPage.value);
});

// Fetch products
const fetchProducts = async () => {
  loading.value = true;
  error.value = null;
  try {
    const response = await getProducts(siteId.value, {});
    if (response?.success && response?.data) {
      products.value = response.data.products || [];

      // คำนวณ analytics จากข้อมูลจริง
      const totalProducts = products.value.length;
      const activeProducts = products.value.filter(p => p.status === 'active').length;
      const lowStockProducts = products.value.filter(
        p => p.inventory?.quantity <= p.inventory?.lowStockThreshold
      ).length;
      const totalSales = products.value.reduce((sum, p) => sum + (p.analytics?.revenue || 0), 0);
      const averagePrice =
        totalProducts > 0 ? products.value.reduce((sum, p) => sum + p.price, 0) / totalProducts : 0;

      // หาหมวดหมู่ที่ขายดีที่สุด
      const categorySales: Record<string, number> = {};
      products.value.forEach(p => {
        if (p.categories && p.categories.length > 0) {
          p.categories.forEach((cat: string) => {
            categorySales[cat] = (categorySales[cat] || 0) + (p.analytics?.revenue || 0);
          });
        }
      });
      const topCategory =
        Object.keys(categorySales).sort(
          (a, b) => (categorySales[b] || 0) - (categorySales[a] || 0)
        )[0] || '';

      // คำนวณอัตราการเติบโต (ตัวอย่าง)
      const growthRate = totalProducts > 0 ? Math.round((activeProducts / totalProducts) * 100) : 0;

      analytics.value = {
        totalProducts,
        activeProducts,
        lowStockProducts,
        totalSales,
        averagePrice,
        topCategory,
        growthRate,
      };
    }
  } catch (err) {
    error.value = 'เกิดข้อผิดพลาดในการโหลดข้อมูล';
    console.error('Error fetching products:', err);
  } finally {
    loading.value = false;
  }
};

// Fetch products on mount
onMounted(async () => {
  await fetchProducts();
});

// Methods
const handleSort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = field;
    sortOrder.value = 'desc';
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handleEditProduct = (product: any) => {
  // Navigate to edit page or open modal
  navigateTo(`/dashboard/${siteId.value}/products/${product.id}/edit`);
};

const handleDeleteProduct = async (product: any) => {
  if (confirm(`คุณต้องการลบสินค้า "${product.name}" ใช่หรือไม่?`)) {
    try {
      await deleteProduct(product.id, siteId.value);
      await fetchProducts();
    } catch (err) {
      console.error('Error deleting product:', err);
    }
  }
};

const handleCreateProduct = () => {
  navigateTo(`/dashboard/${siteId.value}/products/create`);
};
</script>

<template>
  <div class="space-y-8">
    <!-- Enhanced Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          จัดการสินค้า
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          จัดการสินค้าทั้งหมดในร้านค้าของคุณ
        </p>
      </div>
      <UButton @click="handleCreateProduct"
        class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
        <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
        เพิ่มสินค้าใหม่
      </UButton>
    </div>

    <!-- Enhanced Analytics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">สินค้าทั้งหมด</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalProducts }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">+{{ analytics.growthRate }}% จากเดือนที่แล้ว</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-shopping-bag" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">สินค้าที่เปิดใช้งาน</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.activeProducts }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">
              {{ analytics.totalProducts > 0 ? Math.round((analytics.activeProducts / analytics.totalProducts) * 100) :
                0 }}% ของทั้งหมด
            </p>
          </div>
          <div class="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-check-circle" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">สินค้าคงเหลือน้อย</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.lowStockProducts }}</p>
            <p class="text-xs text-orange-600 dark:text-orange-400 mt-1">ต้องเติมสต็อก</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-exclamation-triangle" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ยอดขายรวม</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatCurrency(analytics.totalSales) }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">เฉลี่ย {{ formatCurrency(analytics.averagePrice)
            }}/ชิ้น</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-currency-dollar" class="w-8 h-8" />
          </div>
        </div>
      </UCard>
    </div>

    <!-- Enhanced Filters -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-funnel" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวกรองและค้นหา</h2>
        </div>
      </template>

      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหาสินค้า</label>
          <UInput v-model="searchQuery" placeholder="ค้นหาตามชื่อหรือคำอธิบาย..." icon="i-heroicons-magnifying-glass"
            class="w-full" />
        </div>

        <!-- Category Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">หมวดหมู่</label>
          <USelect v-model="selectedCategory" :items="categories" option-attribute="label" value-attribute="value"
            placeholder="เลือกหมวดหมู่" class="w-full" />
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สถานะ</label>
          <USelect v-model="selectedStatus" :items="statuses" option-attribute="label" value-attribute="value"
            placeholder="เลือกสถานะ" class="w-full" />
        </div>

        <!-- Sort -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เรียงลำดับ</label>
          <div class="flex gap-2">
            <USelect v-model="sortBy" :items="sortOptions" option-attribute="label" value-attribute="value"
              class="flex-1" />
            <UButton @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'" variant="outline" size="sm" class="px-3">
              <Icon :name="sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down'" class="w-4 h-4" />
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Enhanced Products Table -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <Icon name="i-heroicons-rectangle-stack" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รายการสินค้า</h2>
            <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-400 rounded-full">
              {{ filteredProducts.length }} รายการ
            </span>
          </div>
          <div class="flex items-center gap-2">
            <UButton variant="ghost" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
              <Icon name="i-heroicons-arrow-down-tray" class="w-4 h-4 mr-1" />
              ส่งออก
            </UButton>
            <UButton variant="ghost" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
              <Icon name="i-heroicons-cog-6-tooth" class="w-4 h-4" />
            </UButton>
          </div>
        </div>
      </template>

      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="flex items-center gap-3">
          <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-blue-600" />
          <span class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</span>
        </div>
      </div>

      <div v-else-if="error" class="text-center py-12">
        <div class="text-red-600 dark:text-red-400 mb-4">
          <Icon name="i-heroicons-exclamation-triangle" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">เกิดข้อผิดพลาด</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">{{ error }}</p>
        <UButton @click="fetchProducts()" variant="outline">
          ลองใหม่
        </UButton>
      </div>

      <div v-else-if="paginatedProducts.length === 0" class="text-center py-12">
        <div class="text-gray-400 dark:text-gray-500 mb-4">
          <Icon name="i-heroicons-shopping-bag" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">ไม่พบสินค้า</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          ยังไม่มีสินค้าในระบบ หรือไม่ตรงกับเงื่อนไขการค้นหา
        </p>
        <UButton @click="handleCreateProduct" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
          เพิ่มสินค้าแรก
        </UButton>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>สินค้า</span>
                  <UButton @click="handleSort('name')" variant="ghost" size="xs" class="p-1">
                    <Icon
                      :name="sortBy === 'name' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'"
                      class="w-3 h-3" />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>หมวดหมู่</span>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>ราคา</span>
                  <UButton @click="handleSort('price')" variant="ghost" size="xs" class="p-1">
                    <Icon
                      :name="sortBy === 'price' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'"
                      class="w-3 h-3" />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>คงเหลือ</span>
                  <UButton @click="handleSort('stock')" variant="ghost" size="xs" class="p-1">
                    <Icon
                      :name="sortBy === 'stock' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'"
                      class="w-3 h-3" />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>สถานะ</span>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>ยอดขาย</span>
                  <UButton @click="handleSort('sales')" variant="ghost" size="xs" class="p-1">
                    <Icon
                      :name="sortBy === 'sales' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'"
                      class="w-3 h-3" />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>วันที่สร้าง</span>
                  <UButton @click="handleSort('created_at')" variant="ghost" size="xs" class="p-1">
                    <Icon
                      :name="sortBy === 'created_at' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'"
                      class="w-3 h-3" />
                  </UButton>
                </div>
              </th>
              <th class="text-right py-4 px-6 font-semibold text-gray-900 dark:text-white">การดำเนินการ</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="product in paginatedProducts" :key="product.id"
              class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
              <td class="py-4 px-6">
                <div class="flex items-center gap-4">
                  <UiImage :src="imageUrl(product.image, { width: 80, height: 80, crop: 'thumb' })" :alt="product.name"
                    :fallback-text="product.name.charAt(0).toUpperCase()"
                    fallback-bg="bg-gradient-to-br from-blue-500 to-purple-600 text-white" />
                  <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">{{ product.name }}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-1">{{ product.description }}</p>
                  </div>
                </div>
              </td>
              <td class="py-4 px-6">
                <span
                  class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-700 dark:text-gray-300 rounded-full">
                  {{ product.category }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span class="font-semibold text-gray-900 dark:text-white">
                  {{ formatCurrency(product.price) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center gap-2">
                  <span class="font-semibold text-gray-900 dark:text-white">
                    {{ formatNumber(product.inventory?.quantity || 0) }}
                  </span>
                  <span v-if="product.inventory?.quantity <= product.inventory?.lowStockThreshold"
                    class="px-2 py-1 text-xs bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400 rounded-full">
                    ต่ำ
                  </span>
                </div>
              </td>
              <td class="py-4 px-6">
                <span class="px-3 py-1 text-xs rounded-full font-medium" :class="getStatusBadgeClass(product.status)">
                  {{ getStatusLabel(product.status) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span class="font-semibold text-gray-900 dark:text-white">
                  {{ formatNumber(product.analytics?.sales || 0) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {{ new Date(product.createdAt).toLocaleDateString('th-TH') }}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center justify-end gap-2">
                  <UButton @click="handleEditProduct(product)" variant="ghost" size="sm"
                    class="hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400">
                    <Icon name="i-heroicons-pencil-square" class="w-4 h-4" />
                  </UButton>
                  <UButton @click="handleDeleteProduct(product)" variant="ghost" size="sm"
                    class="hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400">
                    <Icon name="i-heroicons-trash" class="w-4 h-4" />
                  </UButton>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Enhanced Pagination -->
      <div v-if="totalPages > 1"
        class="flex items-center justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} ถึง {{ Math.min(currentPage * itemsPerPage,
            filteredProducts.length) }}
          จาก {{ filteredProducts.length }} รายการ
        </div>
        <div class="flex items-center gap-2">
          <UButton @click="handlePageChange(currentPage - 1)" :disabled="currentPage === 1" variant="outline" size="sm">
            <Icon name="i-heroicons-chevron-left" class="w-4 h-4" />
          </UButton>

          <div class="flex items-center gap-1">
            <UButton v-for="page in Math.min(5, totalPages)" :key="page" @click="handlePageChange(page)"
              :variant="currentPage === page ? 'solid' : 'outline'" size="sm" class="w-8 h-8 p-0">
              {{ page }}
            </UButton>
          </div>

          <UButton @click="handlePageChange(currentPage + 1)" :disabled="currentPage === totalPages" variant="outline"
            size="sm">
            <Icon name="i-heroicons-chevron-right" class="w-4 h-4" />
          </UButton>
        </div>
      </div>
    </UCard>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8>* {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Line clamp utility */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>