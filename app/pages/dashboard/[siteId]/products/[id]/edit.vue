<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useToast } from '#imports';
import { useProducts } from '~~/app/composables/useProducts';

const route = useRoute();
const siteId = computed(() => route.params.siteId as string);
const id = route.params.id as string;
const { getProduct, updateProduct } = useProducts();
const toast = useToast();

const form = ref({
  name: '',
  description: '',
  price: 0,
  inventory_quantity: 0,
});
const isSubmitting = ref(false);
const loading = ref(true);

onMounted(async () => {
  loading.value = true;
  try {
    const res = await getProduct(id, siteId.value);
    const product = res?.data || {};
    form.value = {
      name: product.name || '',
      description: product.description || '',
      price: product.price || 0,
      inventory_quantity: product.inventory?.quantity || 0,
    };
  } catch (e) {
    toast.add({
      title: 'ผิดพลาด',
      description: 'ไม่พบข้อมูลสินค้า',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
});

const handleSubmit = async () => {
  isSubmitting.value = true;
  try {
    await updateProduct(id, siteId.value, {
      name: form.value.name,
      description: form.value.description,
      price: parseFloat(form.value.price.toString()),
      inventory: {
        quantity: parseInt(form.value.inventory_quantity.toString()),
      },
    });
    toast.add({
      title: 'บันทึกสำเร็จ',
      description: `บันทึกสินค้า ${form.value.name} สำหรับเว็บไซต์ ${siteId.value} เรียบร้อยแล้ว`,
      color: 'success',
    });
  } catch (e) {
    toast.add({
      title: 'ผิดพลาด',
      description: 'บันทึกสินค้าไม่สำเร็จ',
      color: 'error',
    });
  } finally {
    isSubmitting.value = false;
  }
};
</script>
<template>
  <div class="p-6 max-w-xl mx-auto">
    <h1 class="text-2xl font-bold mb-4">แก้ไขสินค้า</h1>
    <form @submit.prevent="handleSubmit" class="space-y-4">
      <UFormField label="ชื่อสินค้า" required>
        <UInput v-model="form.name" required />
      </UFormField>
      <UFormField label="รายละเอียด">
        <UTextarea v-model="form.description" />
      </UFormField>
      <div class="grid grid-cols-2 gap-4">
        <UFormField label="ราคา" required>
          <UInput v-model="form.price" type="number" required />
        </UFormField>
        <UFormField label="จำนวนในคลัง" required>
          <UInput v-model="form.inventory_quantity" type="number" required />
        </UFormField>
      </div>
      <div class="mt-4 flex gap-2">
        <UButton type="submit" :loading="isSubmitting" color="primary">บันทึก</UButton>
        <UButton :to="`/dashboard/${siteId}/products`" variant="outline">ยกเลิก</UButton>
      </div>
    </form>
    <div v-if="loading" class="text-center text-gray-500 mt-4">กำลังโหลดข้อมูลสินค้า...</div>
  </div>
</template> 