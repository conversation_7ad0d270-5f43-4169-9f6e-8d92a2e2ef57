<script setup lang="ts">
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);
const orderId = route.params.id;
const order = ref({
  id: orderId,
  customer: 'ลูกค้าตัวอย่าง',
  email: '<EMAIL>',
  phone: '************',
  status: 'pending',
  total: 2500,
  items: [
    { name: 'สินค้า A', quantity: 2, price: 1000 },
    { name: 'สินค้า B', quantity: 1, price: 500 },
  ],
  shippingAddress: '123 ถนนตัวอย่าง แขวงตัวอย่าง เขตตัวอย่าง กรุงเทพฯ 10000',
  orderDate: '2024-01-15T10:30:00Z',
});
</script>
<template>
  <div class="p-6 max-w-4xl mx-auto">
    <div class="flex items-center justify-between mb-6">
      <h1 class="text-2xl font-bold">รายละเอียดคำสั่งซื้อ #{{ orderId }}</h1>
      <UButton to="../orders" variant="outline">
        กลับไปหน้ารายการ
      </UButton>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Order Info -->
      <div class="lg:col-span-2 space-y-6">
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">ข้อมูลคำสั่งซื้อ</h3>
          </template>
          
          <div class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">สถานะ</label>
                <UBadge :color="order.status === 'pending' ? 'warning' : 'success'" class="mt-1">
                  {{ order.status === 'pending' ? 'รอดำเนินการ' : 'เสร็จสิ้น' }}
                </UBadge>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">วันที่สั่งซื้อ</label>
                <p class="text-sm text-gray-900 dark:text-white mt-1">
                  {{ new Date(order.orderDate).toLocaleString('th-TH') }}
                </p>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">ที่อยู่จัดส่ง</label>
              <p class="text-sm text-gray-900 dark:text-white mt-1">{{ order.shippingAddress }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">รายการสินค้า</h3>
          </template>
          
          <div class="space-y-4">
            <div v-for="item in order.items" :key="item.name" class="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700">
              <div>
                <p class="font-medium text-gray-900 dark:text-white">{{ item.name }}</p>
                <p class="text-sm text-gray-500">จำนวน: {{ item.quantity }}</p>
              </div>
              <p class="font-medium text-gray-900 dark:text-white">{{ item.price.toLocaleString() }} บาท</p>
            </div>
            
            <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
              <p class="text-lg font-semibold text-gray-900 dark:text-white">รวมทั้งหมด</p>
              <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ order.total.toLocaleString() }} บาท</p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Customer Info -->
      <div class="space-y-6">
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">ข้อมูลลูกค้า</h3>
          </template>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">ชื่อ</label>
              <p class="text-sm text-gray-900 dark:text-white mt-1">{{ order.customer }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">อีเมล</label>
              <p class="text-sm text-gray-900 dark:text-white mt-1">{{ order.email }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">เบอร์โทร</label>
              <p class="text-sm text-gray-900 dark:text-white mt-1">{{ order.phone }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">การดำเนินการ</h3>
          </template>
          
          <div class="space-y-3">
            <UButton color="success" class="w-full">
              ยืนยันคำสั่งซื้อ
            </UButton>
            <UButton color="warning" class="w-full">
              กำลังจัดส่ง
            </UButton>
            <UButton color="error" class="w-full">
              ยกเลิกคำสั่งซื้อ
            </UButton>
          </div>
        </UCard>
      </div>
    </div>
  </div>
</template> 