
<script setup lang="ts">
definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

const route: any = useRoute();
const siteId = route.params.siteId;

// Mock data - should come from API
const todayStats = ref({
  sales: 125000,
  salesChange: 12.5,
  orders: 48,
  ordersChange: -5.2,
  visitors: 1250,
  visitorsChange: 8.3,
  conversionRate: 3.8,
  conversionChange: 0.5,
});

const recentOrders = ref([
  {
    id: '12345',
    customer: 'สมชาย ใจดี',
    total: 2500,
    status: 'pending',
  },
  {
    id: '12344',
    customer: 'สมหญิง รักดี',
    total: 1800,
    status: 'completed',
  },
  {
    id: '12343',
    customer: 'สมศักดิ์ มีสุข',
    total: 3200,
    status: 'processing',
  },
]);

const topProducts = ref([
  {
    id: '1',
    name: 'iPhone 15 Pro',
    image: 'https://via.placeholder.com/48',
    sold: 25,
    revenue: 62500,
  },
  {
    id: '2',
    name: 'MacBook Air M3',
    image: 'https://via.placeholder.com/48',
    sold: 12,
    revenue: 48000,
  },
  {
    id: '3',
    name: 'AirPods Pro',
    image: 'https://via.placeholder.com/48',
    sold: 35,
    revenue: 28000,
  },
]);

// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
  }).format(amount);
};

const getOrderStatusColor = (
  status: string
): 'warning' | 'info' | 'success' | 'error' | 'primary' | 'secondary' | 'neutral' | undefined => {
  switch (status) {
    case 'pending':
      return 'warning';
    case 'processing':
      return 'info';
    case 'completed':
      return 'success';
    case 'cancelled':
      return 'error';
    default:
      return 'neutral';
  }
};

const getOrderStatusLabel = (status: string) => {
  const labels = {
    pending: 'รอดำเนินการ',
    processing: 'กำลังดำเนินการ',
    completed: 'เสร็จสิ้น',
    cancelled: 'ยกเลิก',
  };
  return labels[status as keyof typeof labels] || status;
};

// Load data on mount
onMounted(async () => {
  // TODO: Load actual data from API
  console.log('Loading dashboard data for site:', siteId);
});
</script>

<template> 
    <!-- Breadcrumb -->
    <!-- <DashboardBreadcrumb /> -->

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <DashboardStatsCard
        title="ยอดขายวันนี้"
        :value="todayStats.sales"
        icon="i-heroicons-currency-dollar"
        :change="todayStats.salesChange"
        format="currency"
        color="green"
        description="เปรียบเทียบกับเมื่อวาน"
      />
      
      <DashboardStatsCard
        title="คำสั่งซื้อใหม่"
        :value="todayStats.orders"
        icon="i-heroicons-shopping-cart"
        :change="todayStats.ordersChange"
        color="blue"
        description="คำสั่งซื้อวันนี้"
      />
      
      <DashboardStatsCard
        title="ผู้เยี่ยมชม"
        :value="todayStats.visitors"
        icon="i-heroicons-users"
        :change="todayStats.visitorsChange"
        color="purple"
        description="ผู้เยี่ยมชมเว็บไซต์"
      />
      
      <DashboardStatsCard
        title="อัตราแปลง"
        :value="todayStats.conversionRate"
        icon="i-heroicons-chart-bar"
        :change="todayStats.conversionChange"
        format="percentage"
        color="yellow"
        description="จากผู้เยี่ยมชมเป็นลูกค้า"
      />
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Quick Actions -->
      <div class="lg:col-span-2">
        <DashboardQuickActions :site-id="siteId" />
      </div>

      <!-- Recent Activity -->
      <div>
        <DashboardRecentActivity :site-id="siteId" :limit="5" />
      </div>
    </div>

    <!-- Additional Content -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
      <!-- Recent Orders -->
      <UCard variant="soft">
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              คำสั่งซื้อล่าสุด
            </h3>
            <UButton
              :to="`/dashboard/${siteId}/orders`"
              variant="ghost"
              size="sm"
              label="ดูทั้งหมด"
            />
          </div>
        </template>

        <div class="space-y-4">
          <div
            v-for="order in recentOrders"
            :key="order.id"
            class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                <Icon name="i-heroicons-shopping-bag" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p class="font-medium text-gray-900 dark:text-white">
                  #{{ order.id }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ order.customer }}
                </p>
              </div>
            </div>
            <div class="text-right">
              <p class="font-medium text-gray-900 dark:text-white">
                {{ formatCurrency(order.total) }}
              </p>
              <UBadge :color="getOrderStatusColor(order.status)" variant="soft" size="xs">
                {{ getOrderStatusLabel(order.status) }}
              </UBadge>
            </div>
          </div>

          <DashboardEmptyState
            v-if="recentOrders.length === 0"
            title="ยังไม่มีคำสั่งซื้อ"
            description="เมื่อมีลูกค้าสั่งซื้อสินค้า คำสั่งซื้อจะแสดงที่นี่"
            icon="i-heroicons-shopping-cart"
            :primary-action="{
              label: 'ดูสินค้า',
              icon: 'i-heroicons-shopping-bag',
              to: `/dashboard/${siteId}/products`
            }"
          />
        </div>
      </UCard>

      <!-- Top Products -->
      <UCard variant="soft">
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              สินค้าขายดี
            </h3>
            <UButton
              :to="`/dashboard/${siteId}/products`"
              variant="ghost"
              size="sm"
              label="ดูทั้งหมด"
            />
          </div>
        </template>

        <div class="space-y-4">
          <div
            v-for="product in topProducts"
            :key="product.id"
            class="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <img
              :src="product.image"
              :alt="product.name"
              class="w-12 h-12 object-cover rounded-lg"
            />
            <div class="flex-1 min-w-0">
              <p class="font-medium text-gray-900 dark:text-white truncate">
                {{ product.name }}
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                ขายได้ {{ product.sold }} ชิ้น
              </p>
            </div>
            <div class="text-right">
              <p class="font-medium text-gray-900 dark:text-white">
                {{ formatCurrency(product.revenue) }}
              </p>
            </div>
          </div>

          <DashboardEmptyState
            v-if="topProducts.length === 0"
            title="ยังไม่มีข้อมูลการขาย"
            description="เมื่อมีการขายสินค้า สถิติจะแสดงที่นี่"
            icon="i-heroicons-chart-bar"
            :primary-action="{
              label: 'เพิ่มสินค้า',
              icon: 'i-heroicons-plus',
              to: `/dashboard/${siteId}/products/create`
            }"
          />
        </div>
      </UCard>
    </div> 
</template>
