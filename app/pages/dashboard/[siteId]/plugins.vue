<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

const loading = ref(false);
const searchQuery = ref('');
const selectedStatus = ref('all');
const sortBy = ref('created_at');
const sortOrder = ref('desc');
const currentPage = ref(1);
const itemsPerPage = ref(10);

const statuses = ref([
  { value: 'all', label: 'ทุกสถานะ' },
  { value: 'active', label: 'ใช้งาน' },
  { value: 'inactive', label: 'ไม่ใช้งาน' },
  { value: 'update', label: 'มีอัปเดต' },
]);

const plugins = ref([
  {
    id: '1',
    name: 'SEO Booster',
    description: 'ปลั๊กอินช่วยเพิ่ม SEO ให้ร้านค้า',
    status: 'active',
    version: '1.0.0',
    author: 'Webshop Team',
    created_at: '2024-01-01',
    updated_at: '2024-06-01',
    icon: 'i-heroicons-magnifying-glass',
    color: 'blue',
  },
  {
    id: '2',
    name: 'Facebook Pixel',
    description: 'ปลั๊กอินสำหรับเชื่อมต่อ Facebook Pixel',
    status: 'inactive',
    version: '2.1.0',
    author: 'Webshop Team',
    created_at: '2023-12-01',
    updated_at: '2024-05-20',
    icon: 'i-heroicons-facebook',
    color: 'blue',
  },
  {
    id: '3',
    name: 'Line Notify',
    description: 'ปลั๊กอินแจ้งเตือนผ่าน LINE',
    status: 'update',
    version: '1.2.0',
    author: 'Webshop Team',
    created_at: '2024-02-10',
    updated_at: '2024-06-01',
    icon: 'i-heroicons-chat-bubble-left-right',
    color: 'green',
  },
]);

const analytics = ref({
  totalPlugins: 3,
  activePlugins: 1,
  updatePlugins: 1,
});

const filteredPlugins = computed(() => {
  let filtered = plugins.value;
  if (searchQuery.value) {
    filtered = filtered.filter(
      p => p.name.includes(searchQuery.value) || p.description.includes(searchQuery.value)
    );
  }
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(p => p.status === selectedStatus.value);
  }
  filtered.sort((a, b) => {
    const aVal = (a as any)[sortBy.value];
    const bVal = (b as any)[sortBy.value];

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });
  return filtered;
});

const paginatedPlugins = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredPlugins.value.slice(start, end);
});

const totalPages = computed(() => {
  return Math.ceil(filteredPlugins.value.length / itemsPerPage.value);
});

const handleSort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = field;
    sortOrder.value = 'desc';
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

// Use composables
const { getPlugins, installPlugin, uninstallPlugin, updatePlugin, getPluginAnalytics } =
  usePlugins();

// Load plugins
const loadPlugins = async () => {
  loading.value = true;
  try {
    const filters = {
      search: searchQuery.value,
      status: selectedStatus.value,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value as 'asc' | 'desc',
    };

    const response = (await getPlugins(siteId.value, filters)) as any;
    plugins.value = response.data || plugins.value;

    // Load analytics
    try {
      const analyticsResponse = (await getPluginAnalytics(siteId.value)) as any;
      analytics.value = analyticsResponse.data || analytics.value;
    } catch (analyticsErr) {
      console.log('Analytics not available:', analyticsErr);
    }
  } catch (err: any) {
    console.error('Error loading plugins:', err);
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: err.message || 'ไม่สามารถโหลดข้อมูลปลั๊กอินได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

const handleInstall = async (plugin: any) => {
  try {
    await installPlugin(siteId.value, plugin.id);
    plugin.status = 'active';

    useToast().add({
      title: 'ติดตั้งปลั๊กอิน',
      description: `ติดตั้ง ${plugin.name} สำเร็จ`,
      color: 'success',
    });
  } catch (error: any) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: error.message || 'ไม่สามารถติดตั้งปลั๊กอินได้',
      color: 'error',
    });
  }
};

const handleUpdate = async (plugin: any) => {
  try {
    await updatePlugin(siteId.value, plugin.id);
    plugin.status = 'active';

    useToast().add({
      title: 'อัปเดตปลั๊กอิน',
      description: `อัปเดต ${plugin.name} สำเร็จ`,
      color: 'info',
    });
  } catch (error: any) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: error.message || 'ไม่สามารถอัปเดตปลั๊กอินได้',
      color: 'error',
    });
  }
};

const handleUninstall = async (plugin: any) => {
  if (confirm(`คุณต้องการถอนการติดตั้ง ${plugin.name} ใช่หรือไม่?`)) {
    try {
      await uninstallPlugin(siteId.value, plugin.id);
      plugin.status = 'inactive';

      useToast().add({
        title: 'ถอนการติดตั้ง',
        description: `ถอน ${plugin.name} สำเร็จ`,
        color: 'success',
      });
    } catch (error: any) {
      useToast().add({
        title: 'ข้อผิดพลาด',
        description: error.message || 'ไม่สามารถถอนการติดตั้งปลั๊กอินได้',
        color: 'error',
      });
    }
  }
};
</script>

<template>
  <div class="space-y-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">ปลั๊กอิน</h1>
        <p class="text-gray-600 dark:text-gray-400">จัดการปลั๊กอิน เพิ่มความสามารถให้ร้านค้า</p>
      </div>
    </div>
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      <UCard v-for="plugin in filteredPlugins" :key="plugin.id" class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center gap-4 p-4">
          <div class="w-12 h-12 rounded-lg flex items-center justify-center text-white" :class="`bg-${plugin.color}-500`">
            <Icon :name="plugin.icon" class="w-6 h-6" />
          </div>
          <div>
            <h3 class="font-semibold text-gray-900 dark:text-white">{{ plugin.name }}</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-1">{{ plugin.description }}</p>
            <div class="flex items-center gap-2 mt-2">
              <UBadge :color="plugin.status === 'active' ? 'success' : plugin.status === 'update' ? 'warning' : 'neutral'">
                {{ plugin.status === 'active' ? 'ใช้งาน' : plugin.status === 'update' ? 'มีอัปเดต' : 'ไม่ใช้งาน' }}
              </UBadge>
              <span class="text-xs text-gray-400">v{{ plugin.version }}</span>
            </div>
            <div class="flex items-center gap-2 mt-4">
              <UButton v-if="plugin.status === 'inactive'" @click="handleInstall(plugin)" size="sm">ติดตั้ง</UButton>
              <UButton v-if="plugin.status === 'update'" @click="handleUpdate(plugin)" size="sm">อัปเดต</UButton>
              <UButton v-if="plugin.status === 'active'" @click="handleUninstall(plugin)" variant="outline" size="sm">ถอนการติดตั้ง</UButton>
            </div>
          </div>
        </div>
      </UCard>
    </div>
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-funnel" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวกรองและค้นหา</h2>
        </div>
      </template>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหาปลั๊กอิน</label>
          <UInput v-model="searchQuery" placeholder="ค้นหาตามชื่อหรือคำอธิบาย..." icon="i-heroicons-magnifying-glass" class="w-full" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สถานะ</label>
          <USelect v-model="selectedStatus" :items="statuses" option-attribute="label" value-attribute="value" placeholder="เลือกสถานะ" class="w-full" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เรียงลำดับ</label>
          <div class="flex gap-2">
            <USelect v-model="sortBy" :items="[
              { value: 'created_at', label: 'วันที่สร้าง' },
              { value: 'name', label: 'ชื่อ' }
            ]" option-attribute="label" value-attribute="value" class="flex-1" />
            <UButton @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'" variant="outline" size="sm" class="px-3">
              <Icon :name="sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down'" class="w-4 h-4" />
            </UButton>
          </div>
        </div>
      </div>
    </UCard>
  </div>
</template>

<style scoped>
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}
.space-y-8 > * { animation: fadeInUp 0.6s ease-out forwards; }
.group:hover { transform: translateY(-2px); }
.bg-gradient-to-r { background-size: 200% 200%; animation: gradient 3s ease infinite; }
@keyframes gradient { 0% { background-position: 0% 50%; } 50% { background-position: 100% 50%; } 100% { background-position: 0% 50%; } }
.line-clamp-1 { display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden; }
</style> 