<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

definePageMeta({
    layout: 'dashboard',
    middleware: ['auth'],
});

// Get siteId from route params
const route: any = useRoute();
const siteId = computed(() => route.params.siteId);

const loading = ref(false);
const uploadLoading = ref(false);
const importedTheme = ref<any>(null);
const fileInput = ref<HTMLInputElement>();
const dragOver = ref(false);

// Import methods
const importMethods = ref([
    {
        id: 'file',
        title: 'นำเข้าจากไฟล์',
        description: 'อัปโหลดไฟล์ธีม (.json) จากคอมพิวเตอร์',
        icon: 'i-heroicons-document-arrow-up',
        active: true
    },
    {
        id: 'url',
        title: 'นำเข้าจาก URL',
        description: 'ดาวน์โหลดธีมจากลิงก์ออนไลน์',
        icon: 'i-heroicons-link',
        active: false
    },
    {
        id: 'marketplace',
        title: 'ธีมจาก Marketplace',
        description: 'เลือกธีมจากร้านค้าธีมออนไลน์',
        icon: 'i-heroicons-shopping-bag',
        active: false
    }
]);

const activeMethod = ref('file');
const importUrl = ref('');

// Marketplace themes
const marketplaceThemes = ref([
    {
        id: 'modern-shop',
        name: 'Modern Shop',
        description: 'ธีมร้านค้าโมเดิร์นสำหรับอีคอมเมิร์ซ',
        preview: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400',
        author: 'ThemeForest',
        rating: 4.8,
        downloads: 1250,
        price: 'ฟรี',
        tags: ['ecommerce', 'modern', 'responsive']
    },
    {
        id: 'creative-portfolio',
        name: 'Creative Portfolio',
        description: 'ธีมสำหรับแสดงผลงานสร้างสรรค์',
        preview: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400',
        author: 'Creative Tim',
        rating: 4.9,
        downloads: 890,
        price: '$29',
        tags: ['portfolio', 'creative', 'minimal']
    },
    {
        id: 'business-pro',
        name: 'Business Pro',
        description: 'ธีมธุรกิจระดับมืออาชีพ',
        preview: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400',
        author: 'TemplateMonster',
        rating: 4.7,
        downloads: 2100,
        price: '$49',
        tags: ['business', 'professional', 'corporate']
    }
]);

// File upload handling
const handleFileSelect = (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files[0]) {
        handleFile(target.files[0]);
    }
};

const handleDrop = (event: DragEvent) => {
    event.preventDefault();
    dragOver.value = false;

    if (event.dataTransfer?.files && event.dataTransfer.files[0]) {
        handleFile(event.dataTransfer.files[0]);
    }
};

const handleFile = async (file: File) => {
    if (!file.name.endsWith('.json')) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'กรุณาเลือกไฟล์ .json เท่านั้น',
            color: 'error',
        });
        return;
    }

    uploadLoading.value = true;
    try {
        const text = await file.text();
        const themeData = JSON.parse(text);

        // Validate theme structure
        if (!themeData.name || !themeData.settings) {
            throw new Error('ไฟล์ธีมไม่ถูกต้อง');
        }

        importedTheme.value = themeData;

        useToast().add({
            title: 'สำเร็จ',
            description: 'อ่านไฟล์ธีมเรียบร้อยแล้ว',
            color: 'success',
        });
    } catch (error: any) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: error.message || 'ไม่สามารถอ่านไฟล์ธีมได้',
            color: 'error',
        });
    } finally {
        uploadLoading.value = false;
    }
};

// Import from URL
const handleImportFromUrl = async () => {
    if (!importUrl.value) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'กรุณากรอก URL',
            color: 'error',
        });
        return;
    }

    uploadLoading.value = true;
    try {
        // Mock API call to fetch theme from URL
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Mock theme data
        importedTheme.value = {
            name: 'ธีมจาก URL',
            description: 'ธีมที่นำเข้าจาก URL',
            settings: {
                colors: {
                    primary: '#3B82F6',
                    secondary: '#64748B'
                }
            }
        };

        useToast().add({
            title: 'สำเร็จ',
            description: 'นำเข้าธีมจาก URL เรียบร้อยแล้ว',
            color: 'success',
        });
    } catch (error: any) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'ไม่สามารถดาวน์โหลดธีมจาก URL ได้',
            color: 'error',
        });
    } finally {
        uploadLoading.value = false;
    }
};

// Import from marketplace
const handleImportFromMarketplace = async (theme: any) => {
    uploadLoading.value = true;
    try {
        // Mock API call to download theme
        await new Promise(resolve => setTimeout(resolve, 1500));

        importedTheme.value = {
            name: theme.name,
            description: theme.description,
            settings: {
                colors: {
                    primary: '#3B82F6',
                    secondary: '#64748B'
                }
            }
        };

        useToast().add({
            title: 'สำเร็จ',
            description: `นำเข้าธีม "${theme.name}" เรียบร้อยแล้ว`,
            color: 'success',
        });
    } catch (error: any) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'ไม่สามารถดาวน์โหลดธีมได้',
            color: 'error',
        });
    } finally {
        uploadLoading.value = false;
    }
};

// Apply imported theme
const applyTheme = async () => {
    if (!importedTheme.value) return;

    loading.value = true;
    try {
        // Mock API call to apply theme
        await new Promise(resolve => setTimeout(resolve, 1000));

        useToast().add({
            title: 'สำเร็จ',
            description: `ใช้ธีม "${importedTheme.value.name}" เรียบร้อยแล้ว`,
            color: 'success',
        });

        // Navigate back to themes page
        await navigateTo(`/dashboard/${siteId.value}/themes`);
    } catch (error: any) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'ไม่สามารถใช้ธีมได้',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

// Go back
const handleBack = () => {
    navigateTo(`/dashboard/${siteId.value}/themes`);
};

// Format number
const formatNumber = (num: number) => {
    return new Intl.NumberFormat('th-TH').format(num);
};
</script>

<template>
    <div class="space-y-8">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <div class="flex items-center gap-3 mb-2">
                    <UButton @click="handleBack" variant="ghost" size="sm" icon="i-heroicons-arrow-left" />
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">นำเข้าธีม</h1>
                </div>
                <p class="text-gray-600 dark:text-gray-400">
                    นำเข้าธีมจากไฟล์ URL หรือ Marketplace
                </p>
            </div>
        </div>

        <!-- Import Methods -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <button v-for="method in importMethods" :key="method.id" @click="activeMethod = method.id" :class="[
                'p-6 border-2 rounded-lg text-left transition-all duration-200',
                activeMethod === method.id
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
            ]">
                <div class="flex items-center gap-4 mb-3">
                    <div :class="[
                        'p-3 rounded-lg',
                        activeMethod === method.id
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400'
                    ]">
                        <Icon :name="method.icon" class="w-6 h-6" />
                    </div>
                    <div>
                        <h3 :class="[
                            'text-lg font-semibold',
                            activeMethod === method.id
                                ? 'text-blue-700 dark:text-blue-300'
                                : 'text-gray-900 dark:text-white'
                        ]">
                            {{ method.title }}
                        </h3>
                    </div>
                </div>
                <p :class="[
                    'text-sm',
                    activeMethod === method.id
                        ? 'text-blue-600 dark:text-blue-400'
                        : 'text-gray-600 dark:text-gray-400'
                ]">
                    {{ method.description }}
                </p>
            </button>
        </div>

        <!-- Import Content -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Import Form -->
            <div>
                <!-- File Upload -->
                <UCard v-if="activeMethod === 'file'" class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                                <Icon name="i-heroicons-document-arrow-up" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">อัปโหลดไฟล์ธีม</h2>
                        </div>
                    </template>

                    <div class="space-y-6">
                        <!-- Upload Area -->
                        <div :class="[
                            'border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200',
                            dragOver
                                ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                                : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                        ]" @dragover.prevent="dragOver = true" @dragleave.prevent="dragOver = false" @drop.prevent="handleDrop">

                            <div class="space-y-4">
                                <div
                                    class="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                                    <Icon name="i-heroicons-cloud-arrow-up" class="w-8 h-8 text-gray-400" />
                                </div>

                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                        อัปโหลดไฟล์ธีม
                                    </h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                        ลากและวางไฟล์ .json ที่นี่ หรือคลิกเพื่อเลือกไฟล์
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        รองรับไฟล์ .json เท่านั้น (สูงสุด 10MB)
                                    </p>
                                </div>

                                <UButton @click="fileInput?.click()" :loading="uploadLoading" variant="outline">
                                    <Icon name="i-heroicons-folder-open" class="w-4 h-4 mr-2" />
                                    เลือกไฟล์
                                </UButton>
                            </div>

                            <input ref="fileInput" type="file" accept=".json" class="hidden"
                                @change="handleFileSelect" />
                        </div>

                        <!-- File Requirements -->
                        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">ข้อกำหนดไฟล์</h4>
                            <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                                <li class="flex items-center gap-2">
                                    <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500" />
                                    ไฟล์ต้องเป็นนามสกุล .json
                                </li>
                                <li class="flex items-center gap-2">
                                    <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500" />
                                    ขนาดไฟล์ไม่เกิน 10MB
                                </li>
                                <li class="flex items-center gap-2">
                                    <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500" />
                                    ต้องมีโครงสร้างธีมที่ถูกต้อง
                                </li>
                            </ul>
                        </div>
                    </div>
                </UCard>

                <!-- URL Import -->
                <UCard v-if="activeMethod === 'url'" class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                                <Icon name="i-heroicons-link" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">นำเข้าจาก URL</h2>
                        </div>
                    </template>

                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                URL ของไฟล์ธีม
                            </label>
                            <UInput v-model="importUrl" placeholder="https://example.com/theme.json" class="w-full" />
                        </div>

                        <UButton @click="handleImportFromUrl" :loading="uploadLoading" class="w-full">
                            <Icon name="i-heroicons-arrow-down-tray" class="w-4 h-4 mr-2" />
                            ดาวน์โหลดธีม
                        </UButton>

                        <!-- URL Examples -->
                        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">ตัวอย่าง URL</h4>
                            <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                                <li>https://github.com/user/theme/raw/main/theme.json</li>
                                <li>https://cdn.example.com/themes/modern.json</li>
                                <li>https://api.themestore.com/themes/123/download</li>
                            </ul>
                        </div>
                    </div>
                </UCard>

                <!-- Marketplace -->
                <UCard v-if="activeMethod === 'marketplace'" class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
                                <Icon name="i-heroicons-shopping-bag" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ธีมจาก Marketplace</h2>
                        </div>
                    </template>

                    <div class="space-y-6">
                        <!-- Search -->
                        <UInput placeholder="ค้นหาธีม..." class="w-full">
                            <template #leading>
                                <Icon name="i-heroicons-magnifying-glass" class="w-4 h-4" />
                            </template>
                        </UInput>

                        <!-- Themes List -->
                        <div class="space-y-4 max-h-96 overflow-y-auto">
                            <div v-for="theme in marketplaceThemes" :key="theme.id"
                                class="flex gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-gray-300 dark:hover:border-gray-600 transition-colors">

                                <!-- Theme Preview -->
                                <div
                                    class="w-20 h-16 bg-gray-100 dark:bg-gray-800 rounded overflow-hidden flex-shrink-0">
                                    <img :src="theme.preview" :alt="theme.name" class="w-full h-full object-cover" />
                                </div>

                                <!-- Theme Info -->
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between mb-2">
                                        <div>
                                            <h4 class="font-semibold text-gray-900 dark:text-white truncate">{{
                                                theme.name }}</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">โดย {{ theme.author }}
                                            </p>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-semibold text-gray-900 dark:text-white">{{ theme.price }}</p>
                                            <div class="flex items-center gap-1 text-xs text-gray-500">
                                                <Icon name="i-heroicons-star" class="w-3 h-3 text-yellow-400" />
                                                {{ theme.rating }}
                                            </div>
                                        </div>
                                    </div>

                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
                                        {{ theme.description }}
                                    </p>

                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-2">
                                            <UBadge v-for="tag in theme.tags.slice(0, 2)" :key="tag" variant="subtle"
                                                size="sm">
                                                {{ tag }}
                                            </UBadge>
                                        </div>
                                        <div class="flex items-center gap-3">
                                            <span class="text-xs text-gray-500">{{ formatNumber(theme.downloads) }}
                                                ดาวน์โหลด</span>
                                            <UButton @click="handleImportFromMarketplace(theme)"
                                                :loading="uploadLoading" size="sm">
                                                <Icon name="i-heroicons-arrow-down-tray" class="w-4 h-4 mr-1" />
                                                นำเข้า
                                            </UButton>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </UCard>
            </div>

            <!-- Preview -->
            <div>
                <UCard v-if="importedTheme" class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                                <Icon name="i-heroicons-eye" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวอย่างธีม</h2>
                        </div>
                    </template>

                    <div class="space-y-6">
                        <!-- Theme Info -->
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                                {{ importedTheme.name }}
                            </h3>
                            <p class="text-gray-600 dark:text-gray-400">
                                {{ importedTheme.description }}
                            </p>
                        </div>

                        <!-- Theme Settings Preview -->
                        <div v-if="importedTheme.settings">
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">การตั้งค่า</h4>

                            <!-- Colors -->
                            <div v-if="importedTheme.settings.colors" class="mb-6">
                                <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">สี</h5>
                                <div class="flex gap-3">
                                    <div v-for="(color, key) in importedTheme.settings.colors" :key="key"
                                        class="text-center">
                                        <div class="w-12 h-12 rounded-lg border border-gray-200 dark:border-gray-600 mb-2"
                                            :style="{ backgroundColor: color }"></div>
                                        <p class="text-xs text-gray-600 dark:text-gray-400 capitalize">{{ key }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Typography -->
                            <div v-if="importedTheme.settings.typography" class="mb-6">
                                <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">ตัวอักษร</h5>
                                <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                        ฟอนต์: {{ importedTheme.settings.typography.fontFamily || 'ไม่ระบุ' }}
                                    </p>
                                    <div :style="{ fontFamily: importedTheme.settings.typography.fontFamily }">
                                        <h6 class="text-lg font-bold mb-1">ตัวอย่างหัวข้อ</h6>
                                        <p class="text-sm">ตัวอย่างข้อความปกติ</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Components -->
                            <div v-if="importedTheme.settings.components" class="mb-6">
                                <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">คอมโพเนนต์</h5>
                                <div class="space-y-3">
                                    <button class="px-4 py-2 bg-blue-500 text-white transition-colors hover:bg-blue-600"
                                        :style="{ borderRadius: importedTheme.settings.components.buttonRadius || '6px' }">
                                        ปุ่มตัวอย่าง
                                    </button>
                                    <div class="p-4 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 shadow-md"
                                        :style="{ borderRadius: importedTheme.settings.components.cardRadius || '8px' }">
                                        การ์ดตัวอย่าง
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="flex items-center gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                            <UButton @click="applyTheme" :loading="loading" class="flex-1">
                                <Icon name="i-heroicons-check" class="w-4 h-4 mr-2" />
                                ใช้ธีมนี้
                            </UButton>
                            <UButton @click="importedTheme = null" variant="outline">
                                <Icon name="i-heroicons-x-mark" class="w-4 h-4" />
                            </UButton>
                        </div>
                    </div>
                </UCard>

                <!-- Empty State -->
                <UCard v-else class="overflow-hidden">
                    <div class="text-center py-12">
                        <div
                            class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                            <Icon name="i-heroicons-eye" class="w-12 h-12 text-gray-400" />
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                            ยังไม่มีธีมที่นำเข้า
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400">
                            เลือกวิธีการนำเข้าธีมด้านซ้ายเพื่อดูตัวอย่าง
                        </p>
                    </div>
                </UCard>
            </div>
        </div>
    </div>
</template>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Add smooth animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.space-y-8>* {
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Drag and drop styles */
.border-dashed {
    transition: all 0.2s ease;
}
</style>