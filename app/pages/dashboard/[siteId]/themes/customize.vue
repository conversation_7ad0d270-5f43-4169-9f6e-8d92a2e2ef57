<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

definePageMeta({
    layout: 'dashboard',
    middleware: ['auth'],
});

// Get siteId from route params
const route: any = useRoute();
const siteId = computed(() => route.params.siteId);

// Theme settings
const themeSettings = reactive({
    // Colors
    colors: {
        primary: '#3B82F6',
        secondary: '#64748B',
        accent: '#F59E0B',
        success: '#10B981',
        warning: '#F59E0B',
        error: '#EF4444',
        background: '#FFFFFF',
        surface: '#F8FAFC',
        text: '#1F2937',
        textSecondary: '#6B7280',
        border: '#E5E7EB'
    },

    // Typography
    typography: {
        fontFamily: 'Inter',
        headingFont: 'Inter',
        fontSize: {
            xs: '12px',
            sm: '14px',
            base: '16px',
            lg: '18px',
            xl: '20px',
            '2xl': '24px',
            '3xl': '30px',
            '4xl': '36px'
        },
        fontWeight: {
            normal: '400',
            medium: '500',
            semibold: '600',
            bold: '700'
        },
        lineHeight: {
            tight: '1.25',
            normal: '1.5',
            relaxed: '1.75'
        }
    },

    // Layout
    layout: {
        containerWidth: '1200px',
        headerHeight: '80px',
        footerHeight: '200px',
        sidebarWidth: '280px',
        spacing: {
            xs: '4px',
            sm: '8px',
            md: '16px',
            lg: '24px',
            xl: '32px',
            '2xl': '48px'
        }
    },

    // Components
    components: {
        buttonRadius: '8px',
        cardRadius: '12px',
        inputRadius: '6px',
        imageRadius: '8px',
        shadowLevel: 'medium',
        borderWidth: '1px'
    },

    // Header
    header: {
        backgroundColor: '#FFFFFF',
        textColor: '#1F2937',
        height: '80px',
        sticky: true,
        showLogo: true,
        showNavigation: true,
        showSearch: true,
        showCart: true,
        logoPosition: 'left',
        navigationStyle: 'horizontal'
    },

    // Footer
    footer: {
        backgroundColor: '#1F2937',
        textColor: '#FFFFFF',
        showSocialLinks: true,
        showNewsletter: true,
        showSitemap: true,
        columns: 4
    },

    // Banner
    banner: {
        show: true,
        position: 'top',
        backgroundColor: '#3B82F6',
        textColor: '#FFFFFF',
        height: '40px',
        text: 'ข้อความประกาศ'
    }
});

const loading = ref(false);
const activeTab = ref('colors');
const previewMode = ref('desktop');

// Available fonts
const availableFonts = ref([
    { value: 'Inter', label: 'Inter', preview: 'Inter Font Preview' },
    { value: 'Roboto', label: 'Roboto', preview: 'Roboto Font Preview' },
    { value: 'Open Sans', label: 'Open Sans', preview: 'Open Sans Font Preview' },
    { value: 'Lato', label: 'Lato', preview: 'Lato Font Preview' },
    { value: 'Montserrat', label: 'Montserrat', preview: 'Montserrat Font Preview' },
    { value: 'Poppins', label: 'Poppins', preview: 'Poppins Font Preview' },
    { value: 'Nunito', label: 'Nunito', preview: 'Nunito Font Preview' },
    { value: 'Source Sans Pro', label: 'Source Sans Pro', preview: 'Source Sans Pro Preview' },
    { value: 'Noto Sans Thai', label: 'Noto Sans Thai', preview: 'ตัวอย่างฟอนต์ไทย' },
    { value: 'Sarabun', label: 'Sarabun', preview: 'ตัวอย่างฟอนต์สารบรรณ' }
]);

// Shadow levels
const shadowLevels = ref([
    { value: 'none', label: 'ไม่มีเงา', preview: 'shadow-none' },
    { value: 'sm', label: 'เงาเล็ก', preview: 'shadow-sm' },
    { value: 'medium', label: 'เงากลาง', preview: 'shadow-md' },
    { value: 'lg', label: 'เงาใหญ่', preview: 'shadow-lg' },
    { value: 'xl', label: 'เงาใหญ่มาก', preview: 'shadow-xl' }
]);

// Tabs
const tabs = ref([
    { key: 'colors', label: 'สี', icon: 'i-heroicons-swatch' },
    { key: 'typography', label: 'ตัวอักษร', icon: 'i-heroicons-language' },
    { key: 'layout', label: 'เลย์เอาต์', icon: 'i-heroicons-squares-2x2' },
    { key: 'components', label: 'คอมโพเนนต์', icon: 'i-heroicons-cube' },
    { key: 'header', label: 'ส่วนหัว', icon: 'i-heroicons-bars-3' },
    { key: 'footer', label: 'ส่วนท้าย', icon: 'i-heroicons-bars-3-bottom-left' },
    { key: 'banner', label: 'แบนเนอร์', icon: 'i-heroicons-megaphone' }
]);

// Preview modes
const previewModes = ref([
    { value: 'desktop', label: 'เดสก์ท็อป', icon: 'i-heroicons-computer-desktop' },
    { value: 'tablet', label: 'แท็บเล็ต', icon: 'i-heroicons-device-tablet' },
    { value: 'mobile', label: 'มือถือ', icon: 'i-heroicons-device-phone-mobile' }
]);

// Color presets
const colorPresets = ref([
    {
        name: 'Blue Ocean',
        colors: {
            primary: '#3B82F6',
            secondary: '#64748B',
            accent: '#06B6D4'
        }
    },
    {
        name: 'Green Nature',
        colors: {
            primary: '#10B981',
            secondary: '#6B7280',
            accent: '#84CC16'
        }
    },
    {
        name: 'Purple Magic',
        colors: {
            primary: '#8B5CF6',
            secondary: '#64748B',
            accent: '#EC4899'
        }
    },
    {
        name: 'Orange Sunset',
        colors: {
            primary: '#F97316',
            secondary: '#6B7280',
            accent: '#EAB308'
        }
    }
]);

// Watch for changes and update preview
watch(themeSettings, () => {
    updatePreview();
}, { deep: true });

// Update preview
const updatePreview = () => {
    // Update CSS custom properties for live preview
    const root = document.documentElement;

    // Colors
    root.style.setProperty('--color-primary', themeSettings.colors.primary);
    root.style.setProperty('--color-secondary', themeSettings.colors.secondary);
    root.style.setProperty('--color-accent', themeSettings.colors.accent);
    root.style.setProperty('--color-background', themeSettings.colors.background);
    root.style.setProperty('--color-surface', themeSettings.colors.surface);
    root.style.setProperty('--color-text', themeSettings.colors.text);

    // Typography
    root.style.setProperty('--font-family', themeSettings.typography.fontFamily);
    root.style.setProperty('--font-size-base', themeSettings.typography.fontSize.base);

    // Components
    root.style.setProperty('--border-radius-button', themeSettings.components.buttonRadius);
    root.style.setProperty('--border-radius-card', themeSettings.components.cardRadius);
};

// Apply color preset
const applyColorPreset = (preset: any) => {
    Object.assign(themeSettings.colors, preset.colors);
    useToast().add({
        title: 'สำเร็จ',
        description: `ใช้ชุดสี ${preset.name} เรียบร้อยแล้ว`,
        color: 'success',
    });
};

// Reset to default
const resetToDefault = () => {
    const confirmed = confirm('คุณแน่ใจหรือไม่ที่จะรีเซ็ตการตั้งค่าทั้งหมด?');
    if (!confirmed) return;

    // Reset all settings to default
    Object.assign(themeSettings, {
        colors: {
            primary: '#3B82F6',
            secondary: '#64748B',
            accent: '#F59E0B',
            success: '#10B981',
            warning: '#F59E0B',
            error: '#EF4444',
            background: '#FFFFFF',
            surface: '#F8FAFC',
            text: '#1F2937',
            textSecondary: '#6B7280',
            border: '#E5E7EB'
        },
        typography: {
            fontFamily: 'Inter',
            headingFont: 'Inter',
            fontSize: {
                xs: '12px',
                sm: '14px',
                base: '16px',
                lg: '18px',
                xl: '20px',
                '2xl': '24px',
                '3xl': '30px',
                '4xl': '36px'
            }
        }
    });

    useToast().add({
        title: 'สำเร็จ',
        description: 'รีเซ็ตการตั้งค่าเรียบร้อยแล้ว',
        color: 'success',
    });
};

// Save theme
const saveTheme = async () => {
    loading.value = true;
    try {
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock API call to save theme
        console.log('Saving theme:', themeSettings);

        useToast().add({
            title: 'สำเร็จ',
            description: 'บันทึกการตั้งค่าธีมเรียบร้อยแล้ว',
            color: 'success',
        });
    } catch (error) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'ไม่สามารถบันทึกการตั้งค่าได้',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

// Go back
const handleBack = () => {
    navigateTo(`/dashboard/${siteId.value}/themes`);
};

// Initialize preview on mount
onMounted(() => {
    updatePreview();
});
</script>

<template>
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div class="flex h-screen">
            <!-- Sidebar -->
            <div class="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
                <!-- Header -->
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center gap-3 mb-4">
                        <UButton @click="handleBack" variant="ghost" size="sm" icon="i-heroicons-arrow-left" />
                        <h1 class="text-xl font-bold text-gray-900 dark:text-white">ปรับแต่งธีม</h1>
                    </div>

                    <!-- Preview Mode Toggle -->
                    <div class="flex items-center gap-2">
                        <UButton v-for="mode in previewModes" :key="mode.value" @click="previewMode = mode.value"
                            :variant="previewMode === mode.value ? 'solid' : 'outline'" size="sm">
                            <Icon :name="mode.icon" class="w-4 h-4" />
                        </UButton>
                    </div>
                </div>

                <!-- Tabs -->
                <div class="flex-1 overflow-y-auto">
                    <div class="p-4">
                        <div class="space-y-2">
                            <button v-for="tab in tabs" :key="tab.key" @click="activeTab = tab.key" :class="[
                                'w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-colors',
                                activeTab === tab.key
                                    ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                            ]">
                                <Icon :name="tab.icon" class="w-5 h-5" />
                                <span class="font-medium">{{ tab.label }}</span>
                            </button>
                        </div>
                    </div>

                    <!-- Settings Content -->
                    <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                        <!-- Colors Tab -->
                        <div v-if="activeTab === 'colors'" class="space-y-6">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">สีของธีม</h3>

                                <!-- Color Presets -->
                                <div class="mb-6">
                                    <label
                                        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">ชุดสีแนะนำ</label>
                                    <div class="grid grid-cols-2 gap-3">
                                        <button v-for="preset in colorPresets" :key="preset.name"
                                            @click="applyColorPreset(preset)"
                                            class="p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-gray-300 dark:hover:border-gray-500 transition-colors">
                                            <div class="flex gap-2 mb-2">
                                                <div class="w-4 h-4 rounded-full"
                                                    :style="{ backgroundColor: preset.colors.primary }"></div>
                                                <div class="w-4 h-4 rounded-full"
                                                    :style="{ backgroundColor: preset.colors.secondary }"></div>
                                                <div class="w-4 h-4 rounded-full"
                                                    :style="{ backgroundColor: preset.colors.accent }"></div>
                                            </div>
                                            <p class="text-xs text-gray-600 dark:text-gray-400">{{ preset.name }}</p>
                                        </button>
                                    </div>
                                </div>

                                <!-- Individual Colors -->
                                <div class="space-y-4">
                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีหลัก</label>
                                        <div class="flex items-center gap-3">
                                            <input v-model="themeSettings.colors.primary" type="color"
                                                class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                            <UInput v-model="themeSettings.colors.primary" class="flex-1" />
                                        </div>
                                    </div>

                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีรอง</label>
                                        <div class="flex items-center gap-3">
                                            <input v-model="themeSettings.colors.secondary" type="color"
                                                class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                            <UInput v-model="themeSettings.colors.secondary" class="flex-1" />
                                        </div>
                                    </div>

                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีเน้น</label>
                                        <div class="flex items-center gap-3">
                                            <input v-model="themeSettings.colors.accent" type="color"
                                                class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                            <UInput v-model="themeSettings.colors.accent" class="flex-1" />
                                        </div>
                                    </div>

                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีพื้นหลัง</label>
                                        <div class="flex items-center gap-3">
                                            <input v-model="themeSettings.colors.background" type="color"
                                                class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                            <UInput v-model="themeSettings.colors.background" class="flex-1" />
                                        </div>
                                    </div>

                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีข้อความ</label>
                                        <div class="flex items-center gap-3">
                                            <input v-model="themeSettings.colors.text" type="color"
                                                class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                            <UInput v-model="themeSettings.colors.text" class="flex-1" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Typography Tab -->
                        <div v-if="activeTab === 'typography'" class="space-y-6">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">ตัวอักษร</h3>

                                <div class="space-y-4">
                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ฟอนต์หลัก</label>
                                        <USelect v-model="themeSettings.typography.fontFamily" :items="availableFonts"
                                            option-attribute="label" value-attribute="value" class="w-full" />
                                    </div>

                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ฟอนต์หัวข้อ</label>
                                        <USelect v-model="themeSettings.typography.headingFont" :items="availableFonts"
                                            option-attribute="label" value-attribute="value" class="w-full" />
                                    </div>

                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ขนาดฟอนต์พื้นฐาน</label>
                                        <UInput v-model="themeSettings.typography.fontSize.base" class="w-full" />
                                    </div>

                                    <!-- Font Preview -->
                                    <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">ตัวอย่าง
                                        </h4>
                                        <div
                                            :style="{ fontFamily: themeSettings.typography.fontFamily, fontSize: themeSettings.typography.fontSize.base }">
                                            <h1 class="text-2xl font-bold mb-2">หัวข้อใหญ่</h1>
                                            <h2 class="text-xl font-semibold mb-2">หัวข้อรอง</h2>
                                            <p class="mb-2">ข้อความปกติ Lorem ipsum dolor sit amet consectetur
                                                adipisicing elit.</p>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">ข้อความเล็ก
                                                และข้อความรอง</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Layout Tab -->
                        <div v-if="activeTab === 'layout'" class="space-y-6">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">เลย์เอาต์</h3>

                                <div class="space-y-4">
                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ความกว้างคอนเทนเนอร์</label>
                                        <UInput v-model="themeSettings.layout.containerWidth" class="w-full" />
                                    </div>

                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ความสูงส่วนหัว</label>
                                        <UInput v-model="themeSettings.layout.headerHeight" class="w-full" />
                                    </div>

                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ความสูงส่วนท้าย</label>
                                        <UInput v-model="themeSettings.layout.footerHeight" class="w-full" />
                                    </div>

                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ความกว้างแถบข้าง</label>
                                        <UInput v-model="themeSettings.layout.sidebarWidth" class="w-full" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Components Tab -->
                        <div v-if="activeTab === 'components'" class="space-y-6">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">คอมโพเนนต์</h3>

                                <div class="space-y-4">
                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">มุมโค้งปุ่ม</label>
                                        <UInput v-model="themeSettings.components.buttonRadius" class="w-full" />
                                    </div>

                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">มุมโค้งการ์ด</label>
                                        <UInput v-model="themeSettings.components.cardRadius" class="w-full" />
                                    </div>

                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">มุมโค้งช่องกรอก</label>
                                        <UInput v-model="themeSettings.components.inputRadius" class="w-full" />
                                    </div>

                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ระดับเงา</label>
                                        <USelect v-model="themeSettings.components.shadowLevel" :items="shadowLevels"
                                            option-attribute="label" value-attribute="value" class="w-full" />
                                    </div>

                                    <!-- Component Preview -->
                                    <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">ตัวอย่าง
                                        </h4>
                                        <div class="space-y-3">
                                            <button
                                                class="px-4 py-2 bg-blue-500 text-white rounded transition-colors hover:bg-blue-600"
                                                :style="{ borderRadius: themeSettings.components.buttonRadius }">
                                                ปุ่มตัวอย่าง
                                            </button>
                                            <div class="p-4 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600"
                                                :style="{ borderRadius: themeSettings.components.cardRadius }"
                                                :class="`shadow-${themeSettings.components.shadowLevel}`">
                                                การ์ดตัวอย่าง
                                            </div>
                                            <input type="text" placeholder="ช่องกรอกตัวอย่าง"
                                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"
                                                :style="{ borderRadius: themeSettings.components.inputRadius }" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Header Tab -->
                        <div v-if="activeTab === 'header'" class="space-y-6">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">ส่วนหัว</h3>

                                <div class="space-y-4">
                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีพื้นหลัง</label>
                                        <div class="flex items-center gap-3">
                                            <input v-model="themeSettings.header.backgroundColor" type="color"
                                                class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                            <UInput v-model="themeSettings.header.backgroundColor" class="flex-1" />
                                        </div>
                                    </div>

                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีข้อความ</label>
                                        <div class="flex items-center gap-3">
                                            <input v-model="themeSettings.header.textColor" type="color"
                                                class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                            <UInput v-model="themeSettings.header.textColor" class="flex-1" />
                                        </div>
                                    </div>

                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ความสูง</label>
                                        <UInput v-model="themeSettings.header.height" class="w-full" />
                                    </div>

                                    <div class="space-y-3">
                                        <UCheckbox v-model="themeSettings.header.sticky" label="ติดด้านบน (Sticky)" />
                                        <UCheckbox v-model="themeSettings.header.showLogo" label="แสดงโลโก้" />
                                        <UCheckbox v-model="themeSettings.header.showNavigation"
                                            label="แสดงเมนูนำทาง" />
                                        <UCheckbox v-model="themeSettings.header.showSearch" label="แสดงช่องค้นหา" />
                                        <UCheckbox v-model="themeSettings.header.showCart" label="แสดงตะกร้าสินค้า" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Footer Tab -->
                        <div v-if="activeTab === 'footer'" class="space-y-6">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">ส่วนท้าย</h3>

                                <div class="space-y-4">
                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีพื้นหลัง</label>
                                        <div class="flex items-center gap-3">
                                            <input v-model="themeSettings.footer.backgroundColor" type="color"
                                                class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                            <UInput v-model="themeSettings.footer.backgroundColor" class="flex-1" />
                                        </div>
                                    </div>

                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีข้อความ</label>
                                        <div class="flex items-center gap-3">
                                            <input v-model="themeSettings.footer.textColor" type="color"
                                                class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                            <UInput v-model="themeSettings.footer.textColor" class="flex-1" />
                                        </div>
                                    </div>

                                    <div>
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">จำนวนคอลัมน์</label>
                                        <UInput v-model.number="themeSettings.footer.columns" type="number" min="1"
                                            max="6" class="w-full" />
                                    </div>

                                    <div class="space-y-3">
                                        <UCheckbox v-model="themeSettings.footer.showSocialLinks"
                                            label="แสดงลิงก์โซเชียล" />
                                        <UCheckbox v-model="themeSettings.footer.showNewsletter"
                                            label="แสดงสมัครจดหมายข่าว" />
                                        <UCheckbox v-model="themeSettings.footer.showSitemap"
                                            label="แสดงแผนผังเว็บไซต์" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Banner Tab -->
                        <div v-if="activeTab === 'banner'" class="space-y-6">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">แบนเนอร์ประกาศ</h3>

                                <div class="space-y-4">
                                    <UCheckbox v-model="themeSettings.banner.show" label="แสดงแบนเนอร์" />

                                    <div v-if="themeSettings.banner.show">
                                        <div class="space-y-4">
                                            <div>
                                                <label
                                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ตำแหน่ง</label>
                                                <USelect v-model="themeSettings.banner.position" :items="[
                                                    { value: 'top', label: 'ด้านบน' },
                                                    { value: 'bottom', label: 'ด้านล่าง' }
                                                ]" option-attribute="label" value-attribute="value" class="w-full" />
                                            </div>

                                            <div>
                                                <label
                                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีพื้นหลัง</label>
                                                <div class="flex items-center gap-3">
                                                    <input v-model="themeSettings.banner.backgroundColor" type="color"
                                                        class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                                    <UInput v-model="themeSettings.banner.backgroundColor"
                                                        class="flex-1" />
                                                </div>
                                            </div>

                                            <div>
                                                <label
                                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีข้อความ</label>
                                                <div class="flex items-center gap-3">
                                                    <input v-model="themeSettings.banner.textColor" type="color"
                                                        class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                                    <UInput v-model="themeSettings.banner.textColor" class="flex-1" />
                                                </div>
                                            </div>

                                            <div>
                                                <label
                                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ความสูง</label>
                                                <UInput v-model="themeSettings.banner.height" class="w-full" />
                                            </div>

                                            <div>
                                                <label
                                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ข้อความ</label>
                                                <UInput v-model="themeSettings.banner.text" class="w-full" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="p-6 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex items-center gap-3">
                        <UButton @click="saveTheme" :loading="loading" class="flex-1">
                            <Icon name="i-heroicons-check" class="w-4 h-4 mr-2" />
                            บันทึก
                        </UButton>
                        <UButton @click="resetToDefault" variant="outline">
                            <Icon name="i-heroicons-arrow-path" class="w-4 h-4" />
                        </UButton>
                    </div>
                </div>
            </div>

            <!-- Preview Area -->
            <div class="flex-1 bg-gray-100 dark:bg-gray-900 p-6">
                <div class="h-full flex items-center justify-center">
                    <div :class="[
                        'bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden transition-all duration-300',
                        previewMode === 'desktop' ? 'w-full max-w-6xl h-full' : '',
                        previewMode === 'tablet' ? 'w-full max-w-3xl h-5/6' : '',
                        previewMode === 'mobile' ? 'w-full max-w-sm h-5/6' : ''
                    ]">
                        <!-- Preview Content -->
                        <div class="h-full flex flex-col">
                            <!-- Banner Preview -->
                            <div v-if="themeSettings.banner.show && themeSettings.banner.position === 'top'"
                                class="flex items-center justify-center text-sm font-medium" :style="{
                                    backgroundColor: themeSettings.banner.backgroundColor,
                                    color: themeSettings.banner.textColor,
                                    height: themeSettings.banner.height
                                }">
                                {{ themeSettings.banner.text }}
                            </div>

                            <!-- Header Preview -->
                            <div class="flex items-center justify-between px-6" :style="{
                                backgroundColor: themeSettings.header.backgroundColor,
                                color: themeSettings.header.textColor,
                                height: themeSettings.header.height
                            }">
                                <div class="flex items-center gap-4">
                                    <div v-if="themeSettings.header.showLogo"
                                        class="w-8 h-8 bg-current opacity-20 rounded"></div>
                                    <div v-if="themeSettings.header.showNavigation"
                                        class="hidden md:flex items-center gap-6">
                                        <span class="text-sm">หน้าแรก</span>
                                        <span class="text-sm">สินค้า</span>
                                        <span class="text-sm">เกี่ยวกับ</span>
                                        <span class="text-sm">ติดต่อ</span>
                                    </div>
                                </div>
                                <div class="flex items-center gap-4">
                                    <div v-if="themeSettings.header.showSearch"
                                        class="w-48 h-8 bg-current opacity-10 rounded"></div>
                                    <div v-if="themeSettings.header.showCart"
                                        class="w-8 h-8 bg-current opacity-20 rounded"></div>
                                </div>
                            </div>

                            <!-- Content Preview -->
                            <div class="flex-1 p-6 overflow-y-auto" :style="{
                                backgroundColor: themeSettings.colors.background,
                                color: themeSettings.colors.text,
                                fontFamily: themeSettings.typography.fontFamily,
                                fontSize: themeSettings.typography.fontSize.base
                            }">
                                <div class="max-w-4xl mx-auto space-y-8">
                                    <!-- Hero Section -->
                                    <div class="text-center py-12">
                                        <h1 class="text-4xl font-bold mb-4"
                                            :style="{ fontFamily: themeSettings.typography.headingFont }">
                                            ยินดีต้อนรับสู่เว็บไซต์
                                        </h1>
                                        <p class="text-xl mb-8" :style="{ color: themeSettings.colors.textSecondary }">
                                            ตัวอย่างเนื้อหาสำหรับการแสดงผลธีม
                                        </p>
                                        <button
                                            class="px-8 py-3 text-white font-medium transition-colors hover:opacity-90"
                                            :style="{
                                                backgroundColor: themeSettings.colors.primary,
                                                borderRadius: themeSettings.components.buttonRadius
                                            }">
                                            เริ่มต้นใช้งาน
                                        </button>
                                    </div>

                                    <!-- Cards Section -->
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div v-for="i in 3" :key="i"
                                            class="p-6 border transition-shadow hover:shadow-lg" :style="{
                                                backgroundColor: themeSettings.colors.surface,
                                                borderColor: themeSettings.colors.border,
                                                borderRadius: themeSettings.components.cardRadius
                                            }">
                                            <div class="w-12 h-12 rounded-lg mb-4"
                                                :style="{ backgroundColor: themeSettings.colors.accent }"></div>
                                            <h3 class="text-lg font-semibold mb-2"
                                                :style="{ fontFamily: themeSettings.typography.headingFont }">
                                                การ์ดตัวอย่าง {{ i }}
                                            </h3>
                                            <p :style="{ color: themeSettings.colors.textSecondary }">
                                                เนื้อหาตัวอย่างสำหรับการ์ดนี้
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Form Section -->
                                    <div class="max-w-md mx-auto">
                                        <h2 class="text-2xl font-bold mb-6 text-center"
                                            :style="{ fontFamily: themeSettings.typography.headingFont }">
                                            ติดต่อเรา
                                        </h2>
                                        <div class="space-y-4">
                                            <input type="text" placeholder="ชื่อ" class="w-full px-4 py-3 border"
                                                :style="{
                                                    borderColor: themeSettings.colors.border,
                                                    borderRadius: themeSettings.components.inputRadius,
                                                    backgroundColor: themeSettings.colors.surface
                                                }" />
                                            <input type="email" placeholder="อีเมล" class="w-full px-4 py-3 border"
                                                :style="{
                                                    borderColor: themeSettings.colors.border,
                                                    borderRadius: themeSettings.components.inputRadius,
                                                    backgroundColor: themeSettings.colors.surface
                                                }" />
                                            <textarea placeholder="ข้อความ" rows="4"
                                                class="w-full px-4 py-3 border resize-none" :style="{
                                                    borderColor: themeSettings.colors.border,
                                                    borderRadius: themeSettings.components.inputRadius,
                                                    backgroundColor: themeSettings.colors.surface
                                                }"></textarea>
                                            <button
                                                class="w-full py-3 text-white font-medium transition-colors hover:opacity-90"
                                                :style="{
                                                    backgroundColor: themeSettings.colors.primary,
                                                    borderRadius: themeSettings.components.buttonRadius
                                                }">
                                                ส่งข้อความ
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Footer Preview -->
                            <div class="px-6 py-8" :style="{
                                backgroundColor: themeSettings.footer.backgroundColor,
                                color: themeSettings.footer.textColor
                            }">
                                <div class="max-w-4xl mx-auto">
                                    <div :class="`grid grid-cols-1 md:grid-cols-${themeSettings.footer.columns} gap-8`">
                                        <div v-for="i in themeSettings.footer.columns" :key="i">
                                            <h4 class="font-semibold mb-4">หมวดหมู่ {{ i }}</h4>
                                            <ul class="space-y-2 text-sm opacity-80">
                                                <li>ลิงก์ 1</li>
                                                <li>ลิงก์ 2</li>
                                                <li>ลิงก์ 3</li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div v-if="themeSettings.footer.showSocialLinks || themeSettings.footer.showNewsletter"
                                        class="mt-8 pt-8 border-t border-current border-opacity-20">
                                        <div class="flex flex-col md:flex-row justify-between items-center gap-4">
                                            <div v-if="themeSettings.footer.showSocialLinks"
                                                class="flex items-center gap-4">
                                                <div v-for="i in 4" :key="i"
                                                    class="w-8 h-8 bg-current opacity-20 rounded"></div>
                                            </div>
                                            <div v-if="themeSettings.footer.showNewsletter"
                                                class="flex items-center gap-2">
                                                <input type="email" placeholder="อีเมลของคุณ"
                                                    class="px-3 py-2 bg-white text-gray-900 rounded" />
                                                <button
                                                    class="px-4 py-2 bg-current bg-opacity-20 rounded">สมัคร</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Bottom Banner Preview -->
                            <div v-if="themeSettings.banner.show && themeSettings.banner.position === 'bottom'"
                                class="flex items-center justify-center text-sm font-medium" :style="{
                                    backgroundColor: themeSettings.banner.backgroundColor,
                                    color: themeSettings.banner.textColor,
                                    height: themeSettings.banner.height
                                }">
                                {{ themeSettings.banner.text }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>