<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useVuelidate } from '@vuelidate/core';
import { required, minLength, maxLength, helpers } from '@vuelidate/validators';

definePageMeta({
    layout: 'dashboard',
    middleware: ['auth'],
});

// Get siteId from route params
const route: any = useRoute();
const siteId = computed(() => route.params.siteId);

// Form data
const form = reactive({
    name: '',
    description: '',
    category: 'modern' as 'basic' | 'modern' | 'business' | 'minimal' | 'creative' | 'ecommerce',
    isPremium: false,
    preview: '',
    settings: {
        colors: {
            primary: '#3B82F6',
            secondary: '#64748B',
            accent: '#F59E0B',
            success: '#10B981',
            warning: '#F59E0B',
            error: '#EF4444',
            background: '#FFFFFF',
            surface: '#F8FAFC',
            text: '#1F2937',
            textSecondary: '#6B7280',
            border: '#E5E7EB'
        },
        typography: {
            fontFamily: 'Inter',
            headingFont: 'Inter',
            fontSize: {
                xs: '12px',
                sm: '14px',
                base: '16px',
                lg: '18px',
                xl: '20px',
                '2xl': '24px',
                '3xl': '30px',
                '4xl': '36px'
            },
            fontWeight: {
                normal: '400',
                medium: '500',
                semibold: '600',
                bold: '700'
            },
            lineHeight: {
                tight: '1.25',
                normal: '1.5',
                relaxed: '1.75'
            }
        },
        layout: {
            containerWidth: '1200px',
            headerHeight: '80px',
            footerHeight: '200px',
            sidebarWidth: '280px',
            spacing: {
                xs: '4px',
                sm: '8px',
                md: '16px',
                lg: '24px',
                xl: '32px',
                '2xl': '48px'
            }
        },
        components: {
            buttonRadius: '8px',
            cardRadius: '12px',
            inputRadius: '6px',
            imageRadius: '8px',
            shadowLevel: 'medium',
            borderWidth: '1px'
        },
        header: {
            backgroundColor: '#FFFFFF',
            textColor: '#1F2937',
            height: '80px',
            sticky: true,
            showLogo: true,
            showNavigation: true,
            showSearch: true,
            showCart: true,
            logoPosition: 'left',
            navigationStyle: 'horizontal'
        },
        footer: {
            backgroundColor: '#1F2937',
            textColor: '#FFFFFF',
            showSocialLinks: true,
            showNewsletter: true,
            showSitemap: true,
            columns: 4
        },
        banner: {
            show: true,
            position: 'top',
            backgroundColor: '#3B82F6',
            textColor: '#FFFFFF',
            height: '40px',
            text: 'ข้อความประกาศ'
        }
    }
});

// Validation rules
const rules = computed(() => ({
    name: {
        required: helpers.withMessage('ชื่อธีมต้องไม่ว่าง', required),
        minLength: helpers.withMessage('ชื่อธีมต้องมีอย่างน้อย 2 ตัวอักษร', minLength(2)),
        maxLength: helpers.withMessage('ชื่อธีมต้องไม่เกิน 100 ตัวอักษร', maxLength(100)),
    },
    description: {
        required: helpers.withMessage('คำอธิบายต้องไม่ว่าง', required),
        maxLength: helpers.withMessage('คำอธิบายต้องไม่เกิน 500 ตัวอักษร', maxLength(500)),
    },
    category: {
        required: helpers.withMessage('ต้องเลือกหมวดหมู่', required),
    },
    preview: {
        required: helpers.withMessage('ต้องมี URL รูปตัวอย่าง', required),
    }
}));

// Initialize Vuelidate
const v$ = useVuelidate(rules, form);

const loading = ref(false);
const activeTab = ref('basic');

// Categories
const categories = ref([
    { value: 'basic', label: 'พื้นฐาน', icon: 'i-heroicons-home', color: 'blue' },
    { value: 'modern', label: 'โมเดิร์น', icon: 'i-heroicons-sparkles', color: 'purple' },
    { value: 'business', label: 'ธุรกิจ', icon: 'i-heroicons-building-office', color: 'green' },
    { value: 'minimal', label: 'มินิมอล', icon: 'i-heroicons-minus', color: 'gray' },
    { value: 'creative', label: 'สร้างสรรค์', icon: 'i-heroicons-paint-brush', color: 'pink' },
    { value: 'ecommerce', label: 'อีคอมเมิร์ซ', icon: 'i-heroicons-shopping-bag', color: 'orange' }
]);

// Available fonts
const availableFonts = ref([
    { value: 'Inter', label: 'Inter' },
    { value: 'Roboto', label: 'Roboto' },
    { value: 'Open Sans', label: 'Open Sans' },
    { value: 'Lato', label: 'Lato' },
    { value: 'Montserrat', label: 'Montserrat' },
    { value: 'Poppins', label: 'Poppins' },
    { value: 'Nunito', label: 'Nunito' },
    { value: 'Noto Sans Thai', label: 'Noto Sans Thai' },
    { value: 'Sarabun', label: 'Sarabun' }
]);

// Shadow levels
const shadowLevels = ref([
    { value: 'none', label: 'ไม่มีเงา' },
    { value: 'sm', label: 'เงาเล็ก' },
    { value: 'medium', label: 'เงากลาง' },
    { value: 'lg', label: 'เงาใหญ่' },
    { value: 'xl', label: 'เงาใหญ่มาก' }
]);

// Tabs
const tabs = ref([
    { key: 'basic', label: 'ข้อมูลพื้นฐาน', icon: 'i-heroicons-information-circle' },
    { key: 'colors', label: 'สี', icon: 'i-heroicons-swatch' },
    { key: 'typography', label: 'ตัวอักษร', icon: 'i-heroicons-language' },
    { key: 'layout', label: 'เลย์เอาต์', icon: 'i-heroicons-squares-2x2' },
    { key: 'components', label: 'คอมโพเนนต์', icon: 'i-heroicons-cube' }
]);

// Color presets
const colorPresets = ref([
    {
        name: 'Blue Ocean',
        colors: {
            primary: '#3B82F6',
            secondary: '#64748B',
            accent: '#06B6D4'
        }
    },
    {
        name: 'Green Nature',
        colors: {
            primary: '#10B981',
            secondary: '#6B7280',
            accent: '#84CC16'
        }
    },
    {
        name: 'Purple Magic',
        colors: {
            primary: '#8B5CF6',
            secondary: '#64748B',
            accent: '#EC4899'
        }
    },
    {
        name: 'Orange Sunset',
        colors: {
            primary: '#F97316',
            secondary: '#6B7280',
            accent: '#EAB308'
        }
    },
    {
        name: 'Dark Mode',
        colors: {
            primary: '#6366F1',
            secondary: '#9CA3AF',
            accent: '#F59E0B',
            background: '#111827',
            surface: '#1F2937',
            text: '#F9FAFB',
            textSecondary: '#D1D5DB'
        }
    }
]);

// Apply color preset
const applyColorPreset = (preset: any) => {
    Object.assign(form.settings.colors, preset.colors);
    useToast().add({
        title: 'สำเร็จ',
        description: `ใช้ชุดสี ${preset.name} เรียบร้อยแล้ว`,
        color: 'success',
    });
};

// Generate theme ID
const generateThemeId = () => {
    return form.name.toLowerCase()
        .replace(/[^\w\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .trim();
};

// Handle form submission
const handleSubmit = async () => {
    const isValid = await v$.value.$validate();
    if (!isValid) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'กรุณาตรวจสอบข้อมูลที่กรอก',
            color: 'error',
        });
        return;
    }

    loading.value = true;
    try {
        // Create theme data
        const themeData = {
            id: generateThemeId(),
            name: form.name,
            description: form.description,
            category: form.category,
            isPremium: form.isPremium,
            preview: form.preview,
            isActive: false,
            settings: form.settings,
            createdAt: new Date().toISOString(),
            author: 'Custom Theme'
        };

        // Mock API call
        console.log('Creating theme:', themeData);
        await new Promise(resolve => setTimeout(resolve, 1000));

        useToast().add({
            title: 'สำเร็จ',
            description: 'สร้างธีมใหม่เรียบร้อยแล้ว',
            color: 'success',
        });

        // Navigate back to themes page
        await navigateTo(`/dashboard/${siteId.value}/themes`);
    } catch (error: any) {
        console.error('Error creating theme:', error);
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: error.message || 'ไม่สามารถสร้างธีมได้',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

// Handle cancel
const handleCancel = () => {
    navigateTo(`/dashboard/${siteId.value}/themes`);
};

// Get selected category info
const selectedCategoryInfo = computed(() => {
    return categories.value.find(cat => cat.value === form.category);
});
</script>

<template>
    <div class="space-y-8">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <div class="flex items-center gap-3 mb-2">
                    <UButton @click="handleCancel" variant="ghost" size="sm" icon="i-heroicons-arrow-left" />
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">สร้างธีมใหม่</h1>
                </div>
                <p class="text-gray-600 dark:text-gray-400">
                    สร้างธีมใหม่สำหรับเว็บไซต์ของคุณ
                </p>
            </div>
            <div class="flex items-center gap-3">
                <UButton @click="handleCancel" variant="outline">
                    ยกเลิก
                </UButton>
                <UButton @click="handleSubmit" :loading="loading"
                    class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
                    <Icon name="i-heroicons-check" class="w-5 h-5 mr-2" />
                    สร้างธีม
                </UButton>
            </div>
        </div>

        <!-- Form -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <UCard class="overflow-hidden sticky top-6">
                    <template #header>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">การตั้งค่า</h3>
                    </template>

                    <div class="space-y-2">
                        <button v-for="tab in tabs" :key="tab.key" @click="activeTab = tab.key" :class="[
                            'w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-colors',
                            activeTab === tab.key
                                ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                                : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                        ]">
                            <Icon :name="tab.icon" class="w-5 h-5" />
                            <span class="font-medium">{{ tab.label }}</span>
                        </button>
                    </div>
                </UCard>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Info Tab -->
                <UCard v-if="activeTab === 'basic'" class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                                <Icon name="i-heroicons-information-circle" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ข้อมูลพื้นฐาน</h2>
                        </div>
                    </template>

                    <div class="space-y-6">
                        <!-- Theme Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                ชื่อธีม <span class="text-red-500">*</span>
                            </label>
                            <UInput v-model="form.name" placeholder="กรอกชื่อธีม"
                                :error="v$.name.$error ? v$.name.$errors[0].$message : ''" class="w-full" />
                        </div>

                        <!-- Description -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                คำอธิบาย <span class="text-red-500">*</span>
                            </label>
                            <UTextarea v-model="form.description" placeholder="อธิบายธีมนี้ (ไม่เกิน 500 ตัวอักษร)"
                                :error="v$.description.$error ? v$.description.$errors[0].$message : ''" :rows="3"
                                class="w-full" />
                        </div>

                        <!-- Category -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                หมวดหมู่ <span class="text-red-500">*</span>
                            </label>
                            <div class="grid grid-cols-2 gap-3">
                                <div v-for="category in categories" :key="category.value" class="relative">
                                    <input :id="`category-${category.value}`" v-model="form.category"
                                        :value="category.value" type="radio" class="sr-only" />
                                    <label :for="`category-${category.value}`" :class="[
                                        'flex flex-col items-center p-4 border-2 rounded-lg cursor-pointer transition-all duration-200',
                                        form.category === category.value
                                            ? `border-${category.color}-500 bg-${category.color}-50 dark:bg-${category.color}-900/20`
                                            : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                                    ]">
                                        <Icon :name="category.icon" :class="[
                                            'w-6 h-6 mb-2',
                                            form.category === category.value ? `text-${category.color}-600` : 'text-gray-400'
                                        ]" />
                                        <span :class="[
                                            'text-sm font-medium',
                                            form.category === category.value ? `text-${category.color}-700 dark:text-${category.color}-300` : 'text-gray-700 dark:text-gray-300'
                                        ]">
                                            {{ category.label }}
                                        </span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Preview URL -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                URL รูปตัวอย่าง <span class="text-red-500">*</span>
                            </label>
                            <UInput v-model="form.preview" placeholder="https://example.com/preview.jpg"
                                :error="v$.preview.$error ? v$.preview.$errors[0].$message : ''" class="w-full" />
                        </div>

                        <!-- Premium -->
                        <div>
                            <UCheckbox v-model="form.isPremium"
                                label="ธีมพรีเมียม (ต้องการแพ็คเกจพรีเมียมเพื่อใช้งาน)" />
                        </div>
                    </div>
                </UCard>

                <!-- Colors Tab -->
                <UCard v-if="activeTab === 'colors'" class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg">
                                <Icon name="i-heroicons-swatch" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">สีของธีม</h2>
                        </div>
                    </template>

                    <div class="space-y-6">
                        <!-- Color Presets -->
                        <div>
                            <label
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">ชุดสีแนะนำ</label>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <button v-for="preset in colorPresets" :key="preset.name"
                                    @click="applyColorPreset(preset)"
                                    class="p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-gray-300 dark:hover:border-gray-500 transition-colors">
                                    <div class="flex gap-2 mb-2">
                                        <div class="w-4 h-4 rounded-full"
                                            :style="{ backgroundColor: preset.colors.primary }"></div>
                                        <div class="w-4 h-4 rounded-full"
                                            :style="{ backgroundColor: preset.colors.secondary }">
                                        </div>
                                        <div class="w-4 h-4 rounded-full"
                                            :style="{ backgroundColor: preset.colors.accent }"></div>
                                    </div>
                                    <p class="text-xs text-gray-600 dark:text-gray-400">{{ preset.name }}</p>
                                </button>
                            </div>
                        </div>

                        <!-- Individual Colors -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีหลัก</label>
                                <div class="flex items-center gap-3">
                                    <input v-model="form.settings.colors.primary" type="color"
                                        class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                    <UInput v-model="form.settings.colors.primary" class="flex-1" />
                                </div>
                            </div>

                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีรอง</label>
                                <div class="flex items-center gap-3">
                                    <input v-model="form.settings.colors.secondary" type="color"
                                        class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                    <UInput v-model="form.settings.colors.secondary" class="flex-1" />
                                </div>
                            </div>

                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีเน้น</label>
                                <div class="flex items-center gap-3">
                                    <input v-model="form.settings.colors.accent" type="color"
                                        class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                    <UInput v-model="form.settings.colors.accent" class="flex-1" />
                                </div>
                            </div>

                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีพื้นหลัง</label>
                                <div class="flex items-center gap-3">
                                    <input v-model="form.settings.colors.background" type="color"
                                        class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                    <UInput v-model="form.settings.colors.background" class="flex-1" />
                                </div>
                            </div>

                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีข้อความ</label>
                                <div class="flex items-center gap-3">
                                    <input v-model="form.settings.colors.text" type="color"
                                        class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                    <UInput v-model="form.settings.colors.text" class="flex-1" />
                                </div>
                            </div>

                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สีข้อความรอง</label>
                                <div class="flex items-center gap-3">
                                    <input v-model="form.settings.colors.textSecondary" type="color"
                                        class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600" />
                                    <UInput v-model="form.settings.colors.textSecondary" class="flex-1" />
                                </div>
                            </div>
                        </div>
                    </div>
                </UCard>

                <!-- Typography Tab -->
                <UCard v-if="activeTab === 'typography'" class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                                <Icon name="i-heroicons-language" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวอักษร</h2>
                        </div>
                    </template>

                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ฟอนต์หลัก</label>
                                <USelect v-model="form.settings.typography.fontFamily" :items="availableFonts"
                                    option-attribute="label" value-attribute="value" class="w-full" />
                            </div>

                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ฟอนต์หัวข้อ</label>
                                <USelect v-model="form.settings.typography.headingFont" :items="availableFonts"
                                    option-attribute="label" value-attribute="value" class="w-full" />
                            </div>

                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ขนาดฟอนต์พื้นฐาน</label>
                                <UInput v-model="form.settings.typography.fontSize.base" class="w-full" />
                            </div>
                        </div>

                        <!-- Font Preview -->
                        <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">ตัวอย่าง</h4>
                            <div
                                :style="{ fontFamily: form.settings.typography.fontFamily, fontSize: form.settings.typography.fontSize.base }">
                                <h1 class="text-2xl font-bold mb-2"
                                    :style="{ fontFamily: form.settings.typography.headingFont }">
                                    หัวข้อใหญ่</h1>
                                <h2 class="text-xl font-semibold mb-2"
                                    :style="{ fontFamily: form.settings.typography.headingFont }">หัวข้อรอง</h2>
                                <p class="mb-2">ข้อความปกติ Lorem ipsum dolor sit amet consectetur adipisicing elit.</p>
                                <p class="text-sm text-gray-600 dark:text-gray-400">ข้อความเล็ก และข้อความรอง</p>
                            </div>
                        </div>
                    </div>
                </UCard>

                <!-- Layout Tab -->
                <UCard v-if="activeTab === 'layout'" class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
                                <Icon name="i-heroicons-squares-2x2" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">เลย์เอาต์</h2>
                        </div>
                    </template>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ความกว้างคอนเทนเนอร์</label>
                            <UInput v-model="form.settings.layout.containerWidth" class="w-full" />
                        </div>

                        <div>
                            <label
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ความสูงส่วนหัว</label>
                            <UInput v-model="form.settings.layout.headerHeight" class="w-full" />
                        </div>

                        <div>
                            <label
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ความสูงส่วนท้าย</label>
                            <UInput v-model="form.settings.layout.footerHeight" class="w-full" />
                        </div>

                        <div>
                            <label
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ความกว้างแถบข้าง</label>
                            <UInput v-model="form.settings.layout.sidebarWidth" class="w-full" />
                        </div>
                    </div>
                </UCard>

                <!-- Components Tab -->
                <UCard v-if="activeTab === 'components'" class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
                                <Icon name="i-heroicons-cube" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">คอมโพเนนต์</h2>
                        </div>
                    </template>

                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">มุมโค้งปุ่ม</label>
                                <UInput v-model="form.settings.components.buttonRadius" class="w-full" />
                            </div>

                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">มุมโค้งการ์ด</label>
                                <UInput v-model="form.settings.components.cardRadius" class="w-full" />
                            </div>

                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">มุมโค้งช่องกรอก</label>
                                <UInput v-model="form.settings.components.inputRadius" class="w-full" />
                            </div>

                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ระดับเงา</label>
                                <USelect v-model="form.settings.components.shadowLevel" :items="shadowLevels"
                                    option-attribute="label" value-attribute="value" class="w-full" />
                            </div>
                        </div>

                        <!-- Component Preview -->
                        <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">ตัวอย่าง</h4>
                            <div class="space-y-3">
                                <button class="px-4 py-2 text-white transition-colors hover:opacity-90" :style="{
                                    backgroundColor: form.settings.colors.primary,
                                    borderRadius: form.settings.components.buttonRadius
                                }">
                                    ปุ่มตัวอย่าง
                                </button>
                                <div class="p-4 border border-gray-200 dark:border-gray-600" :style="{
                                    backgroundColor: form.settings.colors.surface,
                                    borderRadius: form.settings.components.cardRadius
                                }" :class="`shadow-${form.settings.components.shadowLevel}`">
                                    การ์ดตัวอย่าง
                                </div>
                                <input type="text" placeholder="ช่องกรอกตัวอย่าง"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600" :style="{
                                        backgroundColor: form.settings.colors.background,
                                        borderRadius: form.settings.components.inputRadius
                                    }" />
                            </div>
                        </div>
                    </div>
                </UCard>
            </div>

            <!-- Preview -->
            <div class="lg:col-span-1">
                <UCard class="overflow-hidden sticky top-6">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg">
                                <Icon name="i-heroicons-eye" class="w-5 h-5 text-white" />
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">ตัวอย่าง</h3>
                        </div>
                    </template>

                    <div class="space-y-4">
                        <!-- Theme Info -->
                        <div v-if="selectedCategoryInfo" class="flex items-center gap-3">
                            <div
                                :class="`p-2 bg-gradient-to-r from-${selectedCategoryInfo.color}-500 to-${selectedCategoryInfo.color}-600 rounded-lg`">
                                <Icon :name="selectedCategoryInfo.icon" class="w-4 h-4 text-white" />
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{
                                    selectedCategoryInfo.label }}</p>
                                <p class="text-xs text-gray-500">หมวดหมู่</p>
                            </div>
                        </div>

                        <!-- Theme Name -->
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                                {{ form.name || 'ชื่อธีม' }}
                            </h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {{ form.description || 'คำอธิบายธีม' }}
                            </p>
                        </div>

                        <!-- Color Preview -->
                        <div>
                            <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สี</h5>
                            <div class="flex gap-2">
                                <div class="w-8 h-8 rounded border border-gray-200 dark:border-gray-600"
                                    :style="{ backgroundColor: form.settings.colors.primary }"
                                    :title="`สีหลัก: ${form.settings.colors.primary}`"></div>
                                <div class="w-8 h-8 rounded border border-gray-200 dark:border-gray-600"
                                    :style="{ backgroundColor: form.settings.colors.secondary }"
                                    :title="`สีรอง: ${form.settings.colors.secondary}`"></div>
                                <div class="w-8 h-8 rounded border border-gray-200 dark:border-gray-600"
                                    :style="{ backgroundColor: form.settings.colors.accent }"
                                    :title="`สีเน้น: ${form.settings.colors.accent}`"></div>
                            </div>
                        </div>

                        <!-- Typography Preview -->
                        <div>
                            <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ตัวอักษร</h5>
                            <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded">
                                <p class="text-xs text-gray-500 mb-1">{{ form.settings.typography.fontFamily }}</p>
                                <div :style="{ fontFamily: form.settings.typography.fontFamily }">
                                    <h6 class="font-bold">หัวข้อ</h6>
                                    <p class="text-sm">ข้อความ</p>
                                </div>
                            </div>
                        </div>

                        <!-- Premium Badge -->
                        <div v-if="form.isPremium">
                            <UBadge color="yellow" variant="solid" size="sm">
                                <Icon name="i-heroicons-star" class="w-3 h-3 mr-1" />
                                Premium
                            </UBadge>
                        </div>
                    </div>
                </UCard>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.space-y-8>* {
    animation: fadeInUp 0.6s ease-out forwards;
}
</style>