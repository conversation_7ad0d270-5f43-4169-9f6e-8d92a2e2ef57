<script setup lang="ts">

definePageMeta({
    layout: 'dashboard',
    middleware: ['auth'],
});

// Get siteId from route params
const route: any = useRoute();
const siteId = computed(() => route.params.siteId);

// Current theme data
const currentTheme = ref({
    id: 'default',
    name: 'ธีมเริ่มต้น',
    description: 'ธีมพื้นฐานสำหรับเว็บไซต์',
    preview: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800',
    isActive: true,
    settings: {
        colors: {
            primary: '#3B82F6',
            secondary: '#64748B',
            accent: '#F59E0B',
            background: '#FFFFFF',
            surface: '#F8FAFC',
            text: '#1F2937',
            textSecondary: '#6B7280'
        },
        typography: {
            fontFamily: 'Inter',
            headingFont: 'Inter',
            fontSize: {
                base: '16px',
                h1: '2.5rem',
                h2: '2rem',
                h3: '1.5rem'
            }
        },
        layout: {
            containerWidth: '1200px',
            headerHeight: '80px',
            footerHeight: '200px',
            sidebarWidth: '280px'
        },
        components: {
            buttonRadius: '8px',
            cardRadius: '12px',
            inputRadius: '6px',
            shadowLevel: 'medium'
        }
    }
});

// Available themes
const availableThemes = ref([
    {
        id: 'default',
        name: 'ธีมเริ่มต้น',
        description: 'ธีมพื้นฐานสำหรับเว็บไซต์',
        preview: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400',
        category: 'basic',
        isActive: true,
        isPremium: false
    },
    {
        id: 'modern',
        name: 'โมเดิร์น',
        description: 'ธีมสไตล์โมเดิร์นและเรียบง่าย',
        preview: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400',
        category: 'modern',
        isActive: false,
        isPremium: false
    },
    {
        id: 'elegant',
        name: 'หรูหรา',
        description: 'ธีมสำหรับธุรกิจหรูหราและพรีเมียม',
        preview: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400',
        category: 'business',
        isActive: false,
        isPremium: true
    },
    {
        id: 'minimal',
        name: 'มินิมอล',
        description: 'ธีมเรียบง่ายและสะอาดตา',
        preview: 'https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?w=400',
        category: 'minimal',
        isActive: false,
        isPremium: false
    },
    {
        id: 'creative',
        name: 'สร้างสรรค์',
        description: 'ธีมสำหรับงานสร้างสรรค์และศิลปะ',
        preview: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400',
        category: 'creative',
        isActive: false,
        isPremium: true
    },
    {
        id: 'ecommerce',
        name: 'อีคอมเมิร์ซ',
        description: 'ธีมเฉพาะสำหรับร้านค้าออนไลน์',
        preview: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400',
        category: 'ecommerce',
        isActive: false,
        isPremium: true
    },
    // ธีมใหม่ที่เพิ่ม
    {
        id: 'dark-mode',
        name: 'โหมดมืด',
        description: 'ธีมสีเข้มสำหรับการใช้งานในที่มืด',
        preview: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400',
        category: 'modern',
        isActive: false,
        isPremium: false
    },
    {
        id: 'restaurant',
        name: 'ร้านอาหาร',
        description: 'ธีมสำหรับร้านอาหารและคาเฟ่',
        preview: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400',
        category: 'business',
        isActive: false,
        isPremium: true
    },
    {
        id: 'tech-startup',
        name: 'เทคสตาร์ทอัพ',
        description: 'ธีมสำหรับบริษัทเทคโนโลยีและสตาร์ทอัพ',
        preview: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=400',
        category: 'modern',
        isActive: false,
        isPremium: false
    },
    {
        id: 'fashion',
        name: 'แฟชั่น',
        description: 'ธีมสำหรับร้านเสื้อผ้าและแฟชั่น',
        preview: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400',
        category: 'ecommerce',
        isActive: false,
        isPremium: true
    },
    {
        id: 'medical',
        name: 'การแพทย์',
        description: 'ธีมสำหรับคลินิกและโรงพยาบาล',
        preview: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400',
        category: 'business',
        isActive: false,
        isPremium: true
    }
]);

const loading = ref(false);
const searchQuery = ref('');
const selectedCategory = ref('all');

// Theme categories
const categories = ref([
    { value: 'all', label: 'ทั้งหมด', icon: 'i-heroicons-squares-2x2' },
    { value: 'basic', label: 'พื้นฐาน', icon: 'i-heroicons-home' },
    { value: 'modern', label: 'โมเดิร์น', icon: 'i-heroicons-sparkles' },
    { value: 'business', label: 'ธุรกิจ', icon: 'i-heroicons-building-office' },
    { value: 'minimal', label: 'มินิมอล', icon: 'i-heroicons-minus' },
    { value: 'creative', label: 'สร้างสรรค์', icon: 'i-heroicons-paint-brush' },
    { value: 'ecommerce', label: 'อีคอมเมิร์ซ', icon: 'i-heroicons-shopping-bag' }
]);

// Filter themes
const filteredThemes = computed(() => {
    let filtered = availableThemes.value;

    if (searchQuery.value) {
        filtered = filtered.filter(theme =>
            theme.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            theme.description.toLowerCase().includes(searchQuery.value.toLowerCase())
        );
    }

    if (selectedCategory.value !== 'all') {
        filtered = filtered.filter(theme => theme.category === selectedCategory.value);
    }

    return filtered;
});

// Stats
const stats = computed(() => {
    return {
        totalThemes: availableThemes.value.length,
        freeThemes: availableThemes.value.filter(t => !t.isPremium).length,
        premiumThemes: availableThemes.value.filter(t => t.isPremium).length,
        activeTheme: availableThemes.value.find(t => t.isActive)?.name || 'ไม่ระบุ'
    };
});

// Actions
const handleCustomizeTheme = () => {
    navigateTo(`/dashboard/${siteId.value}/themes/customize`);
};

const handlePreviewTheme = (themeId: string) => {
    // Open preview in new tab
    window.open(`/preview/${siteId.value}?theme=${themeId}`, '_blank');
};

const handleActivateTheme = async (themeId: string) => {
    const theme = availableThemes.value.find(t => t.id === themeId);
    if (!theme) return;

    if (theme.isPremium) {
        // Check if user has premium access
        useToast().add({
            title: 'ต้องการแพ็คเกจพรีเมียม',
            description: 'ธีมนี้ต้องการแพ็คเกจพรีเมียมเพื่อใช้งาน',
            color: 'warning',
        });
        return;
    }

    loading.value = true;
    try {
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Update active theme
        availableThemes.value.forEach((t: any) => {
            t.isActive = false;
        });
        theme.isActive = true;
        currentTheme.value.id = themeId;
        currentTheme.value.name = theme.name;

        useToast().add({
            title: 'สำเร็จ',
            description: `เปลี่ยนธีมเป็น "${theme.name}" เรียบร้อยแล้ว`,
            color: 'success',
        });
    } catch (error) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'ไม่สามารถเปลี่ยนธีมได้',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

const handleImportTheme = () => {
    navigateTo(`/dashboard/${siteId.value}/themes/import`);
};

const handleExportTheme = () => {
    // Export current theme settings
    const themeData = {
        ...currentTheme.value,
        exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(themeData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `theme-${currentTheme.value.id}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    useToast().add({
        title: 'สำเร็จ',
        description: 'ส่งออกธีมเรียบร้อยแล้ว',
        color: 'success',
    });
};
</script>

<template>
    <div class="space-y-8">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    จัดการธีม
                </h1>
                <p class="text-gray-600 dark:text-gray-400">
                    ปรับแต่งรูปลักษณ์และการออกแบบของเว็บไซต์
                </p>
            </div>
            <div class="flex flex-wrap items-center gap-3">
                <UButton @click="handleImportTheme" variant="outline" size="sm">
                    <Icon name="i-heroicons-arrow-down-tray" class="w-4 h-4 mr-2" />
                    นำเข้าธีม
                </UButton>
                <UButton @click="handleExportTheme" variant="outline" size="sm">
                    <Icon name="i-heroicons-arrow-up-tray" class="w-4 h-4 mr-2" />
                    ส่งออกธีม
                </UButton>
                <UButton @click="navigateTo(`/dashboard/${siteId}/themes/create`)" variant="outline" size="sm">
                    <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
                    สร้างธีมใหม่
                </UButton>
                <UButton @click="handleCustomizeTheme" size="sm"
                    class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
                    <Icon name="i-heroicons-paint-brush" class="w-4 h-4 mr-2" />
                    ปรับแต่งธีม
                </UButton>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <UCard class="overflow-hidden">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                        <Icon name="i-heroicons-swatch" class="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">ธีมทั้งหมด</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.totalThemes }}</p>
                    </div>
                </div>
            </UCard>

            <UCard class="overflow-hidden">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                        <Icon name="i-heroicons-gift" class="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">ธีมฟรี</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.freeThemes }}</p>
                    </div>
                </div>
            </UCard>

            <UCard class="overflow-hidden">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg">
                        <Icon name="i-heroicons-star" class="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">ธีมพรีเมียม</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.premiumThemes }}</p>
                    </div>
                </div>
            </UCard>

            <UCard class="overflow-hidden">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
                        <Icon name="i-heroicons-check-circle" class="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">ธีมปัจจุบัน</p>
                        <p class="text-lg font-bold text-gray-900 dark:text-white truncate">{{ stats.activeTheme }}</p>
                    </div>
                </div>
            </UCard>
        </div>

        <!-- Current Theme -->
        <UCard class="overflow-hidden">
            <template #header>
                <div class="flex items-center gap-3">
                    <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                        <Icon name="i-heroicons-check-circle" class="w-5 h-5 text-white" />
                    </div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ธีมปัจจุบัน</h2>
                </div>
            </template>

            <div class="flex flex-col md:flex-row gap-6">
                <!-- Theme Preview -->
                <div class="md:w-1/3">
                    <div class="relative rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
                        <img :src="currentTheme.preview" :alt="currentTheme.name" class="w-full h-48 object-cover" />
                        <div class="absolute top-2 right-2">
                            <UBadge color="success" variant="solid" size="sm">
                                <Icon name="i-heroicons-check" class="w-3 h-3 mr-1" />
                                ใช้งานอยู่
                            </UBadge>
                        </div>
                    </div>
                </div>

                <!-- Theme Info -->
                <div class="md:w-2/3 space-y-4">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">{{ currentTheme.name }}</h3>
                        <p class="text-gray-600 dark:text-gray-400">{{ currentTheme.description }}</p>
                    </div>

                    <!-- Quick Settings Preview -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div class="w-8 h-8 rounded-full mx-auto mb-2"
                                :style="{ backgroundColor: currentTheme.settings.colors.primary }"></div>
                            <p class="text-xs text-gray-600 dark:text-gray-400">สีหลัก</p>
                        </div>
                        <div class="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div class="text-sm font-medium mb-1"
                                :style="{ fontFamily: currentTheme.settings.typography.fontFamily }">Aa</div>
                            <p class="text-xs text-gray-600 dark:text-gray-400">ฟอนต์</p>
                        </div>
                        <div class="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div class="w-8 h-4 bg-gray-300 dark:bg-gray-600 rounded mx-auto mb-2"
                                :style="{ borderRadius: currentTheme.settings.components.buttonRadius }"></div>
                            <p class="text-xs text-gray-600 dark:text-gray-400">ปุ่ม</p>
                        </div>
                        <div class="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div class="w-8 h-6 bg-gray-300 dark:bg-gray-600 rounded mx-auto mb-2"
                                :style="{ borderRadius: currentTheme.settings.components.cardRadius }"></div>
                            <p class="text-xs text-gray-600 dark:text-gray-400">การ์ด</p>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center gap-3 pt-4">
                        <UButton @click="handleCustomizeTheme" variant="solid">
                            <Icon name="i-heroicons-paint-brush" class="w-4 h-4 mr-2" />
                            ปรับแต่ง
                        </UButton>
                        <UButton @click="handlePreviewTheme(currentTheme.id)" variant="outline">
                            <Icon name="i-heroicons-eye" class="w-4 h-4 mr-2" />
                            ดูตัวอย่าง
                        </UButton>
                        <UButton @click="handleExportTheme" variant="outline">
                            <Icon name="i-heroicons-arrow-up-tray" class="w-4 h-4 mr-2" />
                            ส่งออก
                        </UButton>
                    </div>
                </div>
            </div>
        </UCard>

        <!-- Filters -->
        <UCard class="overflow-hidden">
            <div class="flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <UInput v-model="searchQuery" placeholder="ค้นหาธีม..." class="w-full">
                        <template #leading>
                            <Icon name="i-heroicons-magnifying-glass" class="w-4 h-4" />
                        </template>
                    </UInput>
                </div>
                <div class="flex items-center gap-2 overflow-x-auto">
                    <UButton v-for="category in categories" :key="category.value"
                        @click="selectedCategory = category.value"
                        :variant="selectedCategory === category.value ? 'solid' : 'outline'" size="sm"
                        class="whitespace-nowrap">
                        <Icon :name="category.icon" class="w-4 h-4 mr-2" />
                        {{ category.label }}
                    </UButton>
                </div>
            </div>
        </UCard>

        <!-- Available Themes -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <UCard v-for="theme in filteredThemes" :key="theme.id"
                class="overflow-hidden hover:shadow-lg transition-all duration-300 group">

                <!-- Theme Preview -->
                <div class="relative">
                    <img :src="theme.preview" :alt="theme.name"
                        class="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105" />

                    <!-- Overlay -->
                    <div
                        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                        <div
                            class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center gap-2">
                            <UButton @click="handlePreviewTheme(theme.id)" size="sm" variant="solid" color="neutral">
                                <Icon name="i-heroicons-eye" class="w-4 h-4" />
                            </UButton>
                            <UButton v-if="!theme.isActive" @click="handleActivateTheme(theme.id)" size="sm"
                                variant="solid" color="neutral">
                                <Icon name="i-heroicons-check" class="w-4 h-4" />
                            </UButton>
                        </div>
                    </div>

                    <!-- Badges -->
                    <div class="absolute top-2 left-2 flex gap-2">
                        <UBadge v-if="theme.isActive" color="success" variant="solid" size="sm">
                            <Icon name="i-heroicons-check" class="w-3 h-3 mr-1" />
                            ใช้งานอยู่
                        </UBadge>
                        <UBadge v-if="theme.isPremium" color="warning" variant="solid" size="sm">
                            <Icon name="i-heroicons-star" class="w-3 h-3 mr-1" />
                            Premium
                        </UBadge>
                    </div>
                </div>

                <!-- Theme Info -->
                <div class="p-6">
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex-1 min-w-0">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate mb-1">
                                {{ theme.name }}
                            </h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                                {{ theme.description }}
                            </p>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center gap-2 mt-4">
                        <UButton v-if="!theme.isActive" @click="handleActivateTheme(theme.id)" :loading="loading"
                            size="sm" class="flex-1" :disabled="theme.isPremium">
                            <Icon name="i-heroicons-check" class="w-4 h-4 mr-2" />
                            {{ theme.isPremium ? 'ต้องการ Premium' : 'เปิดใช้งาน' }}
                        </UButton>
                        <UButton v-else size="sm" class="flex-1" disabled>
                            <Icon name="i-heroicons-check-circle" class="w-4 h-4 mr-2" />
                            ใช้งานอยู่
                        </UButton>
                        <UButton @click="handlePreviewTheme(theme.id)" variant="outline" size="sm">
                            <Icon name="i-heroicons-eye" class="w-4 h-4" />
                        </UButton>
                    </div>
                </div>
            </UCard>
        </div>

        <!-- Empty State -->
        <div v-if="filteredThemes.length === 0" class="text-center py-12">
            <div
                class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                <Icon name="i-heroicons-swatch" class="w-12 h-12 text-gray-400" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {{ searchQuery ? 'ไม่พบธีมที่ค้นหา' : 'ไม่มีธีมในหมวดหมู่นี้' }}
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                {{ searchQuery ? 'ลองเปลี่ยนคำค้นหาหรือหมวดหมู่' : 'เลือกหมวดหมู่อื่นเพื่อดูธีมเพิ่มเติม' }}
            </p>
        </div>
    </div>
</template>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Add smooth animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.space-y-8>* {
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.hover\:shadow-lg:hover {
    transform: translateY(-2px);
}
</style>