<script setup lang="ts">
import { useRoute } from 'vue-router';

const { t } = useI18n();

// ใช้ SEO meta สำหรับหน้า blog
useBlogSeo();

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// Use composables
const { $apiFetch } = useNuxtApp();
const { getPosts, createPost, updatePost, deletePost } = useBlog();

// Reactive data
const posts = ref<any[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);
const searchQuery = ref('');
const selectedCategory = ref('all');
const selectedStatus = ref('all');
const sortBy = ref('created_at');
const sortOrder = ref('desc');
const currentPage = ref(1);
const itemsPerPage = ref(12);
const viewMode = ref<'grid' | 'list'>('grid');

// Filters
const categories = ref([
  { value: 'all', label: 'ทุกหมวดหมู่' },
  { value: 'technology', label: 'เทคโนโลยี' },
  { value: 'business', label: 'ธุรกิจ' },
  { value: 'lifestyle', label: 'ไลฟ์สไตล์' },
  { value: 'health', label: 'สุขภาพ' },
  { value: 'education', label: 'การศึกษา' },
]);

const statuses = ref([
  { value: 'all', label: 'ทุกสถานะ' },
  { value: 'published', label: 'เผยแพร่แล้ว' },
  { value: 'draft', label: 'ร่าง' },
  { value: 'scheduled', label: 'ตั้งเวลา' },
  { value: 'archived', label: 'เก็บถาวร' },
]);

const sortOptions = ref([
  { value: 'created_at', label: 'วันที่สร้าง' },
  { value: 'title', label: 'ชื่อบทความ' },
  { value: 'views', label: 'จำนวนผู้เข้าชม' },
  { value: 'likes', label: 'จำนวนไลค์' },
  { value: 'published_at', label: 'วันที่เผยแพร่' },
]);

// Analytics data
const analytics = ref({
  totalPosts: 28,
  publishedPosts: 24,
  draftPosts: 4,
  totalViews: 15420,
  totalLikes: 892,
  averageViews: 643,
  topCategory: 'เทคโนโลยี',
  growthRate: 8.5,
});

// Format number
const formatNumber = (num: number) => {
  return new Intl.NumberFormat('th-TH').format(num);
};

// Get status badge class
const getStatusBadgeClass = (status: string) => {
  const classes = {
    published: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    draft: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    scheduled: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    archived: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
  };
  return classes[status as keyof typeof classes] || classes.draft;
};

// Get status label
const getStatusLabel = (status: string) => {
  const labels = {
    published: 'เผยแพร่แล้ว',
    draft: 'ร่าง',
    scheduled: 'ตั้งเวลา',
    archived: 'เก็บถาวร',
  };
  return labels[status as keyof typeof labels] || status;
};

// Fetch posts
const fetchPosts = async () => {
  loading.value = true;
  error.value = null;
  try {
    const response = await getPosts(siteId.value, {});
    if (response?.success && response?.data) {
      posts.value = response.data.posts || [];
    }
  } catch (err) {
    error.value = 'เกิดข้อผิดพลาดในการโหลดข้อมูล';
    console.error('Error fetching posts:', err);
  } finally {
    loading.value = false;
  }
};

// Computed filtered posts
const filteredPosts = computed(() => {
  let filtered = posts.value || [];

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(
      (post: any) =>
        post.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  // Category filter
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter((post: any) => post.category === selectedCategory.value);
  }

  // Status filter
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter((post: any) => post.status === selectedStatus.value);
  }

  // Sort
  filtered.sort((a: any, b: any) => {
    let aVal = a[sortBy.value as keyof typeof a];
    let bVal = b[sortBy.value as keyof typeof b];

    if (typeof aVal === 'string') {
      aVal = aVal.toLowerCase();
      bVal = bVal.toLowerCase();
    }

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });

  return filtered;
});

// Paginated posts
const paginatedPosts = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredPosts.value.slice(start, end);
});

// Total pages
const totalPages = computed(() => {
  return Math.ceil(filteredPosts.value.length / itemsPerPage.value);
});

// Fetch posts on mount
onMounted(async () => {
  await fetchPosts();
});

// Watch for filter changes
watch([searchQuery, selectedCategory, selectedStatus], () => {
  currentPage.value = 1;
});

// Methods
const handleSearch = () => {
  currentPage.value = 1;
};

const handleSort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = field;
    sortOrder.value = 'desc';
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handleViewPost = (post: any) => {
  navigateTo(`/dashboard/${siteId.value}/blog/${post.id}`);
};

const handleEditPost = (post: any) => {
  navigateTo(`/dashboard/${siteId.value}/blog/${post.id}/edit`);
};

const handleDeletePost = async (post: any) => {
  if (confirm(`คุณต้องการลบบทความ "${post.title}" ใช่หรือไม่?`)) {
    try {
      await deletePost(siteId.value, post.id);
      await fetchPosts();
    } catch (err) {
      console.error('Error deleting post:', err);
    }
  }
};

const handleCreatePost = () => {
  navigateTo(`/dashboard/${siteId.value}/blog/create`);
};

const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'grid' ? 'list' : 'grid';
};
</script>

<template>
  <div class="space-y-8">
    <!-- Enhanced Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          จัดการบล็อก
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          จัดการบทความและเนื้อหาของคุณ
        </p>
      </div>
      <div class="flex items-center gap-3">
        <UButton
          @click="toggleViewMode"
          variant="outline"
          size="sm"
          class="hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <Icon 
            :name="viewMode === 'grid' ? 'i-heroicons-list-bullet' : 'i-heroicons-squares-2x2'" 
            class="w-4 h-4 mr-2" 
          />
          {{ viewMode === 'grid' ? 'รายการ' : 'กริด' }}
        </UButton>
        <UButton 
          @click="handleCreatePost"
          class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
          เขียนบทความใหม่
        </UButton>
      </div>
    </div>

    <!-- Enhanced Analytics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">บทความทั้งหมด</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalPosts }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">+{{ analytics.growthRate }}% จากเดือนที่แล้ว</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-document-text" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">เผยแพร่แล้ว</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.publishedPosts }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">{{ Math.round((analytics.publishedPosts / analytics.totalPosts) * 100) }}% ของทั้งหมด</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-check-circle" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ผู้เข้าชมรวม</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(analytics.totalViews) }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">เฉลี่ย {{ formatNumber(analytics.averageViews) }}/บทความ</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-eye" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ไลค์รวม</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(analytics.totalLikes) }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">เฉลี่ย {{ Math.round(analytics.totalLikes / analytics.totalPosts) }}/บทความ</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-heart" class="w-8 h-8" />
          </div>
        </div>
      </UCard>
    </div>

    <!-- Enhanced Filters -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-funnel" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวกรองและค้นหา</h2>
        </div>
      </template>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหาบทความ</label>
          <UInput
            v-model="searchQuery"
            placeholder="ค้นหาตามชื่อหรือเนื้อหา..."
            icon="i-heroicons-magnifying-glass"
            @input="handleSearch"
            class="w-full"
          />
        </div>

        <!-- Category Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">หมวดหมู่</label>
          <USelect
            v-model="selectedCategory"
            :items="categories"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกหมวดหมู่"
            class="w-full"
          />
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สถานะ</label>
          <USelect
            v-model="selectedStatus"
            :items="statuses"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกสถานะ"
            class="w-full"
          />
        </div>

        <!-- Sort -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เรียงลำดับ</label>
          <div class="flex gap-2">
            <USelect
              v-model="sortBy"
              :items="sortOptions"
              option-attribute="label"
              value-attribute="value"
              class="flex-1"
            />
            <UButton
              @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'"
              variant="outline"
              size="sm"
              class="px-3"
            >
              <Icon 
                :name="sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down'" 
                class="w-4 h-4" 
              />
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Enhanced Posts Grid/List -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <Icon name="i-heroicons-document-text" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รายการบทความ</h2>
            <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-400 rounded-full">
              {{ filteredPosts.length }} รายการ
            </span>
          </div>
          <div class="flex items-center gap-2">
            <UButton variant="ghost" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
              <Icon name="i-heroicons-arrow-down-tray" class="w-4 h-4 mr-1" />
              ส่งออก
            </UButton>
            <UButton variant="ghost" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
              <Icon name="i-heroicons-cog-6-tooth" class="w-4 h-4" />
            </UButton>
          </div>
        </div>
      </template>
      
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="flex items-center gap-3">
          <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-blue-600" />
          <span class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</span>
        </div>
      </div>

      <div v-else-if="error" class="text-center py-12">
        <div class="text-red-600 dark:text-red-400 mb-4">
          <Icon name="i-heroicons-exclamation-triangle" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">เกิดข้อผิดพลาด</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">{{ error }}</p>
        <UButton @click="fetchPosts()" variant="outline">
          ลองใหม่
        </UButton>
      </div>

      <div v-else-if="paginatedPosts.length === 0" class="text-center py-12">
        <div class="text-gray-400 dark:text-gray-500 mb-4">
          <Icon name="i-heroicons-document-text" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">ไม่พบบทความ</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          ยังไม่มีบทความในระบบ หรือไม่ตรงกับเงื่อนไขการค้นหา
        </p>
        <UButton @click="handleCreatePost" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
          เขียนบทความแรก
        </UButton>
      </div>

      <div v-else>
        <!-- Grid View -->
        <div v-if="viewMode === 'grid'" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <div 
            v-for="post in paginatedPosts" 
            :key="post.id"
            class="group bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 hover:scale-105 overflow-hidden"
          >
            <!-- Post Image -->
            <div class="relative h-48 bg-gradient-to-br from-blue-500 to-purple-600 overflow-hidden">
              <div class="absolute inset-0 bg-black/20"></div>
              <div class="absolute top-4 right-4">
                <span 
                  class="px-2 py-1 text-xs rounded-full font-medium text-white backdrop-blur-sm"
                  :class="getStatusBadgeClass(post.status)"
                >
                  {{ getStatusLabel(post.status) }}
                </span>
              </div>
              <div class="absolute bottom-4 left-4 right-4">
                <span class="px-2 py-1 text-xs bg-white/90 dark:bg-gray-800/90 text-gray-700 dark:text-gray-300 rounded-full">
                  {{ post.category }}
                </span>
              </div>
            </div>

            <!-- Post Content -->
            <div class="p-6">
              <h3 class="font-semibold text-gray-900 dark:text-white text-lg mb-2 line-clamp-2">
                {{ post.title }}
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
                {{ post.excerpt }}
              </p>

              <!-- Post Stats -->
              <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-4">
                <div class="flex items-center gap-4">
                  <span class="flex items-center gap-1">
                    <Icon name="i-heroicons-eye" class="w-3 h-3" />
                    {{ formatNumber(post.views || 0) }}
                  </span>
                  <span class="flex items-center gap-1">
                    <Icon name="i-heroicons-heart" class="w-3 h-3" />
                    {{ formatNumber(post.likes || 0) }}
                  </span>
                </div>
                <span>{{ new Date(post.created_at).toLocaleDateString('th-TH') }}</span>
              </div>

              <!-- Actions -->
              <div class="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700">
                <UButton
                  @click="handleViewPost(post)"
                  variant="ghost"
                  size="sm"
                  class="hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                >
                  <Icon name="i-heroicons-eye" class="w-4 h-4 mr-1" />
                  ดู
                </UButton>
                <UButton
                  @click="handleEditPost(post)"
                  variant="ghost"
                  size="sm"
                  class="hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                >
                  <Icon name="i-heroicons-pencil-square" class="w-4 h-4 mr-1" />
                  แก้ไข
                </UButton>
                <UButton
                  @click="handleDeletePost(post)"
                  variant="ghost"
                  size="sm"
                  class="hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400"
                >
                  <Icon name="i-heroicons-trash" class="w-4 h-4" />
                </UButton>
              </div>
            </div>
          </div>
        </div>

        <!-- List View -->
        <div v-else class="space-y-4">
          <div 
            v-for="post in paginatedPosts" 
            :key="post.id"
            class="flex items-center gap-4 p-4 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 hover:scale-[1.02]"
          >
            <!-- Post Avatar -->
            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-semibold text-lg">
              {{ post.title.charAt(0).toUpperCase() }}
            </div>

            <!-- Post Info -->
            <div class="flex-1 min-w-0">
              <div class="flex items-start justify-between mb-2">
                <h3 class="font-semibold text-gray-900 dark:text-white text-lg line-clamp-1">
                  {{ post.title }}
                </h3>
                <span 
                  class="px-2 py-1 text-xs rounded-full font-medium ml-2 flex-shrink-0"
                  :class="getStatusBadgeClass(post.status)"
                >
                  {{ getStatusLabel(post.status) }}
                </span>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                {{ post.excerpt }}
              </p>
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                  <span class="flex items-center gap-1">
                    <Icon name="i-heroicons-eye" class="w-3 h-3" />
                    {{ formatNumber(post.views || 0) }}
                  </span>
                  <span class="flex items-center gap-1">
                    <Icon name="i-heroicons-heart" class="w-3 h-3" />
                    {{ formatNumber(post.likes || 0) }}
                  </span>
                  <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full">
                    {{ post.category }}
                  </span>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  {{ new Date(post.created_at).toLocaleDateString('th-TH') }}
                </span>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center gap-2">
              <UButton
                @click="handleViewPost(post)"
                variant="ghost"
                size="sm"
                class="hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400"
              >
                <Icon name="i-heroicons-eye" class="w-4 h-4" />
              </UButton>
              <UButton
                @click="handleEditPost(post)"
                variant="ghost"
                size="sm"
                class="hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400"
              >
                <Icon name="i-heroicons-pencil-square" class="w-4 h-4" />
              </UButton>
              <UButton
                @click="handleDeletePost(post)"
                variant="ghost"
                size="sm"
                class="hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400"
              >
                <Icon name="i-heroicons-trash" class="w-4 h-4" />
              </UButton>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Pagination -->
      <div v-if="totalPages > 1" class="flex items-center justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} ถึง {{ Math.min(currentPage * itemsPerPage, filteredPosts.length) }} 
          จาก {{ filteredPosts.length }} รายการ
        </div>
        <div class="flex items-center gap-2">
          <UButton
            @click="handlePageChange(currentPage - 1)"
            :disabled="currentPage === 1"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-left" class="w-4 h-4" />
          </UButton>
          
          <div class="flex items-center gap-1">
            <UButton
              v-for="page in Math.min(5, totalPages)"
              :key="page"
              @click="handlePageChange(page)"
              :variant="currentPage === page ? 'solid' : 'outline'"
              size="sm"
              class="w-8 h-8 p-0"
            >
              {{ page }}
            </UButton>
          </div>
          
          <UButton
            @click="handlePageChange(currentPage + 1)"
            :disabled="currentPage === totalPages"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-right" class="w-4 h-4" />
          </UButton>
        </div>
      </div>
    </UCard>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Line clamp utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 