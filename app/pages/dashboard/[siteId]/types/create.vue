<script setup lang="ts">
import { useVuelidate } from '@vuelidate/core';
import { required, minLength, helpers } from '@vuelidate/validators';
import { computed, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useTypes } from '~/composables/useTypes';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// SEO meta สำหรับหน้าสร้างประเภท
useSeoMeta({
  title: 'สร้างประเภทใหม่ - ระบบจัดการร้านค้า',
  description: 'สร้างประเภทเนื้อหาใหม่ เช่น ประเภทสินค้า ประเภทข่าวสาร ประเภทบล็อก',
  keywords: 'สร้างประเภท, ประเภทสินค้า, ประเภทข่าวสาร, ประเภทบล็อก, ระบบร้านค้า',
  ogTitle: 'สร้างประเภทใหม่ - ระบบจัดการร้านค้า',
  ogDescription: 'สร้างประเภทเนื้อหาใหม่ เช่น ประเภทสินค้า ประเภทข่าวสาร ประเภทบล็อก',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => {
  const params = route.params as { siteId: string };
  return params.siteId || '';
});

// Use composables
const { createType, checkSlugAvailability } = useTypes();

// Form data
const form = reactive({
  name: '',
  description: '',
  slug: '',
  contentType: 'product',
  icon: '',
  status: 'active',
  order: 0,
  seo: {
    title: '',
    description: '',
    keywords: '',
  },
  settings: {
    showInMenu: true,
    showInHomepage: false,
    allowSubtypes: false,
    hasPrice: false,
    hasInventory: false,
    hasGallery: false,
    hasReviews: false,
    hasTags: false,
  },
  fields: [] as Array<{
    name: string;
    type: string;
    required: boolean;
    options?: string[];
  }>,
});

// Vuelidate validation rules
const rules = {
  name: { 
    required: helpers.withMessage('กรุณากรอกชื่อประเภท', required),
    minLength: helpers.withMessage('ชื่อประเภทต้องมีอย่างน้อย 2 ตัวอักษร', minLength(2))
  },
  description: { 
    required: helpers.withMessage('กรุณากรอกรายละเอียดประเภท', required),
    minLength: helpers.withMessage('รายละเอียดต้องมีอย่างน้อย 10 ตัวอักษร', minLength(10))
  },
  slug: { 
    required: helpers.withMessage('กรุณากรอก slug', required),
    pattern: helpers.withMessage('Slug ต้องเป็นตัวอักษรภาษาอังกฤษ ตัวเลข และ - เท่านั้น', helpers.regex(/^[a-z0-9-]+$/))
  },
  contentType: { 
    required: helpers.withMessage('กรุณาเลือกประเภทเนื้อหา', required)
  },
  order: { 
    minValue: helpers.withMessage('ลำดับต้องมากกว่าหรือเท่ากับ 0', helpers.regex(/^\d+$/))
  },
};

// Vuelidate instance
const v$ = useVuelidate(rules, form);

// Content type options
const contentTypes = ref([
  { value: 'product', label: 'สินค้า', icon: 'solar:box-line-duotone' },
  { value: 'news', label: 'ข่าวสาร', icon: 'solar:newspaper-line-duotone' },
  { value: 'blog', label: 'บล็อก', icon: 'solar:document-text-line-duotone' },
  { value: 'page', label: 'หน้าเว็บ', icon: 'solar:file-line-duotone' },
  { value: 'event', label: 'กิจกรรม', icon: 'solar:calendar-line-duotone' },
  { value: 'service', label: 'บริการ', icon: 'solar:briefcase-line-duotone' },
  { value: 'gallery', label: 'แกลเลอรี่', icon: 'solar:gallery-line-duotone' },
  { value: 'testimonial', label: 'รีวิว', icon: 'solar:star-line-duotone' },
  { value: 'faq', label: 'คำถามที่พบบ่อย', icon: 'solar:question-circle-line-duotone' },
]);

// Field type options
const fieldTypes = ref([
  { value: 'text', label: 'ข้อความ' },
  { value: 'textarea', label: 'ข้อความยาว' },
  { value: 'number', label: 'ตัวเลข' },
  { value: 'email', label: 'อีเมล' },
  { value: 'url', label: 'ลิงก์' },
  { value: 'date', label: 'วันที่' },
  { value: 'datetime', label: 'วันที่และเวลา' },
  { value: 'select', label: 'ตัวเลือก' },
  { value: 'multiselect', label: 'ตัวเลือกหลายข้อ' },
  { value: 'checkbox', label: 'ช่องทำเครื่องหมาย' },
  { value: 'radio', label: 'ปุ่มตัวเลือก' },
  { value: 'file', label: 'ไฟล์' },
  { value: 'image', label: 'รูปภาพ' },
  { value: 'color', label: 'สี' },
  { value: 'json', label: 'JSON' },
]);

// Status options
const statuses = ref([
  { value: 'active', label: 'เปิดใช้งาน' },
  { value: 'inactive', label: 'ปิดใช้งาน' },
  { value: 'draft', label: 'ร่าง' },
  { value: 'archived', label: 'เก็บถาวร' },
]);

// Computed properties for error messages
const nameError = computed(() => {
  const error = v$.value.name.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});

const descriptionError = computed(() => {
  const error = v$.value.description.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});

const slugError = computed(() => {
  const error = v$.value.slug.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});

const contentTypeError = computed(() => {
  const error = v$.value.contentType.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});

const orderError = computed(() => {
  const error = v$.value.order.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});

// Auto-generate slug from name
const generateSlug = (name: string) => {
  return name
    .toLowerCase()
    .replace(/[^\u0E00-\u0E7F\w\s-]/g, '') // เก็บเฉพาะภาษาไทย, ภาษาอังกฤษ, ตัวเลข, space, -
    .replace(/\s+/g, '-') // แทนที่ space ด้วย -
    .replace(/-+/g, '-') // ลบ - ที่ซ้ำ
    .replace(/^-+|-+$/g, ''); // ลบ - ที่หัวและท้าย
};

// Watch name to auto-generate slug
watch(() => form.name, (newName) => {
  if (newName && !form.slug) {
    form.slug = generateSlug(newName);
  }
});

// Watch contentType to set default settings
watch(() => form.contentType, (newContentType) => {
  // Set default settings based on content type
  switch (newContentType) {
    case 'product':
      form.settings.hasPrice = true;
      form.settings.hasInventory = true;
      form.settings.hasGallery = true;
      form.settings.hasReviews = true;
      form.settings.hasTags = true;
      break;
    case 'news':
    case 'blog':
      form.settings.hasGallery = true;
      form.settings.hasTags = true;
      break;
    case 'gallery':
      form.settings.hasGallery = true;
      break;
    case 'testimonial':
      form.settings.hasReviews = true;
      break;
    default:
      form.settings.hasPrice = false;
      form.settings.hasInventory = false;
      form.settings.hasGallery = false;
      form.settings.hasReviews = false;
      form.settings.hasTags = false;
  }
});

// Field management
const addField = () => {
  form.fields.push({
    name: '',
    type: 'text',
    required: false,
    options: [],
  });
};

const removeField = (index: number) => {
  form.fields.splice(index, 1);
};

const addOption = (fieldIndex: number) => {
  if (!form.fields[fieldIndex].options) {
    form.fields[fieldIndex].options = [];
  }
  form.fields[fieldIndex].options!.push('');
};

const removeOption = (fieldIndex: number, optionIndex: number) => {
  form.fields[fieldIndex].options!.splice(optionIndex, 1);
};

// Handle form submission
const handleSubmit = async () => {
  const isValid = await v$.value.$validate();
  if (!isValid) return;

  loading.value = true;
  try {
    const { $apiFetch } = useNuxtApp();
    const response = await $apiFetch('/types', {
      method: 'POST',
      body: {
        ...form,
        siteId: siteId.value,
      },
    });
    
    if (response?.success) {
      useToast().add({
        title: 'สำเร็จ',
        description: 'สร้างประเภทเรียบร้อยแล้ว',
        color: 'success',
      });
      
      // Navigate back to types list
      await navigateTo(`/dashboard/${siteId.value}/types`);
    }
  } catch (err: any) {
    useToast().add({
      title: 'ผิดพลาด',
      description: err.message || 'ไม่สามารถสร้างประเภทได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// Navigation
const router = useRouter();
const navigateBack = () => router.push(`/dashboard/${siteId.value}/types`);

// Loading state
const loading = ref(false);
</script>

<template>
  <UContainer>
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          สร้างประเภทใหม่
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          เพิ่มประเภทเนื้อหาใหม่เพื่อจัดระเบียบเนื้อหาของคุณ
        </p>
      </div>
      <UButton
        icon="solar:arrow-left-line-duotone"
        @click="navigateBack"
        variant="soft"
        size="lg"
      >
        กลับไป
      </UButton>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Left Column - Form -->
      <div class="lg:col-span-2">
        <UCard variant="soft">
          <div class="space-y-8">
            <!-- Basic Info Section -->
            <div class="space-y-6">
              <h2 class="text-lg font-semibold flex items-center gap-2">
                <Icon name="solar:info-circle-line-duotone" class="size-7 text-primary" />
                ข้อมูลพื้นฐาน
              </h2>

              <UFormField size="xl" label="ชื่อประเภท" name="name" required :error="nameError">
                <UInput 
                  v-model="form.name" 
                  placeholder="ชื่อประเภท เช่น สมาร์ทโฟน, ข่าวเทคโนโลยี" 
                  class="w-full"
                  @blur="() => { if (v$.name) v$.name.$touch(); }"
                />
              </UFormField>

              <UFormField size="xl" label="Slug" name="slug" required :error="slugError">
                <UInput 
                  v-model="form.slug" 
                  placeholder="slug เช่น smartphones, tech-news" 
                  class="w-full"
                  @blur="() => { if (v$.slug) v$.slug.$touch(); }"
                />
                <template #help>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    URL-friendly name ใช้สำหรับลิงก์ เช่น /type/smartphones
                  </p>
                </template>
              </UFormField>

              <UFormField size="xl" label="ประเภทเนื้อหา" name="contentType" required :error="contentTypeError">
                <USelectMenu
                  v-model="form.contentType"
                  :items="contentTypes"
                  value-key="value"
                  option-attribute="label"
                  placeholder="เลือกประเภทเนื้อหา"
                  class="w-full"
                  @blur="() => { if (v$.contentType) v$.contentType.$touch(); }"
                >
                  <template #option="{ item }">
                    <div class="flex items-center gap-3">
                      <Icon :name="item.icon" class="w-5 h-5" />
                      <span>{{ item.label }}</span>
                    </div>
                  </template>
                </USelectMenu>
              </UFormField>

              <UFormField size="xl" label="รายละเอียด" name="description" required :error="descriptionError">
                <UTextarea 
                  v-model="form.description" 
                  placeholder="รายละเอียดประเภท..." 
                  :rows="4" 
                  class="w-full"
                  @blur="() => { if (v$.description) v$.description.$touch(); }"
                />
              </UFormField>

              <UFormField size="xl" label="ไอคอน" name="icon">
                <UInput 
                  v-model="form.icon" 
                  placeholder="URL ไอคอน (ไม่บังคับ)" 
                  class="w-full"
                />
              </UFormField>
            </div>

            <!-- Settings Section -->
            <div class="space-y-6">
              <h2 class="text-lg font-semibold flex items-center gap-2">
                <Icon name="solar:settings-line-duotone" class="size-7 text-primary" />
                การตั้งค่า
              </h2>

              <UFormField label="สถานะ" name="status">
                <USelectMenu
                  v-model="form.status"
                  :items="statuses"
                  value-key="value"
                  option-attribute="label"
                  placeholder="เลือกสถานะ"
                />
              </UFormField>

              <UFormField label="ลำดับการแสดงผล" name="order" :error="orderError">
                <UInputNumber 
                  v-model.number="form.order" 
                  min="0" 
                  placeholder="0" 
                  class="w-full"
                  @blur="() => { if (v$.order) v$.order.$touch(); }"
                />
                <template #help>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    ลำดับการแสดงผลในเมนู (น้อย = แสดงก่อน)
                  </p>
                </template>
              </UFormField>

              <div class="space-y-4">
                <h3 class="text-md font-medium text-gray-900 dark:text-white">ตัวเลือกเพิ่มเติม</h3>
                
                <UFormField label="แสดงในเมนู" name="showInMenu">
                  <UCheckbox v-model="form.settings.showInMenu" />
                </UFormField>

                <UFormField label="แสดงในหน้าแรก" name="showInHomepage">
                  <UCheckbox v-model="form.settings.showInHomepage" />
                </UFormField>

                <UFormField label="อนุญาตประเภทย่อย" name="allowSubtypes">
                  <UCheckbox v-model="form.settings.allowSubtypes" />
                </UFormField>
              </div>
            </div>

            <!-- Features Section -->
            <div class="space-y-6">
              <h2 class="text-lg font-semibold flex items-center gap-2">
                <Icon name="solar:widget-line-duotone" class="size-7 text-primary" />
                คุณสมบัติ
              </h2>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <UFormField label="มีราคา" name="hasPrice">
                  <UCheckbox v-model="form.settings.hasPrice" />
                </UFormField>

                <UFormField label="มีคลังสินค้า" name="hasInventory">
                  <UCheckbox v-model="form.settings.hasInventory" />
                </UFormField>

                <UFormField label="มีแกลเลอรี่" name="hasGallery">
                  <UCheckbox v-model="form.settings.hasGallery" />
                </UFormField>

                <UFormField label="มีรีวิว" name="hasReviews">
                  <UCheckbox v-model="form.settings.hasReviews" />
                </UFormField>

                <UFormField label="มีแท็ก" name="hasTags">
                  <UCheckbox v-model="form.settings.hasTags" />
                </UFormField>
              </div>
            </div>

            <!-- Custom Fields Section -->
            <div class="space-y-6">
              <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold flex items-center gap-2">
                  <Icon name="solar:widget-line-duotone" class="size-7 text-primary" />
                  ฟิลด์เพิ่มเติม
                </h2>
                <UButton
                  icon="solar:add-circle-line-duotone"
                  @click="addField"
                  size="sm"
                  color="primary"
                >
                  เพิ่มฟิลด์
                </UButton>
              </div>

              <div v-if="form.fields.length === 0" class="text-center py-8 text-gray-500">
                <Icon name="solar:widget-line-duotone" class="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>ยังไม่มีฟิลด์เพิ่มเติม</p>
                <p class="text-sm">คลิก "เพิ่มฟิลด์" เพื่อเริ่มต้น</p>
              </div>

              <div v-else class="space-y-4">
                <div v-for="(field, fieldIndex) in form.fields" :key="fieldIndex" class="border rounded-lg p-4">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="font-medium">ฟิลด์ {{ fieldIndex + 1 }}</h3>
                    <UButton
                      icon="solar:trash-bin-trash-line-duotone"
                      @click="removeField(fieldIndex)"
                      size="sm"
                      color="red"
                      variant="ghost"
                    />
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <UFormField label="ชื่อฟิลด์" :name="`field-${fieldIndex}-name`">
                      <UInput 
                        v-model="field.name" 
                        placeholder="ชื่อฟิลด์" 
                        class="w-full"
                      />
                    </UFormField>

                    <UFormField label="ประเภท" :name="`field-${fieldIndex}-type`">
                      <USelectMenu
                        v-model="field.type"
                        :items="fieldTypes"
                        value-key="value"
                        option-attribute="label"
                        placeholder="เลือกประเภท"
                        class="w-full"
                      />
                    </UFormField>

                    <UFormField label="จำเป็น" :name="`field-${fieldIndex}-required`">
                      <UCheckbox v-model="field.required" />
                    </UFormField>
                  </div>

                  <!-- Options for select/multiselect fields -->
                  <div v-if="field.type === 'select' || field.type === 'multiselect'" class="mt-4">
                    <div class="flex items-center justify-between mb-2">
                      <label class="text-sm font-medium">ตัวเลือก</label>
                      <UButton
                        icon="solar:add-circle-line-duotone"
                        @click="addOption(fieldIndex)"
                        size="xs"
                        color="primary"
                      >
                        เพิ่มตัวเลือก
                      </UButton>
                    </div>
                    <div class="space-y-2">
                      <div v-for="(option, optionIndex) in field.options" :key="optionIndex" class="flex items-center gap-2">
                        <UInput 
                          v-model="field.options[optionIndex]" 
                          placeholder="ตัวเลือก" 
                          class="flex-1"
                        />
                        <UButton
                          icon="solar:trash-bin-trash-line-duotone"
                          @click="removeOption(fieldIndex, optionIndex)"
                          size="sm"
                          color="red"
                          variant="ghost"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- SEO Section -->
            <div class="space-y-6">
              <h2 class="text-lg font-semibold flex items-center gap-2">
                <Icon name="solar:chart-line-duotone" class="size-7 text-primary" />
                SEO และ Meta Tags
              </h2>

              <UFormField label="SEO Title" name="seo.title">
                <UInput 
                  v-model="form.seo.title" 
                  placeholder="SEO Title (ไม่บังคับ)" 
                  class="w-full"
                />
                <template #help>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    Title ที่จะแสดงใน search engines
                  </p>
                </template>
              </UFormField>

              <UFormField label="SEO Description" name="seo.description">
                <UTextarea 
                  v-model="form.seo.description" 
                  placeholder="SEO Description (ไม่บังคับ)" 
                  :rows="3" 
                  class="w-full"
                />
                <template #help>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    Description ที่จะแสดงใน search engines
                  </p>
                </template>
              </UFormField>

              <UFormField label="SEO Keywords" name="seo.keywords">
                <UInput 
                  v-model="form.seo.keywords" 
                  placeholder="คำค้นหา เช่น สมาร์ทโฟน, โทรศัพท์มือถือ, เทคโนโลยี" 
                  class="w-full"
                />
                <template #help>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    คำค้นหาที่เกี่ยวข้อง (คั่นด้วยเครื่องหมายจุลภาค)
                  </p>
                </template>
              </UFormField>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Right Column - Summary -->
      <div class="lg:col-span-1">
        <UCard variant="soft" class="sticky top-8">
          <template #header>
            <h2 class="text-lg font-semibold flex items-center gap-2">
              <Icon name="solar:document-text-line-duotone" class="size-7 text-primary" />
              สรุปรายการ
            </h2>
          </template>

          <div class="space-y-6">
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">ชื่อประเภท:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ form.name || '-' }}
                </span>
              </div>

              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">Slug:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ form.slug || '-' }}
                </span>
              </div>

              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">ประเภทเนื้อหา:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ contentTypes.find(t => t.value === form.contentType)?.label || '-' }}
                </span>
              </div>

              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">สถานะ:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ statuses.find(s => s.value === form.status)?.label || '-' }}
                </span>
              </div>

              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">ลำดับ:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ form.order || 0 }}
                </span>
              </div>
            </div>

            <hr />

            <div class="space-y-3">
              <h3 class="text-sm font-medium text-gray-900 dark:text-white">การตั้งค่า</h3>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">แสดงในเมนู:</span>
                <UBadge :color="form.settings.showInMenu ? 'success' : 'gray'" variant="subtle" size="sm">
                  {{ form.settings.showInMenu ? 'ใช่' : 'ไม่' }}
                </UBadge>
              </div>

              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">แสดงในหน้าแรก:</span>
                <UBadge :color="form.settings.showInHomepage ? 'success' : 'gray'" variant="subtle" size="sm">
                  {{ form.settings.showInHomepage ? 'ใช่' : 'ไม่' }}
                </UBadge>
              </div>

              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">อนุญาตประเภทย่อย:</span>
                <UBadge :color="form.settings.allowSubtypes ? 'success' : 'gray'" variant="subtle" size="sm">
                  {{ form.settings.allowSubtypes ? 'ใช่' : 'ไม่' }}
                </UBadge>
              </div>
            </div>

            <hr />

            <div class="space-y-3">
              <h3 class="text-sm font-medium text-gray-900 dark:text-white">คุณสมบัติ</h3>
              
              <div class="grid grid-cols-2 gap-2">
                <div class="flex items-center justify-between">
                  <span class="text-xs text-gray-600 dark:text-gray-400">ราคา:</span>
                  <UBadge :color="form.settings.hasPrice ? 'success' : 'gray'" variant="subtle" size="xs">
                    {{ form.settings.hasPrice ? 'ใช่' : 'ไม่' }}
                  </UBadge>
                </div>

                <div class="flex items-center justify-between">
                  <span class="text-xs text-gray-600 dark:text-gray-400">คลังสินค้า:</span>
                  <UBadge :color="form.settings.hasInventory ? 'success' : 'gray'" variant="subtle" size="xs">
                    {{ form.settings.hasInventory ? 'ใช่' : 'ไม่' }}
                  </UBadge>
                </div>

                <div class="flex items-center justify-between">
                  <span class="text-xs text-gray-600 dark:text-gray-400">แกลเลอรี่:</span>
                  <UBadge :color="form.settings.hasGallery ? 'success' : 'gray'" variant="subtle" size="xs">
                    {{ form.settings.hasGallery ? 'ใช่' : 'ไม่' }}
                  </UBadge>
                </div>

                <div class="flex items-center justify-between">
                  <span class="text-xs text-gray-600 dark:text-gray-400">รีวิว:</span>
                  <UBadge :color="form.settings.hasReviews ? 'success' : 'gray'" variant="subtle" size="xs">
                    {{ form.settings.hasReviews ? 'ใช่' : 'ไม่' }}
                  </UBadge>
                </div>

                <div class="flex items-center justify-between">
                  <span class="text-xs text-gray-600 dark:text-gray-400">แท็ก:</span>
                  <UBadge :color="form.settings.hasTags ? 'success' : 'gray'" variant="subtle" size="xs">
                    {{ form.settings.hasTags ? 'ใช่' : 'ไม่' }}
                  </UBadge>
                </div>
              </div>
            </div>

            <div v-if="form.fields.length > 0" class="space-y-3">
              <h3 class="text-sm font-medium text-gray-900 dark:text-white">ฟิลด์เพิ่มเติม</h3>
              <div class="text-xs text-gray-600 dark:text-gray-400">
                {{ form.fields.length }} ฟิลด์
              </div>
            </div>
          </div>
        </UCard>

        <div class="mt-8">
          <UButton 
            size="xl" 
            block 
            label="สร้างประเภท" 
            :loading="loading" 
            @click="handleSubmit"
            :disabled="loading || v$.$invalid" 
            icon="solar:add-circle-line-duotone" 
          />
        </div>
      </div>
    </div>
  </UContainer>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
</style> 