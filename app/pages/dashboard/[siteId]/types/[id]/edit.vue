<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useToast } from '#imports';
import { useTypes } from '~/composables/useTypes';

const route = useRoute();
const siteId = computed(() => {
  const params = route.params as { siteId: string };
  return params.siteId || '';
});
const id = (route.params as { id: string }).id || '';
const { getType, updateType } = useTypes();
const toast = useToast();

const form = ref({
  name: '',
  description: '',
  slug: '',
  contentType: 'product',
  icon: '',
  status: 'active',
  order: 0,
  seo: {
    title: '',
    description: '',
    keywords: '',
  },
  settings: {
    showInMenu: true,
    showInHomepage: false,
    allowSubtypes: false,
    hasPrice: false,
    hasInventory: false,
    hasGallery: false,
    hasReviews: false,
    hasTags: false,
    hasCategories: false,
    hasCustomFields: false,
    hasSeo: false,
    hasMeta: false,
  },
  fields: [] as Array<{
    name: string;
    type: string;
    required: boolean;
    options?: string[];
  }>,
});
const isSubmitting = ref(false);
const loading = ref(true);

onMounted(async () => {
  loading.value = true;
  try {
    const res = await getType(id, siteId.value);
    const type = (res?.data as any) || {};
    form.value = {
      name: type.name || '',
      description: type.description || '',
      slug: type.slug || '',
      contentType: type.contentType || 'product',
      icon: type.icon || '',
      status: type.status || 'active',
      order: type.order || 0,
      seo: type.seo || {
        title: '',
        description: '',
        keywords: '',
      },
      settings: type.settings || {
        showInMenu: true,
        showInHomepage: false,
        allowSubtypes: false,
        hasPrice: false,
        hasInventory: false,
        hasGallery: false,
        hasReviews: false,
        hasTags: false,
        hasCategories: false,
        hasCustomFields: false,
        hasSeo: false,
        hasMeta: false,
      },
      fields: type.fields || [],
    };
  } catch (e) {
    toast.add({
      title: 'ผิดพลาด',
      description: 'ไม่พบข้อมูลประเภท',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
});

const handleSubmit = async () => {
  isSubmitting.value = true;
  try {
    await updateType(id, siteId.value, {
      name: form.value.name,
      description: form.value.description,
      slug: form.value.slug,
      contentType: form.value.contentType,
      icon: form.value.icon,
      status: form.value.status,
      order: form.value.order,
      seo: form.value.seo,
      settings: form.value.settings,
      fields: form.value.fields,
    });
    toast.add({
      title: 'บันทึกสำเร็จ',
      description: `บันทึกประเภท ${form.value.name} สำหรับเว็บไซต์ ${siteId.value} เรียบร้อยแล้ว`,
      color: 'success',
    });
  } catch (e) {
    toast.add({
      title: 'ผิดพลาด',
      description: 'บันทึกประเภทไม่สำเร็จ',
      color: 'error',
    });
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<template>
  <UContainer>
    <div class="max-w-4xl mx-auto">
      <div class="mb-8">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          แก้ไขประเภท
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          แก้ไขข้อมูลประเภทเนื้อหา
        </p>
      </div>

      <div v-if="loading" class="text-center py-8">
        <UIcon name="solar:loading-line-duotone" class="w-8 h-8 animate-spin mx-auto mb-4" />
        <p class="text-gray-500">กำลังโหลดข้อมูลประเภท...</p>
      </div>

      <form v-else @submit.prevent="handleSubmit" class="space-y-6">
        <!-- Basic Information -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">ข้อมูลพื้นฐาน</h3>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <UFormField label="ชื่อประเภท" required>
              <UInput v-model="form.name" placeholder="กรอกชื่อประเภท" required />
            </UFormField>

            <UFormField label="Slug" required>
              <UInput v-model="form.slug" placeholder="กรอก slug" required />
            </UFormField>

            <UFormField label="ประเภทเนื้อหา" required>
              <USelect v-model="form.contentType" :items="[
                { value: 'product', label: 'สินค้า' },
                { value: 'news', label: 'ข่าวสาร' },
                { value: 'blog', label: 'บล็อก' },
                { value: 'page', label: 'หน้าเว็บ' },
                { value: 'event', label: 'กิจกรรม' },
                { value: 'service', label: 'บริการ' },
                { value: 'gallery', label: 'แกลเลอรี่' },
                { value: 'testimonial', label: 'รีวิว' },
                { value: 'faq', label: 'คำถามที่พบบ่อย' },
              ]" required />
            </UFormField>

            <UFormField label="สถานะ">
              <USelect v-model="form.status" :items="[
                { value: 'active', label: 'เปิดใช้งาน' },
                { value: 'inactive', label: 'ปิดใช้งาน' },
                { value: 'draft', label: 'ร่าง' },
                { value: 'archived', label: 'เก็บถาวร' },
              ]" />
            </UFormField>

            <UFormField label="ไอคอน">
              <UInput v-model="form.icon" placeholder="ชื่อไอคอน" />
            </UFormField>

            <UFormField label="ลำดับ">
              <UInput v-model="form.order" type="number" placeholder="0" />
            </UFormField>
          </div>

          <UFormField label="รายละเอียด" class="mt-4">
            <UTextarea v-model="form.description" placeholder="กรอกรายละเอียดประเภท" :rows="3" />
          </UFormField>
        </UCard>

        <!-- SEO Settings -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">SEO</h3>
          </template>

          <div class="space-y-4">
            <UFormField label="SEO Title">
              <UInput v-model="form.seo.title" placeholder="SEO Title" />
            </UFormField>

            <UFormField label="SEO Description">
              <UTextarea v-model="form.seo.description" placeholder="SEO Description" :rows="2" />
            </UFormField>

            <UFormField label="SEO Keywords">
              <UInput v-model="form.seo.keywords" placeholder="คำค้นหา (คั่นด้วยเครื่องหมายจุลภาค)" />
            </UFormField>
          </div>
        </UCard>

        <!-- Settings -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">การตั้งค่า</h3>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UCheckbox v-model="form.settings.showInMenu" label="แสดงในเมนู" />
            <UCheckbox v-model="form.settings.showInHomepage" label="แสดงในหน้าแรก" />
            <UCheckbox v-model="form.settings.allowSubtypes" label="อนุญาตให้มีประเภทย่อย" />
            <UCheckbox v-model="form.settings.hasPrice" label="มีราคา" />
            <UCheckbox v-model="form.settings.hasInventory" label="มีคลังสินค้า" />
            <UCheckbox v-model="form.settings.hasGallery" label="มีแกลเลอรี่" />
            <UCheckbox v-model="form.settings.hasReviews" label="มีรีวิว" />
            <UCheckbox v-model="form.settings.hasTags" label="มีแท็ก" />
            <UCheckbox v-model="form.settings.hasCategories" label="มีหมวดหมู่" />
            <UCheckbox v-model="form.settings.hasCustomFields" label="มีฟิลด์กำหนดเอง" />
            <UCheckbox v-model="form.settings.hasSeo" label="มี SEO" />
            <UCheckbox v-model="form.settings.hasMeta" label="มี Meta" />
          </div>
        </UCard>

        <!-- Actions -->
        <div class="flex justify-end gap-3">
          <UButton :to="`/dashboard/${siteId}/types`" variant="outline" color="neutral">
            ยกเลิก
          </UButton>
          <UButton type="submit" :loading="isSubmitting" color="primary">
            บันทึกการเปลี่ยนแปลง
          </UButton>
        </div>
      </form>
    </div>
  </UContainer>
</template>