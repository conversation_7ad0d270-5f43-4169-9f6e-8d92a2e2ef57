# ระบบจัดการประเภท (Types Management)

ระบบจัดการประเภทที่ยืดหยุ่นสำหรับรองรับประเภทเนื้อหาต่างๆ เช่น ประเภทสินค้า ประเภทข่าวสาร ประเภทบล็อก

## คุณสมบัติ

### 1. การจัดการประเภทพื้นฐาน
- สร้าง แก้ไข ลบ ประเภท
- จัดการสถานะ (เปิดใช้งาน, ปิดใช้งาน, ร่าง, เก็บถาวร)
- จัดลำดับประเภท
- กำหนดไอคอน

### 2. ประเภทเนื้อหาที่รองรับ
- **สินค้า (Product)** - สำหรับร้านค้าออนไลน์
- **ข่าวสาร (News)** - สำหรับข่าวและประกาศ
- **บล็อก (Blog)** - สำหรับบทความและเนื้อหา
- **หน้าเว็บ (Page)** - สำหรับหน้าสถิต
- **กิจกรรม (Event)** - สำหรับกิจกรรมและอีเวนต์
- **บริการ (Service)** - สำหรับบริการต่างๆ
- **แกลเลอรี่ (Gallery)** - สำหรับรูปภาพและสื่อ
- **รีวิว (Testimonial)** - สำหรับรีวิวและความคิดเห็น
- **คำถามที่พบบ่อย (FAQ)** - สำหรับคำถามและคำตอบ

### 3. การตั้งค่าขั้นสูง
- **SEO Settings** - ตั้งค่า SEO สำหรับแต่ละประเภท
- **Display Settings** - ควบคุมการแสดงผล
- **Feature Settings** - เปิด/ปิดฟีเจอร์ต่างๆ
- **Custom Fields** - กำหนดฟิลด์เพิ่มเติม

### 4. ฟีเจอร์ที่รองรับ
- **ราคา (Price)** - สำหรับสินค้าและบริการ
- **คลังสินค้า (Inventory)** - จัดการสต็อก
- **แกลเลอรี่ (Gallery)** - รูปภาพและสื่อ
- **รีวิว (Reviews)** - ระบบรีวิวและคะแนน
- **แท็ก (Tags)** - จัดหมวดหมู่ด้วยแท็ก
- **หมวดหมู่ (Categories)** - จัดหมวดหมู่
- **ฟิลด์กำหนดเอง (Custom Fields)** - เพิ่มฟิลด์ตามต้องการ
- **SEO** - ตั้งค่า SEO
- **Meta** - ข้อมูล Meta

## โครงสร้างไฟล์

```
types/
├── index.vue              # หน้าจัดการประเภททั้งหมด
├── create.vue             # หน้าสร้างประเภทใหม่
├── [id]/
│   └── edit.vue          # หน้าแก้ไขประเภท
└── README.md             # เอกสารนี้
```

## API Endpoints

### Frontend API (Nuxt)
- `GET /api/v1/types` - ดึงรายการประเภท
- `POST /api/v1/types` - สร้างประเภทใหม่
- `GET /api/v1/types/[id]` - ดึงข้อมูลประเภท
- `PUT /api/v1/types/[id]` - อัปเดตประเภท
- `DELETE /api/v1/types/[id]` - ลบประเภท
- `POST /api/v1/types/check-slug` - ตรวจสอบ slug ซ้ำ
- `GET /api/v1/types/analytics` - ดึงสถิติการใช้งาน

### Backend API (Elysia)
- `GET /api/v1/types` - ดึงรายการประเภท
- `POST /api/v1/types` - สร้างประเภทใหม่
- `GET /api/v1/types/:id` - ดึงข้อมูลประเภท
- `PUT /api/v1/types/:id` - อัปเดตประเภท
- `DELETE /api/v1/types/:id` - ลบประเภท
- `POST /api/v1/types/check-slug` - ตรวจสอบ slug ซ้ำ
- `GET /api/v1/types/analytics` - ดึงสถิติการใช้งาน

## Composable

### useTypes()
```typescript
const {
  getTypes,              // ดึงรายการประเภท
  getType,               // ดึงข้อมูลประเภท
  createType,            // สร้างประเภทใหม่
  updateType,            // อัปเดตประเภท
  deleteType,            // ลบประเภท
  updateTypeStatus,      // อัปเดตสถานะ
  getTypesByContentType, // ดึงประเภทตามประเภทเนื้อหา
  checkSlugAvailability, // ตรวจสอบ slug ซ้ำ
  getTypeAnalytics,      // ดึงสถิติการใช้งาน
  duplicateType,         // คัดลอกประเภท
  importTypes,           // นำเข้าประเภท
  exportTypes,           // ส่งออกประเภท
} = useTypes();
```

## Types

### Type
```typescript
interface Type {
  _id: string;
  name: string;
  description: string;
  slug: string;
  contentType: string;
  icon: string;
  status: 'active' | 'inactive' | 'draft' | 'archived';
  order: number;
  seo: TypeSeo;
  settings: TypeSettings;
  fields: TypeField[];
  usageCount: number;
  createdAt: string;
  updatedAt: string;
}
```

### TypeSettings
```typescript
interface TypeSettings {
  showInMenu: boolean;
  showInHomepage: boolean;
  allowSubtypes: boolean;
  hasPrice: boolean;
  hasInventory: boolean;
  hasGallery: boolean;
  hasReviews: boolean;
  hasTags: boolean;
  hasCategories: boolean;
  hasCustomFields: boolean;
  hasSeo: boolean;
  hasMeta: boolean;
}
```

## การใช้งาน

### 1. สร้างประเภทใหม่
```typescript
const { createType } = useTypes();

const newType = await createType(siteId, {
  name: 'สินค้าใหม่',
  description: 'ประเภทสินค้าใหม่',
  slug: 'new-products',
  contentType: 'product',
  icon: 'solar:box-line-duotone',
  status: 'active',
  order: 1,
  seo: {
    title: 'สินค้าใหม่ - SEO Title',
    description: 'SEO Description',
    keywords: 'สินค้า, ใหม่, online',
  },
  settings: {
    showInMenu: true,
    hasPrice: true,
    hasInventory: true,
    hasGallery: true,
  },
});
```

### 2. ดึงรายการประเภท
```typescript
const { getTypes } = useTypes();

const response = await getTypes({
  siteId: 'site-id',
  page: 1,
  limit: 20,
  status: 'active',
  contentType: 'product',
  search: 'สินค้า',
  sortBy: 'createdAt',
  sortOrder: 'desc',
});
```

### 3. อัปเดตสถานะ
```typescript
const { updateTypeStatus } = useTypes();

await updateTypeStatus(typeId, siteId, 'inactive');
```

## การตั้งค่า

### 1. SEO Settings
- **Title** - ชื่อหน้าเว็บ
- **Description** - คำอธิบาย
- **Keywords** - คำค้นหา

### 2. Display Settings
- **Show in Menu** - แสดงในเมนู
- **Show in Homepage** - แสดงในหน้าแรก
- **Allow Subtypes** - อนุญาตให้มีประเภทย่อย

### 3. Feature Settings
- **Has Price** - มีราคา
- **Has Inventory** - มีคลังสินค้า
- **Has Gallery** - มีแกลเลอรี่
- **Has Reviews** - มีรีวิว
- **Has Tags** - มีแท็ก
- **Has Categories** - มีหมวดหมู่
- **Has Custom Fields** - มีฟิลด์กำหนดเอง
- **Has SEO** - มี SEO
- **Has Meta** - มี Meta

## การพัฒนา

### 1. เพิ่มประเภทเนื้อหาใหม่
1. เพิ่มใน `contentTypes` array
2. เพิ่มไอคอนที่เหมาะสม
3. อัปเดต UI components

### 2. เพิ่มฟีเจอร์ใหม่
1. เพิ่มใน `TypeSettings` interface
2. อัปเดต form components
3. อัปเดต API endpoints

### 3. เพิ่มฟิลด์กำหนดเอง
1. เพิ่มใน `TypeField` interface
2. สร้าง field editor component
3. อัปเดต form validation

## หมายเหตุ

- ระบบนี้รองรับการขยายตัวได้ง่าย
- สามารถเพิ่มประเภทเนื้อหาใหม่ได้โดยไม่ต้องแก้ไขโค้ดหลัก
- รองรับการตั้งค่าที่ยืดหยุ่นสำหรับแต่ละประเภท
- มีระบบ SEO และ Meta ที่ครบถ้วน
- รองรับการนำเข้าและส่งออกข้อมูล 