<script setup lang="ts">
import { useRoute } from 'vue-router';
import { useTypes } from '~/composables/useTypes';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

const { t } = useI18n();

// SEO meta สำหรับหน้าจัดการประเภท
useSeoMeta({
  title: 'จัดการประเภท - ระบบจัดการร้านค้า',
  description: 'จัดการประเภทเนื้อหาต่างๆ เช่น ประเภทสินค้า ประเภทข่าวสาร ประเภทบล็อก',
  keywords: 'จัดการประเภท, ประเภทสินค้า, ประเภทข่าวสาร, ประเภทบล็อก, ระบบร้านค้า',
  ogTitle: 'จัดการประเภท - ระบบจัดการร้านค้า',
  ogDescription: 'จัดการประเภทเนื้อหาต่างๆ เช่น ประเภทสินค้า ประเภทข่าวสาร ประเภทบล็อก',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});

// Get siteId from route params
const route: any = useRoute();
const siteId = computed(() => route.params.siteId);

// Use composables
const { $apiFetch } = useNuxtApp();
const {
  getTypes,
  deleteType,
  updateTypeStatus,
  getTypeAnalytics,
} = useTypes();

// Reactive data
const types = ref<any[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);

// Reactive data
const searchQuery = ref('');
const selectedContentType = ref('all');
const selectedStatus = ref('all');
const sortBy = ref('created_at');
const sortOrder = ref('desc');
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Content type options
const contentTypes = ref([
  { value: 'all', label: 'ทุกประเภท' },
  { value: 'product', label: 'สินค้า' },
  { value: 'news', label: 'ข่าวสาร' },
  { value: 'blog', label: 'บล็อก' },
  { value: 'page', label: 'หน้าเว็บ' },
  { value: 'event', label: 'กิจกรรม' },
  { value: 'service', label: 'บริการ' },
  { value: 'gallery', label: 'แกลเลอรี่' },
  { value: 'testimonial', label: 'รีวิว' },
  { value: 'faq', label: 'คำถามที่พบบ่อย' },
]);

const statuses = ref([
  { value: 'all', label: 'ทุกสถานะ' },
  { value: 'active', label: 'เปิดใช้งาน' },
  { value: 'inactive', label: 'ปิดใช้งาน' },
  { value: 'draft', label: 'ร่าง' },
  { value: 'archived', label: 'เก็บถาวร' },
]);

const sortOptions = ref([
  { value: 'created_at', label: 'วันที่สร้าง' },
  { value: 'name', label: 'ชื่อประเภท' },
  { value: 'content_type', label: 'ประเภทเนื้อหา' },
  { value: 'usage_count', label: 'จำนวนการใช้งาน' },
  { value: 'order', label: 'ลำดับ' },
]);

// Analytics data - จะอัปเดตจากข้อมูลจริง
const analytics = ref({
  totalTypes: 0,
  activeTypes: 0,
  totalUsage: 0,
  averageUsagePerType: 0,
  topContentType: '',
  growthRate: 0,
});

// Format number
const formatNumber = (num: number) => {
  return new Intl.NumberFormat('th-TH').format(num);
};

// Get status badge class
const getStatusBadgeClass = (status: string) => {
  const classes = {
    active: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
    draft: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    archived: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
  };
  return classes[status as keyof typeof classes] || classes.inactive;
};

// Get status label
const getStatusLabel = (status: string) => {
  const labels = {
    active: 'เปิดใช้งาน',
    inactive: 'ปิดใช้งาน',
    draft: 'ร่าง',
    archived: 'เก็บถาวร',
  };
  return labels[status as keyof typeof labels] || status;
};

// Get content type badge class
const getContentTypeBadgeClass = (contentType: string) => {
  const classes = {
    product: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    news: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    blog: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
    page: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
    event: 'bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-400',
    service: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400',
    gallery: 'bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400',
    testimonial: 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400',
    faq: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-400',
  };
  return classes[contentType as keyof typeof classes] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
};

// Get content type label
const getContentTypeLabel = (contentType: string) => {
  const labels = {
    product: 'สินค้า',
    news: 'ข่าวสาร',
    blog: 'บล็อก',
    page: 'หน้าเว็บ',
    event: 'กิจกรรม',
    service: 'บริการ',
    gallery: 'แกลเลอรี่',
    testimonial: 'รีวิว',
    faq: 'คำถามที่พบบ่อย',
  };
  return labels[contentType as keyof typeof labels] || contentType;
};

// Computed filtered types
const filteredTypes = computed(() => {
  let filtered = types.value || [];

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(
      type =>
        type.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        type.description?.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  // Content type filter
  if (selectedContentType.value !== 'all') {
    filtered = filtered.filter(type => type.contentType === selectedContentType.value);
  }

  // Status filter
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(type => type.status === selectedStatus.value);
  }

  // Sort
  filtered.sort((a, b) => {
    let aVal = a[sortBy.value as keyof typeof a];
    let bVal = b[sortBy.value as keyof typeof b];

    // Handle nested properties
    if (sortBy.value === 'usage_count') {
      aVal = a.usageCount || 0;
      bVal = b.usageCount || 0;
    } else if (sortBy.value === 'created_at') {
      aVal = new Date(a.createdAt).getTime();
      bVal = new Date(b.createdAt).getTime();
    }

    if (typeof aVal === 'string') {
      aVal = aVal.toLowerCase();
      bVal = bVal.toLowerCase();
    }

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });

  return filtered;
});

// Paginated types
const paginatedTypes = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredTypes.value.slice(start, end);
});

// Total pages
const totalPages = computed(() => {
  return Math.ceil(filteredTypes.value.length / itemsPerPage.value);
});

// Fetch types
const fetchTypes = async () => {
  loading.value = true;
  error.value = null;
  try {
    const response = await getTypes({
      siteId: siteId.value,
      page: currentPage.value,
      limit: itemsPerPage.value,
      status: selectedStatus.value === 'all' ? undefined : selectedStatus.value,
      contentType: selectedContentType.value === 'all' ? undefined : selectedContentType.value,
      search: searchQuery.value,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value,
    });
    
    if (response?.success) {
      const responseData = response.data;
      if (responseData?.types) {
        types.value = responseData.types;
      } else if (Array.isArray(responseData)) {
        types.value = responseData;
      } else {
        types.value = [];
      }

      // คำนวณ analytics จากข้อมูลจริง
      const totalTypes = types.value.length;
      const activeTypes = types.value.filter(t => t?.status === 'active').length;
      const totalUsage = types.value.reduce((sum, t) => sum + (t?.usageCount || 0), 0);
      const averageUsagePerType = totalTypes > 0 ? totalUsage / totalTypes : 0;

      // หาประเภทเนื้อหาที่ใช้มากที่สุด
      const contentTypeUsage: Record<string, number> = {};
      types.value.forEach(type => {
        if (type?.contentType) {
          contentTypeUsage[type.contentType] = (contentTypeUsage[type.contentType] || 0) + (type?.usageCount || 0);
        }
      });
      const contentTypeKeys = Object.keys(contentTypeUsage);
      const topContentType = contentTypeKeys.length > 0 
        ? contentTypeKeys.reduce((top, current) => 
            (contentTypeUsage[current] || 0) > (contentTypeUsage[top] || 0) ? current : top
          , contentTypeKeys[0] || '')
        : '';

      analytics.value = {
        totalTypes,
        activeTypes,
        totalUsage,
        averageUsagePerType,
        topContentType,
        growthRate: 0, // จะคำนวณจากข้อมูลย้อนหลัง
      };
    }
  } catch (err: any) {
    error.value = err.message || 'ไม่สามารถโหลดข้อมูลประเภทได้';
    console.error('Error fetching types:', err);
  } finally {
    loading.value = false;
  }
};

// Delete type
const handleDeleteType = async (typeId: string) => {
  try {
    const response = await deleteType(typeId, siteId.value);
    
    if (response?.success) {
      useToast().add({
        title: 'สำเร็จ',
        description: 'ลบประเภทเรียบร้อยแล้ว',
        color: 'success',
      });
      await fetchTypes();
    }
  } catch (err: any) {
    useToast().add({
      title: 'ผิดพลาด',
      description: err.message || 'ไม่สามารถลบประเภทได้',
      color: 'error',
    });
  }
};

// Toggle type status
const handleToggleTypeStatus = async (type: any) => {
  try {
    const newStatus = type.status === 'active' ? 'inactive' : 'active';
    const response = await updateTypeStatus(type._id, siteId.value, newStatus);
    
    if (response?.success) {
      useToast().add({
        title: 'สำเร็จ',
        description: `อัปเดตสถานะประเภทเรียบร้อยแล้ว`,
        color: 'success',
      });
      await fetchTypes();
    }
  } catch (err: any) {
    useToast().add({
      title: 'ผิดพลาด',
      description: err.message || 'ไม่สามารถอัปเดตสถานะได้',
      color: 'error',
    });
  }
};

// Navigation
const router = useRouter();
const navigateToCreate = () => router.push(`/dashboard/${siteId.value}/types/create`);
const navigateToEdit = (typeId: string) => router.push(`/dashboard/${siteId.value}/types/${typeId}/edit`);

// Watch for changes
watch([searchQuery, selectedContentType, selectedStatus, sortBy, sortOrder], () => {
  currentPage.value = 1;
});

// Load data on mount
onMounted(() => {
  fetchTypes();
});
</script>

<template>
  <UContainer>
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          จัดการประเภท
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          จัดการประเภทเนื้อหาต่างๆ เช่น ประเภทสินค้า ประเภทข่าวสาร ประเภทบล็อก
        </p>
      </div>
      <UButton
        icon="solar:add-circle-line-duotone"
        @click="navigateToCreate"
        color="primary"
        size="lg"
      >
        เพิ่มประเภทใหม่
      </UButton>
    </div>

    <!-- Analytics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <UCard variant="soft">
        <div class="flex items-center gap-3">
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
            <Icon name="solar:category-line-duotone" class="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">ประเภททั้งหมด</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ formatNumber(analytics.totalTypes) }}
            </p>
          </div>
        </div>
      </UCard>

      <UCard variant="soft">
        <div class="flex items-center gap-3">
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
            <Icon name="solar:check-circle-line-duotone" class="w-6 h-6 text-green-600" />
          </div>
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">ประเภทที่ใช้งาน</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ formatNumber(analytics.activeTypes) }}
            </p>
          </div>
        </div>
      </UCard>

      <UCard variant="soft">
        <div class="flex items-center gap-3">
          <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
            <Icon name="solar:chart-line-duotone" class="w-6 h-6 text-purple-600" />
          </div>
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">การใช้งานรวม</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ formatNumber(analytics.totalUsage) }}
            </p>
          </div>
        </div>
      </UCard>

      <UCard variant="soft">
        <div class="flex items-center gap-3">
          <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
            <Icon name="solar:chart-line-duotone" class="w-6 h-6 text-orange-600" />
          </div>
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">การใช้งานเฉลี่ยต่อประเภท</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ formatNumber(Math.round(analytics.averageUsagePerType)) }}
            </p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Filters and Search -->
    <UCard variant="soft" class="mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Search -->
        <UFormField label="ค้นหา" name="search">
          <UInput
            v-model="searchQuery"
            placeholder="ค้นหาประเภท..."
            icon="solar:magnifer-line-duotone"
          />
        </UFormField>

        <!-- Content Type Filter -->
        <UFormField label="ประเภทเนื้อหา" name="contentType">
          <USelectMenu
            v-model="selectedContentType"
            :items="contentTypes"
            value-key="value"
            option-attribute="label"
            placeholder="เลือกประเภทเนื้อหา"
          />
        </UFormField>

        <!-- Status Filter -->
        <UFormField label="สถานะ" name="status">
          <USelectMenu
            v-model="selectedStatus"
            :items="statuses"
            value-key="value"
            option-attribute="label"
            placeholder="เลือกสถานะ"
          />
        </UFormField>

        <!-- Sort Order -->
        <UFormField label="ลำดับ" name="sortOrder">
          <USelectMenu
            v-model="sortOrder"
            :items="[
              { value: 'desc', label: 'มากไปน้อย' },
              { value: 'asc', label: 'น้อยไปมาก' },
            ]"
            value-key="value"
            option-attribute="label"
            placeholder="เลือกลำดับ"
          />
        </UFormField>
      </div>
    </UCard>

    <!-- Types Table -->
    <UCard variant="soft">
      <div v-if="loading" class="flex justify-center items-center py-8">
        <UIcon name="eos-icons:bubble-loading" class="w-8 h-8 animate-spin text-primary-500" />
        <span class="ml-2">กำลังโหลดข้อมูล...</span>
      </div>

      <div v-else-if="error" class="text-center py-8">
        <Icon name="solar:info-circle-line-duotone" class="w-12 h-12 text-red-500 mx-auto mb-4" />
        <p class="text-red-600 dark:text-red-400">{{ error }}</p>
        <UButton @click="fetchTypes" class="mt-4" variant="soft">
          ลองใหม่
        </UButton>
      </div>

      <div v-else-if="paginatedTypes.length === 0" class="text-center py-8">
        <Icon name="solar:category-line-duotone" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p class="text-gray-600 dark:text-gray-400">ไม่พบประเภท</p>
        <UButton @click="navigateToCreate" class="mt-4" color="primary">
          เพิ่มประเภทแรก
        </UButton>
      </div>

      <div v-else>
        <!-- Table -->
        <UTable
          :data="paginatedTypes"
          :columns="[
            {
              key: 'name',
              label: 'ชื่อประเภท',
              sortable: true,
            },
            {
              key: 'contentType',
              label: 'ประเภทเนื้อหา',
              sortable: true,
            },
            {
              key: 'description',
              label: 'รายละเอียด',
            },
            {
              key: 'usageCount',
              label: 'การใช้งาน',
              sortable: true,
            },
            {
              key: 'status',
              label: 'สถานะ',
            },
            {
              key: 'order',
              label: 'ลำดับ',
              sortable: true,
            },
            {
              key: 'actions',
              label: 'การจัดการ',
            },
          ]"
        >
          <template #name-data="{ row }">
            <div class="flex items-center gap-3">
              <div v-if="row.icon" class="w-10 h-10 rounded-lg overflow-hidden">
                <img :src="row.icon" :alt="row.name" class="w-full h-full object-cover" />
              </div>
              <div v-else class="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                <Icon name="solar:category-line-duotone" class="w-5 h-5 text-gray-500" />
              </div>
              <div>
                <p class="font-medium text-gray-900 dark:text-white">{{ row.name }}</p>
                <p v-if="row.slug" class="text-sm text-gray-500 dark:text-gray-400">
                  {{ row.slug }}
                </p>
              </div>
            </div>
          </template>

          <template #contentType-data="{ row }">
            <UBadge
              :class="getContentTypeBadgeClass(row.contentType)"
              variant="subtle"
              size="sm"
            >
              {{ getContentTypeLabel(row.contentType) }}
            </UBadge>
          </template>

          <template #description-data="{ row }">
            <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
              {{ row.description || '-' }}
            </p>
          </template>

          <template #usageCount-data="{ row }">
            <span class="font-medium text-gray-900 dark:text-white">
              {{ formatNumber(row.usageCount || 0) }}
            </span>
          </template>

          <template #status-data="{ row }">
            <UBadge
              :class="getStatusBadgeClass(row.status)"
              variant="subtle"
              size="sm"
            >
              {{ getStatusLabel(row.status) }}
            </UBadge>
          </template>

          <template #order-data="{ row }">
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ row.order || 0 }}
            </span>
          </template>

          <template #actions-data="{ row }">
            <div class="flex items-center gap-2">
              <UButton
                icon="solar:eye-line-duotone"
                color="gray"
                variant="ghost"
                size="sm"
                @click="navigateToEdit(row._id)"
              />
              <UButton
                :icon="row.status === 'active' ? 'solar:eye-closed-line-duotone' : 'solar:eye-line-duotone'"
                :color="row.status === 'active' ? 'orange' : 'green'"
                variant="ghost"
                size="sm"
                @click="toggleTypeStatus(row)"
              />
              <UButton
                icon="solar:trash-bin-trash-line-duotone"
                color="red"
                variant="ghost"
                size="sm"
                @click="deleteType(row._id)"
              />
            </div>
          </template>
        </UTable>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="flex justify-center mt-6">
          <UPagination
            v-model="currentPage"
            :total="totalPages"
            :ui="{ wrapper: 'flex items-center gap-1' }"
          />
        </div>
      </div>
    </UCard>
  </UContainer>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8>* {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Line clamp utility */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>