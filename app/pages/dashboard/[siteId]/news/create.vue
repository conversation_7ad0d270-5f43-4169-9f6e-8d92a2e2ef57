<script setup lang="ts">
import { useRoute } from 'vue-router';
import { useNuxtApp } from '#app';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// Use composables
const { $apiFetch } = useNuxtApp();
const { createNews } = useNews();

// Form data
const form = ref({
  title: '',
  content: '',
  excerpt: '',
  status: 'draft',
  category: '',
  priority: 'normal',
  tags: [] as string[],
  featured_image: '',
  author: '',
  publish_date: '',
  is_urgent: false,
  seo: {
    title: '',
    description: '',
    keywords: '',
  },
});

// Validation
const errors = ref<Record<string, string>>({});
const loading = ref(false);

// Categories
const categories = ref([
  { value: 'breaking', label: 'ข่าวด่วน' },
  { value: 'politics', label: 'การเมือง' },
  { value: 'business', label: 'ธุรกิจ' },
  { value: 'technology', label: 'เทคโนโลยี' },
  { value: 'sports', label: 'กีฬา' },
  { value: 'entertainment', label: 'บันเทิง' },
  { value: 'health', label: 'สุขภาพ' },
  { value: 'education', label: 'การศึกษา' },
  { value: 'other', label: 'อื่นๆ' },
]);

// Status options
const statuses = ref([
  { value: 'draft', label: 'ร่าง' },
  { value: 'published', label: 'เผยแพร่' },
  { value: 'archived', label: 'เก็บถาวร' },
]);

// Priority options
const priorities = ref([
  { value: 'urgent', label: 'ด่วนมาก' },
  { value: 'high', label: 'สูง' },
  { value: 'normal', label: 'ปกติ' },
  { value: 'low', label: 'ต่ำ' },
]);

// Authors
const authors = ref([
  { value: 'admin', label: 'ผู้ดูแลระบบ' },
  { value: 'editor1', label: 'บรรณาธิการ 1' },
  { value: 'editor2', label: 'บรรณาธิการ 2' },
]);

// Validation rules
const validateForm = () => {
  errors.value = {};

  if (!form.value.title.trim()) {
    errors.value.title = 'กรุณากรอกชื่อข่าว';
  }

  if (!form.value.content.trim()) {
    errors.value.content = 'กรุณากรอกเนื้อหาข่าว';
  }

  if (!form.value.excerpt.trim()) {
    errors.value.excerpt = 'กรุณากรอกบทคัดย่อ';
  }

  if (!form.value.category) {
    errors.value.category = 'กรุณาเลือกหมวดหมู่';
  }

  return Object.keys(errors.value).length === 0;
};

// Handle form submission
const handleSubmit = async () => {
  if (!validateForm()) return;

  loading.value = true;
  try {
    await createNews(siteId.value, form.value);
    await navigateTo(`/dashboard/${siteId.value}/news`);
  } catch (error) {
    console.error('Error creating news:', error);
  } finally {
    loading.value = false;
  }
};

// Handle cancel
const handleCancel = () => {
  navigateTo(`/dashboard/${siteId.value}/news`);
};

// Add tag
const addTag = (tag: string) => {
  if (tag && !form.value.tags.includes(tag)) {
    form.value.tags.push(tag);
  }
};

// Remove tag
const removeTag = (tag: string) => {
  form.value.tags = form.value.tags.filter(t => t !== tag);
};

// Auto-generate excerpt from content
const generateExcerpt = () => {
  if (form.value.content && !form.value.excerpt) {
    form.value.excerpt = form.value.content.substring(0, 150) + '...';
  }
};

// Auto-generate SEO title
const generateSEOTitle = () => {
  if (form.value.title && !form.value.seo.title) {
    form.value.seo.title = form.value.title;
  }
};

// Auto-generate SEO description
const generateSEODescription = () => {
  if (form.value.excerpt && !form.value.seo.description) {
    form.value.seo.description = form.value.excerpt;
  }
};

// Word count
const wordCount = computed(() => {
  return form.value.content.split(' ').length;
});

// Reading time
const readingTime = computed(() => {
  const wordsPerMinute = 200;
  return Math.ceil(wordCount.value / wordsPerMinute);
});

// Tag input ref
const tagInput = ref<HTMLInputElement>();

// Add tag function
const handleAddTag = () => {
  if (tagInput.value?.value) {
    addTag(tagInput.value.value);
    tagInput.value.value = '';
  }
};

// SEO
useSeoMeta({
  title: 'เขียนข่าวใหม่ - ระบบจัดการร้านค้า',
  description: 'เขียนข่าวใหม่ จัดการเนื้อหา และการตั้งค่า SEO สำหรับข่าวสาร',
  keywords: 'เขียนข่าว, จัดการเนื้อหา, SEO, ข่าวสาร',
  ogTitle: 'เขียนข่าวใหม่ - ระบบจัดการร้านค้า',
  ogDescription: 'เขียนข่าวใหม่ จัดการเนื้อหา และการตั้งค่า SEO สำหรับข่าวสาร',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          เขียนข่าวใหม่
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          สร้างข่าวใหม่สำหรับเว็บไซต์
        </p>
      </div>
      <div class="flex items-center gap-3">
        <UButton
          @click="handleCancel"
          variant="outline"
          size="sm"
          class="hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <Icon name="i-heroicons-arrow-left" class="w-4 h-4 mr-2" />
          ย้อนกลับ
        </UButton>
        <UButton 
          @click="handleSubmit"
          :loading="loading"
          class="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <Icon name="i-heroicons-check" class="w-5 h-5 mr-2" />
          บันทึกข่าว
        </UButton>
      </div>
    </div>

    <!-- Form -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
      <!-- Main Content -->
      <div class="lg:col-span-3 space-y-6">
        <!-- Title -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-red-500 to-red-600 rounded-lg">
                <Icon name="i-heroicons-newspaper" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ชื่อข่าว</h2>
            </div>
          </template>
          
          <UInput
            v-model="form.title"
            placeholder="กรอกชื่อข่าว"
            :error="errors.title"
            class="w-full text-2xl font-bold"
            @blur="generateSEOTitle"
          />
        </UCard>

        <!-- Content Editor -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
                  <Icon name="i-heroicons-pencil-square" class="w-5 h-5 text-white" />
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">เนื้อหา</h2>
              </div>
              <div class="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                <span>{{ wordCount }} คำ</span>
                <span>{{ readingTime }} นาที</span>
              </div>
            </div>
          </template>
          
          <div class="space-y-4">
            <UTextarea
              v-model="form.content"
              placeholder="เริ่มเขียนเนื้อหาของคุณที่นี่..."
              :error="errors.content"
              :rows="20"
              class="w-full font-serif text-lg leading-relaxed"
            />
            
            <!-- Editor Toolbar -->
            <div class="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <UButton variant="ghost" size="sm" icon="i-heroicons-bold" />
              <UButton variant="ghost" size="sm" icon="i-heroicons-italic" />
              <UButton variant="ghost" size="sm" icon="i-heroicons-underline" />
              <div class="w-px h-6 bg-gray-300 dark:bg-gray-600"></div>
              <UButton variant="ghost" size="sm" icon="i-heroicons-list-bullet" />
              <UButton variant="ghost" size="sm" icon="i-heroicons-list-number" />
              <UButton variant="ghost" size="sm" icon="i-heroicons-link" />
              <UButton variant="ghost" size="sm" icon="i-heroicons-photo" />
            </div>
          </div>
        </UCard>

        <!-- Excerpt -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="p-2 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg">
                  <Icon name="i-heroicons-document" class="w-5 h-5 text-white" />
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">บทคัดย่อ</h2>
              </div>
              <UButton
                @click="generateExcerpt"
                variant="outline"
                size="sm"
              >
                สร้างอัตโนมัติ
              </UButton>
            </div>
          </template>
          
          <UTextarea
            v-model="form.excerpt"
            placeholder="เขียนบทคัดย่อสั้นๆ เกี่ยวกับข่าวนี้"
            :error="errors.excerpt"
            :rows="4"
            class="w-full"
            @blur="generateSEODescription"
          />
        </UCard>
      </div>

      <!-- Settings Sidebar -->
      <div class="space-y-6">
        <!-- Status and Priority -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-red-500 to-red-600 rounded-lg">
                <Icon name="i-heroicons-flag" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">สถานะและความสำคัญ</h2>
            </div>
          </template>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                สถานะ
              </label>
              <USelect
                v-model="form.status"
                :items="statuses"
                option-attribute="label"
                value-attribute="value"
                placeholder="เลือกสถานะ"
                class="w-full"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ความสำคัญ
              </label>
              <USelect
                v-model="form.priority"
                :items="priorities"
                option-attribute="label"
                value-attribute="value"
                placeholder="เลือกความสำคัญ"
                class="w-full"
              />
            </div>
            
            <div class="flex items-center gap-2">
              <UCheckbox v-model="form.is_urgent" />
              <label class="text-sm text-gray-700 dark:text-gray-300">ข่าวด่วน</label>
            </div>
            
            <div v-if="form.status === 'published'">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                วันที่เผยแพร่
              </label>
              <UInput
                v-model="form.publish_date"
                type="datetime-local"
                class="w-full"
              />
            </div>
          </div>
        </UCard>

        <!-- Category and Author -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                <Icon name="i-heroicons-folder" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">หมวดหมู่และผู้เขียน</h2>
            </div>
          </template>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                หมวดหมู่
              </label>
              <USelect
                v-model="form.category"
                :items="categories"
                option-attribute="label"
                value-attribute="value"
                placeholder="เลือกหมวดหมู่"
                :error="errors.category"
                class="w-full"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ผู้เขียน
              </label>
              <USelect
                v-model="form.author"
                :items="authors"
                option-attribute="label"
                value-attribute="value"
                placeholder="เลือกผู้เขียน"
                class="w-full"
              />
            </div>
          </div>
        </UCard>

        <!-- Tags -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg">
                <Icon name="i-heroicons-tag" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">แท็ก</h2>
            </div>
          </template>
          
          <div class="space-y-4">
            <div class="flex gap-2">
              <UInput
                ref="tagInput"
                placeholder="เพิ่มแท็ก"
                class="flex-1"
                @keyup.enter="handleAddTag"
              />
              <UButton
                @click="handleAddTag"
                variant="outline"
                size="sm"
              >
                เพิ่ม
              </UButton>
            </div>
            
            <div v-if="form.tags.length > 0" class="flex flex-wrap gap-2">
              <UBadge
                v-for="tag in form.tags"
                :key="tag"
                color="primary"
                variant="subtle"
                class="cursor-pointer hover:bg-red-100 dark:hover:bg-red-900/20"
                @click="removeTag(tag)"
              >
                {{ tag }}
                <Icon name="i-heroicons-x-mark" class="w-3 h-3 ml-1" />
              </UBadge>
            </div>
          </div>
        </UCard>

        <!-- Featured Image -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg">
                <Icon name="i-heroicons-photo" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รูปภาพหลัก</h2>
            </div>
          </template>
          
          <div class="space-y-4">
            <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
              <Icon name="i-heroicons-photo" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-600 dark:text-gray-400 mb-2">ลากและวางรูปภาพที่นี่</p>
              <UButton variant="outline" size="sm">
                เลือกรูปภาพ
              </UButton>
            </div>
          </div>
        </UCard>

        <!-- SEO -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg">
                <Icon name="i-heroicons-magnifying-glass" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">SEO</h2>
            </div>
          </template>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SEO Title
              </label>
              <UInput
                v-model="form.seo.title"
                placeholder="SEO Title"
                class="w-full"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SEO Description
              </label>
              <UTextarea
                v-model="form.seo.description"
                placeholder="SEO Description"
                :rows="3"
                class="w-full"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SEO Keywords
              </label>
              <UInput
                v-model="form.seo.keywords"
                placeholder="SEO Keywords"
                class="w-full"
              />
            </div>
          </div>
        </UCard>

        <!-- Quick Actions -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg">
                <Icon name="i-heroicons-bolt" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">การดำเนินการด่วน</h2>
            </div>
          </template>
          
          <div class="space-y-3">
            <UButton
              variant="outline"
              size="sm"
              class="w-full justify-start"
            >
              <Icon name="i-heroicons-eye" class="w-4 h-4 mr-2" />
              ดูตัวอย่าง
            </UButton>
            
            <UButton
              variant="outline"
              size="sm"
              class="w-full justify-start"
            >
              <Icon name="i-heroicons-document-duplicate" class="w-4 h-4 mr-2" />
              คัดลอกจากข่าวอื่น
            </UButton>
            
            <UButton
              variant="outline"
              size="sm"
              class="w-full justify-start"
            >
              <Icon name="i-heroicons-chart-bar" class="w-4 h-4 mr-2" />
              ดูสถิติข่าว
            </UButton>
          </div>
        </UCard>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
</style> 