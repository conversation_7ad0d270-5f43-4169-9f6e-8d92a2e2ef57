<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// Use tracking composable
const { getTrackingEvents, getTrackingAnalytics, clearTrackingData, exportTrackingData } =
  useTracking();

// Reactive data
const trackingData = ref<any[]>([]);
const analytics = ref<any>({});
const loading = ref(false);
const error = ref<string | null>(null);

// Filters and search
const searchQuery = ref('');
const selectedType = ref('all');
const selectedDateRange = ref('today');
const sortBy = ref('created_at');
const sortOrder = ref<'asc' | 'desc'>('desc');
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Filter options
const typeOptions = ref([
  { value: 'all', label: 'ทุกประเภท' },
  { value: 'page_view', label: 'การดูหน้า' },
  { value: 'click', label: 'การคลิก' },
  { value: 'scroll', label: 'การเลื่อน' },
  { value: 'form_submit', label: 'การส่งฟอร์ม' },
  { value: 'download', label: 'การดาวน์โหลด' },
]);

const dateRangeOptions = ref([
  { value: 'today', label: 'วันนี้' },
  { value: 'yesterday', label: 'เมื่อวาน' },
  { value: 'week', label: 'สัปดาห์นี้' },
  { value: 'month', label: 'เดือนนี้' },
  { value: 'year', label: 'ปีนี้' },
]);

const sortOptions = ref([
  { value: 'created_at', label: 'วันที่' },
  { value: 'type', label: 'ประเภท' },
  { value: 'user', label: 'ผู้ใช้' },
  { value: 'page', label: 'หน้า' },
]);

// Load tracking data
const loadTrackingData = async () => {
  loading.value = true;
  error.value = null;
  try {
    const response = await getTrackingEvents(siteId.value, {
      search: searchQuery.value,
      type: selectedType.value,
      dateRange: selectedDateRange.value,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value,
      page: currentPage.value,
      limit: itemsPerPage.value,
    });
    trackingData.value = (response as any)?.data || [];
  } catch (err: any) {
    error.value = err.message || 'เกิดข้อผิดพลาดในการโหลดข้อมูล';
    console.error('Error loading tracking data:', err);
  } finally {
    loading.value = false;
  }
};

// Load analytics data
const loadAnalytics = async () => {
  try {
    const response = await getTrackingAnalytics(siteId.value);
    analytics.value = (response as any)?.data || {
      totalEvents: 0,
      uniqueUsers: 0,
      pageViews: 0,
      clicks: 0,
      averageSession: 0,
      bounceRate: 0,
      topPage: '',
      growthRate: 0,
    };
  } catch (err: any) {
    console.error('Error loading analytics:', err);
  }
};

// Load data on mount
onMounted(async () => {
  await Promise.all([loadTrackingData(), loadAnalytics()]);
});

// Watch for filter changes
watch([searchQuery, selectedType, selectedDateRange, sortBy, sortOrder], async () => {
  currentPage.value = 1;
  await loadTrackingData();
});

// Watch for route changes
watch(
  () => siteId.value,
  async newSiteId => {
    if (newSiteId) {
      await Promise.all([loadTrackingData(), loadAnalytics()]);
    }
  }
);

// Format number
const formatNumber = (num: number) => {
  return new Intl.NumberFormat('th-TH').format(num);
};

// Get type badge class
const getTypeBadgeClass = (type: string) => {
  const classes = {
    page_view: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    click: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    scroll: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
    form_submit: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
    download: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
  };
  return classes[type as keyof typeof classes] || classes.page_view;
};

// Get type label
const getTypeLabel = (type: string) => {
  const labels = {
    page_view: 'การดูหน้า',
    click: 'การคลิก',
    scroll: 'การเลื่อน',
    form_submit: 'การส่งฟอร์ม',
    download: 'การดาวน์โหลด',
  };
  return labels[type as keyof typeof labels] || type;
};

// Format date
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Computed filtered tracking data
const filteredTrackingData = computed(() => {
  let filtered = trackingData.value || [];

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(
      (item: any) =>
        item.user.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        item.page.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        item.location.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  // Type filter
  if (selectedType.value !== 'all') {
    filtered = filtered.filter((item: any) => item.type === selectedType.value);
  }

  // Sort
  filtered.sort((a: any, b: any) => {
    let aVal = a[sortBy.value];
    let bVal = b[sortBy.value];

    if (typeof aVal === 'string') {
      aVal = aVal.toLowerCase();
      bVal = bVal.toLowerCase();
    }

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });

  return filtered;
});

// Paginated tracking data
const paginatedTrackingData = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredTrackingData.value.slice(start, end);
});

// Total pages
const totalPages = computed(() => {
  return Math.ceil(filteredTrackingData.value.length / itemsPerPage.value);
});

// Methods
const handleSort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = field;
    sortOrder.value = 'desc';
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handleViewDetails = (item: any) => {
  // Navigate to tracking detail page
  navigateTo(`/dashboard/${siteId.value}/tracking/${item.id}`);
};

const handleExportData = async () => {
  try {
    loading.value = true;
    await exportTrackingData(siteId.value, 'csv');
    useToast().add({
      title: 'สำเร็จ',
      description: 'ส่งออกข้อมูลการติดตามเรียบร้อยแล้ว',
      color: 'success',
    });
  } catch (error: any) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: error.message || 'ไม่สามารถส่งออกข้อมูลได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

const handleClearData = async () => {
  if (confirm('คุณต้องการล้างข้อมูลการติดตามทั้งหมดใช่หรือไม่?')) {
    try {
      loading.value = true;
      await clearTrackingData(siteId.value);
      trackingData.value = [];
      useToast().add({
        title: 'สำเร็จ',
        description: 'ล้างข้อมูลการติดตามเรียบร้อยแล้ว',
        color: 'success',
      });
    } catch (error: any) {
      useToast().add({
        title: 'ข้อผิดพลาด',
        description: error.message || 'ไม่สามารถล้างข้อมูลได้',
        color: 'error',
      });
    } finally {
      loading.value = false;
    }
  }
};

// SEO
useSeoMeta({
  title: 'การติดตาม - ระบบจัดการร้านค้า',
  description: 'ติดตามพฤติกรรมผู้ใช้และการใช้งานเว็บไซต์',
  keywords: 'การติดตาม, วิเคราะห์, สถิติ, ผู้ใช้',
  ogTitle: 'การติดตาม - ระบบจัดการร้านค้า',
  ogDescription: 'ติดตามพฤติกรรมผู้ใช้และการใช้งานเว็บไซต์',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div class="space-y-8">
    <!-- Enhanced Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          การติดตาม
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          ติดตามพฤติกรรมผู้ใช้และการใช้งานเว็บไซต์
        </p>
      </div>
      <div class="flex items-center gap-2">
        <UButton 
          @click="loadTrackingData"
          :loading="loading"
          variant="outline"
          icon="i-heroicons-arrow-path"
        >
          รีเฟรช
        </UButton>
        <UButton 
          @click="handleExportData"
          :loading="loading"
          variant="outline"
          icon="i-heroicons-arrow-down-tray"
        >
          ส่งออก
        </UButton>
        <UButton 
          @click="handleClearData"
          :loading="loading"
          variant="outline"
          icon="i-heroicons-trash"
          color="error"
        >
          ล้างข้อมูล
        </UButton>
      </div>
    </div>

    <!-- Enhanced Analytics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">เหตุการณ์ทั้งหมด</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(analytics.totalEvents || 0) }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">+{{ analytics.growthRate || 0 }}% จากเดือนที่แล้ว</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-chart-bar" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ผู้ใช้ที่ไม่ซ้ำ</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(analytics.uniqueUsers || 0) }}</p>
            <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">เฉลี่ย {{ analytics.averageSession || 0 }} ครั้ง/คน</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-users" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">การดูหน้า</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(analytics.pageViews || 0) }}</p>
            <p class="text-xs text-purple-600 dark:text-purple-400 mt-1">81% ของเหตุการณ์ทั้งหมด</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-eye" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">อัตราการเด้ง</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.bounceRate || 0 }}%</p>
            <p class="text-xs text-yellow-600 dark:text-yellow-400 mt-1">ต่ำกว่าค่าเฉลี่ย</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-arrow-path" class="w-8 h-8" />
          </div>
        </div>
      </UCard>
    </div>

    <!-- Enhanced Filters -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-funnel" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวกรองและค้นหา</h2>
        </div>
      </template>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหา</label>
          <UInput
            v-model="searchQuery"
            placeholder="ค้นหาตามผู้ใช้, หน้า, หรือตำแหน่ง..."
            icon="i-heroicons-magnifying-glass"
            class="w-full"
          />
        </div>

        <!-- Type Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ประเภท</label>
          <USelect
            v-model="selectedType"
            :items="typeOptions"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกประเภท"
            class="w-full"
          />
        </div>

        <!-- Date Range Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ช่วงเวลา</label>
          <USelect
            v-model="selectedDateRange"
            :items="dateRangeOptions"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกช่วงเวลา"
            class="w-full"
          />
        </div>

        <!-- Sort -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เรียงลำดับ</label>
          <div class="flex gap-2">
            <USelect
              v-model="sortBy"
              :items="sortOptions"
              option-attribute="label"
              value-attribute="value"
              class="flex-1"
            />
            <UButton
              @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'"
              variant="outline"
              size="sm"
              class="px-3"
            >
              <Icon 
                :name="sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down'" 
                class="w-4 h-4" 
              />
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Enhanced Tracking Table -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <Icon name="i-heroicons-chart-bar" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รายการการติดตาม</h2>
            <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-400 rounded-full">
              {{ filteredTrackingData.length }} รายการ
            </span>
          </div>
          <div class="flex items-center gap-2">
            <UButton variant="ghost" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
              <Icon name="i-heroicons-cog-6-tooth" class="w-4 h-4" />
            </UButton>
          </div>
        </div>
      </template>
      
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="flex items-center gap-3">
          <Icon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-blue-600" />
          <span class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</span>
        </div>
      </div>

      <div v-else-if="error" class="text-center py-12">
        <div class="text-red-600 dark:text-red-400 mb-4">
          <Icon name="i-heroicons-exclamation-triangle" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">เกิดข้อผิดพลาด</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">{{ error }}</p>
        <UButton @click="loadTrackingData" variant="outline">
          ลองใหม่
        </UButton>
      </div>

      <div v-else-if="paginatedTrackingData.length === 0" class="text-center py-12">
        <div class="text-gray-400 dark:text-gray-500 mb-4">
          <Icon name="i-heroicons-chart-bar" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">ไม่พบข้อมูลการติดตาม</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          ยังไม่มีข้อมูลการติดตาม หรือไม่ตรงกับเงื่อนไขการค้นหา
        </p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>เหตุการณ์</span>
                  <UButton
                    @click="handleSort('type')"
                    variant="ghost"
                    size="xs"
                    class="p-1"
                  >
                    <Icon 
                      :name="sortBy === 'type' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'" 
                      class="w-3 h-3" 
                    />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>ผู้ใช้</span>
                  <UButton
                    @click="handleSort('user')"
                    variant="ghost"
                    size="xs"
                    class="p-1"
                  >
                    <Icon 
                      :name="sortBy === 'user' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'" 
                      class="w-3 h-3" 
                    />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>หน้า</span>
                  <UButton
                    @click="handleSort('page')"
                    variant="ghost"
                    size="xs"
                    class="p-1"
                  >
                    <Icon 
                      :name="sortBy === 'page' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'" 
                      class="w-3 h-3" 
                    />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>ตำแหน่ง</span>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>วันที่</span>
                  <UButton
                    @click="handleSort('created_at')"
                    variant="ghost"
                    size="xs"
                    class="p-1"
                  >
                    <Icon 
                      :name="sortBy === 'created_at' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'" 
                      class="w-3 h-3" 
                    />
                  </UButton>
                </div>
              </th>
              <th class="text-right py-4 px-6 font-semibold text-gray-900 dark:text-white">การดำเนินการ</th>
            </tr>
          </thead>
          <tbody>
            <tr 
              v-for="item in paginatedTrackingData" 
              :key="item.id"
              class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
            >
              <td class="py-4 px-6">
                <div class="flex items-center gap-3">
                  <span 
                    class="px-3 py-1 text-xs rounded-full font-medium"
                    :class="getTypeBadgeClass(item.type)"
                  >
                    {{ getTypeLabel(item.type) }}
                  </span>
                  <div v-if="item.duration" class="text-xs text-gray-500 dark:text-gray-400">
                    {{ item.duration }}s
                  </div>
                </div>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center gap-3">
                  <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                    {{ item.user.charAt(0).toUpperCase() }}
                  </div>
                  <span class="text-sm text-gray-900 dark:text-white">{{ item.user }}</span>
                </div>
              </td>
              <td class="py-4 px-6">
                <span class="text-sm text-gray-900 dark:text-white">{{ item.page }}</span>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center gap-2">
                  <Icon name="i-heroicons-map-pin" class="w-4 h-4 text-gray-400" />
                  <span class="text-sm text-gray-600 dark:text-gray-400">{{ item.location }}</span>
                </div>
              </td>
              <td class="py-4 px-6">
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {{ formatDate(item.createdAt) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center justify-end gap-2">
                  <UButton
                    @click="handleViewDetails(item)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                  >
                    <Icon name="i-heroicons-eye" class="w-4 h-4" />
                  </UButton>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Enhanced Pagination -->
      <div v-if="totalPages > 1" class="flex items-center justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} ถึง {{ Math.min(currentPage * itemsPerPage, filteredTrackingData.length) }} 
          จาก {{ filteredTrackingData.length }} รายการ
        </div>
        <div class="flex items-center gap-2">
          <UButton
            @click="handlePageChange(currentPage - 1)"
            :disabled="currentPage === 1"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-left" class="w-4 h-4" />
          </UButton>
          
          <div class="flex items-center gap-1">
            <UButton
              v-for="page in Math.min(5, totalPages)"
              :key="page"
              @click="handlePageChange(page)"
              :variant="currentPage === page ? 'solid' : 'outline'"
              size="sm"
              class="w-8 h-8 p-0"
            >
              {{ page }}
            </UButton>
          </div>
          
          <UButton
            @click="handlePageChange(currentPage + 1)"
            :disabled="currentPage === totalPages"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-right" class="w-4 h-4" />
          </UButton>
        </div>
      </div>
    </UCard>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style> 