<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// Use composables
const { $apiFetch } = useNuxtApp() as any;
const { getNovels, createNovel, updateNovel, deleteNovel, getNovelStats } = useNovel();

// Reactive data
const novels = ref<any[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);
const searchQuery = ref('');
const selectedGenre = ref('all');
const selectedStatus = ref('all');
const selectedAgeRating = ref('all');
const sortBy = ref('created_at');
const sortOrder = ref('desc');
const currentPage = ref(1);
const itemsPerPage = ref(12);
const viewMode = ref<'grid' | 'list'>('grid');

// Filters
const genres = ref([
  { value: 'all', label: 'ทุกหมวดหมู่' },
  { value: 'romance', label: 'โรแมนติก' },
  { value: 'fantasy', label: 'แฟนตาซี' },
  { value: 'adventure', label: 'ผจญภัย' },
  { value: 'mystery', label: 'ลึกลับ' },
  { value: 'thriller', label: 'ระทึกขวัญ' },
  { value: 'horror', label: 'สยองขวัญ' },
  { value: 'comedy', label: 'ตลก' },
  { value: 'drama', label: 'ดราม่า' },
  { value: 'action', label: 'แอคชั่น' },
  { value: 'sci-fi', label: 'ไซไฟ' },
  { value: 'historical', label: 'ประวัติศาสตร์' },
  { value: 'slice-of-life', label: 'ชีวิตประจำวัน' },
  { value: 'supernatural', label: 'เหนือธรรมชาติ' },
  { value: 'school', label: 'โรงเรียน' },
  { value: 'other', label: 'อื่นๆ' },
]);

const statuses = ref([
  { value: 'all', label: 'ทุกสถานะ' },
  { value: 'ongoing', label: 'กำลังดำเนิน' },
  { value: 'completed', label: 'เสร็จสิ้น' },
  { value: 'hiatus', label: 'หยุดชั่วคราว' },
  { value: 'cancelled', label: 'ยกเลิก' },
]);

const ageRatings = ref([
  { value: 'all', label: 'ทุกอายุ' },
  { value: 'all', label: 'ทุกวัย' },
  { value: '13+', label: '13+' },
  { value: '16+', label: '16+' },
  { value: '18+', label: '18+' },
]);

const sortOptions = ref([
  { value: 'created_at', label: 'วันที่สร้าง' },
  { value: 'title', label: 'ชื่อนิยาย' },
  { value: 'total_views', label: 'จำนวนผู้เข้าชม' },
  { value: 'total_likes', label: 'จำนวนไลค์' },
  { value: 'average_rating', label: 'คะแนนเฉลี่ย' },
  { value: 'total_chapters', label: 'จำนวนตอน' },
]);

// Analytics data
const analytics = ref({
  totalNovels: 0,
  ongoingNovels: 0,
  completedNovels: 0,
  totalChapters: 0,
  totalViews: 0,
  totalLikes: 0,
  averageViews: 0,
});

// Format number
const formatNumber = (num: number) => {
  return new Intl.NumberFormat('th-TH').format(num);
};

// Get status badge class
const getStatusBadgeClass = (status: string) => {
  const classes = {
    ongoing: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    completed: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    hiatus: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    cancelled: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
  };
  return classes[status as keyof typeof classes] || classes.ongoing;
};

// Get status label
const getStatusLabel = (status: string) => {
  const labels = {
    ongoing: 'กำลังดำเนิน',
    completed: 'เสร็จสิ้น',
    hiatus: 'หยุดชั่วคราว',
    cancelled: 'ยกเลิก',
  };
  return labels[status as keyof typeof labels] || status;
};

// Get age rating badge class
const getAgeRatingBadgeClass = (ageRating: string) => {
  const classes = {
    all: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    '13+': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    '16+': 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
    '18+': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
  };
  return classes[ageRating as keyof typeof classes] || classes.all;
};

// Fetch novels
const fetchNovels = async () => {
  loading.value = true;
  error.value = null;
  try {
    const response = await getNovels(siteId.value, {
      page: currentPage.value,
      limit: itemsPerPage.value,
      search: searchQuery.value,
      genre: selectedGenre.value !== 'all' ? [selectedGenre.value] : undefined,
      status: selectedStatus.value !== 'all' ? selectedStatus.value : undefined,
      age_rating: selectedAgeRating.value !== 'all' ? selectedAgeRating.value : undefined,
      sort_by: sortBy.value,
      sort_order: sortOrder.value,
    });
    if (response?.success && response?.data) {
      novels.value = response.data.novels || [];
    }
  } catch (err) {
    error.value = 'เกิดข้อผิดพลาดในการโหลดข้อมูล';
    console.error('Error fetching novels:', err);
  } finally {
    loading.value = false;
  }
};

// Fetch analytics
const fetchAnalytics = async () => {
  try {
    const response = await getNovelStats(siteId.value);
    if (response?.success && response?.data) {
      analytics.value = response.data;
    }
  } catch (err) {
    console.error('Error fetching analytics:', err);
  }
};

// Computed filtered novels
const filteredNovels = computed(() => {
  let filtered = novels.value || [];

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(
      (item: any) =>
        item.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        item.author_name.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  // Genre filter
  if (selectedGenre.value !== 'all') {
    filtered = filtered.filter((item: any) => item.genre.includes(selectedGenre.value));
  }

  // Status filter
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter((item: any) => item.status === selectedStatus.value);
  }

  // Age rating filter
  if (selectedAgeRating.value !== 'all') {
    filtered = filtered.filter((item: any) => item.age_rating === selectedAgeRating.value);
  }

  // Sort
  filtered.sort((a: any, b: any) => {
    let aVal = a[sortBy.value as keyof typeof a];
    let bVal = b[sortBy.value as keyof typeof b];

    if (typeof aVal === 'string') {
      aVal = aVal.toLowerCase();
      bVal = bVal.toLowerCase();
    }

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });

  return filtered;
});

// Paginated novels
const paginatedNovels = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredNovels.value.slice(start, end);
});

// Total pages
const totalPages = computed(() => {
  return Math.ceil(filteredNovels.value.length / itemsPerPage.value);
});

// Fetch data on mount
onMounted(async () => {
  await Promise.all([fetchNovels(), fetchAnalytics()]);
});

// Watch for filter changes
watch([searchQuery, selectedGenre, selectedStatus, selectedAgeRating], () => {
  currentPage.value = 1;
});

// Methods
const handleSearch = () => {
  currentPage.value = 1;
};

const handleSort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = field;
    sortOrder.value = 'desc';
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handleViewNovel = (novel: any) => {
  navigateTo(`/dashboard/${siteId.value}/novel/${novel.id}`);
};

const handleEditNovel = (novel: any) => {
  navigateTo(`/dashboard/${siteId.value}/novel/${novel.id}/edit`);
};

const handleDeleteNovel = async (novel: any) => {
  if (confirm(`คุณต้องการลบนิยาย "${novel.title}" ใช่หรือไม่?`)) {
    try {
      await deleteNovel(siteId.value, novel.id);
      await fetchNovels();
    } catch (err) {
      console.error('Error deleting novel:', err);
    }
  }
};

const handleCreateNovel = () => {
  navigateTo(`/dashboard/${siteId.value}/novel/create`);
};

const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'grid' ? 'list' : 'grid';
};
</script>

<template>
  <div class="space-y-8">
    <!-- Enhanced Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          จัดการนิยาย
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          จัดการนิยายและเนื้อหาของคุณ
        </p>
      </div>
      <div class="flex items-center gap-3">
        <UButton
          @click="toggleViewMode"
          variant="outline"
          size="sm"
          class="hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <Icon 
            :name="viewMode === 'grid' ? 'i-heroicons-list-bullet' : 'i-heroicons-squares-2x2'" 
            class="w-4 h-4 mr-2" 
          />
          {{ viewMode === 'grid' ? 'รายการ' : 'กริด' }}
        </UButton>
        <UButton 
          @click="handleCreateNovel"
          class="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
          เขียนนิยายใหม่
        </UButton>
      </div>
    </div>

    <!-- Enhanced Analytics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">นิยายทั้งหมด</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalNovels }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">นิยายล่าสุด</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-book-open" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">กำลังดำเนิน</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.ongoingNovels }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">{{ Math.round((analytics.ongoingNovels / analytics.totalNovels) * 100) }}% ของทั้งหมด</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-play" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ตอนทั้งหมด</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalChapters }}</p>
            <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">เฉลี่ย {{ Math.round(analytics.totalChapters / analytics.totalNovels) }}/นิยาย</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-document-text" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ผู้เข้าชมรวม</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(analytics.totalViews) }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">เฉลี่ย {{ formatNumber(analytics.averageViews) }}/นิยาย</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-eye" class="w-8 h-8" />
          </div>
        </div>
      </UCard>
    </div>

    <!-- Enhanced Filters -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-funnel" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวกรองและค้นหา</h2>
        </div>
      </template>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหานิยาย</label>
          <UInput
            v-model="searchQuery"
            placeholder="ค้นหาตามชื่อหรือผู้เขียน..."
            icon="i-heroicons-magnifying-glass"
            @input="handleSearch"
            class="w-full"
          />
        </div>

        <!-- Genre Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">หมวดหมู่</label>
          <USelect
            v-model="selectedGenre"
            :items="genres"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกหมวดหมู่"
            class="w-full"
          />
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สถานะ</label>
          <USelect
            v-model="selectedStatus"
            :items="statuses"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกสถานะ"
            class="w-full"
          />
        </div>

        <!-- Age Rating Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">อายุ</label>
          <USelect
            v-model="selectedAgeRating"
            :items="ageRatings"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกอายุ"
            class="w-full"
          />
        </div>

        <!-- Sort -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เรียงลำดับ</label>
          <div class="flex gap-2">
            <USelect
              v-model="sortBy"
              :items="sortOptions"
              option-attribute="label"
              value-attribute="value"
              class="flex-1"
            />
            <UButton
              @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'"
              variant="outline"
              size="sm"
              class="px-3"
            >
              <Icon 
                :name="sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down'" 
                class="w-4 h-4" 
              />
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Enhanced Novels Grid/List -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
              <Icon name="i-heroicons-book-open" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รายการนิยาย</h2>
            <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-400 rounded-full">
              {{ filteredNovels.length }} รายการ
            </span>
          </div>
          <div class="flex items-center gap-2">
            <UButton variant="ghost" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
              <Icon name="i-heroicons-arrow-down-tray" class="w-4 h-4 mr-1" />
              ส่งออก
            </UButton>
            <UButton variant="ghost" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
              <Icon name="i-heroicons-cog-6-tooth" class="w-4 h-4" />
            </UButton>
          </div>
        </div>
      </template>
      
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="flex items-center gap-3">
          <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-purple-600" />
          <span class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</span>
        </div>
      </div>

      <div v-else-if="error" class="text-center py-12">
        <div class="text-red-600 dark:text-red-400 mb-4">
          <Icon name="i-heroicons-exclamation-triangle" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">เกิดข้อผิดพลาด</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">{{ error }}</p>
        <UButton @click="fetchNovels()" variant="outline">
          ลองใหม่
        </UButton>
      </div>

      <div v-else-if="paginatedNovels.length === 0" class="text-center py-12">
        <div class="text-gray-400 dark:text-gray-500 mb-4">
          <Icon name="i-heroicons-book-open" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">ไม่พบนิยาย</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          ยังไม่มีนิยายในระบบ หรือไม่ตรงกับเงื่อนไขการค้นหา
        </p>
        <UButton @click="handleCreateNovel" class="bg-gradient-to-r from-purple-600 to-pink-600 text-white">
          <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
          เขียนนิยายแรก
        </UButton>
      </div>

      <div v-else>
        <!-- Grid View -->
        <div v-if="viewMode === 'grid'" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <div 
            v-for="item in paginatedNovels" 
            :key="item.id"
            class="group bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 hover:scale-105 overflow-hidden"
          >
            <!-- Novel Cover -->
            <div class="relative h-48 bg-gradient-to-br from-purple-500 to-pink-600 overflow-hidden">
              <div class="absolute inset-0 bg-black/20"></div>
              <div class="absolute top-4 right-4 flex gap-2">
                <span 
                  class="px-2 py-1 text-xs rounded-full font-medium text-white backdrop-blur-sm"
                  :class="getStatusBadgeClass(item.status)"
                >
                  {{ getStatusLabel(item.status) }}
                </span>
                <span 
                  class="px-2 py-1 text-xs rounded-full font-medium text-white backdrop-blur-sm"
                  :class="getAgeRatingBadgeClass(item.age_rating)"
                >
                  {{ item.age_rating }}
                </span>
              </div>
              <div class="absolute bottom-4 left-4 right-4">
                <span class="px-2 py-1 text-xs bg-white/90 dark:bg-gray-800/90 text-gray-700 dark:text-gray-300 rounded-full">
                  {{ item.genre[0] }}
                </span>
              </div>
            </div>

            <!-- Novel Content -->
            <div class="p-6">
              <h3 class="font-semibold text-gray-900 dark:text-white text-lg mb-2 line-clamp-2">
                {{ item.title }}
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                โดย {{ item.author_name }}
              </p>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
                {{ item.description }}
              </p>

              <!-- Novel Stats -->
              <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-4">
                <div class="flex items-center gap-4">
                  <span class="flex items-center gap-1">
                    <Icon name="i-heroicons-eye" class="w-3 h-3" />
                    {{ formatNumber(item.total_views || 0) }}
                  </span>
                  <span class="flex items-center gap-1">
                    <Icon name="i-heroicons-heart" class="w-3 h-3" />
                    {{ formatNumber(item.total_likes || 0) }}
                  </span>
                  <span class="flex items-center gap-1">
                    <Icon name="i-heroicons-star" class="w-3 h-3" />
                    {{ item.average_rating?.toFixed(1) || '0.0' }}
                  </span>
                </div>
                <span>{{ item.total_chapters || 0 }} ตอน</span>
              </div>

              <!-- Actions -->
              <div class="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700">
                <UButton
                  @click="handleViewNovel(item)"
                  variant="ghost"
                  size="sm"
                  class="hover:bg-purple-50 dark:hover:bg-purple-900/20 text-purple-600 dark:text-purple-400"
                >
                  <Icon name="i-heroicons-eye" class="w-4 h-4 mr-1" />
                  ดู
                </UButton>
                <UButton
                  @click="handleEditNovel(item)"
                  variant="ghost"
                  size="sm"
                  class="hover:bg-purple-50 dark:hover:bg-purple-900/20 text-purple-600 dark:text-purple-400"
                >
                  <Icon name="i-heroicons-pencil-square" class="w-4 h-4 mr-1" />
                  แก้ไข
                </UButton>
                <UButton
                  @click="handleDeleteNovel(item)"
                  variant="ghost"
                  size="sm"
                  class="hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400"
                >
                  <Icon name="i-heroicons-trash" class="w-4 h-4" />
                </UButton>
              </div>
            </div>
          </div>
        </div>

        <!-- List View -->
        <div v-else class="space-y-4">
          <div 
            v-for="item in paginatedNovels" 
            :key="item.id"
            class="flex items-center gap-4 p-4 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 hover:scale-[1.02]"
          >
            <!-- Novel Avatar -->
            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center text-white font-semibold text-lg">
              {{ item.title.charAt(0).toUpperCase() }}
            </div>

            <!-- Novel Info -->
            <div class="flex-1 min-w-0">
              <div class="flex items-start justify-between mb-2">
                <h3 class="font-semibold text-gray-900 dark:text-white text-lg line-clamp-1">
                  {{ item.title }}
                </h3>
                <div class="flex gap-2 ml-2 flex-shrink-0">
                  <span 
                    class="px-2 py-1 text-xs rounded-full font-medium"
                    :class="getStatusBadgeClass(item.status)"
                  >
                    {{ getStatusLabel(item.status) }}
                  </span>
                  <span 
                    class="px-2 py-1 text-xs rounded-full font-medium"
                    :class="getAgeRatingBadgeClass(item.age_rating)"
                  >
                    {{ item.age_rating }}
                  </span>
                </div>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">
                โดย {{ item.author_name }}
              </p>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                {{ item.description }}
              </p>
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                  <span class="flex items-center gap-1">
                    <Icon name="i-heroicons-eye" class="w-3 h-3" />
                    {{ formatNumber(item.total_views || 0) }}
                  </span>
                  <span class="flex items-center gap-1">
                    <Icon name="i-heroicons-heart" class="w-3 h-3" />
                    {{ formatNumber(item.total_likes || 0) }}
                  </span>
                  <span class="flex items-center gap-1">
                    <Icon name="i-heroicons-star" class="w-3 h-3" />
                    {{ item.average_rating?.toFixed(1) || '0.0' }}
                  </span>
                  <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full">
                    {{ item.genre[0] }}
                  </span>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  {{ item.total_chapters || 0 }} ตอน
                </span>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center gap-2">
              <UButton
                @click="handleViewNovel(item)"
                variant="ghost"
                size="sm"
                class="hover:bg-purple-50 dark:hover:bg-purple-900/20 text-purple-600 dark:text-purple-400"
              >
                <Icon name="i-heroicons-eye" class="w-4 h-4" />
              </UButton>
              <UButton
                @click="handleEditNovel(item)"
                variant="ghost"
                size="sm"
                class="hover:bg-purple-50 dark:hover:bg-purple-900/20 text-purple-600 dark:text-purple-400"
              >
                <Icon name="i-heroicons-pencil-square" class="w-4 h-4" />
              </UButton>
              <UButton
                @click="handleDeleteNovel(item)"
                variant="ghost"
                size="sm"
                class="hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400"
              >
                <Icon name="i-heroicons-trash" class="w-4 h-4" />
              </UButton>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Pagination -->
      <div v-if="totalPages > 1" class="flex items-center justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} ถึง {{ Math.min(currentPage * itemsPerPage, filteredNovels.length) }} 
          จาก {{ filteredNovels.length }} รายการ
        </div>
        <div class="flex items-center gap-2">
          <UButton
            @click="handlePageChange(currentPage - 1)"
            :disabled="currentPage === 1"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-left" class="w-4 h-4" />
          </UButton>
          
          <div class="flex items-center gap-1">
            <UButton
              v-for="page in Math.min(5, totalPages)"
              :key="page"
              @click="handlePageChange(page)"
              :variant="currentPage === page ? 'solid' : 'outline'"
              size="sm"
              class="w-8 h-8 p-0"
            >
              {{ page }}
            </UButton>
          </div>
          
          <UButton
            @click="handlePageChange(currentPage + 1)"
            :disabled="currentPage === totalPages"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-right" class="w-4 h-4" />
          </UButton>
        </div>
      </div>
    </UCard>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Line clamp utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 