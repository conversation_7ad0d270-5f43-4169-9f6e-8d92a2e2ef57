<script setup lang="ts">
import { useRoute } from 'vue-router';
import { useNuxtApp } from '#app';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// Use composables
const { $apiFetch } = useNuxtApp();
const { createNovel } = useNovel();

// Form data
const form = ref({
  title: '',
  description: '',
  genre: [] as string[],
  status: 'ongoing',
  age_rating: 'all',
  author_name: '',
  cover_image: '',
  is_premium: false,
  price_per_chapter: 0,
  tags: [] as string[],
  seo: {
    title: '',
    description: '',
    keywords: '',
  },
});

// Validation
const errors = ref<Record<string, string>>({});
const loading = ref(false);

// Genres
const genres = ref([
  { value: 'romance', label: 'โรแมนติก' },
  { value: 'fantasy', label: 'แฟนตาซี' },
  { value: 'adventure', label: 'ผจญภัย' },
  { value: 'mystery', label: 'ลึกลับ' },
  { value: 'thriller', label: 'ระทึกขวัญ' },
  { value: 'horror', label: 'สยองขวัญ' },
  { value: 'comedy', label: 'ตลก' },
  { value: 'drama', label: 'ดราม่า' },
  { value: 'action', label: 'แอคชั่น' },
  { value: 'sci-fi', label: 'ไซไฟ' },
  { value: 'historical', label: 'ประวัติศาสตร์' },
  { value: 'slice-of-life', label: 'ชีวิตประจำวัน' },
  { value: 'supernatural', label: 'เหนือธรรมชาติ' },
  { value: 'school', label: 'โรงเรียน' },
  { value: 'other', label: 'อื่นๆ' },
]);

// Status options
const statuses = ref([
  { value: 'ongoing', label: 'กำลังดำเนิน' },
  { value: 'completed', label: 'เสร็จสิ้น' },
  { value: 'hiatus', label: 'หยุดชั่วคราว' },
  { value: 'cancelled', label: 'ยกเลิก' },
]);

// Age rating options
const ageRatings = ref([
  { value: 'all', label: 'ทุกวัย' },
  { value: '13+', label: '13+' },
  { value: '16+', label: '16+' },
  { value: '18+', label: '18+' },
]);

// Validation rules
const validateForm = () => {
  errors.value = {};

  if (!form.value.title.trim()) {
    errors.value.title = 'กรุณากรอกชื่อนิยาย';
  }

  if (!form.value.description.trim()) {
    errors.value.description = 'กรุณากรอกคำอธิบายนิยาย';
  }

  if (form.value.genre.length === 0) {
    errors.value.genre = 'กรุณาเลือกหมวดหมู่อย่างน้อย 1 หมวด';
  }

  if (!form.value.author_name.trim()) {
    errors.value.author_name = 'กรุณากรอกชื่อผู้เขียน';
  }

  return Object.keys(errors.value).length === 0;
};

// Handle form submission
const handleSubmit = async () => {
  if (!validateForm()) return;

  loading.value = true;
  try {
    await createNovel(siteId.value, form.value);
    await navigateTo(`/dashboard/${siteId.value}/novel`);
  } catch (error) {
    console.error('Error creating novel:', error);
  } finally {
    loading.value = false;
  }
};

// Handle cancel
const handleCancel = () => {
  navigateTo(`/dashboard/${siteId.value}/novel`);
};

// Add genre
const addGenre = (genre: string) => {
  if (genre && !form.value.genre.includes(genre)) {
    form.value.genre.push(genre);
  }
};

// Remove genre
const removeGenre = (genre: string) => {
  form.value.genre = form.value.genre.filter(g => g !== genre);
};

// Add tag
const addTag = (tag: string) => {
  if (tag && !form.value.tags.includes(tag)) {
    form.value.tags.push(tag);
  }
};

// Remove tag
const removeTag = (tag: string) => {
  form.value.tags = form.value.tags.filter(t => t !== tag);
};

// Auto-generate SEO title
const generateSEOTitle = () => {
  if (form.value.title && !form.value.seo.title) {
    form.value.seo.title = form.value.title;
  }
};

// Auto-generate SEO description
const generateSEODescription = () => {
  if (form.value.description && !form.value.seo.description) {
    form.value.seo.description = form.value.description;
  }
};

// Word count
const wordCount = computed(() => {
  return form.value.description.split(' ').length;
});

// Tag input ref
const tagInput = ref<HTMLInputElement>();

// Add tag function
const handleAddTag = () => {
  if (tagInput.value?.value) {
    addTag(tagInput.value.value);
    tagInput.value.value = '';
  }
};

// SEO
useSeoMeta({
  title: 'เขียนนิยายใหม่ - ระบบจัดการร้านค้า',
  description: 'เขียนนิยายใหม่ จัดการเนื้อหา และการตั้งค่า SEO สำหรับนิยาย',
  keywords: 'เขียนนิยาย, จัดการเนื้อหา, SEO, นิยาย',
  ogTitle: 'เขียนนิยายใหม่ - ระบบจัดการร้านค้า',
  ogDescription: 'เขียนนิยายใหม่ จัดการเนื้อหา และการตั้งค่า SEO สำหรับนิยาย',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          เขียนนิยายใหม่
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          สร้างนิยายใหม่สำหรับเว็บไซต์
        </p>
      </div>
      <div class="flex items-center gap-3">
        <UButton
          @click="handleCancel"
          variant="outline"
          size="sm"
          class="hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <Icon name="i-heroicons-arrow-left" class="w-4 h-4 mr-2" />
          ย้อนกลับ
        </UButton>
        <UButton 
          @click="handleSubmit"
          :loading="loading"
          class="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <Icon name="i-heroicons-check" class="w-5 h-5 mr-2" />
          บันทึกนิยาย
        </UButton>
      </div>
    </div>

    <!-- Form -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
      <!-- Main Content -->
      <div class="lg:col-span-3 space-y-6">
        <!-- Title -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
                <Icon name="i-heroicons-book-open" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ชื่อนิยาย</h2>
            </div>
          </template>
          
          <UInput
            v-model="form.title"
            placeholder="กรอกชื่อนิยาย"
            :error="errors.title"
            class="w-full text-2xl font-bold"
            @blur="generateSEOTitle"
          />
        </UCard>

        <!-- Description -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="p-2 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg">
                  <Icon name="i-heroicons-document-text" class="w-5 h-5 text-white" />
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">คำอธิบาย</h2>
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                {{ wordCount }} คำ
              </div>
            </div>
          </template>
          
          <UTextarea
            v-model="form.description"
            placeholder="เขียนคำอธิบายนิยายของคุณที่นี่..."
            :error="errors.description"
            :rows="8"
            class="w-full font-serif text-lg leading-relaxed"
            @blur="generateSEODescription"
          />
        </UCard>

        <!-- Author -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                <Icon name="i-heroicons-user" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ผู้เขียน</h2>
            </div>
          </template>
          
          <UInput
            v-model="form.author_name"
            placeholder="กรอกชื่อผู้เขียน"
            :error="errors.author_name"
            class="w-full"
          />
        </UCard>
      </div>

      <!-- Settings Sidebar -->
      <div class="space-y-6">
        <!-- Status and Age Rating -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                <Icon name="i-heroicons-flag" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">สถานะและอายุ</h2>
            </div>
          </template>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                สถานะ
              </label>
              <USelect
                v-model="form.status"
                :items="statuses"
                option-attribute="label"
                value-attribute="value"
                placeholder="เลือกสถานะ"
                class="w-full"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                อายุ
              </label>
              <USelect
                v-model="form.age_rating"
                :items="ageRatings"
                option-attribute="label"
                value-attribute="value"
                placeholder="เลือกอายุ"
                class="w-full"
              />
            </div>
          </div>
        </UCard>

        <!-- Genre -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg">
                <Icon name="i-heroicons-tag" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">หมวดหมู่</h2>
            </div>
          </template>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                เลือกหมวดหมู่
              </label>
              <USelect
                :items="genres"
                option-attribute="label"
                value-attribute="value"
                placeholder="เลือกหมวดหมู่"
                :error="errors.genre"
                class="w-full"
                @update:model-value="addGenre"
              />
            </div>
            
            <div v-if="form.genre.length > 0" class="flex flex-wrap gap-2">
              <UBadge
                v-for="genre in form.genre"
                :key="genre"
                color="primary"
                variant="subtle"
                class="cursor-pointer hover:bg-purple-100 dark:hover:bg-purple-900/20"
                @click="removeGenre(genre)"
              >
                {{ genres.find(g => g.value === genre)?.label }}
                <Icon name="i-heroicons-x-mark" class="w-3 h-3 ml-1" />
              </UBadge>
            </div>
          </div>
        </UCard>

        <!-- Premium Settings -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg">
                <Icon name="i-heroicons-currency-dollar" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">การตั้งค่าพรีเมียม</h2>
            </div>
          </template>
          
          <div class="space-y-4">
            <div class="flex items-center gap-2">
              <UCheckbox v-model="form.is_premium" />
              <label class="text-sm text-gray-700 dark:text-gray-300">นิยายพรีเมียม</label>
            </div>
            
            <div v-if="form.is_premium">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ราคาต่อตอน (บาท)
              </label>
              <UInput
                v-model="form.price_per_chapter"
                type="number"
                min="0"
                step="0.01"
                placeholder="0.00"
                class="w-full"
              />
            </div>
          </div>
        </UCard>

        <!-- Tags -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg">
                <Icon name="i-heroicons-tag" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">แท็ก</h2>
            </div>
          </template>
          
          <div class="space-y-4">
            <div class="flex gap-2">
              <UInput
                ref="tagInput"
                placeholder="เพิ่มแท็ก"
                class="flex-1"
                @keyup.enter="handleAddTag"
              />
              <UButton
                @click="handleAddTag"
                variant="outline"
                size="sm"
              >
                เพิ่ม
              </UButton>
            </div>
            
            <div v-if="form.tags.length > 0" class="flex flex-wrap gap-2">
              <UBadge
                v-for="tag in form.tags"
                :key="tag"
                color="primary"
                variant="subtle"
                class="cursor-pointer hover:bg-purple-100 dark:hover:bg-purple-900/20"
                @click="removeTag(tag)"
              >
                {{ tag }}
                <Icon name="i-heroicons-x-mark" class="w-3 h-3 ml-1" />
              </UBadge>
            </div>
          </div>
        </UCard>

        <!-- Cover Image -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg">
                <Icon name="i-heroicons-photo" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รูปปก</h2>
            </div>
          </template>
          
          <div class="space-y-4">
            <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
              <Icon name="i-heroicons-photo" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-600 dark:text-gray-400 mb-2">ลากและวางรูปภาพที่นี่</p>
              <UButton variant="outline" size="sm">
                เลือกรูปภาพ
              </UButton>
            </div>
          </div>
        </UCard>

        <!-- SEO -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg">
                <Icon name="i-heroicons-magnifying-glass" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">SEO</h2>
            </div>
          </template>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SEO Title
              </label>
              <UInput
                v-model="form.seo.title"
                placeholder="SEO Title"
                class="w-full"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SEO Description
              </label>
              <UTextarea
                v-model="form.seo.description"
                placeholder="SEO Description"
                :rows="3"
                class="w-full"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SEO Keywords
              </label>
              <UInput
                v-model="form.seo.keywords"
                placeholder="SEO Keywords"
                class="w-full"
              />
            </div>
          </div>
        </UCard>

        <!-- Quick Actions -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg">
                <Icon name="i-heroicons-bolt" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">การดำเนินการด่วน</h2>
            </div>
          </template>
          
          <div class="space-y-3">
            <UButton
              variant="outline"
              size="sm"
              class="w-full justify-start"
            >
              <Icon name="i-heroicons-eye" class="w-4 h-4 mr-2" />
              ดูตัวอย่าง
            </UButton>
            
            <UButton
              variant="outline"
              size="sm"
              class="w-full justify-start"
            >
              <Icon name="i-heroicons-document-duplicate" class="w-4 h-4 mr-2" />
              คัดลอกจากนิยายอื่น
            </UButton>
            
            <UButton
              variant="outline"
              size="sm"
              class="w-full justify-start"
            >
              <Icon name="i-heroicons-chart-bar" class="w-4 h-4 mr-2" />
              ดูสถิตินิยาย
            </UButton>
          </div>
        </UCard>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
</style> 