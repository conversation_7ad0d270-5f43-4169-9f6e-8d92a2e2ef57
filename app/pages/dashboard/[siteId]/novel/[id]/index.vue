<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);
const novelId = computed(() => route.params.id as string);

// Use composables
const { $apiFetch } = useNuxtApp();
const { getNovel, deleteNovel } = useNovel();

// Reactive data
const novel = ref<any>(null);
const loading = ref(false);
const error = ref<string | null>(null);
const showDeleteModal = ref(false);

// Analytics data
const analytics = ref({
  views: 0,
  likes: 0,
  shares: 0,
  comments: 0,
  readingTime: 0,
  wordCount: 0,
});

// Fetch novel data
const fetchNovel = async () => {
  loading.value = true;
  error.value = null;
  try {
    const response = await getNovel(siteId.value, novelId.value);
    if (response?.success && response?.data) {
      novel.value = response.data;
      analytics.value = {
        views: novel.value.views || 0,
        likes: novel.value.likes || 0,
        shares: novel.value.shares || 0,
        comments: novel.value.comments || 0,
        readingTime: Math.ceil((novel.value.content?.length || 0) / 200),
        wordCount: novel.value.content?.split(' ').length || 0,
      };
    }
  } catch (err) {
    error.value = 'เกิดข้อผิดพลาดในการโหลดข้อมูล';
    console.error('Error fetching novel:', err);
  } finally {
    loading.value = false;
  }
};

// Format number
const formatNumber = (num: number) => {
  return new Intl.NumberFormat('th-TH').format(num);
};

// Get status badge class
const getStatusBadgeClass = (status: string) => {
  const classes = {
    published: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    draft: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    scheduled: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    archived: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
  };
  return classes[status as keyof typeof classes] || classes.draft;
};

// Get status label
const getStatusLabel = (status: string) => {
  const labels = {
    published: 'เผยแพร่แล้ว',
    draft: 'ร่าง',
    scheduled: 'ตั้งเวลา',
    archived: 'เก็บถาวร',
  };
  return labels[status as keyof typeof labels] || status;
};

// Handle edit
const handleEdit = () => {
  navigateTo(`/dashboard/${siteId.value}/novel/${novelId.value}/edit`);
};

// Handle delete
const handleDelete = async () => {
  try {
    await deleteNovel(siteId.value, novelId.value);
    await navigateTo(`/dashboard/${siteId.value}/novel`);
  } catch (err) {
    console.error('Error deleting novel:', err);
  }
};

// Handle preview
const handlePreview = () => {
  // Open in new tab
  window.open(`/novel/${novelId.value}`, '_blank');
};

// Handle share
const handleShare = () => {
  if (navigator.share) {
    navigator.share({
      title: novel.value?.title,
      text: novel.value?.excerpt,
      url: window.location.href,
    });
  } else {
    // Fallback to clipboard
    navigator.clipboard.writeText(window.location.href);
  }
};

// Fetch novel on mount
onMounted(async () => {
  await fetchNovel();
});

// SEO
useSeoMeta({
  title: computed(() =>
    novel.value?.title ? `${novel.value.title} - ระบบจัดการร้านค้า` : 'รายละเอียดนิยาย'
  ),
  description: computed(() => novel.value?.excerpt || 'รายละเอียดนิยาย'),
  keywords: computed(() => novel.value?.seo?.keywords || 'นิยาย, เรื่องสั้น'),
  ogTitle: computed(() => novel.value?.title || 'รายละเอียดนิยาย'),
  ogDescription: computed(() => novel.value?.excerpt || 'รายละเอียดนิยาย'),
  ogType: 'article',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          รายละเอียดนิยาย
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          ดูรายละเอียดและสถิติของนิยาย
        </p>
      </div>
      <div class="flex items-center gap-3">
        <UButton
          @click="handlePreview"
          variant="outline"
          size="sm"
          class="hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <Icon name="i-heroicons-eye" class="w-4 h-4 mr-2" />
          ดูตัวอย่าง
        </UButton>
        <UButton
          @click="handleEdit"
          variant="outline"
          size="sm"
          class="hover:bg-purple-50 dark:hover:bg-purple-900/20 text-purple-600 dark:text-purple-400"
        >
          <Icon name="i-heroicons-pencil-square" class="w-4 h-4 mr-2" />
          แก้ไข
        </UButton>
        <UButton
          @click="showDeleteModal = true"
          variant="outline"
          size="sm"
          class="hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400"
        >
          <Icon name="i-heroicons-trash" class="w-4 h-4 mr-2" />
          ลบ
        </UButton>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="flex items-center gap-3">
        <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-purple-600" />
        <span class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</span>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-12">
      <div class="text-red-600 dark:text-red-400 mb-4">
        <Icon name="i-heroicons-exclamation-triangle" class="w-12 h-12 mx-auto" />
      </div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">เกิดข้อผิดพลาด</h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">{{ error }}</p>
      <UButton @click="fetchNovel()" variant="outline">
        ลองใหม่
      </UButton>
    </div>

    <!-- Novel Content -->
    <div v-else-if="novel" class="space-y-8">
      <!-- Novel Header -->
      <UCard class="overflow-hidden">
        <div class="relative">
          <!-- Featured Image -->
          <div v-if="novel.featured_image" class="h-64 bg-gradient-to-br from-purple-500 to-pink-600 rounded-t-xl overflow-hidden">
            <img 
              :src="novel.featured_image" 
              :alt="novel.title"
              class="w-full h-full object-cover"
            />
            <div class="absolute inset-0 bg-black/20"></div>
          </div>
          <div v-else class="h-64 bg-gradient-to-br from-purple-500 to-pink-600 rounded-t-xl flex items-center justify-center">
            <Icon name="i-heroicons-book-open" class="w-16 h-16 text-white/50" />
          </div>

          <!-- Status Badge -->
          <div class="absolute top-4 right-4">
            <span 
              class="px-3 py-1 text-sm rounded-full font-medium text-white backdrop-blur-sm"
              :class="getStatusBadgeClass(novel.status)"
            >
              {{ getStatusLabel(novel.status) }}
            </span>
          </div>

          <!-- Category Badge -->
          <div class="absolute bottom-4 left-4">
            <span class="px-3 py-1 text-sm bg-white/90 dark:bg-gray-800/90 text-gray-700 dark:text-gray-300 rounded-full">
              {{ novel.category }}
            </span>
          </div>
        </div>

        <div class="p-6">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            {{ novel.title }}
          </h1>
          
          <div class="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-6">
            <span class="flex items-center gap-1">
              <Icon name="i-heroicons-user" class="w-4 h-4" />
              {{ novel.author }}
            </span>
            <span class="flex items-center gap-1">
              <Icon name="i-heroicons-calendar" class="w-4 h-4" />
              {{ new Date(novel.created_at).toLocaleDateString('th-TH') }}
            </span>
            <span class="flex items-center gap-1">
              <Icon name="i-heroicons-clock" class="w-4 h-4" />
              {{ analytics.readingTime }} นาที
            </span>
            <span class="flex items-center gap-1">
              <Icon name="i-heroicons-list-number" class="w-4 h-4" />
              ตอนที่ {{ novel.chapter_number }}
            </span>
          </div>

          <p class="text-lg text-gray-700 dark:text-gray-300 leading-relaxed mb-6">
            {{ novel.excerpt }}
          </p>

          <!-- Chapter Info -->
          <div class="flex items-center gap-4 mb-6">
            <div class="flex items-center gap-2">
              <span class="text-sm text-gray-600 dark:text-gray-400">ตอนที่:</span>
              <span class="font-semibold text-gray-900 dark:text-white">{{ novel.chapter_number }}</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-sm text-gray-600 dark:text-gray-400">ทั้งหมด:</span>
              <span class="font-semibold text-gray-900 dark:text-white">{{ novel.total_chapters }}</span>
            </div>
            <div v-if="novel.is_completed" class="flex items-center gap-1">
              <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-600" />
              <span class="text-sm text-green-600">เสร็จสิ้นแล้ว</span>
            </div>
          </div>

          <!-- Rating -->
          <div v-if="novel.rating > 0" class="flex items-center gap-2 mb-6">
            <span class="text-sm text-gray-600 dark:text-gray-400">คะแนน:</span>
            <div class="flex items-center gap-1">
              <Icon 
                v-for="i in 5" 
                :key="i"
                :name="i <= novel.rating ? 'i-heroicons-star-solid' : 'i-heroicons-star'"
                class="w-4 h-4 text-yellow-500"
              />
            </div>
            <span class="text-sm text-gray-600 dark:text-gray-400">({{ novel.rating }}/5)</span>
          </div>

          <!-- Tags -->
          <div v-if="novel.tags && novel.tags.length > 0" class="flex flex-wrap gap-2 mb-6">
            <UBadge
              v-for="tag in novel.tags"
              :key="tag"
              color="primary"
              variant="subtle"
            >
              {{ tag }}
            </UBadge>
          </div>

          <!-- Actions -->
          <div class="flex items-center gap-3">
            <UButton
              @click="handleShare"
              variant="outline"
              size="sm"
            >
              <Icon name="i-heroicons-share" class="w-4 h-4 mr-2" />
              แชร์
            </UButton>
            <UButton
              @click="handleEdit"
              variant="outline"
              size="sm"
            >
              <Icon name="i-heroicons-pencil-square" class="w-4 h-4 mr-2" />
              แก้ไข
            </UButton>
          </div>
        </div>
      </UCard>

      <!-- Analytics -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ผู้เข้าชม</p>
              <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(analytics.views) }}</p>
            </div>
            <div class="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl text-white shadow-lg">
              <Icon name="i-heroicons-eye" class="w-8 h-8" />
            </div>
          </div>
        </UCard>

        <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ไลค์</p>
              <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(analytics.likes) }}</p>
            </div>
            <div class="p-4 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl text-white shadow-lg">
              <Icon name="i-heroicons-heart" class="w-8 h-8" />
            </div>
          </div>
        </UCard>

        <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">แชร์</p>
              <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(analytics.shares) }}</p>
            </div>
            <div class="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl text-white shadow-lg">
              <Icon name="i-heroicons-share" class="w-8 h-8" />
            </div>
          </div>
        </UCard>

        <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ความคิดเห็น</p>
              <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(analytics.comments) }}</p>
            </div>
            <div class="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white shadow-lg">
              <Icon name="i-heroicons-chat-bubble-left" class="w-8 h-8" />
            </div>
          </div>
        </UCard>
      </div>

      <!-- Content -->
      <UCard class="overflow-hidden">
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
              <Icon name="i-heroicons-book-open" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">เนื้อหา</h2>
          </div>
        </template>
        
        <div class="prose prose-lg max-w-none dark:prose-invert">
          <div v-html="novel.content" class="text-gray-700 dark:text-gray-300 leading-relaxed"></div>
        </div>
      </UCard>

      <!-- SEO Information -->
      <UCard class="overflow-hidden">
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg">
              <Icon name="i-heroicons-magnifying-glass" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ข้อมูล SEO</h2>
          </div>
        </template>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              SEO Title
            </label>
            <p class="text-gray-900 dark:text-white">{{ novel.seo?.title || novel.title }}</p>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              SEO Description
            </label>
            <p class="text-gray-900 dark:text-white">{{ novel.seo?.description || novel.excerpt }}</p>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              SEO Keywords
            </label>
            <p class="text-gray-900 dark:text-white">{{ novel.seo?.keywords || '-' }}</p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Delete Modal -->
    <UModal v-model="showDeleteModal">
      <UCard>
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-red-100 rounded-lg">
              <Icon name="i-heroicons-exclamation-triangle" class="w-5 h-5 text-red-600" />
            </div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">ยืนยันการลบ</h3>
          </div>
        </template>
        
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          คุณต้องการลบนิยาย "{{ novel?.title }}" ใช่หรือไม่? การดำเนินการนี้ไม่สามารถยกเลิกได้
        </p>
        
        <template #footer>
          <div class="flex items-center gap-3">
            <UButton
              @click="showDeleteModal = false"
              variant="outline"
            >
              ยกเลิก
            </UButton>
            <UButton
              @click="handleDelete"
              color="red"
            >
              ลบ
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
</style> 