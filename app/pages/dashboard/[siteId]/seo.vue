
<script setup lang="ts"> 

// Route and utilities
const route = useRoute()
const toast = useToast()

// Get siteId from route
// const siteId = computed(() => route.params.siteId as string)

// Loading states
const isSaving = ref(false)
const isAnalyzing = ref(false)
const isGeneratingSitemap = ref(false)
const isCheckingMobile = ref(false)
const isCheckingSpeed = ref(false)
const isSubmittingSitemap = ref(false)

// Active tab
const activeTab = ref(0)

// Tab items
const tabItems = [
  { key: 'meta-tags', label: 'Meta Tags', icon: 'i-heroicons-tag' },
  { key: 'sitemap', label: 'Sitemap', icon: 'i-heroicons-map' },
  { key: 'robots', label: 'Robots.txt', icon: 'i-heroicons-shield-check' },
  { key: 'analytics', label: 'Analytics', icon: 'i-heroicons-chart-bar' },
  { key: 'tools', label: 'เครื่องมือ', icon: 'i-heroicons-wrench-screwdriver' }
]

// SEO Score
const seoScore = ref({
  overall: 85,
  performance: 78,
  accessibility: 92,
  bestPractices: 88
})

// Meta Tags
const metaTags = ref({
  title: '',
  description: '',
  keywords: '',
  author: '',
  canonical: ''
})

// Open Graph
const openGraph = ref({
  title: '',
  description: '',
  image: '',
  type: 'website'
})

// Twitter Card
const twitterCard = ref({
  cardType: 'summary_large_image',
  title: '',
  description: '',
  image: '',
  site: ''
})

// Sitemap
const sitemapUrl = ref('')
const sitemapStatus = ref('active')
const lastSitemapUpdate = ref('2024-01-15 14:30:00')
const sitemapSettings = ref({
  includePages: true,
  includeProducts: true,
  includeBlog: true,
  includeCategories: true,
  changeFreq: 'weekly'
})

// Robots.txt
const robotsContent = ref('')
const robotsSettings = ref({
  blockAdmin: true,
  blockPrivate: true,
  blockTemp: true,
  blockBackup: true,
  allowGoogle: true,
  allowBing: true,
  allowYandex: false,
  allowBaidu: false
})

// Analytics
const analytics = ref({
  googleAnalyticsId: '',
  gtmId: '',
  enableEcommerce: false,
  anonymizeIP: true,
  searchConsoleCode: '',
  propertyUrl: '',
  facebookPixelId: '',
  enableFacebookConversions: false
})

// SEO Recommendations
const seoRecommendations = ref([
  {
    id: 1,
    type: 'warning',
    title: 'Meta Description สั้นเกินไป',
    description: 'Meta Description ควรมีความยาว 150-160 ตัวอักษร เพื่อแสดงผลที่ดีใน Google'
  },
  {
    id: 2,
    type: 'info',
    title: 'เพิ่ม Alt Text ให้รูปภาพ',
    description: 'รูปภาพบางรูปยังไม่มี Alt Text ซึ่งจะช่วยในการ SEO และการเข้าถึงของผู้พิการ'
  }
])

// Sitemap Preview
const sitemapPreview = ref([
  {
    loc: 'https://example.com/',
    lastmod: '2024-01-15',
    changefreq: 'weekly',
    priority: '1.0'
  },
  {
    loc: 'https://example.com/products',
    lastmod: '2024-01-14',
    changefreq: 'daily',
    priority: '0.8'
  },
  {
    loc: 'https://example.com/blog',
    lastmod: '2024-01-13',
    changefreq: 'weekly',
    priority: '0.7'
  }
])

// Options
const ogTypeOptions = [
  { label: 'เว็บไซต์', value: 'website' },
  { label: 'บทความ', value: 'article' },
  { label: 'สินค้า', value: 'product' },
  { label: 'วิดีโอ', value: 'video' }
]

const twitterCardOptions = [
  { label: 'Summary', value: 'summary' },
  { label: 'Summary Large Image', value: 'summary_large_image' },
  { label: 'App', value: 'app' },
  { label: 'Player', value: 'player' }
]

const changeFreqOptions = [
  { label: 'ทุกวัน', value: 'daily' },
  { label: 'ทุกสัปดาห์', value: 'weekly' },
  { label: 'ทุกเดือน', value: 'monthly' },
  { label: 'ทุกปี', value: 'yearly' }
]

// Methods
const saveSEOSettings = async () => {
  isSaving.value = true
  try {
    // API call to save SEO settings
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    toast.add({
      title: 'บันทึกสำเร็จ',
      description: 'การตั้งค่า SEO ได้รับการบันทึกแล้ว',
      icon: 'i-heroicons-check-circle',
      color: 'success'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถบันทึกการตั้งค่า SEO ได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  } finally {
    isSaving.value = false
  }
}

const analyzeSEO = async () => {
  isAnalyzing.value = true
  try {
    // API call to analyze SEO
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Update SEO score
    seoScore.value = {
      overall: Math.floor(Math.random() * 20) + 80,
      performance: Math.floor(Math.random() * 20) + 70,
      accessibility: Math.floor(Math.random() * 20) + 85,
      bestPractices: Math.floor(Math.random() * 20) + 80
    }
    
    toast.add({
      title: 'วิเคราะห์เสร็จสิ้น',
      description: 'ผลการวิเคราะห์ SEO ได้รับการอัพเดทแล้ว',
      icon: 'i-heroicons-chart-bar',
      color: 'info'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถวิเคราะห์ SEO ได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  } finally {
    isAnalyzing.value = false
  }
}

const generateSitemap = async () => {
  isGeneratingSitemap.value = true
  try {
    // API call to generate sitemap
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    lastSitemapUpdate.value = new Date().toLocaleString('th-TH')
    
    toast.add({
      title: 'สร้าง Sitemap สำเร็จ',
      description: 'Sitemap ได้รับการสร้างและอัพเดทแล้ว',
      icon: 'i-heroicons-map',
      color: 'success'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถสร้าง Sitemap ได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  } finally {
    isGeneratingSitemap.value = false
  }
}

const copySitemapUrl = async () => {
  try {
    await navigator.clipboard.writeText(sitemapUrl.value)
    toast.add({
      title: 'คัดลอกแล้ว',
      description: 'URL ของ Sitemap ถูกคัดลอกไปยังคลิปบอร์ด',
      icon: 'i-heroicons-clipboard',
      color: 'info'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถคัดลอก URL ได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  }
}

const uploadOGImage = () => {
  // Implement image upload logic
  toast.add({
    title: 'ฟีเจอร์ยังไม่พร้อม',
    description: 'การอัพโหลดรูปภาพจะเปิดใช้งานในเร็วๆ นี้',
    icon: 'i-heroicons-photo',
    color: 'warning'
  })
}

const useDefaultRobots = () => {
  robotsContent.value = `User-agent: *
Disallow: /admin/
Disallow: /private/
Disallow: /temp/
Disallow: /backup/
Allow: /

Sitemap: ${sitemapUrl.value}`
}

const validateRobots = () => {
  toast.add({
    title: 'Robots.txt ถูกต้อง',
    description: 'ไฟล์ Robots.txt มีรูปแบบที่ถูกต้อง',
    icon: 'i-heroicons-shield-check',
    color: 'success'
  })
}

const previewRobots = () => {
  // Open robots.txt preview in new tab
  const robotsBlob = new Blob([robotsContent.value], { type: 'text/plain' })
  const url = URL.createObjectURL(robotsBlob)
  window.open(url, '_blank')
}

const checkMobileFriendly = async () => {
  isCheckingMobile.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    toast.add({
      title: 'ตรวจสอบเสร็จสิ้น',
      description: 'เว็บไซต์เหมาะสมกับการใช้งานบนมือถือ',
      icon: 'i-heroicons-device-phone-mobile',
      color: 'success'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถตรวจสอบ Mobile-Friendly ได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  } finally {
    isCheckingMobile.value = false
  }
}

const checkPageSpeed = async () => {
  isCheckingSpeed.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 3000))
    toast.add({
      title: 'ตรวจสอบเสร็จสิ้น',
      description: 'ความเร็วในการโหลดหน้าเว็บอยู่ในเกณฑ์ดี',
      icon: 'i-heroicons-bolt',
      color: 'success'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถตรวจสอบความเร็วได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  } finally {
    isCheckingSpeed.value = false
  }
}

const submitSitemap = async () => {
  isSubmittingSitemap.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1500))
    toast.add({
      title: 'ส่ง Sitemap สำเร็จ',
      description: 'Sitemap ได้รับการส่งไปยัง Google Search Console แล้ว',
      icon: 'i-heroicons-paper-airplane',
      color: 'success'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถส่ง Sitemap ได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  } finally {
    isSubmittingSitemap.value = false
  }
}

// Initialize data
onMounted(async () => {
  // Load SEO settings
  sitemapUrl.value = `https://example.com/sitemap.xml`
  
  // Load default robots.txt
  useDefaultRobots()
})

// Page meta
definePageMeta({
  layout: 'dashboard',
  middleware: 'auth'
})
</script> 
<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">การจัดการ SEO</h1>
        <p class="text-gray-600 dark:text-gray-400">จัดการการตั้งค่า SEO และการเพิ่มประสิทธิภาพเว็บไซต์</p>
      </div>
      <div class="flex items-center gap-3">
        <UButton
          variant="outline"
          icon="i-heroicons-arrow-path"
          :loading="isAnalyzing"
          @click="analyzeSEO"
        >
          วิเคราะห์ SEO
        </UButton>
        <UButton
          icon="i-heroicons-check"
          :loading="isSaving"
          @click="saveSEOSettings"
        >
          บันทึก
        </UButton>
      </div>
    </div>

    <!-- SEO Score Card -->
    <UCard>
      <template #header>
        <div class="flex items-center gap-2">
          <Icon name="solar:chart-line-duotone" class="w-5 h-5 text-primary" />
          <h3 class="text-lg font-semibold">คะแนน SEO</h3>
        </div>
      </template>

      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div class="text-3xl font-bold text-green-600 dark:text-green-400">{{ seoScore.overall }}</div>
          <div class="text-sm text-green-600 dark:text-green-400">คะแนนรวม</div>
        </div>
        <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ seoScore.performance }}</div>
          <div class="text-sm text-blue-600 dark:text-blue-400">ประสิทธิภาพ</div>
        </div>
        <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div class="text-3xl font-bold text-purple-600 dark:text-purple-400">{{ seoScore.accessibility }}</div>
          <div class="text-sm text-purple-600 dark:text-purple-400">การเข้าถึง</div>
        </div>
        <div class="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
          <div class="text-3xl font-bold text-orange-600 dark:text-orange-400">{{ seoScore.bestPractices }}</div>
          <div class="text-sm text-orange-600 dark:text-orange-400">แนวทางปฏิบัติ</div>
        </div>
      </div>
    </UCard>

    <!-- Tabs -->
    <UTabs :items="tabItems" v-model="activeTab">
      <!-- Meta Tags Tab -->
      <template #meta-tags>
        <div class="space-y-6">
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">Meta Tags พื้นฐาน</h3>
            </template>

            <div class="space-y-4">
              <UFormField label="Title Tag" required>
                <UInput
                  v-model="metaTags.title"
                  placeholder="ชื่อเว็บไซต์ที่จะแสดงใน Google"
                  :maxlength="60"
                />
                <template #help>
                  <div class="flex justify-between text-sm">
                    <span>ควรมีความยาว 50-60 ตัวอักษร</span>
                    <span :class="metaTags.title.length > 60 ? 'text-red-500' : 'text-gray-500'">
                      {{ metaTags.title.length }}/60
                    </span>
                  </div>
                </template>
              </UFormField>

              <UFormField label="Meta Description" required>
                <UTextarea
                  v-model="metaTags.description"
                  placeholder="คำอธิบายเว็บไซต์ที่จะแสดงใน Google"
                  :maxlength="160"
                  :rows="3"
                />
                <template #help>
                  <div class="flex justify-between text-sm">
                    <span>ควรมีความยาว 150-160 ตัวอักษร</span>
                    <span :class="metaTags.description.length > 160 ? 'text-red-500' : 'text-gray-500'">
                      {{ metaTags.description.length }}/160
                    </span>
                  </div>
                </template>
              </UFormField>

              <UFormField label="Keywords">
                <UInput
                  v-model="metaTags.keywords"
                  placeholder="คำค้นหาหลัก, คำค้นหารอง, ..."
                />
                <template #help>
                  <span class="text-sm text-gray-500">แยกคำค้นหาด้วยเครื่องหมายจุลภาค</span>
                </template>
              </UFormField>

              <UFormField label="Author">
                <UInput
                  v-model="metaTags.author"
                  placeholder="ชื่อผู้เขียนหรือเจ้าของเว็บไซต์"
                />
              </UFormField>

              <UFormField label="Canonical URL">
                <UInput
                  v-model="metaTags.canonical"
                  placeholder="https://example.com"
                />
                <template #help>
                  <span class="text-sm text-gray-500">URL หลักของเว็บไซต์</span>
                </template>
              </UFormField>
            </div>
          </UCard>

          <!-- Open Graph -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">Open Graph (Facebook)</h3>
            </template>

            <div class="space-y-4">
              <UFormField label="OG Title">
                <UInput
                  v-model="openGraph.title"
                  placeholder="ชื่อที่จะแสดงเมื่อแชร์ใน Facebook"
                />
              </UFormField>

              <UFormField label="OG Description">
                <UTextarea
                  v-model="openGraph.description"
                  placeholder="คำอธิบายที่จะแสดงเมื่อแชร์ใน Facebook"
                  :rows="3"
                />
              </UFormField>

              <UFormField label="OG Image">
                <div class="space-y-2">
                  <UInput
                    v-model="openGraph.image"
                    placeholder="https://example.com/image.jpg"
                  />
                  <div class="flex items-center gap-2">
                    <UButton variant="outline" size="sm" @click="uploadOGImage">
                      อัพโหลดรูปภาพ
                    </UButton>
                    <span class="text-sm text-gray-500">ขนาดแนะนำ: 1200x630px</span>
                  </div>
                </div>
              </UFormField>

              <UFormField label="OG Type">
                <USelect
                  v-model="openGraph.type"
                  :items="ogTypeOptions"
                  placeholder="เลือกประเภทเนื้อหา"
                />
              </UFormField>
            </div>
          </UCard>

          <!-- Twitter Card -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">Twitter Card</h3>
            </template>

            <div class="space-y-4">
              <UFormField label="Twitter Card Type">
                <USelect
                  v-model="twitterCard.cardType"
                  :items="twitterCardOptions"
                  placeholder="เลือกประเภท Twitter Card"
                />
              </UFormField>

              <UFormField label="Twitter Title">
                <UInput
                  v-model="twitterCard.title"
                  placeholder="ชื่อที่จะแสดงใน Twitter"
                />
              </UFormField>

              <UFormField label="Twitter Description">
                <UTextarea
                  v-model="twitterCard.description"
                  placeholder="คำอธิบายที่จะแสดงใน Twitter"
                  :rows="3"
                />
              </UFormField>

              <UFormField label="Twitter Image">
                <UInput
                  v-model="twitterCard.image"
                  placeholder="https://example.com/image.jpg"
                />
              </UFormField>

              <UFormField label="Twitter Site">
                <UInput
                  v-model="twitterCard.site"
                  placeholder="@username"
                />
              </UFormField>
            </div>
          </UCard>
        </div>
      </template>

      <!-- Sitemap Tab -->
      <template #sitemap>
        <div class="space-y-6">
          <UCard>
            <template #header>
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold">Sitemap</h3>
                <UButton
                  variant="outline"
                  icon="i-heroicons-arrow-path"
                  :loading="isGeneratingSitemap"
                  @click="generateSitemap"
                >
                  สร้าง Sitemap
                </UButton>
              </div>
            </template>

            <div class="space-y-4">
              <UFormField label="Sitemap URL">
                <div class="flex items-center gap-2">
                  <UInput
                    v-model="sitemapUrl"
                    readonly
                    class="flex-1"
                  />
                  <UButton
                    variant="outline"
                    icon="i-heroicons-clipboard"
                    @click="copySitemapUrl"
                  >
                    คัดลอก
                  </UButton>
                </div>
              </UFormField>

              <UFormField label="สถานะ Sitemap">
                <div class="flex items-center gap-2">
                  <UBadge
                    :color="sitemapStatus === 'active' ? 'green' : 'red'"
                    :label="sitemapStatus === 'active' ? 'ใช้งานได้' : 'ไม่พร้อมใช้งาน'"
                  />
                  <span class="text-sm text-gray-500">
                    อัพเดทล่าสุด: {{ lastSitemapUpdate }}
                  </span>
                </div>
              </UFormField>

              <UFormField label="รวมหน้าเพจ">
                <div class="space-y-2">
                  <UCheckbox v-model="sitemapSettings.includePages" label="หน้าเพจทั้งหมด" />
                  <UCheckbox v-model="sitemapSettings.includeProducts" label="หน้าสินค้า" />
                  <UCheckbox v-model="sitemapSettings.includeBlog" label="บล็อกโพสต์" />
                  <UCheckbox v-model="sitemapSettings.includeCategories" label="หมวดหมู่" />
                </div>
              </UFormField>

              <UFormField label="ความถี่ในการอัพเดท">
                <USelect
                  v-model="sitemapSettings.changeFreq"
                  :items="changeFreqOptions"
                  placeholder="เลือกความถี่"
                />
              </UFormField>
            </div>
          </UCard>

          <!-- Sitemap Preview -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">ตัวอย่าง Sitemap</h3>
            </template>

            <div class="space-y-2 max-h-96 overflow-y-auto">
              <div
                v-for="url in sitemapPreview"
                :key="url.loc"
                class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
              >
                <div class="flex-1">
                  <div class="font-medium">{{ url.loc }}</div>
                  <div class="text-sm text-gray-500">
                    อัพเดท: {{ url.lastmod }} | ความสำคัญ: {{ url.priority }}
                  </div>
                </div>
                <UBadge :label="url.changefreq" variant="soft" />
              </div>
            </div>
          </UCard>
        </div>
      </template>

      <!-- Robots.txt Tab -->
      <template #robots>
        <div class="space-y-6">
          <UCard>
            <template #header>
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold">Robots.txt</h3>
                <UButton
                  variant="outline"
                  icon="i-heroicons-eye"
                  @click="previewRobots"
                >
                  ดูตัวอย่าง
                </UButton>
              </div>
            </template>

            <div class="space-y-4">
              <UFormField label="เนื้อหา Robots.txt">
                <UTextarea
                  v-model="robotsContent"
                  :rows="10"
                  placeholder="User-agent: *&#10;Disallow: /admin/&#10;Sitemap: https://example.com/sitemap.xml"
                />
              </UFormField>

              <div class="flex items-center gap-2">
                <UButton @click="useDefaultRobots">ใช้ค่าเริ่มต้น</UButton>
                <UButton variant="outline" @click="validateRobots">ตรวจสอบ</UButton>
              </div>
            </div>
          </UCard>

          <!-- Quick Settings -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">การตั้งค่าด่วน</h3>
            </template>

            <div class="space-y-4">
              <UFormField label="ปิดกั้นโฟลเดอร์">
                <div class="space-y-2">
                  <UCheckbox v-model="robotsSettings.blockAdmin" label="ปิดกั้น /admin/" />
                  <UCheckbox v-model="robotsSettings.blockPrivate" label="ปิดกั้น /private/" />
                  <UCheckbox v-model="robotsSettings.blockTemp" label="ปิดกั้น /temp/" />
                  <UCheckbox v-model="robotsSettings.blockBackup" label="ปิดกั้น /backup/" />
                </div>
              </UFormField>

              <UFormField label="อนุญาตให้ Bot">
                <div class="space-y-2">
                  <UCheckbox v-model="robotsSettings.allowGoogle" label="Google Bot" />
                  <UCheckbox v-model="robotsSettings.allowBing" label="Bing Bot" />
                  <UCheckbox v-model="robotsSettings.allowYandex" label="Yandex Bot" />
                  <UCheckbox v-model="robotsSettings.allowBaidu" label="Baidu Bot" />
                </div>
              </UFormField>
            </div>
          </UCard>
        </div>
      </template>

      <!-- Analytics Tab -->
      <template #analytics>
        <div class="space-y-6">
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">Google Analytics</h3>
            </template>

            <div class="space-y-4">
              <UFormField label="Tracking ID">
                <UInput
                  v-model="analytics.googleAnalyticsId"
                  placeholder="G-XXXXXXXXXX"
                />
              </UFormField>

              <UFormField label="Google Tag Manager ID">
                <UInput
                  v-model="analytics.gtmId"
                  placeholder="GTM-XXXXXXX"
                />
              </UFormField>

              <UCheckbox
                v-model="analytics.enableEcommerce"
                label="เปิดใช้งาน Enhanced Ecommerce"
              />

              <UCheckbox
                v-model="analytics.anonymizeIP"
                label="ซ่อน IP Address"
              />
            </div>
          </UCard>

          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">Google Search Console</h3>
            </template>

            <div class="space-y-4">
              <UFormField label="Verification Code">
                <UInput
                  v-model="analytics.searchConsoleCode"
                  placeholder="google-site-verification=..."
                />
              </UFormField>

              <UFormField label="Property URL">
                <UInput
                  v-model="analytics.propertyUrl"
                  placeholder="https://example.com"
                />
              </UFormField>
            </div>
          </UCard>

          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">Facebook Pixel</h3>
            </template>

            <div class="space-y-4">
              <UFormField label="Pixel ID">
                <UInput
                  v-model="analytics.facebookPixelId"
                  placeholder="123456789012345"
                />
              </UFormField>

              <UCheckbox
                v-model="analytics.enableFacebookConversions"
                label="เปิดใช้งาน Conversion Tracking"
              />
            </div>
          </UCard>
        </div>
      </template>

      <!-- Tools Tab -->
      <template #tools>
        <div class="space-y-6">
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">เครื่องมือ SEO</h3>
            </template>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h4 class="font-medium mb-2">ตรวจสอบ SEO</h4>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  วิเคราะห์ SEO ของเว็บไซต์และแสดงคำแนะนำ
                </p>
                <UButton
                  variant="outline"
                  :loading="isAnalyzing"
                  @click="analyzeSEO"
                >
                  เริ่มวิเคราะห์
                </UButton>
              </div>

              <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h4 class="font-medium mb-2">ตรวจสอบ Mobile-Friendly</h4>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  ตรวจสอบว่าเว็บไซต์เหมาะสมกับมือถือหรือไม่
                </p>
                <UButton
                  variant="outline"
                  :loading="isCheckingMobile"
                  @click="checkMobileFriendly"
                >
                  ตรวจสอบ
                </UButton>
              </div>

              <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h4 class="font-medium mb-2">ตรวจสอบความเร็ว</h4>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  วิเคราะห์ความเร็วในการโหลดหน้าเว็บ
                </p>
                <UButton
                  variant="outline"
                  :loading="isCheckingSpeed"
                  @click="checkPageSpeed"
                >
                  ตรวจสอบ
                </UButton>
              </div>

              <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h4 class="font-medium mb-2">ส่ง Sitemap</h4>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  ส่ง Sitemap ไปยัง Google Search Console
                </p>
                <UButton
                  variant="outline"
                  :loading="isSubmittingSitemap"
                  @click="submitSitemap"
                >
                  ส่ง Sitemap
                </UButton>
              </div>
            </div>
          </UCard>

          <!-- SEO Recommendations -->
          <UCard v-if="seoRecommendations.length > 0">
            <template #header>
              <h3 class="text-lg font-semibold">คำแนะนำ SEO</h3>
            </template>

            <div class="space-y-3">
              <div
                v-for="recommendation in seoRecommendations"
                :key="recommendation.id"
                class="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
              >
                <Icon
                  :name="recommendation.type === 'error' ? 'i-heroicons-x-circle' : 
                         recommendation.type === 'warning' ? 'i-heroicons-exclamation-triangle' : 
                         'i-heroicons-information-circle'"
                  :class="recommendation.type === 'error' ? 'text-red-500' : 
                          recommendation.type === 'warning' ? 'text-yellow-500' : 
                          'text-blue-500'"
                  class="w-5 h-5 mt-0.5"
                />
                <div class="flex-1">
                  <div class="font-medium">{{ recommendation.title }}</div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">
                    {{ recommendation.description }}
                  </div>
                </div>
              </div>
            </div>
          </UCard>
        </div>
      </template>
    </UTabs>
  </div>
</template>
