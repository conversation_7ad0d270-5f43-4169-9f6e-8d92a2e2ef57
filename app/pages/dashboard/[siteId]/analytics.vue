<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// ใช้ SEO meta สำหรับหน้า analytics
const { t } = useI18n();
useDashboardSeo(
  t('analytics.title', 'Analytics'),
  t('analytics.description', 'View detailed analytics and insights for your website')
);

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// Analytics data
const analytics = ref({
  overview: {
    totalVisitors: 15420,
    totalPageViews: 45230,
    totalSessions: 12340,
    bounceRate: 32.5,
    avgSessionDuration: 245,
    conversionRate: 8.2,
  },
  realTime: {
    currentVisitors: 156,
    activePages: 23,
    topReferrers: [
      { source: 'Google', visitors: 45 },
      { source: 'Direct', visitors: 32 },
      { source: 'Facebook', visitors: 28 },
      { source: 'Twitter', visitors: 15 },
    ],
  },
  topPages: [
    { page: '/', views: 12540, visitors: 8920, bounceRate: 28.5 },
    { page: '/products', views: 8920, visitors: 6540, bounceRate: 35.2 },
    { page: '/blog', views: 6540, visitors: 4320, bounceRate: 42.1 },
    { page: '/about', views: 4320, visitors: 3210, bounceRate: 38.7 },
    { page: '/contact', views: 3210, visitors: 2890, bounceRate: 45.3 },
  ],
  referrers: [
    { source: 'Google', visitors: 8920, percentage: 58.2 },
    { source: 'Direct', visitors: 4320, percentage: 28.1 },
    { source: 'Facebook', visitors: 1540, percentage: 10.0 },
    { source: 'Twitter', visitors: 540, percentage: 3.5 },
    { source: 'Other', visitors: 100, percentage: 0.2 },
  ],
  devices: [
    { device: 'Desktop', visitors: 8920, percentage: 58.2 },
    { device: 'Mobile', visitors: 5640, percentage: 36.8 },
    { device: 'Tablet', visitors: 860, percentage: 5.0 },
  ],
  browsers: [
    { browser: 'Chrome', visitors: 8920, percentage: 58.2 },
    { browser: 'Safari', visitors: 4320, percentage: 28.1 },
    { browser: 'Firefox', visitors: 1540, percentage: 10.0 },
    { browser: 'Edge', visitors: 540, percentage: 3.5 },
    { browser: 'Other', visitors: 100, percentage: 0.2 },
  ],
  countries: [
    { country: 'Thailand', visitors: 8920, percentage: 58.2 },
    { country: 'United States', visitors: 4320, percentage: 28.1 },
    { country: 'Singapore', visitors: 1540, percentage: 10.0 },
    { country: 'Malaysia', visitors: 540, percentage: 3.5 },
    { country: 'Other', visitors: 100, percentage: 0.2 },
  ],
});

// Format number
const formatNumber = (num: number) => {
  return new Intl.NumberFormat('th-TH').format(num);
};

// Format percentage
const formatPercentage = (num: number) => {
  return num.toFixed(1);
};

// Format duration
const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// Get color for progress bars
const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return 'from-green-500 to-green-600';
  if (percentage >= 60) return 'from-blue-500 to-blue-600';
  if (percentage >= 40) return 'from-yellow-500 to-yellow-600';
  return 'from-red-500 to-red-600';
};

// Get icon for device/browser
const getDeviceIcon = (device: string) => {
  const icons = {
    Desktop: 'i-heroicons-computer-desktop',
    Mobile: 'i-heroicons-device-phone-mobile',
    Tablet: 'i-heroicons-device-tablet',
    Chrome: 'i-simple-icons-googlechrome',
    Safari: 'i-simple-icons-safari',
    Firefox: 'i-simple-icons-firefox',
    Edge: 'i-simple-icons-microsoftedge',
  };
  return icons[device as keyof typeof icons] || 'i-heroicons-question-mark-circle';
};

// Get country flag emoji
const getCountryFlag = (country: string) => {
  const flags = {
    Thailand: '🇹🇭',
    'United States': '🇺🇸',
    Singapore: '🇸🇬',
    Malaysia: '🇲🇾',
  };
  return flags[country as keyof typeof flags] || '🌍';
};

// Chart data for demo
const chartData = ref({
  visitors: {
    labels: ['จันทร์', 'อังคาร', 'พุธ', 'พฤหัสบดี', 'ศุกร์', 'เสาร์', 'อาทิตย์'],
    datasets: [
      {
        label: 'ผู้เข้าชม',
        data: [1200, 1900, 1500, 2500, 2200, 3000, 2800],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
      },
    ],
  },
  pageViews: {
    labels: ['จันทร์', 'อังคาร', 'พุธ', 'พฤหัสบดี', 'ศุกร์', 'เสาร์', 'อาทิตย์'],
    datasets: [
      {
        label: 'หน้าเว็บ',
        data: [3500, 4200, 3800, 5200, 4800, 6500, 5800],
        borderColor: 'rgb(16, 185, 129)',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.4,
      },
    ],
  },
});
</script>

<template>
  <div class="space-y-8">
    <!-- Enhanced Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          วิเคราะห์ข้อมูล
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          ติดตามประสิทธิภาพและพฤติกรรมของผู้เข้าชมเว็บไซต์
        </p>
      </div>
      <div class="flex items-center gap-3">
        <UButton
          variant="outline"
          size="sm"
          class="hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <Icon name="i-heroicons-calendar" class="w-4 h-4 mr-2" />
          7 วันล่าสุด
        </UButton>
        <UButton
          class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <Icon name="i-heroicons-arrow-down-tray" class="w-4 h-4 mr-2" />
          ส่งออกรายงาน
        </UButton>
      </div>
    </div>

    <!-- Real-time Stats -->
    <UCard class="overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
            <Icon name="i-heroicons-signal" class="w-5 h-5" />
          </div>
          <h2 class="text-xl font-semibold">ข้อมูลแบบเรียลไทม์</h2>
          <div class="flex items-center gap-2 ml-auto">
            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span class="text-sm text-blue-100">ออนไลน์</span>
          </div>
        </div>
      </template>
      
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-6">
        <div class="text-center">
          <div class="text-3xl font-bold mb-2">{{ analytics.realTime.currentVisitors }}</div>
          <div class="text-blue-100 text-sm">ผู้เข้าชมปัจจุบัน</div>
        </div>
        <div class="text-center">
          <div class="text-3xl font-bold mb-2">{{ analytics.realTime.activePages }}</div>
          <div class="text-blue-100 text-sm">หน้าที่ยังใช้งาน</div>
        </div>
                 <div class="text-center">
           <div class="text-3xl font-bold mb-2">{{ analytics.realTime.topReferrers[0]?.visitors || 0 }}</div>
           <div class="text-blue-100 text-sm">จาก Google</div>
         </div>
      </div>
    </UCard>

    <!-- Overview Stats -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ผู้เข้าชมทั้งหมด</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(analytics.overview.totalVisitors) }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">+12% จากเดือนที่แล้ว</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-users" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">หน้าเว็บทั้งหมด</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(analytics.overview.totalPageViews) }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">+8% จากเดือนที่แล้ว</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-document-text" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">เซสชันทั้งหมด</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatNumber(analytics.overview.totalSessions) }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">+15% จากเดือนที่แล้ว</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-clock" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">อัตราการแปลง</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.overview.conversionRate }}%</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">+2.5% จากเดือนที่แล้ว</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-chart-bar" class="w-8 h-8" />
          </div>
        </div>
      </UCard>
    </div>

    <!-- Performance Metrics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <UCard class="overflow-hidden">
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
              <Icon name="i-heroicons-chart-bar" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">เมตริกประสิทธิภาพ</h2>
          </div>
        </template>
        
        <div class="space-y-6">
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">อัตราการตีกลับ</span>
              <span class="text-sm font-semibold text-gray-900 dark:text-white">{{ analytics.overview.bounceRate }}%</span>
            </div>
            <div class="relative">
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
                <div class="h-3 rounded-full transition-all duration-1000 ease-out bg-gradient-to-r from-red-500 to-red-600"
                     :style="{ width: `${analytics.overview.bounceRate}%` }"></div>
              </div>
              <div class="absolute inset-0 flex items-center justify-center">
                <span class="text-xs font-medium text-white drop-shadow-sm">{{ analytics.overview.bounceRate }}%</span>
              </div>
            </div>
          </div>
          
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">เวลาเฉลี่ยต่อเซสชัน</span>
              <span class="text-sm font-semibold text-gray-900 dark:text-white">{{ formatDuration(analytics.overview.avgSessionDuration) }}</span>
            </div>
            <div class="relative">
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
                <div class="h-3 rounded-full transition-all duration-1000 ease-out bg-gradient-to-r from-green-500 to-green-600"
                     :style="{ width: `${(analytics.overview.avgSessionDuration / 300) * 100}%` }"></div>
              </div>
              <div class="absolute inset-0 flex items-center justify-center">
                <span class="text-xs font-medium text-white drop-shadow-sm">{{ formatDuration(analytics.overview.avgSessionDuration) }}</span>
              </div>
            </div>
          </div>
        </div>
      </UCard>

      <UCard class="overflow-hidden">
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <Icon name="i-heroicons-globe-alt" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">แหล่งที่มา</h2>
          </div>
        </template>
        
        <div class="space-y-4">
          <div 
            v-for="referrer in analytics.referrers.slice(0, 5)" 
            :key="referrer.source"
            class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
          >
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white text-sm font-semibold">
                {{ referrer.source.charAt(0) }}
              </div>
              <div>
                <div class="font-medium text-gray-900 dark:text-white">{{ referrer.source }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">{{ formatNumber(referrer.visitors) }} ผู้เข้าชม</div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-sm font-semibold text-gray-900 dark:text-white">{{ referrer.percentage }}%</div>
              <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                <div class="h-2 rounded-full bg-gradient-to-r from-blue-500 to-blue-600"
                     :style="{ width: `${referrer.percentage}%` }"></div>
              </div>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Top Pages -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
              <Icon name="i-heroicons-document-text" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">หน้าที่ยอดนิยม</h2>
          </div>
          <UButton variant="ghost" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
            ดูทั้งหมด
            <Icon name="i-heroicons-arrow-right" class="w-4 h-4 ml-1" />
          </UButton>
        </div>
      </template>
      
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">หน้าเว็บ</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">การดู</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">ผู้เข้าชม</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">อัตราการตีกลับ</th>
            </tr>
          </thead>
          <tbody>
            <tr 
              v-for="page in analytics.topPages" 
              :key="page.page"
              class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
            >
              <td class="py-4 px-6">
                <div class="flex items-center gap-3">
                  <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-sm font-semibold">
                    {{ page.page.charAt(1).toUpperCase() }}
                  </div>
                  <span class="font-medium text-gray-900 dark:text-white">{{ page.page }}</span>
                </div>
              </td>
              <td class="py-4 px-6">
                <span class="font-semibold text-gray-900 dark:text-white">{{ formatNumber(page.views) }}</span>
              </td>
              <td class="py-4 px-6">
                <span class="font-semibold text-gray-900 dark:text-white">{{ formatNumber(page.visitors) }}</span>
              </td>
              <td class="py-4 px-6">
                <span class="font-semibold text-gray-900 dark:text-white">{{ page.bounceRate }}%</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </UCard>

    <!-- Devices & Browsers -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <UCard class="overflow-hidden">
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
              <Icon name="i-heroicons-device-phone-mobile" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">อุปกรณ์</h2>
          </div>
        </template>
        
        <div class="space-y-4">
          <div 
            v-for="device in analytics.devices" 
            :key="device.device"
            class="flex items-center justify-between p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
          >
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg text-white">
                <Icon :name="getDeviceIcon(device.device)" class="w-5 h-5" />
              </div>
              <div>
                <div class="font-medium text-gray-900 dark:text-white">{{ device.device }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">{{ formatNumber(device.visitors) }} ผู้เข้าชม</div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-sm font-semibold text-gray-900 dark:text-white">{{ device.percentage }}%</div>
              <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                <div class="h-2 rounded-full bg-gradient-to-r from-orange-500 to-orange-600"
                     :style="{ width: `${device.percentage}%` }"></div>
              </div>
            </div>
          </div>
        </div>
      </UCard>

      <UCard class="overflow-hidden">
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <Icon name="i-heroicons-computer-desktop" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">เบราว์เซอร์</h2>
          </div>
        </template>
        
        <div class="space-y-4">
          <div 
            v-for="browser in analytics.browsers" 
            :key="browser.browser"
            class="flex items-center justify-between p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
          >
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-br from-green-500 to-green-600 rounded-lg text-white">
                <Icon :name="getDeviceIcon(browser.browser)" class="w-5 h-5" />
              </div>
              <div>
                <div class="font-medium text-gray-900 dark:text-white">{{ browser.browser }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">{{ formatNumber(browser.visitors) }} ผู้เข้าชม</div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-sm font-semibold text-gray-900 dark:text-white">{{ browser.percentage }}%</div>
              <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                <div class="h-2 rounded-full bg-gradient-to-r from-green-500 to-green-600"
                     :style="{ width: `${browser.percentage}%` }"></div>
              </div>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Countries -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-globe-alt" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ประเทศ</h2>
        </div>
      </template>
      
      <div class="space-y-4">
        <div 
          v-for="country in analytics.countries" 
          :key="country.country"
          class="flex items-center justify-between p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
        >
          <div class="flex items-center gap-3">
            <div class="text-2xl">{{ getCountryFlag(country.country) }}</div>
            <div>
              <div class="font-medium text-gray-900 dark:text-white">{{ country.country }}</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">{{ formatNumber(country.visitors) }} ผู้เข้าชม</div>
            </div>
          </div>
          <div class="text-right">
            <div class="text-sm font-semibold text-gray-900 dark:text-white">{{ country.percentage }}%</div>
            <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
              <div class="h-2 rounded-full bg-gradient-to-r from-purple-500 to-purple-600"
                   :style="{ width: `${country.percentage}%` }"></div>
            </div>
          </div>
        </div>
      </div>
    </UCard>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style> 