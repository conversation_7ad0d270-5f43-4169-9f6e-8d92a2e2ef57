<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

definePageMeta({
    layout: 'dashboard',
    middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// Gallery data
const galleries = ref([
    {
        id: '1',
        name: 'รูปภาพสินค้า',
        description: 'คลังรูปภาพสำหรับสินค้าต่างๆ',
        type: 'product',
        images: [
            {
                id: '1',
                url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400',
                alt: 'หูฟัง',
                filename: 'headphones-1.jpg',
                size: 245760,
                uploadedAt: '2024-01-15T10:30:00Z',
                usedBy: ['product-1', 'product-2']
            },
            {
                id: '2',
                url: 'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=400',
                alt: 'แว่นตา',
                filename: 'sunglasses-1.jpg',
                size: 189440,
                uploadedAt: '2024-01-16T14:20:00Z',
                usedBy: ['product-3']
            }
        ],
        createdAt: '2024-01-10T08:00:00Z',
        updatedAt: '2024-01-16T14:20:00Z'
    },
    {
        id: '2',
        name: 'รูปภาพหมวดหมู่',
        description: 'รูปภาพสำหรับแสดงในหมวดหมู่สินค้า',
        type: 'category',
        images: [
            {
                id: '3',
                url: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400',
                alt: 'อิเล็กทรอนิกส์',
                filename: 'electronics-banner.jpg',
                size: 512000,
                uploadedAt: '2024-01-12T09:15:00Z',
                usedBy: ['category-1']
            }
        ],
        createdAt: '2024-01-12T09:00:00Z',
        updatedAt: '2024-01-12T09:15:00Z'
    },
    {
        id: '3',
        name: 'รูปภาพบทความ',
        description: 'รูปภาพสำหรับบทความและข่าวสาร',
        type: 'article',
        images: [
            {
                id: '4',
                url: 'https://images.unsplash.com/photo-1486312338219-ce68e2c6b7d3?w=400',
                alt: 'เทคโนโลยี',
                filename: 'tech-article.jpg',
                size: 367890,
                uploadedAt: '2024-01-14T16:45:00Z',
                usedBy: ['article-1', 'blog-1']
            }
        ],
        createdAt: '2024-01-14T16:00:00Z',
        updatedAt: '2024-01-14T16:45:00Z'
    }
]);

const loading = ref(false);
const searchQuery = ref('');
const selectedType = ref('all');
const viewMode = ref<'grid' | 'list'>('grid');
const selectedGallery = ref<string | null>(null);

// Gallery type options
const galleryTypes = [
    { value: 'all', label: 'ทั้งหมด' },
    { value: 'product', label: 'สินค้า' },
    { value: 'category', label: 'หมวดหมู่' },
    { value: 'article', label: 'บทความ' },
    { value: 'blog', label: 'บล็อก' },
    { value: 'banner', label: 'แบนเนอร์' },
    { value: 'other', label: 'อื่นๆ' },
];

// Filter galleries
const filteredGalleries = computed(() => {
    let filtered = galleries.value;

    if (searchQuery.value) {
        filtered = filtered.filter(gallery =>
            gallery.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            gallery.description.toLowerCase().includes(searchQuery.value.toLowerCase())
        );
    }

    if (selectedType.value !== 'all') {
        filtered = filtered.filter(gallery => gallery.type === selectedType.value);
    }

    return filtered;
});

// Stats
const stats = computed(() => {
    const totalImages = galleries.value.reduce((sum, gallery) => sum + gallery.images.length, 0);
    const totalSize = galleries.value.reduce((sum, gallery) =>
        sum + gallery.images.reduce((imgSum, img) => imgSum + img.size, 0), 0
    );

    return {
        totalGalleries: galleries.value.length,
        totalImages,
        totalSize: (totalSize / (1024 * 1024)).toFixed(2) // Convert to MB
    };
});

// Actions
const handleCreateGallery = () => {
    navigateTo(`/dashboard/${siteId.value}/galleries/create`);
};

const handleViewGallery = (id: string) => {
    navigateTo(`/dashboard/${siteId.value}/galleries/${id}`);
};

const handleEditGallery = (id: string) => {
    navigateTo(`/dashboard/${siteId.value}/galleries/${id}/edit`);
};

const handleDeleteGallery = async (id: string) => {
    const gallery = galleries.value.find(g => g.id === id);
    if (!gallery) return;

    const confirmed = confirm(`คุณแน่ใจหรือไม่ที่จะลบแกลเลอรี "${gallery.name}"?`);
    if (!confirmed) return;

    loading.value = true;
    try {
        await new Promise(resolve => setTimeout(resolve, 1000));

        galleries.value = galleries.value.filter(g => g.id !== id);

        useToast().add({
            title: 'สำเร็จ',
            description: 'ลบแกลเลอรีเรียบร้อยแล้ว',
            color: 'success',
        });
    } catch (error) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'ไม่สามารถลบแกลเลอรีได้',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

// Format file size
const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Format date
const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('th-TH', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// Get type label
const getTypeLabel = (type: string) => {
    const typeOption = galleryTypes.find(t => t.value === type);
    return typeOption ? typeOption.label : type;
};

// Get type color
const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
        product: 'blue',
        category: 'green',
        article: 'purple',
        blog: 'orange',
        banner: 'red',
        other: 'gray'
    };
    return colors[type] || 'gray';
};
</script>

<template>
    <div class="space-y-8">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    คลังรูปภาพ
                </h1>
                <p class="text-gray-600 dark:text-gray-400">
                    จัดการคลังรูปภาพสำหรับสินค้า หมวดหมู่ บทความ และอื่นๆ
                </p>
            </div>
            <div class="flex items-center gap-3">
                <UButton @click="handleCreateGallery"
                    class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
                    <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
                    สร้างแกลเลอรีใหม่
                </UButton>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <UCard class="overflow-hidden">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                        <Icon name="i-heroicons-photo" class="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">แกลเลอรีทั้งหมด</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.totalGalleries }}</p>
                    </div>
                </div>
            </UCard>

            <UCard class="overflow-hidden">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                        <Icon name="i-heroicons-camera" class="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">รูปภาพทั้งหมด</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.totalImages }}</p>
                    </div>
                </div>
            </UCard>

            <UCard class="overflow-hidden">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
                        <Icon name="i-heroicons-server" class="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">ขนาดรวม</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.totalSize }} MB</p>
                    </div>
                </div>
            </UCard>
        </div>

        <!-- Filters and View Toggle -->
        <UCard class="overflow-hidden">
            <div class="flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <UInput v-model="searchQuery" placeholder="ค้นหาแกลเลอรี..." class="w-full">
                        <template #leading>
                            <Icon name="i-heroicons-magnifying-glass" class="w-4 h-4" />
                        </template>
                    </UInput>
                </div>
                <div class="sm:w-48">
                    <USelect v-model="selectedType" :items="galleryTypes" option-attribute="label"
                        value-attribute="value" placeholder="ประเภท" class="w-full" />
                </div>
                <div class="flex items-center gap-2">
                    <UButton @click="viewMode = 'grid'" :variant="viewMode === 'grid' ? 'solid' : 'outline'" size="sm">
                        <Icon name="i-heroicons-squares-2x2" class="w-4 h-4" />
                    </UButton>
                    <UButton @click="viewMode = 'list'" :variant="viewMode === 'list' ? 'solid' : 'outline'" size="sm">
                        <Icon name="i-heroicons-list-bullet" class="w-4 h-4" />
                    </UButton>
                </div>
            </div>
        </UCard>

        <!-- Grid View -->
        <div v-if="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <UCard v-for="gallery in filteredGalleries" :key="gallery.id"
                class="overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer group"
                @click="handleViewGallery(gallery.id)">

                <!-- Gallery Preview -->
                <div class="relative h-48 bg-gray-100 dark:bg-gray-800 overflow-hidden">
                    <div v-if="gallery.images.length > 0" class="grid grid-cols-2 gap-1 h-full">
                        <img v-for="(image, index) in gallery.images.slice(0, 4)" :key="image.id" :src="image.url"
                            :alt="image.alt" :class="[
                                'object-cover transition-transform duration-300 group-hover:scale-105',
                                gallery.images.length === 1 ? 'col-span-2 h-full' : 'h-full',
                                index >= 2 && gallery.images.length > 4 ? 'opacity-75' : ''
                            ]" />

                        <!-- More images indicator -->
                        <div v-if="gallery.images.length > 4"
                            class="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                            +{{ gallery.images.length - 4 }}
                        </div>
                    </div>

                    <!-- Empty state -->
                    <div v-else class="flex items-center justify-center h-full">
                        <div class="text-center">
                            <Icon name="i-heroicons-photo" class="w-12 h-12 text-gray-400 mx-auto mb-2" />
                            <p class="text-sm text-gray-500">ไม่มีรูปภาพ</p>
                        </div>
                    </div>
                </div>

                <!-- Gallery Info -->
                <div class="p-6">
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex-1 min-w-0">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate mb-1">
                                {{ gallery.name }}
                            </h3>
                            <UBadge :color="getTypeColor(gallery.type)" variant="subtle" size="sm">
                                {{ getTypeLabel(gallery.type) }}
                            </UBadge>
                        </div>

                        <UDropdown :items="[
                            [
                                {
                                    label: 'ดู',
                                    icon: 'i-heroicons-eye',
                                    click: () => handleViewGallery(gallery.id)
                                },
                                {
                                    label: 'แก้ไข',
                                    icon: 'i-heroicons-pencil-square',
                                    click: () => handleEditGallery(gallery.id)
                                }
                            ],
                            [
                                {
                                    label: 'ลบ',
                                    icon: 'i-heroicons-trash',
                                    click: () => handleDeleteGallery(gallery.id)
                                }
                            ]
                        ]" @click.stop>
                            <UButton variant="ghost" size="sm">
                                <Icon name="i-heroicons-ellipsis-vertical" class="w-4 h-4" />
                            </UButton>
                        </UDropdown>
                    </div>

                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                        {{ gallery.description }}
                    </p>

                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <span>{{ gallery.images.length }} รูปภาพ</span>
                        <span>{{ formatDate(gallery.updatedAt) }}</span>
                    </div>
                </div>
            </UCard>
        </div>

        <!-- List View -->
        <div v-else class="space-y-4">
            <UCard v-for="gallery in filteredGalleries" :key="gallery.id"
                class="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <div class="flex items-center gap-6 p-6">
                    <!-- Gallery Thumbnail -->
                    <div class="w-20 h-20 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden flex-shrink-0">
                        <img v-if="gallery.images.length > 0" :src="gallery.images[0].url" :alt="gallery.images[0].alt"
                            class="w-full h-full object-cover" />
                        <div v-else class="w-full h-full flex items-center justify-center">
                            <Icon name="i-heroicons-photo" class="w-8 h-8 text-gray-400" />
                        </div>
                    </div>

                    <!-- Gallery Info -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center gap-3 mb-2">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                                {{ gallery.name }}
                            </h3>
                            <UBadge :color="getTypeColor(gallery.type)" variant="subtle" size="sm">
                                {{ getTypeLabel(gallery.type) }}
                            </UBadge>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400 truncate mb-2">
                            {{ gallery.description }}
                        </p>
                        <div class="flex items-center gap-4 text-sm text-gray-500">
                            <span>{{ gallery.images.length }} รูปภาพ</span>
                            <span>อัปเดต {{ formatDate(gallery.updatedAt) }}</span>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center gap-2">
                        <UButton @click="handleViewGallery(gallery.id)" variant="ghost" size="sm">
                            <Icon name="i-heroicons-eye" class="w-4 h-4" />
                        </UButton>
                        <UDropdown :items="[
                            [
                                {
                                    label: 'แก้ไข',
                                    icon: 'i-heroicons-pencil-square',
                                    click: () => handleEditGallery(gallery.id)
                                }
                            ],
                            [
                                {
                                    label: 'ลบ',
                                    icon: 'i-heroicons-trash',
                                    click: () => handleDeleteGallery(gallery.id)
                                }
                            ]
                        ]">
                            <UButton variant="ghost" size="sm">
                                <Icon name="i-heroicons-ellipsis-vertical" class="w-4 h-4" />
                            </UButton>
                        </UDropdown>
                    </div>
                </div>
            </UCard>
        </div>

        <!-- Empty State -->
        <div v-if="filteredGalleries.length === 0" class="text-center py-12">
            <div
                class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                <Icon name="i-heroicons-photo" class="w-12 h-12 text-gray-400" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {{ searchQuery ? 'ไม่พบแกลเลอรีที่ค้นหา' : 'ยังไม่มีแกลเลอรี' }}
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                {{ searchQuery ? 'ลองเปลี่ยนคำค้นหาหรือตัวกรอง' : 'เริ่มต้นสร้างแกลเลอรีแรกของคุณ' }}
            </p>
            <UButton v-if="!searchQuery" @click="handleCreateGallery"
                class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">
                <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
                สร้างแกลเลอรีใหม่
            </UButton>
        </div>
    </div>
</template>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Add smooth animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.space-y-8>* {
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.hover\:shadow-lg:hover {
    transform: translateY(-2px);
}
</style>