<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

definePageMeta({
    layout: 'dashboard',
    middleware: ['auth'],
});

// Get route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);
const galleryId = computed(() => route.params.galleryId as string);

// Mock gallery data
const gallery = ref({
    id: '1',
    name: 'รูปภาพสินค้า',
    description: 'คลังรูปภาพสำหรับสินค้าต่างๆ ในร้านค้า',
    type: 'product',
    images: [
        {
            id: '1',
            url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=800',
            alt: 'หูฟังไร้สาย',
            filename: 'headphones-wireless.jpg',
            size: 245760,
            uploadedAt: '2024-01-15T10:30:00Z',
            usedBy: ['product-1', 'product-2'],
            dimensions: { width: 800, height: 600 }
        },
        {
            id: '2',
            url: 'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=800',
            alt: 'แว่นตากันแดด',
            filename: 'sunglasses-premium.jpg',
            size: 189440,
            uploadedAt: '2024-01-16T14:20:00Z',
            usedBy: ['product-3'],
            dimensions: { width: 800, height: 600 }
        },
        {
            id: '3',
            url: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=800',
            alt: 'รองเท้าผ้าใบ',
            filename: 'sneakers-red.jpg',
            size: 312580,
            uploadedAt: '2024-01-17T09:45:00Z',
            usedBy: ['product-4', 'product-5', 'category-2'],
            dimensions: { width: 800, height: 600 }
        },
        {
            id: '4',
            url: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=800',
            alt: 'นาฬิกาข้อมือ',
            filename: 'watch-luxury.jpg',
            size: 278900,
            uploadedAt: '2024-01-18T16:10:00Z',
            usedBy: ['product-6'],
            dimensions: { width: 800, height: 600 }
        },
        {
            id: '5',
            url: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800',
            alt: 'กระเป๋าเป้',
            filename: 'backpack-travel.jpg',
            size: 198760,
            uploadedAt: '2024-01-19T11:25:00Z',
            usedBy: ['product-7', 'product-8'],
            dimensions: { width: 800, height: 600 }
        },
        {
            id: '6',
            url: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',
            alt: 'เสื้อยืด',
            filename: 'tshirt-collection.jpg',
            size: 156890,
            uploadedAt: '2024-01-20T13:50:00Z',
            usedBy: ['product-9'],
            dimensions: { width: 800, height: 600 }
        }
    ],
    createdAt: '2024-01-10T08:00:00Z',
    updatedAt: '2024-01-20T13:50:00Z'
});

const loading = ref(false);
const searchQuery = ref('');
const selectedImage = ref<any>(null);
const showImageModal = ref(false);
const showDeleteModal = ref(false);
const imageToDelete = ref<any>(null);
const viewMode = ref<'grid' | 'list'>('grid');
const sortBy = ref<'name' | 'date' | 'size'>('date');
const sortOrder = ref<'asc' | 'desc'>('desc');

// Filter and sort images
const filteredImages = computed(() => {
    let filtered = gallery.value.images;

    // Filter by search query
    if (searchQuery.value) {
        filtered = filtered.filter(image =>
            image.alt.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            image.filename.toLowerCase().includes(searchQuery.value.toLowerCase())
        );
    }

    // Sort images
    filtered = [...filtered].sort((a, b) => {
        let aValue: any, bValue: any;

        switch (sortBy.value) {
            case 'name':
                aValue = a.filename.toLowerCase();
                bValue = b.filename.toLowerCase();
                break;
            case 'date':
                aValue = new Date(a.uploadedAt);
                bValue = new Date(b.uploadedAt);
                break;
            case 'size':
                aValue = a.size;
                bValue = b.size;
                break;
            default:
                return 0;
        }

        if (sortOrder.value === 'asc') {
            return aValue > bValue ? 1 : -1;
        } else {
            return aValue < bValue ? 1 : -1;
        }
    });

    return filtered;
});

// Stats
const stats = computed(() => {
    const totalSize = gallery.value.images.reduce((sum, img) => sum + img.size, 0);
    const totalUsage = gallery.value.images.reduce((sum, img) => sum + img.usedBy.length, 0);

    return {
        totalImages: gallery.value.images.length,
        totalSize: (totalSize / (1024 * 1024)).toFixed(2),
        totalUsage,
        averageSize: gallery.value.images.length > 0 ? (totalSize / gallery.value.images.length / 1024).toFixed(0) : '0'
    };
});

// Format file size
const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Format date
const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('th-TH', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// Get type info
const getTypeInfo = (type: string) => {
    const types: Record<string, { label: string; color: string; icon: string }> = {
        product: { label: 'สินค้า', color: 'blue', icon: 'i-heroicons-cube' },
        category: { label: 'หมวดหมู่', color: 'green', icon: 'i-heroicons-folder' },
        article: { label: 'บทความ', color: 'purple', icon: 'i-heroicons-document-text' },
        blog: { label: 'บล็อก', color: 'orange', icon: 'i-heroicons-pencil-square' },
        banner: { label: 'แบนเนอร์', color: 'red', icon: 'i-heroicons-photo' },
        other: { label: 'อื่นๆ', color: 'gray', icon: 'i-heroicons-ellipsis-horizontal' }
    };
    return types[type] || types.other;
};

// Actions
const handleEdit = () => {
    navigateTo(`/dashboard/${siteId.value}/galleries/${galleryId.value}/edit`);
};

const handleBack = () => {
    navigateTo(`/dashboard/${siteId.value}/galleries`);
};

const handleImageClick = (image: any) => {
    selectedImage.value = image;
    showImageModal.value = true;
};

const handleDeleteImage = (image: any) => {
    imageToDelete.value = image;
    showDeleteModal.value = true;
};

const confirmDeleteImage = async () => {
    if (!imageToDelete.value) return;

    loading.value = true;
    try {
        await new Promise(resolve => setTimeout(resolve, 1000));

        gallery.value.images = gallery.value.images.filter(img => img.id !== imageToDelete.value.id);

        useToast().add({
            title: 'สำเร็จ',
            description: 'ลบรูปภาพเรียบร้อยแล้ว',
            color: 'success',
        });

        showDeleteModal.value = false;
        imageToDelete.value = null;
    } catch (error) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'ไม่สามารถลบรูปภาพได้',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

const handleCopyImageUrl = (url: string) => {
    navigator.clipboard.writeText(url);
    useToast().add({
        title: 'สำเร็จ',
        description: 'คัดลอก URL รูปภาพแล้ว',
        color: 'success',
    });
};

const handleDownloadImage = (image: any) => {
    const link = document.createElement('a');
    link.href = image.url;
    link.download = image.filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

const typeInfo = computed(() => getTypeInfo(gallery.value.type));
</script>

<template>
    <div class="space-y-8">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <div class="flex items-center gap-3 mb-2">
                    <UButton @click="handleBack" variant="ghost" size="sm">
                        <Icon name="i-heroicons-arrow-left" class="w-4 h-4" />
                    </UButton>
                    <div :class="`p-2 bg-gradient-to-r from-${typeInfo.color}-500 to-${typeInfo.color}-600 rounded-lg`">
                        <Icon :name="typeInfo.icon" class="w-5 h-5 text-white" />
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                            {{ gallery.name }}
                        </h1>
                        <UBadge :color="typeInfo.color" variant="subtle" size="sm" class="mt-1">
                            {{ typeInfo.label }}
                        </UBadge>
                    </div>
                </div>
                <p class="text-gray-600 dark:text-gray-400">
                    {{ gallery.description }}
                </p>
            </div>
            <div class="flex items-center gap-3">
                <UButton @click="handleEdit" variant="outline">
                    <Icon name="i-heroicons-pencil-square" class="w-4 h-4 mr-2" />
                    แก้ไข
                </UButton>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <UCard class="overflow-hidden">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                        <Icon name="i-heroicons-photo" class="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">รูปภาพทั้งหมด</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.totalImages }}</p>
                    </div>
                </div>
            </UCard>

            <UCard class="overflow-hidden">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                        <Icon name="i-heroicons-server" class="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">ขนาดรวม</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.totalSize }} MB</p>
                    </div>
                </div>
            </UCard>

            <UCard class="overflow-hidden">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
                        <Icon name="i-heroicons-link" class="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">การใช้งาน</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.totalUsage }}</p>
                    </div>
                </div>
            </UCard>

            <UCard class="overflow-hidden">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
                        <Icon name="i-heroicons-chart-bar" class="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">ขนาดเฉลี่ย</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.averageSize }} KB</p>
                    </div>
                </div>
            </UCard>
        </div>

        <!-- Filters and Controls -->
        <UCard class="overflow-hidden">
            <div class="flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <UInput v-model="searchQuery" placeholder="ค้นหารูปภาพ..." class="w-full">
                        <template #leading>
                            <Icon name="i-heroicons-magnifying-glass" class="w-4 h-4" />
                        </template>
                    </UInput>
                </div>

                <div class="flex items-center gap-3">
                    <!-- Sort Options -->
                    <USelect v-model="sortBy" :items="[
                        { value: 'date', label: 'วันที่' },
                        { value: 'name', label: 'ชื่อ' },
                        { value: 'size', label: 'ขนาด' }
                    ]" option-attribute="label" value-attribute="value" class="w-32" />

                    <UButton @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'" variant="outline" size="sm">
                        <Icon :name="sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down'"
                            class="w-4 h-4" />
                    </UButton>

                    <!-- View Mode Toggle -->
                    <div class="flex items-center gap-2">
                        <UButton @click="viewMode = 'grid'" :variant="viewMode === 'grid' ? 'solid' : 'outline'"
                            size="sm">
                            <Icon name="i-heroicons-squares-2x2" class="w-4 h-4" />
                        </UButton>
                        <UButton @click="viewMode = 'list'" :variant="viewMode === 'list' ? 'solid' : 'outline'"
                            size="sm">
                            <Icon name="i-heroicons-list-bullet" class="w-4 h-4" />
                        </UButton>
                    </div>
                </div>
            </div>
        </UCard>

        <!-- Images Grid View -->
        <div v-if="viewMode === 'grid'" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            <UCard v-for="image in filteredImages" :key="image.id"
                class="overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer group"
                @click="handleImageClick(image)">

                <!-- Image -->
                <div class="relative h-48 bg-gray-100 dark:bg-gray-800 overflow-hidden">
                    <img :src="image.url" :alt="image.alt"
                        class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />

                    <!-- Overlay -->
                    <div
                        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                        <div
                            class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center gap-2">
                            <UButton @click.stop="handleImageClick(image)" size="sm" variant="solid" color="white">
                                <Icon name="i-heroicons-eye" class="w-4 h-4" />
                            </UButton>
                            <UButton @click.stop="handleCopyImageUrl(image.url)" size="sm" variant="solid"
                                color="white">
                                <Icon name="i-heroicons-link" class="w-4 h-4" />
                            </UButton>
                            <UButton @click.stop="handleDownloadImage(image)" size="sm" variant="solid" color="white">
                                <Icon name="i-heroicons-arrow-down-tray" class="w-4 h-4" />
                            </UButton>
                        </div>
                    </div>

                    <!-- Usage Badge -->
                    <div v-if="image.usedBy.length > 0" class="absolute top-2 right-2">
                        <UBadge color="green" variant="solid" size="sm">
                            {{ image.usedBy.length }}
                        </UBadge>
                    </div>
                </div>

                <!-- Image Info -->
                <div class="p-4">
                    <h3 class="font-medium text-gray-900 dark:text-white truncate mb-1">
                        {{ image.alt }}
                    </h3>
                    <p class="text-sm text-gray-500 truncate mb-2">{{ image.filename }}</p>
                    <div class="flex items-center justify-between text-xs text-gray-400">
                        <span>{{ formatFileSize(image.size) }}</span>
                        <span>{{ formatDate(image.uploadedAt) }}</span>
                    </div>
                </div>
            </UCard>
        </div>

        <!-- Images List View -->
        <div v-else class="space-y-4">
            <UCard v-for="image in filteredImages" :key="image.id"
                class="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <div class="flex items-center gap-6 p-6">
                    <!-- Image Thumbnail -->
                    <div class="w-20 h-20 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden flex-shrink-0 cursor-pointer"
                        @click="handleImageClick(image)">
                        <img :src="image.url" :alt="image.alt"
                            class="w-full h-full object-cover hover:scale-105 transition-transform duration-300" />
                    </div>

                    <!-- Image Info -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center gap-3 mb-2">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                                {{ image.alt }}
                            </h3>
                            <UBadge v-if="image.usedBy.length > 0" color="green" variant="subtle" size="sm">
                                ใช้งาน {{ image.usedBy.length }} ครั้ง
                            </UBadge>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400 truncate mb-2">
                            {{ image.filename }}
                        </p>
                        <div class="flex items-center gap-4 text-sm text-gray-500">
                            <span>{{ formatFileSize(image.size) }}</span>
                            <span>{{ image.dimensions.width }}×{{ image.dimensions.height }}</span>
                            <span>{{ formatDate(image.uploadedAt) }}</span>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center gap-2">
                        <UButton @click="handleImageClick(image)" variant="ghost" size="sm">
                            <Icon name="i-heroicons-eye" class="w-4 h-4" />
                        </UButton>
                        <UDropdown :items="[
                            [
                                {
                                    label: 'คัดลอก URL',
                                    icon: 'i-heroicons-link',
                                    click: () => handleCopyImageUrl(image.url)
                                },
                                {
                                    label: 'ดาวน์โหลด',
                                    icon: 'i-heroicons-arrow-down-tray',
                                    click: () => handleDownloadImage(image)
                                }
                            ],
                            [
                                {
                                    label: 'ลบ',
                                    icon: 'i-heroicons-trash',
                                    click: () => handleDeleteImage(image)
                                }
                            ]
                        ]">
                            <UButton variant="ghost" size="sm">
                                <Icon name="i-heroicons-ellipsis-vertical" class="w-4 h-4" />
                            </UButton>
                        </UDropdown>
                    </div>
                </div>
            </UCard>
        </div>

        <!-- Empty State -->
        <div v-if="filteredImages.length === 0" class="text-center py-12">
            <div
                class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                <Icon name="i-heroicons-photo" class="w-12 h-12 text-gray-400" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {{ searchQuery ? 'ไม่พบรูปภาพที่ค้นหา' : 'ยังไม่มีรูปภาพ' }}
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                {{ searchQuery ? 'ลองเปลี่ยนคำค้นหา' : 'เริ่มต้นเพิ่มรูปภาพในแกลเลอรีนี้' }}
            </p>
        </div>

        <!-- Image Modal -->
        <UModal v-model="showImageModal">
            <UCard class="max-w-4xl mx-auto">
                <template #header>
                    <div class="flex items-center justify-between">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                            {{ selectedImage?.alt }}
                        </h3>
                        <UButton @click="showImageModal = false" variant="ghost" size="sm" icon="i-heroicons-x-mark" />
                    </div>
                </template>

                <div v-if="selectedImage" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Image -->
                    <div class="bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
                        <img :src="selectedImage.url" :alt="selectedImage.alt" class="w-full h-auto" />
                    </div>

                    <!-- Image Details -->
                    <div class="space-y-4">
                        <div>
                            <label
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ชื่อไฟล์</label>
                            <p class="text-gray-900 dark:text-white">{{ selectedImage.filename }}</p>
                        </div>

                        <div>
                            <label
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ข้อความทางเลือก</label>
                            <p class="text-gray-900 dark:text-white">{{ selectedImage.alt }}</p>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ขนาดไฟล์</label>
                                <p class="text-gray-900 dark:text-white">{{ formatFileSize(selectedImage.size) }}</p>
                            </div>
                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ขนาดภาพ</label>
                                <p class="text-gray-900 dark:text-white">{{ selectedImage.dimensions.width }}×{{
                                    selectedImage.dimensions.height }}</p>
                            </div>
                        </div>

                        <div>
                            <label
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">วันที่อัปโหลด</label>
                            <p class="text-gray-900 dark:text-white">{{ formatDate(selectedImage.uploadedAt) }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">URL
                                รูปภาพ</label>
                            <div class="flex items-center gap-2">
                                <UInput :model-value="selectedImage.url" readonly class="flex-1" />
                                <UButton @click="handleCopyImageUrl(selectedImage.url)" size="sm"
                                    icon="i-heroicons-clipboard" />
                            </div>
                        </div>

                        <div v-if="selectedImage.usedBy.length > 0">
                            <label
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ใช้งานโดย</label>
                            <div class="flex flex-wrap gap-2">
                                <UBadge v-for="usage in selectedImage.usedBy" :key="usage" variant="subtle">
                                    {{ usage }}
                                </UBadge>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="flex items-center gap-3 pt-4">
                            <UButton @click="handleDownloadImage(selectedImage)" variant="outline"
                                icon="i-heroicons-arrow-down-tray">
                                ดาวน์โหลด
                            </UButton>
                            <UButton @click="handleDeleteImage(selectedImage)" color="red" variant="outline"
                                icon="i-heroicons-trash">
                                ลบ
                            </UButton>
                        </div>
                    </div>
                </div>
            </UCard>
        </UModal>

        <!-- Delete Confirmation Modal -->
        <UModal v-model="showDeleteModal">
            <UCard>
                <template #header>
                    <div class="flex items-center gap-4">
                        <div class="p-3 bg-red-100 dark:bg-red-900/20 rounded-full">
                            <Icon name="i-heroicons-exclamation-triangle" class="w-6 h-6 text-red-600" />
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">ยืนยันการลบรูปภาพ</h3>
                            <p class="text-gray-600 dark:text-gray-400">การดำเนินการนี้ไม่สามารถย้อนกลับได้</p>
                        </div>
                    </div>
                </template>

                <div v-if="imageToDelete" class="mb-6">
                    <div class="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <img :src="imageToDelete.url" :alt="imageToDelete.alt" class="w-16 h-16 object-cover rounded" />
                        <div>
                            <p class="font-medium text-gray-900 dark:text-white">{{ imageToDelete.alt }}</p>
                            <p class="text-sm text-gray-500">{{ imageToDelete.filename }}</p>
                        </div>
                    </div>
                </div>

                <template #footer>
                    <div class="flex items-center justify-end gap-3">
                        <UButton @click="showDeleteModal = false" variant="outline">
                            ยกเลิก
                        </UButton>
                        <UButton @click="confirmDeleteImage" :loading="loading" color="red">
                            ลบรูปภาพ
                        </UButton>
                    </div>
                </template>
            </UCard>
        </UModal>
    </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.space-y-8>* {
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.hover\:shadow-lg:hover {
    transform: translateY(-2px);
}
</style>