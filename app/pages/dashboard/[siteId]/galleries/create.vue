<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useVuelidate } from '@vuelidate/core';
import { required, minLength, maxLength, helpers } from '@vuelidate/validators';

definePageMeta({
    layout: 'dashboard',
    middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// Form data
const form = reactive({
    name: '',
    description: '',
    type: 'product' as 'product' | 'category' | 'article' | 'blog' | 'banner' | 'other',
    images: [] as Array<{
        id: string;
        url: string;
        alt: string;
        filename: string;
        size: number;
        uploadedAt: string;
    }>
});

// Validation rules
const rules = computed(() => ({
    name: {
        required: helpers.withMessage('ชื่อแกลเลอรีต้องไม่ว่าง', required),
        minLength: helpers.withMessage('ชื่อแกลเลอรีต้องมีอย่างน้อย 2 ตัวอักษร', minLength(2)),
        maxLength: helpers.withMessage('ชื่อแกลเลอรีต้องไม่เกิน 100 ตัวอักษร', maxLength(100)),
    },
    description: {
        maxLength: helpers.withMessage('คำอธิบายต้องไม่เกิน 500 ตัวอักษร', maxLength(500)),
    },
    type: {
        required: helpers.withMessage('ต้องเลือกประเภทแกลเลอรี', required),
    },
}));

// Initialize Vuelidate
const v$ = useVuelidate(rules, form);

const loading = ref(false);
const uploadLoading = ref(false);

// Gallery type options
const galleryTypes = ref([
    { value: 'product', label: 'สินค้า', icon: 'i-heroicons-cube', color: 'blue' },
    { value: 'category', label: 'หมวดหมู่', icon: 'i-heroicons-folder', color: 'green' },
    { value: 'article', label: 'บทความ', icon: 'i-heroicons-document-text', color: 'purple' },
    { value: 'blog', label: 'บล็อก', icon: 'i-heroicons-pencil-square', color: 'orange' },
    { value: 'banner', label: 'แบนเนอร์', icon: 'i-heroicons-photo', color: 'red' },
    { value: 'other', label: 'อื่นๆ', icon: 'i-heroicons-ellipsis-horizontal', color: 'gray' },
]);

// File upload handling
const fileInput = ref<HTMLInputElement>();
const dragOver = ref(false);

const handleFileSelect = (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target.files) {
        handleFiles(Array.from(target.files));
    }
};

const handleDrop = (event: DragEvent) => {
    event.preventDefault();
    dragOver.value = false;

    if (event.dataTransfer?.files) {
        handleFiles(Array.from(event.dataTransfer.files));
    }
};

const handleFiles = async (files: File[]) => {
    uploadLoading.value = true;

    try {
        for (const file of files) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                useToast().add({
                    title: 'ข้อผิดพลาด',
                    description: `ไฟล์ ${file.name} ไม่ใช่รูปภาพ`,
                    color: 'error',
                });
                continue;
            }

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                useToast().add({
                    title: 'ข้อผิดพลาด',
                    description: `ไฟล์ ${file.name} มีขนาดใหญ่เกินไป (สูงสุด 5MB)`,
                    color: 'error',
                });
                continue;
            }

            // Create preview URL
            const url = URL.createObjectURL(file);

            // Add to images array
            const newImage = {
                id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
                url,
                alt: file.name.split('.')[0],
                filename: file.name,
                size: file.size,
                uploadedAt: new Date().toISOString(),
            };

            form.images.push(newImage);
        }

        useToast().add({
            title: 'สำเร็จ',
            description: `อัปโหลดรูปภาพ ${files.length} ไฟล์เรียบร้อยแล้ว`,
            color: 'success',
        });
    } catch (error) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'ไม่สามารถอัปโหลดรูปภาพได้',
            color: 'error',
        });
    } finally {
        uploadLoading.value = false;
    }
};

const removeImage = (index: number) => {
    const image = form.images[index];
    URL.revokeObjectURL(image.url); // Clean up object URL
    form.images.splice(index, 1);
};

const updateImageAlt = (index: number, alt: string) => {
    form.images[index].alt = alt;
};

// Format file size
const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Handle form submission
const handleSubmit = async () => {
    const isValid = await v$.value.$validate();
    if (!isValid) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'กรุณาตรวจสอบข้อมูลที่กรอก',
            color: 'error',
        });
        return;
    }

    if (form.images.length === 0) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'กรุณาเพิ่มรูปภาพอย่างน้อย 1 รูป',
            color: 'error',
        });
        return;
    }

    loading.value = true;
    try {
        // Mock API call
        console.log('Creating gallery:', form);
        await new Promise(resolve => setTimeout(resolve, 1000));

        useToast().add({
            title: 'สำเร็จ',
            description: 'สร้างแกลเลอรีเรียบร้อยแล้ว',
            color: 'success',
        });

        // Navigate back to galleries page
        await navigateTo(`/dashboard/${siteId.value}/galleries`);
    } catch (error: any) {
        console.error('Error creating gallery:', error);
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: error.message || 'ไม่สามารถสร้างแกลเลอรีได้',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

// Handle cancel
const handleCancel = () => {
    // Clean up object URLs
    form.images.forEach(image => {
        URL.revokeObjectURL(image.url);
    });
    navigateTo(`/dashboard/${siteId.value}/galleries`);
};

// Get selected type info
const selectedTypeInfo = computed(() => {
    return galleryTypes.value.find(type => type.value === form.type);
});
</script>

<template>
    <div class="space-y-8">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    สร้างแกลเลอรีใหม่
                </h1>
                <p class="text-gray-600 dark:text-gray-400">
                    สร้างคลังรูปภาพใหม่สำหรับจัดเก็บและจัดการรูปภาพ
                </p>
            </div>
            <div class="flex items-center gap-3">
                <UButton @click="handleCancel" variant="outline" size="sm"
                    class="hover:bg-gray-100 dark:hover:bg-gray-800">
                    <Icon name="i-heroicons-arrow-left" class="w-4 h-4 mr-2" />
                    ย้อนกลับ
                </UButton>
                <UButton @click="handleSubmit" :loading="loading"
                    class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
                    <Icon name="i-heroicons-check" class="w-5 h-5 mr-2" />
                    สร้างแกลเลอรี
                </UButton>
            </div>
        </div>

        <!-- Form -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Form -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <UCard class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                                <Icon name="i-heroicons-information-circle" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ข้อมูลพื้นฐาน</h2>
                        </div>
                    </template>

                    <div class="space-y-6">
                        <!-- Gallery Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                ชื่อแกลเลอรี <span class="text-red-500">*</span>
                            </label>
                            <UInput v-model="form.name" placeholder="กรอกชื่อแกลเลอรี"
                                :error="v$.name.$error ? v$.name.$errors[0].$message : ''" class="w-full" />
                        </div>

                        <!-- Gallery Type -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                ประเภทแกลเลอรี <span class="text-red-500">*</span>
                            </label>
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                <div v-for="type in galleryTypes" :key="type.value" class="relative">
                                    <input :id="`type-${type.value}`" v-model="form.type" :value="type.value"
                                        type="radio" class="sr-only" />
                                    <label :for="`type-${type.value}`" :class="[
                                        'flex flex-col items-center p-4 border-2 rounded-lg cursor-pointer transition-all duration-200',
                                        form.type === type.value
                                            ? `border-${type.color}-500 bg-${type.color}-50 dark:bg-${type.color}-900/20`
                                            : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                                    ]">
                                        <Icon :name="type.icon" :class="[
                                            'w-6 h-6 mb-2',
                                            form.type === type.value ? `text-${type.color}-600` : 'text-gray-400'
                                        ]" />
                                        <span :class="[
                                            'text-sm font-medium',
                                            form.type === type.value ? `text-${type.color}-700 dark:text-${type.color}-300` : 'text-gray-700 dark:text-gray-300'
                                        ]">
                                            {{ type.label }}
                                        </span>
                                    </label>
                                </div>
                            </div>
                            <p v-if="v$.type.$error" class="mt-2 text-sm text-red-600">
                                {{ v$.type.$errors[0].$message }}
                            </p>
                        </div>

                        <!-- Description -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                คำอธิบาย
                            </label>
                            <UTextarea v-model="form.description" placeholder="อธิบายแกลเลอรีนี้ (ไม่เกิน 500 ตัวอักษร)"
                                :error="v$.description.$error ? v$.description.$errors[0].$message : ''" :rows="3"
                                class="w-full" />
                        </div>
                    </div>
                </UCard>

                <!-- Image Upload -->
                <UCard class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                                <Icon name="i-heroicons-photo" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รูปภาพ</h2>
                        </div>
                    </template>

                    <div class="space-y-6">
                        <!-- Upload Area -->
                        <div :class="[
                            'border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200',
                            dragOver
                                ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                                : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                        ]" @dragover.prevent="dragOver = true" @dragleave.prevent="dragOver = false" @drop.prevent="handleDrop">

                            <div class="space-y-4">
                                <div
                                    class="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                                    <Icon name="i-heroicons-cloud-arrow-up" class="w-8 h-8 text-gray-400" />
                                </div>

                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                        อัปโหลดรูปภาพ
                                    </h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                        ลากและวางไฟล์รูปภาพที่นี่ หรือคลิกเพื่อเลือกไฟล์
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        รองรับไฟล์ JPG, PNG, GIF, WebP (สูงสุด 5MB ต่อไฟล์)
                                    </p>
                                </div>

                                <UButton @click="fileInput?.click()" :loading="uploadLoading" variant="outline">
                                    <Icon name="i-heroicons-folder-open" class="w-4 h-4 mr-2" />
                                    เลือกไฟล์
                                </UButton>
                            </div>

                            <input ref="fileInput" type="file" multiple accept="image/*" class="hidden"
                                @change="handleFileSelect" />
                        </div>

                        <!-- Uploaded Images -->
                        <div v-if="form.images.length > 0" class="space-y-4">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                รูปภาพที่อัปโหลด ({{ form.images.length }})
                            </h3>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div v-for="(image, index) in form.images" :key="image.id"
                                    class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">

                                    <!-- Image Preview -->
                                    <div class="relative h-48 bg-gray-100 dark:bg-gray-800">
                                        <img :src="image.url" :alt="image.alt" class="w-full h-full object-cover" />

                                        <!-- Remove Button -->
                                        <button @click="removeImage(index)"
                                            class="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors">
                                            <Icon name="i-heroicons-x-mark" class="w-4 h-4" />
                                        </button>
                                    </div>

                                    <!-- Image Info -->
                                    <div class="p-4 space-y-3">
                                        <div>
                                            <label
                                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                ข้อความทางเลือก (Alt Text)
                                            </label>
                                            <UInput :model-value="image.alt"
                                                @update:model-value="(value) => updateImageAlt(index, value)"
                                                placeholder="อธิบายรูปภาพ" class="w-full" />
                                        </div>

                                        <div class="flex items-center justify-between text-sm text-gray-500">
                                            <span class="truncate">{{ image.filename }}</span>
                                            <span>{{ formatFileSize(image.size) }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </UCard>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Preview -->
                <UCard class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
                                <Icon name="i-heroicons-eye" class="w-5 h-5 text-white" />
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">ตัวอย่าง</h3>
                        </div>
                    </template>

                    <div class="space-y-4">
                        <!-- Gallery Type -->
                        <div v-if="selectedTypeInfo" class="flex items-center gap-3">
                            <div
                                :class="`p-2 bg-gradient-to-r from-${selectedTypeInfo.color}-500 to-${selectedTypeInfo.color}-600 rounded-lg`">
                                <Icon :name="selectedTypeInfo.icon" class="w-4 h-4 text-white" />
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ selectedTypeInfo.label
                                    }}</p>
                                <p class="text-xs text-gray-500">ประเภทแกลเลอรี</p>
                            </div>
                        </div>

                        <!-- Gallery Name -->
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                                {{ form.name || 'ชื่อแกลเลอรี' }}
                            </h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {{ form.description || 'คำอธิบายแกลเลอรี' }}
                            </p>
                        </div>

                        <!-- Image Count -->
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">จำนวนรูปภาพ</span>
                            <span class="font-medium text-gray-900 dark:text-white">{{ form.images.length }}</span>
                        </div>

                        <!-- Total Size -->
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">ขนาดรวม</span>
                            <span class="font-medium text-gray-900 dark:text-white">
                                {{formatFileSize(form.images.reduce((sum, img) => sum + img.size, 0))}}
                            </span>
                        </div>
                    </div>
                </UCard>

                <!-- Tips -->
                <UCard class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg">
                                <Icon name="i-heroicons-light-bulb" class="w-5 h-5 text-white" />
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">เคล็ดลับ</h3>
                        </div>
                    </template>

                    <div class="space-y-3 text-sm text-gray-600 dark:text-gray-400">
                        <div class="flex items-start gap-2">
                            <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <p>ใช้ชื่อไฟล์ที่มีความหมายเพื่อง่ายต่อการค้นหา</p>
                        </div>
                        <div class="flex items-start gap-2">
                            <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <p>เพิ่ม Alt Text เพื่อการเข้าถึงที่ดีขึ้น</p>
                        </div>
                        <div class="flex items-start gap-2">
                            <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <p>จัดกลุ่มรูปภาพตามประเภทการใช้งาน</p>
                        </div>
                        <div class="flex items-start gap-2">
                            <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <p>ใช้รูปภาพที่มีคุณภาพสูงสำหรับผลลัพธ์ที่ดี</p>
                        </div>
                    </div>
                </UCard>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.space-y-8>* {
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Drag and drop styles */
.border-dashed {
    transition: all 0.2s ease;
}
</style>