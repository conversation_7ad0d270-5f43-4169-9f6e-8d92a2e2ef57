<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route: any = useRoute();
const siteId = computed(() => route.params.siteId);

// Reactive data
const roles = ref<any[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);

// Filters and search
const searchQuery = ref('');
const selectedStatus = ref('all');
const selectedType = ref('all');
const sortBy = ref('created_at');
const sortOrder = ref('desc');
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Filter options
const statusOptions = ref([
  { value: 'all', label: 'ทุกสถานะ' },
  { value: 'active', label: 'ใช้งาน' },
  { value: 'inactive', label: 'ไม่ใช้งาน' },
]);

const typeOptions = ref([
  { value: 'all', label: 'ทุกประเภท' },
  { value: 'admin', label: 'ผู้ดูแลระบบ' },
  { value: 'manager', label: 'ผู้จัดการ' },
  { value: 'editor', label: 'ผู้แก้ไข' },
  { value: 'viewer', label: 'ผู้ดู' },
]);

const sortOptions = ref([
  { value: 'created_at', label: 'วันที่สร้าง' },
  { value: 'name', label: 'ชื่อบทบาท' },
  { value: 'users', label: 'จำนวนผู้ใช้' },
  { value: 'updated_at', label: 'วันที่แก้ไข' },
]);

// Analytics data
const analytics = ref({
  totalRoles: 6,
  activeRoles: 5,
  totalUsers: 24,
  averageUsers: 4,
  topRole: 'ผู้ดู',
  growthRate: 10.5,
});

// Mock roles data
const mockRoles = [
  {
    id: 1,
    name: 'ผู้ดูแลระบบ',
    type: 'admin',
    status: 'active',
    users: 2,
    description: 'สิทธิ์เต็มในการจัดการระบบ',
    permissions: ['all'],
    createdAt: '2024-01-01',
    updatedAt: '2024-01-15',
  },
  {
    id: 2,
    name: 'ผู้จัดการ',
    type: 'manager',
    status: 'active',
    users: 3,
    description: 'จัดการทีมและโครงการ',
    permissions: ['manage_team', 'manage_projects', 'view_reports'],
    createdAt: '2024-01-02',
    updatedAt: '2024-01-10',
  },
  {
    id: 3,
    name: 'ผู้แก้ไข',
    type: 'editor',
    status: 'active',
    users: 8,
    description: 'แก้ไขเนื้อหาและบทความ',
    permissions: ['edit_content', 'publish_posts', 'manage_comments'],
    createdAt: '2024-01-03',
    updatedAt: '2024-01-12',
  },
  {
    id: 4,
    name: 'ผู้ดู',
    type: 'viewer',
    status: 'active',
    users: 11,
    description: 'ดูข้อมูลและรายงาน',
    permissions: ['view_reports', 'view_analytics'],
    createdAt: '2024-01-05',
    updatedAt: '2024-01-08',
  },
  {
    id: 5,
    name: 'ผู้ทดสอบ',
    type: 'viewer',
    status: 'inactive',
    users: 0,
    description: 'บทบาทสำหรับการทดสอบ',
    permissions: ['view_reports'],
    createdAt: '2024-01-10',
    updatedAt: '2024-01-10',
  },
];

// Format number
const formatNumber = (num: number) => {
  return new Intl.NumberFormat('th-TH').format(num);
};

// Get status badge class
const getStatusBadgeClass = (status: string) => {
  const classes = {
    active: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
  };
  return classes[status as keyof typeof classes] || classes.inactive;
};

// Get status label
const getStatusLabel = (status: string) => {
  const labels = {
    active: 'ใช้งาน',
    inactive: 'ไม่ใช้งาน',
  };
  return labels[status as keyof typeof labels] || status;
};

// Get type badge class
const getTypeBadgeClass = (type: string) => {
  const classes = {
    admin: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
    manager: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    editor: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    viewer: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
  };
  return classes[type as keyof typeof classes] || classes.viewer;
};

// Get type label
const getTypeLabel = (type: string) => {
  const labels = {
    admin: 'ผู้ดูแลระบบ',
    manager: 'ผู้จัดการ',
    editor: 'ผู้แก้ไข',
    viewer: 'ผู้ดู',
  };
  return labels[type as keyof typeof labels] || type;
};

// Computed filtered roles
const filteredRoles = computed(() => {
  let filtered = roles.value || [];

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(
      role =>
        role.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        role.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  // Status filter
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(role => role.status === selectedStatus.value);
  }

  // Type filter
  if (selectedType.value !== 'all') {
    filtered = filtered.filter(role => role.type === selectedType.value);
  }

  // Sort
  filtered.sort((a, b) => {
    let aVal = a[sortBy.value as keyof typeof a];
    let bVal = b[sortBy.value as keyof typeof b];

    if (typeof aVal === 'string') {
      aVal = aVal.toLowerCase();
      bVal = bVal.toLowerCase();
    }

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });

  return filtered;
});

// Paginated roles
const paginatedRoles = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredRoles.value.slice(start, end);
});

// Total pages
const totalPages = computed(() => {
  return Math.ceil(filteredRoles.value.length / itemsPerPage.value);
});

// Use composables
const { getRoles, getRoleAnalytics } = useRoles();

// Load roles
const loadRoles = async () => {
  loading.value = true;
  error.value = null;
  try {
    const filters = {
      search: searchQuery.value,
      status: selectedStatus.value,
      type: selectedType.value,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value as 'asc' | 'desc',
      page: currentPage.value,
      limit: itemsPerPage.value,
    };

    const response = (await getRoles(siteId.value, filters)) as any;
    roles.value = response.data || [];

    // Load analytics
    const analyticsResponse = (await getRoleAnalytics(siteId.value)) as any;
    analytics.value = analyticsResponse.data || analytics.value;
  } catch (err: any) {
    error.value = err.message;
    console.error('Error loading roles:', err);
  } finally {
    loading.value = false;
  }
};

// Load data on mount
onMounted(async () => {
  await loadRoles();
});

// Methods
const handleSort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = field;
    sortOrder.value = 'desc';
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handleEditRole = (role: any) => {
  // Navigate to edit page
  navigateTo(`/dashboard/${siteId.value}/roles/${role.id}/edit`);
};

const handleViewRole = (role: any) => {
  // Navigate to role detail page
  navigateTo(`/dashboard/${siteId.value}/roles/${role.id}`);
};

const handleDuplicateRole = async (role: any) => {
  if (confirm(`คุณต้องการคัดลอกบทบาท "${role.name}" ใช่หรือไม่?`)) {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      useToast().add({
        title: 'สำเร็จ',
        description: `คัดลอกบทบาท ${role.name} เรียบร้อยแล้ว`,
        color: 'success',
      });
    } catch (err) {
      console.error('Error duplicating role:', err);
    }
  }
};

const handleDeleteRole = async (role: any) => {
  if (confirm(`คุณต้องการลบบทบาท "${role.name}" ใช่หรือไม่?`)) {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      roles.value = roles.value.filter(r => r.id !== role.id);
      useToast().add({
        title: 'สำเร็จ',
        description: `ลบบทบาท ${role.name} เรียบร้อยแล้ว`,
        color: 'success',
      });
    } catch (err) {
      console.error('Error deleting role:', err);
    }
  }
};

const handleCreateRole = () => {
  navigateTo(`/dashboard/${siteId.value}/roles/create`);
};
</script>

<template>
  <div class="space-y-8">
    <!-- Enhanced Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          จัดการบทบาท
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          จัดการบทบาทและสิทธิ์ของผู้ใช้ในระบบ
        </p>
      </div>
      <UButton 
        @click="handleCreateRole"
        class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
      >
        <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
        สร้างบทบาทใหม่
      </UButton>
    </div>

    <!-- Enhanced Analytics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">บทบาททั้งหมด</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalRoles }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">+{{ analytics.growthRate }}% จากเดือนที่แล้ว</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-shield-check" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">บทบาทที่ใช้งาน</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.activeRoles }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">{{ Math.round((analytics.activeRoles / analytics.totalRoles) * 100) }}% ของทั้งหมด</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-check-circle" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ผู้ใช้ทั้งหมด</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalUsers }}</p>
            <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">เฉลี่ย {{ analytics.averageUsers }}/บทบาท</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-users" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">บทบาทยอดนิยม</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.topRole }}</p>
            <p class="text-xs text-yellow-600 dark:text-yellow-400 mt-1">ผู้ดูข้อมูล</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-star" class="w-8 h-8" />
          </div>
        </div>
      </UCard>
    </div>

    <!-- Enhanced Filters -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-funnel" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวกรองและค้นหา</h2>
        </div>
      </template>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหาบทบาท</label>
          <UInput
            v-model="searchQuery"
            placeholder="ค้นหาตามชื่อหรือคำอธิบาย..."
            icon="i-heroicons-magnifying-glass"
            class="w-full"
          />
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สถานะ</label>
          <USelect
            v-model="selectedStatus"
            :items="statusOptions"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกสถานะ"
            class="w-full"
          />
        </div>

        <!-- Type Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ประเภท</label>
          <USelect
            v-model="selectedType"
            :items="typeOptions"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกประเภท"
            class="w-full"
          />
        </div>

        <!-- Sort -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เรียงลำดับ</label>
          <div class="flex gap-2">
            <USelect
              v-model="sortBy"
              :items="sortOptions"
              option-attribute="label"
              value-attribute="value"
              class="flex-1"
            />
            <UButton
              @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'"
              variant="outline"
              size="sm"
              class="px-3"
            >
              <Icon 
                :name="sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down'" 
                class="w-4 h-4" 
              />
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Enhanced Roles Table -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <Icon name="i-heroicons-shield-check" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รายการบทบาท</h2>
            <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-400 rounded-full">
              {{ filteredRoles.length }} รายการ
            </span>
          </div>
          <div class="flex items-center gap-2">
            <UButton variant="ghost" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
              <Icon name="i-heroicons-arrow-down-tray" class="w-4 h-4 mr-1" />
              ส่งออก
            </UButton>
            <UButton variant="ghost" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
              <Icon name="i-heroicons-cog-6-tooth" class="w-4 h-4" />
            </UButton>
          </div>
        </div>
      </template>
      
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="flex items-center gap-3">
          <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-blue-600" />
          <span class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</span>
        </div>
      </div>

      <div v-else-if="error" class="text-center py-12">
        <div class="text-red-600 dark:text-red-400 mb-4">
          <Icon name="i-heroicons-exclamation-triangle" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">เกิดข้อผิดพลาด</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">{{ error }}</p>
        <UButton @click="loadRoles" variant="outline">
          ลองใหม่
        </UButton>
      </div>

      <div v-else-if="paginatedRoles.length === 0" class="text-center py-12">
        <div class="text-gray-400 dark:text-gray-500 mb-4">
          <Icon name="i-heroicons-shield-check" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">ไม่พบบทบาท</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          ยังไม่มีบทบาทในระบบ หรือไม่ตรงกับเงื่อนไขการค้นหา
        </p>
        <UButton @click="handleCreateRole" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
          สร้างบทบาทแรก
        </UButton>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>บทบาท</span>
                  <UButton
                    @click="handleSort('name')"
                    variant="ghost"
                    size="xs"
                    class="p-1"
                  >
                    <Icon 
                      :name="sortBy === 'name' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'" 
                      class="w-3 h-3" 
                    />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>ประเภท</span>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>สถานะ</span>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>ผู้ใช้</span>
                  <UButton
                    @click="handleSort('users')"
                    variant="ghost"
                    size="xs"
                    class="p-1"
                  >
                    <Icon 
                      :name="sortBy === 'users' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'" 
                      class="w-3 h-3" 
                    />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>วันที่สร้าง</span>
                  <UButton
                    @click="handleSort('created_at')"
                    variant="ghost"
                    size="xs"
                    class="p-1"
                  >
                    <Icon 
                      :name="sortBy === 'created_at' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'" 
                      class="w-3 h-3" 
                    />
                  </UButton>
                </div>
              </th>
              <th class="text-right py-4 px-6 font-semibold text-gray-900 dark:text-white">การดำเนินการ</th>
            </tr>
          </thead>
          <tbody>
            <tr 
              v-for="role in paginatedRoles" 
              :key="role.id"
              class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
            >
              <td class="py-4 px-6">
                <div class="flex items-center gap-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-semibold">
                    {{ role.name.charAt(0).toUpperCase() }}
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">{{ role.name }}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ role.description }}</p>
                  </div>
                </div>
              </td>
              <td class="py-4 px-6">
                <span 
                  class="px-3 py-1 text-xs rounded-full font-medium"
                  :class="getTypeBadgeClass(role.type)"
                >
                  {{ getTypeLabel(role.type) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span 
                  class="px-3 py-1 text-xs rounded-full font-medium"
                  :class="getStatusBadgeClass(role.status)"
                >
                  {{ getStatusLabel(role.status) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span class="font-semibold text-gray-900 dark:text-white">
                  {{ formatNumber(role.users) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {{ new Date(role.createdAt).toLocaleDateString('th-TH') }}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center justify-end gap-2">
                  <UButton
                    @click="handleViewRole(role)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                  >
                    <Icon name="i-heroicons-eye" class="w-4 h-4" />
                  </UButton>
                  <UButton
                    @click="handleEditRole(role)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-green-50 dark:hover:bg-green-900/20 text-green-600 dark:text-green-400"
                  >
                    <Icon name="i-heroicons-pencil-square" class="w-4 h-4" />
                  </UButton>
                  <UButton
                    @click="handleDuplicateRole(role)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-purple-50 dark:hover:bg-purple-900/20 text-purple-600 dark:text-purple-400"
                  >
                    <Icon name="i-heroicons-document-duplicate" class="w-4 h-4" />
                  </UButton>
                  <UButton
                    @click="handleDeleteRole(role)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400"
                  >
                    <Icon name="i-heroicons-trash" class="w-4 h-4" />
                  </UButton>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Enhanced Pagination -->
      <div v-if="totalPages > 1" class="flex items-center justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} ถึง {{ Math.min(currentPage * itemsPerPage, filteredRoles.length) }} 
          จาก {{ filteredRoles.length }} รายการ
        </div>
        <div class="flex items-center gap-2">
          <UButton
            @click="handlePageChange(currentPage - 1)"
            :disabled="currentPage === 1"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-left" class="w-4 h-4" />
          </UButton>
          
          <div class="flex items-center gap-1">
            <UButton
              v-for="page in Math.min(5, totalPages)"
              :key="page"
              @click="handlePageChange(page)"
              :variant="currentPage === page ? 'solid' : 'outline'"
              size="sm"
              class="w-8 h-8 p-0"
            >
              {{ page }}
            </UButton>
          </div>
          
          <UButton
            @click="handlePageChange(currentPage + 1)"
            :disabled="currentPage === totalPages"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-right" class="w-4 h-4" />
          </UButton>
        </div>
      </div>
    </UCard>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style> 