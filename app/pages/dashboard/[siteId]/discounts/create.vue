<script setup lang="ts">
import { useVuelidate } from '@vuelidate/core';
import { required, minLength, helpers } from '@vuelidate/validators';
import { useRoute, useRouter } from 'vue-router';
import { ref, reactive, computed, onMounted } from 'vue';
import { useProducts } from '~/composables/useProducts';
import { useUser } from '~/composables/useUser'; 

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

const route = useRoute();
const { getProductCategories, getProducts } = useProducts();
const { getUsers } = useUser();

const siteId = computed(() => {
  const params = route.params as { siteId?: string };
  return params.siteId || '';
});
const router = useRouter();

// Loading state
const loading = ref(false);

// สร้าง state สำหรับ vuelidate
const form = reactive({
  name: '',
  description: '',
  type: 'percentage',
  value: 0,
  maxDiscountAmount: 0,
  target: 'all',
  status: 'scheduled',
  startDate: '',
  endDate: '',
  code: '',
  bannerImage: '',
  highlightColor: '',
  displayOrder: 0,
  conditions: {
    applicableCategories: [],
    applicableProducts: [],
    excludedProducts: [],
    userGroups: [],
    firstTimeOnly: false,
    usageLimit: 0,
    usageLimitPerUser: 0,
    allowCombinable: false,
    allowedDaysOfWeek: [],
    allowedTimeRange: { start: '', end: '' },
    channel: [],
    minOrderAmount: 0,
    maxOrderAmount: 0,
    minQuantity: 0,
    maxQuantity: 0,
  },
});

// Vuelidate validation rules
const rules = {
  name: { 
    required: helpers.withMessage('กรุณากรอกชื่อส่วนลด', required),
    minLength: helpers.withMessage('ชื่อส่วนลดต้องมีอย่างน้อย 2 ตัวอักษร', minLength(2))
  },
  type: { required: helpers.withMessage('กรุณาเลือกประเภทส่วนลด', required) },
  value: { 
    required: helpers.withMessage('กรุณากรอกมูลค่าส่วนลด', required),
    minValue: helpers.withMessage('มูลค่าส่วนลดต้องมากกว่า 0', helpers.regex(/^[1-9]\d*$/))
  },
  status: { required: helpers.withMessage('กรุณาเลือกสถานะ', required) },
  target: { required: helpers.withMessage('กรุณาเลือกเป้าหมาย', required) },
  startDate: { required: helpers.withMessage('กรุณาเลือกวันที่เริ่ม', required) },
  endDate: { required: helpers.withMessage('กรุณาเลือกวันหมดอายุ', required) },
  conditions: {
    usageLimit: { 
      required: helpers.withMessage('กรุณากรอกจำนวนครั้งที่ใช้ได้', required),
      minValue: helpers.withMessage('จำนวนครั้งต้องมากกว่าหรือเท่ากับ 0', helpers.regex(/^\d+$/))
    },
  },
};

// Vuelidate instance
const v$ = useVuelidate(rules, form);

const typeOptions = [
  { value: 'percentage', label: 'เปอร์เซ็นต์' },
  { value: 'fixed', label: 'ลดราคาคงที่' },
  { value: 'free_shipping', label: 'ส่งฟรี' },
  { value: 'buy_x_get_y', label: 'ซื้อ X แถม Y' },
];
const targetOptions = [
  { value: 'all', label: 'ทั้งหมด' },
  { value: 'package', label: 'แพ็กเกจ' },
  { value: 'category', label: 'หมวดหมู่' },
  { value: 'product', label: 'สินค้า' },
  { value: 'user_group', label: 'กลุ่มผู้ใช้' },
  { value: 'first_time', label: 'ลูกค้าใหม่' },
];
const statusOptions = [
  { value: 'active', label: 'ใช้งาน' },
  { value: 'inactive', label: 'ไม่ใช้งาน' },
  { value: 'expired', label: 'หมดอายุ' },
  { value: 'scheduled', label: 'รอเริ่ม' },
];
const dayOptions = [
  { value: 0, label: 'อาทิตย์' },
  { value: 1, label: 'จันทร์' },
  { value: 2, label: 'อังคาร' },
  { value: 3, label: 'พุธ' },
  { value: 4, label: 'พฤหัสบดี' },
  { value: 5, label: 'ศุกร์' },
  { value: 6, label: 'เสาร์' },
];
const channelOptions = [
  { value: 'online', label: 'ออนไลน์' },
  { value: 'offline', label: 'หน้าร้าน' },
  { value: 'mobile', label: 'แอปมือถือ' },
];

const categoryOptions = ref<Array<{ label: string; value: string }>>([]);
const productOptions = ref<Array<{ label: string; value: string }>>([]);
const userGroupOptions = ref<Array<{ label: string; value: string }>>([]);

// Computed properties for error messages
const nameError = computed(() => {
  const error = v$.value.name.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});

const valueError = computed(() => {
  const error = v$.value.value.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});

const typeError = computed(() => {
  const error = v$.value.type.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});

const statusError = computed(() => {
  const error = v$.value.status.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});

const targetError = computed(() => {
  const error = v$.value.target.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});

const startDateError = computed(() => {
  const error = v$.value.startDate.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});

const endDateError = computed(() => {
  const error = v$.value.endDate.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});

const usageLimitError = computed(() => {
  const error = v$.value.conditions?.usageLimit?.$errors[0]?.$message;
  return typeof error === 'string' ? error : undefined;
});

onMounted(async () => {
  // ดึงหมวดหมู่
  const cats = await getProductCategories(siteId.value);
  categoryOptions.value = (cats?.data || cats)?.map((c: any) => ({ label: c.name, value: c._id }));
  // ดึงสินค้า
  const prods = await getProducts(siteId.value, { limit: 100 });
  productOptions.value = (prods?.data || prods)?.map((p: any) => ({ label: p.name, value: p._id }));
  // ดึงกลุ่มผู้ใช้ (สมมติใช้ user role หรือ group)
  const users = await getUsers(siteId.value);
  const userData = users?.data || users;
  if (Array.isArray(userData)) {
    userGroupOptions.value = userData.map((u: any) => ({ label: u.group || u.role || u.email, value: u.group || u.role || u._id }));
  }
});

const handleCreateDiscount = async () => {
  const isValid = await v$.value.$validate();
  if (!isValid) return;

  loading.value = true;
  try {
    // TODO: ส่งข้อมูลไป backend หรือ API จริง
    useToast().add({
      title: 'สร้างส่วนลดสำเร็จ',
      color: 'success',
    });
    router.push(`/dashboard/${siteId.value}/discounts`);
  } catch (error: any) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: error.message || 'ไม่สามารถสร้างส่วนลดได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// SEO
useSeoMeta({
  title: 'สร้างส่วนลด - ระบบจัดการร้านค้า',
  description: 'สร้างส่วนลดใหม่ จัดการโปรโมชั่นและเงื่อนไขส่วนลด',
  keywords: 'สร้างส่วนลด, โปรโมชั่น, ส่วนลด, ระบบร้านค้า',
  ogTitle: 'สร้างส่วนลด - ระบบจัดการร้านค้า',
  ogDescription: 'สร้างส่วนลดใหม่ จัดการโปรโมชั่นและเงื่อนไขส่วนลด',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <UContainer>
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
        สร้างส่วนลดใหม่
      </h1>
      <p class="text-gray-600 dark:text-gray-400 mt-2">
        กรอกข้อมูลเพื่อสร้างโปรโมชั่นหรือส่วนลดสำหรับร้านค้าของคุณ
      </p>
    </div>
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Left Column - Form -->
      <div class="lg:col-span-2">
        <UCard variant="soft">
          <div class="space-y-8">
            <!-- Basic Info Section -->
            <div class="space-y-6">
              <h2 class="text-lg font-semibold flex items-center gap-2">
                <Icon name="solar:info-circle-line-duotone" class="size-7 text-primary" />
                ข้อมูลส่วนลด
              </h2>
              <UFormField size="xl" label="ชื่อส่วนลด" name="name" required :error="nameError">
                <UInput v-model="form.name" placeholder="ชื่อส่วนลด" class="w-full" 
                  @blur="() => { if (v$.name) v$.name.$touch(); }" />
              </UFormField>
              <UFormField size="xl" label="รายละเอียด" name="description">
                <UTextarea v-model="form.description" placeholder="รายละเอียด (ไม่บังคับ)" :rows="2" class="w-full" />
              </UFormField>
            </div>
            <!-- Discount Details Section -->
            <div class="space-y-6">
              <h2 class="text-lg font-semibold flex items-center gap-2">
                <Icon name="solar:percent-square-line-duotone" class="size-7 text-primary" />
                รายละเอียดส่วนลด
              </h2>
              <UFormField label="ประเภทส่วนลด" name="type" required :error="typeError">
                <USelectMenu v-model="form.type" :items="typeOptions" value-key="value" option-attribute="label" placeholder="เลือกประเภท" 
                  @update:model-value="() => { if (v$.type) v$.type.$touch(); }" />
              </UFormField>
              <UFormField label="เป้าหมายส่วนลด" name="target" required :error="targetError">
                <USelectMenu v-model="form.target" :items="targetOptions" value-key="value" option-attribute="label" placeholder="เลือกเป้าหมาย" 
                  @update:model-value="() => { if (v$.target) v$.target.$touch(); }" />
              </UFormField>
              <UFormField label="สถานะ" name="status" required :error="statusError">
                <USelectMenu v-model="form.status" :items="statusOptions" value-key="value" option-attribute="label" placeholder="เลือกสถานะ" 
                  @update:model-value="() => { if (v$.status) v$.status.$touch(); }" />
              </UFormField>
              <UFormField label="มูลค่าส่วนลด" name="value" :error="valueError" v-if="form.type !== 'free_shipping' && form.type !== 'buy_x_get_y'">
                <UInputNumber v-model.number="form.value" min="0" placeholder="เช่น 10 หรือ 100" class="w-full" 
                  @blur="() => { if (v$.value) v$.value.$touch(); }" />
              </UFormField>
              <UFormField label="ส่วนลดสูงสุด (ถ้ามี)" name="maxDiscountAmount">
                <UInputNumber v-model.number="form.maxDiscountAmount" min="0" placeholder="เช่น 1000" class="w-full" />
              </UFormField>
              <UFormField label="รหัสส่วนลด (Discount Code)" name="code">
                <UInput v-model="form.code" placeholder="กรอกรหัสส่วนลด (ถ้ามี)" class="w-full" />
              </UFormField>
              <UFormField label="Banner Image URL" name="bannerImage">
                <UInput v-model="form.bannerImage" placeholder="URL รูปภาพ (ถ้ามี)" class="w-full" />
              </UFormField>
              <UFormField label="Highlight Color" name="highlightColor">
                <UInput v-model="form.highlightColor" placeholder="#HEX หรือชื่อสี (ถ้ามี)" class="w-full" />
              </UFormField>
              <UFormField label="ลำดับการแสดงผล" name="displayOrder">
                <UInputNumber v-model.number="form.displayOrder" min="0" placeholder="0" class="w-full" />
              </UFormField>
              <div class="flex gap-4">
                <UFormField label="วันที่เริ่ม" name="startDate" :error="startDateError">
                  <UInput v-model="form.startDate" type="date" class="w-full" 
                    @blur="() => { if (v$.startDate) v$.startDate.$touch(); }" />
                </UFormField>
                <UFormField label="วันหมดอายุ" name="endDate" :error="endDateError">
                  <UInput v-model="form.endDate" type="date" class="w-full" 
                    @blur="() => { if (v$.endDate) v$.endDate.$touch(); }" />
                </UFormField>
              </div>
            </div>
            <!-- Condition Section -->
            <div class="space-y-6">
              <h2 class="text-lg font-semibold flex items-center gap-2">
                <Icon name="solar:filter-line-duotone" class="size-7 text-primary" />
                เงื่อนไขการใช้ส่วนลด
              </h2>
              <UFormField label="หมวดหมู่ที่ใช้ได้" name="conditions.applicableCategories">
                <USelectMenu v-model="form.conditions.applicableCategories" :items="categoryOptions" multiple searchable value-key="value" option-attribute="label" placeholder="เลือกหมวดหมู่" required />
              </UFormField>
              <UFormField label="สินค้าที่ใช้ได้" name="conditions.applicableProducts">
                <USelectMenu v-model="form.conditions.applicableProducts" :items="productOptions" multiple searchable value-key="value" option-attribute="label" placeholder="เลือกสินค้า" />
              </UFormField>
              <UFormField label="สินค้าที่ไม่ร่วมรายการ" name="conditions.excludedProducts">
                <USelectMenu v-model="form.conditions.excludedProducts" :items="productOptions" multiple searchable value-key="value" option-attribute="label" placeholder="เลือกสินค้าไม่ร่วมรายการ" />
              </UFormField>
              <UFormField label="กลุ่มผู้ใช้" name="conditions.userGroups">
                <USelectMenu v-model="form.conditions.userGroups" :items="userGroupOptions" multiple searchable value-key="value" option-attribute="label" placeholder="เลือกกลุ่มผู้ใช้" />
              </UFormField>
              <UFormField label="ลูกค้าใหม่เท่านั้น" name="conditions.firstTimeOnly">
                <USelectMenu v-model="form.conditions.firstTimeOnly" :items="[{ label: 'ใช่', value: true }, { label: 'ไม่ใช่', value: false }]" value-key="value" option-attribute="label" placeholder="เลือกเงื่อนไข" required />
              </UFormField>
              <UFormField label="จำนวนครั้งที่ใช้ได้ทั้งหมด" name="conditions.usageLimit" :error="usageLimitError">
                <UInputNumber v-model.number="form.conditions.usageLimit" min="0" placeholder="0 = ไม่จำกัด" required 
                  @blur="() => { if (v$.conditions?.usageLimit) v$.conditions.usageLimit.$touch(); }" />
              </UFormField>
              <UFormField label="จำนวนครั้งต่อผู้ใช้" name="conditions.usageLimitPerUser">
                <UInputNumber v-model.number="form.conditions.usageLimitPerUser" min="0" placeholder="0 = ไม่จำกัด" />
              </UFormField>
              <UFormField label="ใช้ร่วมกับโปรอื่นได้" name="conditions.allowCombinable">
                <USelectMenu v-model="form.conditions.allowCombinable" :items="[{ label: 'ได้', value: true }, { label: 'ไม่ได้', value: false }]" value-key="value" option-attribute="label" placeholder="เลือกเงื่อนไข" />
              </UFormField>
              <UFormField label="วันที่ใช้ได้ (เลือกหลายวัน)" name="conditions.allowedDaysOfWeek">
                <USelectMenu v-model="form.conditions.allowedDaysOfWeek" :items="dayOptions" multiple value-key="value" option-attribute="label" placeholder="เลือกวัน" />
              </UFormField>
              <UFormField label="ช่วงเวลา (HH:mm)" name="conditions.allowedTimeRange">
                <div class="flex gap-2">
                  <UInput v-model="form.conditions.allowedTimeRange.start" placeholder="เริ่ม เช่น 09:00" class="w-full" />
                  <UInput v-model="form.conditions.allowedTimeRange.end" placeholder="จบ เช่น 18:00" class="w-full" />
                </div>
              </UFormField>
              <UFormField label="ช่องทางที่ใช้ได้ (เลือกหลายช่องทาง)" name="conditions.channel">
                <USelectMenu v-model="form.conditions.channel" :items="channelOptions" multiple value-key="value" option-attribute="label" placeholder="เลือกช่องทาง" />
              </UFormField>
            </div>
          </div>
        </UCard>
      </div>
      <!-- Right Column - Summary -->
      <div class="lg:col-span-1">
        <UCard variant="soft" class="sticky top-8">
          <template #header>
            <h2 class="text-lg font-semibold flex items-center gap-2">
              <Icon name="solar:document-text-line-duotone" class="size-7 text-primary" />
              สรุปรายการส่วนลด
            </h2>
          </template>
          <div class="space-y-6">
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">ชื่อส่วนลด:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ form.name || '-' }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">ประเภท:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ typeOptions.find((t: any) => t.value === form.type)?.label || '-' }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">เป้าหมาย:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ targetOptions.find((t: any) => t.value === form.target)?.label || '-' }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">สถานะ:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ statusOptions.find((t: any) => t.value === form.status)?.label || '-' }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">มูลค่าส่วนลด:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ form.value || '-' }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">วันหมดอายุ:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ form.endDate || '-' }}</span>
              </div>
            </div>
            <hr />
            <div class="flex justify-end">
              <UButton size="xl" block color="primary" :loading="loading" @click="handleCreateDiscount" :disabled="loading || v$.$invalid">
                <Icon name="solar:card-line-duotone" class="mr-2" />
                สร้างส่วนลด
              </UButton>
            </div>
          </div>
        </UCard>
      </div>
    </div>
  </UContainer>
</template>

<style scoped>
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}
.space-y-6 > * { animation: fadeInUp 0.6s ease-out forwards; }
</style> 