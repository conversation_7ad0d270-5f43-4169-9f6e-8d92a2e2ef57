<script setup lang="ts">
import { useRoute } from 'vue-router';

const route = useRoute();
const discountId = computed(() => route.params.discountId as string);

// Mock: ดึงข้อมูล discount จาก mock data หรือ API จริงในอนาคต
const discount = ref({
  id: discountId.value,
  name: 'ส่วนลดต้อนรับ',
  description: 'ลด 10% สำหรับลูกค้าใหม่',
  type: 'percentage',
  value: 10,
  minOrderAmount: 1000,
  maxDiscount: 500,
  usageLimit: 1000,
  usedCount: 156,
  status: 'active',
  startDate: '2024-01-01T00:00:00Z',
  endDate: '2024-12-31T23:59:59Z',
  createdAt: '2024-01-01T09:00:00Z',
  createdBy: 'admin',
  applicableProducts: ['all'],
  applicableCategories: ['electronics', 'clothing'],
  firstTimeOnly: true,
  requireCode: true,
  code: 'WELCOME10',
});

const formatCurrency = (amount: number) => new Intl.NumberFormat('th-TH', { style: 'currency', currency: 'THB' }).format(amount);
const formatDate = (date: string) => new Date(date).toLocaleDateString('th-TH', { year: 'numeric', month: 'short', day: 'numeric' });

const getTypeLabel = (type: string) => {
  const labels = { percentage: 'เปอร์เซ็นต์', fixed: 'ลดราคาคงที่', free_shipping: 'ส่งฟรี' };
  return labels[type as keyof typeof labels] || type;
};

const getDiscountDisplay = (discount: any) => {
  if (discount.type === 'percentage') return `${discount.value}%`;
  if (discount.type === 'fixed') return formatCurrency(discount.value);
  if (discount.type === 'free_shipping') return 'ส่งฟรี';
  return '';
};
</script>
<template>
  <div class="max-w-2xl mx-auto py-8 space-y-6">
    <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">รายละเอียดส่วนลด</h1>
    <UCard>
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <span class="text-lg font-semibold">{{ discount.name }}</span>
          <span class="px-2 py-1 text-xs rounded-full font-medium bg-blue-100 text-blue-800">{{ getTypeLabel(discount.type) }}</span>
          <span v-if="discount.requireCode" class="px-2 py-1 text-xs rounded-full font-medium bg-purple-100 text-purple-800">โค้ด: {{ discount.code }}</span>
          <span v-else class="px-2 py-1 text-xs rounded-full font-medium bg-green-100 text-green-800">Auto</span>
        </div>
        <div class="text-gray-600 dark:text-gray-400">{{ discount.description }}</div>
        <div class="flex flex-wrap gap-4 mt-2">
          <div>
            <span class="font-medium">ประเภท:</span> {{ getTypeLabel(discount.type) }}
          </div>
          <div>
            <span class="font-medium">มูลค่าส่วนลด:</span> {{ getDiscountDisplay(discount) }}
          </div>
          <div>
            <span class="font-medium">สถานะ:</span> <span :class="discount.status === 'active' ? 'text-green-600' : 'text-gray-500'">{{ discount.status === 'active' ? 'ใช้งาน' : 'ไม่ใช้งาน' }}</span>
          </div>
        </div>
        <div class="flex flex-wrap gap-4 mt-2">
          <div>
            <span class="font-medium">วันที่เริ่ม:</span> {{ formatDate(discount.startDate) }}
          </div>
          <div>
            <span class="font-medium">วันหมดอายุ:</span> {{ formatDate(discount.endDate) }}
          </div>
        </div>
        <div class="flex flex-wrap gap-4 mt-2">
          <div>
            <span class="font-medium">ขั้นต่ำ:</span> {{ formatCurrency(discount.minOrderAmount) }}
          </div>
          <div v-if="discount.maxDiscount > 0">
            <span class="font-medium">สูงสุด:</span> {{ formatCurrency(discount.maxDiscount) }}
          </div>
          <div>
            <span class="font-medium">จำนวนใช้:</span> {{ discount.usedCount }}/{{ discount.usageLimit }}
          </div>
        </div>
        <div v-if="discount.firstTimeOnly" class="text-xs text-orange-600 mt-2">ลูกค้าใหม่เท่านั้น</div>
      </div>
    </UCard>
  </div>
</template> 