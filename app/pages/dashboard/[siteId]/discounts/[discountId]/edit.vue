<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useProducts } from '~/composables/useProducts';
import { useUser } from '~/composables/useUser';

const route = useRoute();
const router = useRouter();
const discountId = computed(() => route.params.discountId as string);

// Mock: โหลดข้อมูล discount เดิม (ในอนาคตเชื่อมต่อ API จริง)
const form = ref({
  name: '',
  description: '',
  type: 'percentage',
  value: 10,
  maxDiscountAmount: 500,
  target: 'all',
  status: 'active',
  startDate: '2024-01-01',
  endDate: '2024-12-31',
  code: 'WELCOME10',
  bannerImage: '',
  highlightColor: '',
  displayOrder: 0,
  createdBy: '',
  updatedBy: '',
  conditions: {
    minOrderAmount: 1000,
    maxOrderAmount: 0,
    minQuantity: 0,
    maxQuantity: 0,
    applicableCategories: [],
    applicableProducts: [],
    excludedProducts: [],
    userGroups: [],
    firstTimeOnly: true,
    usageLimit: 1000,
    usageLimitPerUser: 1,
    allowCombinable: false,
    allowedDaysOfWeek: [],
    allowedTimeRange: { start: '', end: '' },
    channel: [],
  },
});

const typeOptions = [
  { value: 'percentage', label: 'เปอร์เซ็นต์' },
  { value: 'fixed', label: 'ลดราคาคงที่' },
  { value: 'free_shipping', label: 'ส่งฟรี' },
];

const targetOptions = [
  { value: 'all', label: 'ทั้งหมด' },
  { value: 'specific_products', label: 'สินค้าที่กำหนด' },
  { value: 'specific_categories', label: 'หมวดหมู่ที่กำหนด' },
  { value: 'specific_users', label: 'กลุ่มผู้ใช้ที่กำหนด' },
];

const statusOptions = [
  { value: 'active', label: 'เปิดใช้งาน' },
  { value: 'inactive', label: 'ปิดใช้งาน' },
  { value: 'expired', label: 'หมดอายุ' },
];

const dayOptions = [
  { value: 'sunday', label: 'วันอาทิตย์' },
  { value: 'monday', label: 'วันจันทร์' },
  { value: 'tuesday', label: 'วันอังคาร' },
  { value: 'wednesday', label: 'วันพฤหัสบดี' },
  { value: 'thursday', label: 'วันศุกร์' },
  { value: 'saturday', label: 'วันเสาร์' },
];

const channelOptions = [
  { value: 'web', label: 'เว็บไซต์' },
  { value: 'app', label: 'แอปพลิเคชัน' },
  { value: 'mobile_app', label: 'แอปพลิเคชันมือถือ' },
  { value: 'social_media', label: 'โซเชียลมีเดีย' },
  { value: 'email', label: 'อีเมล' },
  { value: 'sms', label: 'SMS' },
];

const handleSubmit = () => {
  // TODO: ส่งข้อมูลไป backend หรือ API
  alert('บันทึกการแก้ไขสำเร็จ!');
  router.push('../../');
};

const { getProductCategories, getProducts } = useProducts();
const { getUsers } = useUser();

const siteId = computed(() => route.params.siteId as string);

const categoryOptions = ref([]);
const productOptions = ref([]);
const userGroupOptions = ref([]);

onMounted(async () => {
  // ดึงหมวดหมู่
  const cats = await getProductCategories(siteId.value);
  categoryOptions.value = (cats?.data || cats)?.map((c: any) => ({ label: c.name, value: c._id }));
  // ดึงสินค้า
  const prods = await getProducts(siteId.value, { limit: 100 });
  productOptions.value = (prods?.data || prods)?.map((p: any) => ({ label: p.name, value: p._id }));
  // ดึงกลุ่มผู้ใช้ (สมมติใช้ user role หรือ group)
  const users = await getUsers(siteId.value);
  userGroupOptions.value = (users?.data || users)?.map((u: any) => ({ label: u.group || u.role || u.email, value: u.group || u.role || u._id }));
});
</script>
<template>
  <UContainer>
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
        แก้ไขส่วนลด
      </h1>
      <p class="text-gray-600 dark:text-gray-400 mt-2">
        แก้ไขข้อมูลโปรโมชั่นหรือส่วนลดสำหรับร้านค้าของคุณ
      </p>
    </div>
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Left Column - Form -->
      <div class="lg:col-span-2">
        <UCard variant="soft">
          <div class="space-y-8">
            <!-- Basic Info Section -->
            <div class="space-y-6">
              <h2 class="text-lg font-semibold flex items-center gap-2">
                <Icon name="solar:info-circle-line-duotone" class="size-7 text-primary" />
                ข้อมูลส่วนลด
              </h2>
              <UFormField size="xl" label="ชื่อส่วนลด" name="name" required>
                <UInput v-model="form.value.name" placeholder="ชื่อส่วนลด" class="w-full" />
              </UFormField>
              <UFormField size="xl" label="รายละเอียด" name="description">
                <UTextarea v-model="form.value.description" placeholder="รายละเอียด (ไม่บังคับ)" :rows="2" class="w-full" />
              </UFormField>
            </div>
            <!-- Discount Details Section -->
            <div class="space-y-6">
              <h2 class="text-lg font-semibold flex items-center gap-2">
                <Icon name="solar:percent-square-line-duotone" class="size-7 text-primary" />
                รายละเอียดส่วนลด
              </h2>
              <UFormField label="ประเภทส่วนลด" name="type" required>
                <USelectMenu v-model="form.value.type" :items="typeOptions" value-key="value" option-attribute="label" placeholder="เลือกประเภท" />
              </UFormField>
              <UFormField label="เป้าหมายส่วนลด" name="target" required>
                <USelectMenu v-model="form.value.target" :items="targetOptions" value-key="value" option-attribute="label" placeholder="เลือกเป้าหมาย" />
              </UFormField>
              <UFormField label="สถานะ" name="status" required>
                <USelectMenu v-model="form.value.status" :items="statusOptions" value-key="value" option-attribute="label" placeholder="เลือกสถานะ" />
              </UFormField>
              <UFormField label="มูลค่าส่วนลด" name="value" v-if="form.value.type !== 'free_shipping' && form.value.type !== 'buy_x_get_y'">
                <UInputNumber v-model.number="form.value.value" min="0" placeholder="เช่น 10 หรือ 100" class="w-full" />
              </UFormField>
              <UFormField label="ส่วนลดสูงสุด (ถ้ามี)" name="maxDiscountAmount">
                <UInputNumber v-model.number="form.value.maxDiscountAmount" min="0" placeholder="เช่น 1000" class="w-full" />
              </UFormField>
              <UFormField label="รหัสส่วนลด (Discount Code)" name="code">
                <UInput v-model="form.value.code" placeholder="กรอกรหัสส่วนลด (ถ้ามี)" class="w-full" />
              </UFormField>
              <UFormField label="Banner Image URL" name="bannerImage">
                <UInput v-model="form.value.bannerImage" placeholder="URL รูปภาพ (ถ้ามี)" class="w-full" />
              </UFormField>
              <UFormField label="Highlight Color" name="highlightColor">
                <UInput v-model="form.value.highlightColor" placeholder="#HEX หรือชื่อสี (ถ้ามี)" class="w-full" />
              </UFormField>
              <UFormField label="ลำดับการแสดงผล" name="displayOrder">
                <UInputNumber v-model.number="form.value.displayOrder" min="0" placeholder="0" class="w-full" />
              </UFormField>
              <div class="flex gap-4">
                <UFormField label="วันที่เริ่ม" name="startDate">
                  <UInput v-model="form.value.startDate" type="date" class="w-full" />
                </UFormField>
                <UFormField label="วันหมดอายุ" name="endDate">
                  <UInput v-model="form.value.endDate" type="date" class="w-full" />
                </UFormField>
              </div>
            </div>
            <!-- Condition Section -->
            <div class="space-y-6">
              <h2 class="text-lg font-semibold flex items-center gap-2">
                <Icon name="solar:filter-line-duotone" class="size-7 text-primary" />
                เงื่อนไขการใช้ส่วนลด
              </h2>
              <UFormField label="หมวดหมู่ที่ใช้ได้" name="conditions.applicableCategories">
                <USelectMenu v-model="form.value.conditions.applicableCategories" :items="categoryOptions" multiple searchable value-key="value" option-attribute="label" placeholder="เลือกหมวดหมู่" required />
              </UFormField>
              <UFormField label="สินค้าที่ใช้ได้" name="conditions.applicableProducts">
                <USelectMenu v-model="form.value.conditions.applicableProducts" :items="productOptions" multiple searchable value-key="value" option-attribute="label" placeholder="เลือกสินค้า" />
              </UFormField>
              <UFormField label="สินค้าที่ไม่ร่วมรายการ" name="conditions.excludedProducts">
                <USelectMenu v-model="form.value.conditions.excludedProducts" :items="productOptions" multiple searchable value-key="value" option-attribute="label" placeholder="เลือกสินค้าไม่ร่วมรายการ" />
              </UFormField>
              <UFormField label="กลุ่มผู้ใช้" name="conditions.userGroups">
                <USelectMenu v-model="form.value.conditions.userGroups" :items="userGroupOptions" multiple searchable value-key="value" option-attribute="label" placeholder="เลือกกลุ่มผู้ใช้" />
              </UFormField>
              <UFormField label="ลูกค้าใหม่เท่านั้น" name="conditions.firstTimeOnly">
                <USelectMenu v-model="form.value.conditions.firstTimeOnly" :items="[{ label: 'ใช่', value: true }, { label: 'ไม่ใช่', value: false }]" value-key="value" option-attribute="label" placeholder="เลือกเงื่อนไข" required />
              </UFormField>
              <UFormField label="จำนวนครั้งที่ใช้ได้ทั้งหมด" name="conditions.usageLimit">
                <UInputNumber v-model.number="form.value.conditions.usageLimit" min="0" placeholder="0 = ไม่จำกัด" required />
              </UFormField>
              <UFormField label="จำนวนครั้งต่อผู้ใช้" name="conditions.usageLimitPerUser">
                <UInputNumber v-model.number="form.value.conditions.usageLimitPerUser" min="0" placeholder="0 = ไม่จำกัด" />
              </UFormField>
              <UFormField label="ใช้ร่วมกับโปรอื่นได้" name="conditions.allowCombinable">
                <USelectMenu v-model="form.value.conditions.allowCombinable" :items="[{ label: 'ได้', value: true }, { label: 'ไม่ได้', value: false }]" value-key="value" option-attribute="label" placeholder="เลือกเงื่อนไข" />
              </UFormField>
              <UFormField label="วันที่ใช้ได้ (เลือกหลายวัน)" name="conditions.allowedDaysOfWeek">
                <USelectMenu v-model="form.value.conditions.allowedDaysOfWeek" :items="dayOptions" multiple value-key="value" option-attribute="label" placeholder="เลือกวัน" />
              </UFormField>
              <UFormField label="ช่วงเวลา (HH:mm)" name="conditions.allowedTimeRange">
                <div class="flex gap-2">
                  <UInput v-model="form.value.conditions.allowedTimeRange.start" placeholder="เริ่ม เช่น 09:00" class="w-full" />
                  <UInput v-model="form.value.conditions.allowedTimeRange.end" placeholder="จบ เช่น 18:00" class="w-full" />
                </div>
              </UFormField>
              <UFormField label="ช่องทางที่ใช้ได้ (เลือกหลายช่องทาง)" name="conditions.channel">
                <USelectMenu v-model="form.value.conditions.channel" :items="channelOptions" multiple value-key="value" option-attribute="label" placeholder="เลือกช่องทาง" />
              </UFormField>
            </div>
          </div>
        </UCard>
      </div>
      <!-- Right Column - Summary -->
      <div class="lg:col-span-1">
        <UCard variant="soft" class="sticky top-8">
          <template #header>
            <h2 class="text-lg font-semibold flex items-center gap-2">
              <Icon name="solar:document-text-line-duotone" class="size-7 text-primary" />
              สรุปรายการส่วนลด
            </h2>
          </template>
          <div class="space-y-6">
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">ชื่อส่วนลด:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ form.value.name || '-' }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">ประเภท:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ typeOptions.find(t => t.value === form.value.type)?.label || '-' }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">เป้าหมาย:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ targetOptions.find(t => t.value === form.value.target)?.label || '-' }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">สถานะ:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ statusOptions.find(t => t.value === form.value.status)?.label || '-' }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">มูลค่าส่วนลด:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ form.value.value || '-' }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">วันหมดอายุ:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ form.value.endDate || '-' }}</span>
              </div>
            </div>
            <hr />
            <div class="flex justify-end">
              <UButton size="xl" block color="primary" :loading="loading" @click="handleSubmit" :disabled="loading">
                <Icon name="solar:card-line-duotone" class="mr-2" />
                บันทึกการแก้ไข
              </UButton>
            </div>
          </div>
        </UCard>
      </div>
    </div>
  </UContainer>
</template> 