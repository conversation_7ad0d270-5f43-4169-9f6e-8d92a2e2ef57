<script setup lang="ts">
import { useRoute } from 'vue-router';
import { ref, computed } from 'vue';

const route = useRoute();
const discountId = computed(() => route.params.discountId as string);

// Mock: ดึงข้อมูล discount จาก mock data หรือ API จริงในอนาคต
const discount = ref({
  id: discountId.value,
  nameI18n: { th: 'ส่วนลดต้อนรับ', en: 'Welcome Discount' },
  descriptionI18n: { th: 'ลด 10% สำหรับลูกค้าใหม่', en: '10% off for new customers' },
  type: 'percentage',
  value: 10,
  maxDiscountAmount: 500,
  target: 'all',
  status: 'active',
  startDate: '2024-01-01T00:00:00Z',
  endDate: '2024-12-31T23:59:59Z',
  code: 'WELCOME10',
  bannerImage: '',
  highlightColor: '',
  displayOrder: 0,
  createdBy: 'admin',
  updatedBy: 'admin',
  conditions: {
    minOrderAmount: 1000,
    maxOrderAmount: 0,
    minQuantity: 0,
    maxQuantity: 0,
    applicableCategories: [],
    applicableProducts: [],
    excludedProducts: [],
    userGroups: [],
    firstTimeOnly: true,
    usageLimit: 1000,
    usageLimitPerUser: 1,
    allowCombinable: false,
    allowedDaysOfWeek: [],
    allowedTimeRange: { start: '', end: '' },
    channel: [],
  },
  totalUsage: 156,
  totalDiscountAmount: 12000,
  createdAt: '2024-01-01T09:00:00Z',
  updatedAt: '2024-01-10T09:00:00Z',
});

const formatCurrency = (amount: number) => new Intl.NumberFormat('th-TH', { style: 'currency', currency: 'THB' }).format(amount);
const formatDate = (date: string) => new Date(date).toLocaleDateString('th-TH', { year: 'numeric', month: 'short', day: 'numeric' });
</script>
<template>
  <div class="max-w-2xl mx-auto py-8 space-y-6">
    <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">รายละเอียดส่วนลด</h1>
    <UCard>
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <span class="text-lg font-semibold">{{ discount.nameI18n.th }}</span>
          <span v-if="discount.nameI18n.en" class="text-sm text-gray-500">({{ discount.nameI18n.en }})</span>
          <span class="px-2 py-1 text-xs rounded-full font-medium bg-blue-100 text-blue-800">{{ discount.type }}</span>
          <span class="px-2 py-1 text-xs rounded-full font-medium bg-purple-100 text-purple-800">{{ discount.target }}</span>
          <span class="px-2 py-1 text-xs rounded-full font-medium bg-green-100 text-green-800">{{ discount.status }}</span>
        </div>
        <div class="text-gray-600 dark:text-gray-400">{{ discount.descriptionI18n.th }}</div>
        <div v-if="discount.descriptionI18n.en" class="text-gray-400 text-xs">{{ discount.descriptionI18n.en }}</div>
        <div class="flex flex-wrap gap-4 mt-2">
          <div><span class="font-medium">มูลค่าส่วนลด:</span> {{ discount.value }}</div>
          <div v-if="discount.maxDiscountAmount"><span class="font-medium">สูงสุด:</span> {{ formatCurrency(discount.maxDiscountAmount) }}</div>
          <div><span class="font-medium">รหัส:</span> {{ discount.code || '-' }}</div>
        </div>
        <div class="flex flex-wrap gap-4 mt-2">
          <div><span class="font-medium">วันที่เริ่ม:</span> {{ formatDate(discount.startDate) }}</div>
          <div><span class="font-medium">วันหมดอายุ:</span> {{ formatDate(discount.endDate) }}</div>
        </div>
        <div class="flex flex-wrap gap-4 mt-2">
          <div><span class="font-medium">Banner:</span> {{ discount.bannerImage || '-' }}</div>
          <div><span class="font-medium">Highlight:</span> {{ discount.highlightColor || '-' }}</div>
          <div><span class="font-medium">ลำดับแสดงผล:</span> {{ discount.displayOrder }}</div>
        </div>
        <div class="flex flex-wrap gap-4 mt-2">
          <div><span class="font-medium">สร้างโดย:</span> {{ discount.createdBy || '-' }}</div>
          <div><span class="font-medium">แก้ไขโดย:</span> {{ discount.updatedBy || '-' }}</div>
        </div>
        <div class="flex flex-wrap gap-4 mt-2">
          <div><span class="font-medium">สร้างเมื่อ:</span> {{ formatDate(discount.createdAt) }}</div>
          <div><span class="font-medium">แก้ไขล่าสุด:</span> {{ formatDate(discount.updatedAt) }}</div>
        </div>
        <div class="flex flex-wrap gap-4 mt-2">
          <div><span class="font-medium">จำนวนใช้:</span> {{ discount.totalUsage }}</div>
          <div><span class="font-medium">ยอดส่วนลดรวม:</span> {{ formatCurrency(discount.totalDiscountAmount) }}</div>
        </div>
        <div class="mt-4">
          <div class="font-semibold mb-2">เงื่อนไขการใช้ส่วนลด</div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
            <div>ยอดขั้นต่ำ: {{ discount.conditions.minOrderAmount }}</div>
            <div>ยอดสูงสุด: {{ discount.conditions.maxOrderAmount }}</div>
            <div>จำนวนสินค้าขั้นต่ำ: {{ discount.conditions.minQuantity }}</div>
            <div>จำนวนสินค้าสูงสุด: {{ discount.conditions.maxQuantity }}</div>
            <div>หมวดหมู่ที่ใช้ได้: {{ discount.conditions.applicableCategories?.join(', ') || '-' }}</div>
            <div>สินค้าที่ใช้ได้: {{ discount.conditions.applicableProducts?.join(', ') || '-' }}</div>
            <div>สินค้าที่ไม่ร่วมรายการ: {{ discount.conditions.excludedProducts?.join(', ') || '-' }}</div>
            <div>กลุ่มผู้ใช้: {{ discount.conditions.userGroups?.join(', ') || '-' }}</div>
            <div>ลูกค้าใหม่เท่านั้น: {{ discount.conditions.firstTimeOnly ? 'ใช่' : 'ไม่ใช่' }}</div>
            <div>จำนวนครั้งที่ใช้ได้ทั้งหมด: {{ discount.conditions.usageLimit }}</div>
            <div>จำนวนครั้งต่อผู้ใช้: {{ discount.conditions.usageLimitPerUser }}</div>
            <div>ใช้ร่วมกับโปรอื่นได้: {{ discount.conditions.allowCombinable ? 'ใช่' : 'ไม่ใช่' }}</div>
            <div>วันที่ใช้ได้: {{ discount.conditions.allowedDaysOfWeek?.join(', ') || '-' }}</div>
            <div>ช่วงเวลา: {{ discount.conditions.allowedTimeRange?.start || '-' }} - {{ discount.conditions.allowedTimeRange?.end || '-' }}</div>
            <div>ช่องทางที่ใช้ได้: {{ discount.conditions.channel?.join(', ') || '-' }}</div>
          </div>
        </div>
      </div>
    </UCard>
  </div>
</template> 