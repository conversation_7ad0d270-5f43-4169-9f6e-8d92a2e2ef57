<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route: any = useRoute();
const siteId = computed(() => route.params.siteId);

// State
const loading = ref(false);
const searchQuery = ref('');
const selectedStatus = ref('all');
const selectedType = ref('all');
const sortBy = ref('created_at');
const sortOrder = ref('desc');
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Status options
const statuses = ref([
  { value: 'all', label: 'ทุกสถานะ' },
  { value: 'active', label: 'ใช้งาน' },
  { value: 'inactive', label: 'ไม่ใช้งาน' },
  { value: 'expired', label: 'หมดอายุ' },
]);

const types = ref([
  { value: 'all', label: 'ทุกประเภท' },
  { value: 'percentage', label: 'เปอร์เซ็นต์' },
  { value: 'fixed', label: 'ลดราคาคงที่' },
  { value: 'free_shipping', label: 'ส่งฟรี' },
]);

const discounts = ref([
  {
    id: '1',
    code: 'WELCOME10',
    name: 'ส่วนลดต้อนรับ',
    description: 'ลด 10% สำหรับลูกค้าใหม่',
    type: 'percentage',
    value: 10,
    minOrderAmount: 1000,
    maxDiscount: 500,
    usageLimit: 1000,
    usedCount: 156,
    status: 'active',
    startDate: '2024-01-01T00:00:00Z',
    endDate: '2024-12-31T23:59:59Z',
    createdAt: '2024-01-01T09:00:00Z',
    createdBy: 'admin',
    applicableProducts: ['all'],
    applicableCategories: ['electronics', 'clothing'],
    firstTimeOnly: true,
    requireCode: true,
  },
  {
    id: '2',
    code: '',
    name: 'Flash Sale 20%',
    description: 'ลด 20% อัตโนมัติสำหรับสินค้าแฟลชเซล',
    type: 'percentage',
    value: 20,
    minOrderAmount: 500,
    maxDiscount: 1000,
    usageLimit: 100,
    usedCount: 100,
    status: 'active',
    startDate: '2024-01-10T00:00:00Z',
    endDate: '2024-01-12T23:59:59Z',
    createdAt: '2024-01-10T09:00:00Z',
    createdBy: 'admin',
    applicableProducts: ['flash_sale'],
    applicableCategories: ['electronics'],
    firstTimeOnly: false,
    requireCode: false, // auto discount
  },
  {
    id: '3',
    code: 'SAVE500',
    name: 'ประหยัด 500 บาท',
    description: 'ลดราคา 500 บาทเมื่อสั่งซื้อขั้นต่ำ 3000 บาท',
    type: 'fixed',
    value: 500,
    minOrderAmount: 3000,
    maxDiscount: 500,
    usageLimit: 500,
    usedCount: 89,
    status: 'active',
    startDate: '2024-01-15T00:00:00Z',
    endDate: '2024-02-15T23:59:59Z',
    createdAt: '2024-01-15T10:30:00Z',
    createdBy: 'admin',
    applicableProducts: ['all'],
    applicableCategories: ['all'],
    firstTimeOnly: false,
    requireCode: true,
  },
  {
    id: '4',
    code: '',
    name: 'ส่งฟรีอัตโนมัติ',
    description: 'ส่งฟรีเมื่อสั่งซื้อขั้นต่ำ 1000 บาท',
    type: 'free_shipping',
    value: 0,
    minOrderAmount: 1000,
    maxDiscount: 0,
    usageLimit: 2000,
    usedCount: 342,
    status: 'active',
    startDate: '2024-01-01T00:00:00Z',
    endDate: '2024-03-31T23:59:59Z',
    createdAt: '2024-01-01T08:00:00Z',
    createdBy: 'admin',
    applicableProducts: ['all'],
    applicableCategories: ['all'],
    firstTimeOnly: false,
    requireCode: false, // auto discount
  },
]);

// Analytics data
const analytics = ref({
  totalDiscounts: 15,
  activeDiscounts: 12,
  totalUsage: 710,
  totalSavings: 125000,
  averageDiscount: 176,
  conversionRate: 8.5,
});

// Computed filtered discounts
const filteredDiscounts = computed(() => {
  let filtered = discounts.value;

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(
      discount =>
        discount.code.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        discount.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        discount.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  // Status filter
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(discount => discount.status === selectedStatus.value);
  }

  // Type filter
  if (selectedType.value !== 'all') {
    filtered = filtered.filter(discount => discount.type === selectedType.value);
  }

  // Sort
  filtered.sort((a, b) => {
    let aVal = a[sortBy.value as keyof typeof a];
    let bVal = b[sortBy.value as keyof typeof b];

    if (typeof aVal === 'string') {
      aVal = aVal.toLowerCase();
      bVal = bVal.toLowerCase();
    }

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });

  return filtered;
});

// Paginated discounts
const paginatedDiscounts = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredDiscounts.value.slice(start, end);
});

// Total pages
const totalPages = computed(() => {
  return Math.ceil(filteredDiscounts.value.length / itemsPerPage.value);
});

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
  }).format(amount);
};

// Format date
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

// Get status badge class
const getStatusBadgeClass = (status: string) => {
  const classes = {
    active: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
    expired: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
  };
  return classes[status as keyof typeof classes] || classes.inactive;
};

// Get status label
const getStatusLabel = (status: string) => {
  const labels = {
    active: 'ใช้งาน',
    inactive: 'ไม่ใช้งาน',
    expired: 'หมดอายุ',
  };
  return labels[status as keyof typeof labels] || status;
};

// Get type badge class
const getTypeBadgeClass = (type: string) => {
  const classes = {
    percentage: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    fixed: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
    free_shipping: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
  };
  return classes[type as keyof typeof classes] || classes.percentage;
};

// Get type label
const getTypeLabel = (type: string) => {
  const labels = {
    percentage: 'เปอร์เซ็นต์',
    fixed: 'ลดราคาคงที่',
    free_shipping: 'ส่งฟรี',
  };
  return labels[type as keyof typeof labels] || type;
};

// Get discount display
const getDiscountDisplay = (discount: any) => {
  if (discount.type === 'percentage') {
    return `${discount.value}%`;
  } else if (discount.type === 'fixed') {
    return formatCurrency(discount.value);
  } else if (discount.type === 'free_shipping') {
    return 'ส่งฟรี';
  }
  return '';
};

// Check if discount is expired
const isExpired = (discount: any) => {
  return new Date() > new Date(discount.endDate);
};

// Check if discount is active
const isActive = (discount: any) => {
  const now = new Date();
  const startDate = new Date(discount.startDate);
  const endDate = new Date(discount.endDate);
  return now >= startDate && now <= endDate && discount.status === 'active';
};

// Methods
const handleSort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = field;
    sortOrder.value = 'desc';
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handleViewDiscount = (discount: any) => {
  navigateTo(`/dashboard/${siteId.value}/discounts/${discount.id}`);
};

const handleEditDiscount = (discount: any) => {
  navigateTo(`/dashboard/${siteId.value}/discounts/${discount.id}/edit`);
};

const handleToggleStatus = async (discount: any) => {
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    discount.status = discount.status === 'active' ? 'inactive' : 'active';

    useToast().add({
      title: 'สำเร็จ',
      description: `อัปเดตสถานะส่วนลด ${discount.name} เรียบร้อยแล้ว`,
      color: 'success',
    });
  } catch (error) {
    useToast().add({
      title: 'ผิดพลาด',
      description: 'ไม่สามารถอัปเดตสถานะได้',
      color: 'error',
    });
  }
};

const handleDeleteDiscount = async (discount: any) => {
  if (confirm(`คุณต้องการลบส่วนลด "${discount.name}" ใช่หรือไม่?`)) {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const index = discounts.value.findIndex(d => d.id === discount.id);
      if (index > -1) {
        discounts.value.splice(index, 1);
      }

      useToast().add({
        title: 'สำเร็จ',
        description: `ลบส่วนลด ${discount.name} เรียบร้อยแล้ว`,
        color: 'success',
      });
    } catch (error) {
      useToast().add({
        title: 'ผิดพลาด',
        description: 'ไม่สามารถลบส่วนลดได้',
        color: 'error',
      });
    }
  }
};

const handleCreateDiscount = () => {
  navigateTo(`/dashboard/${siteId.value}/discounts/create`);
};

const handleExportDiscounts = () => {
  useToast().add({
    title: 'สำเร็จ',
    description: 'ส่งออกข้อมูลส่วนลดเรียบร้อยแล้ว',
    color: 'success',
  });
};
</script>

<template>
  <div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          ส่วนลด
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          จัดการส่วนลดและโปรโมชั่นต่างๆ
        </p>
      </div>
      <div class="flex items-center gap-2">
        <UButton @click="handleExportDiscounts" variant="outline" icon="i-heroicons-arrow-down-tray">
          ส่งออก
        </UButton>
        <UButton @click="handleCreateDiscount"
          class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
          <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
          สร้างส่วนลดใหม่
        </UButton>
      </div>
    </div>

    <!-- Analytics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ส่วนลดทั้งหมด</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalDiscounts }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">{{ analytics.activeDiscounts }} ใช้งาน</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-ticket" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">การใช้งานรวม</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalUsage }}</p>
            <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">ครั้ง</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-chart-bar" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ประหยัดรวม</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatCurrency(analytics.totalSavings) }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">เฉลี่ย {{
              formatCurrency(analytics.averageDiscount) }}/ครั้ง</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-currency-dollar" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">อัตราการแปลง</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.conversionRate }}%</p>
            <p class="text-xs text-orange-600 dark:text-orange-400 mt-1">จากผู้เข้าชม</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-arrow-trending-up" class="w-8 h-8" />
          </div>
        </div>
      </UCard>
    </div>

    <!-- Filters -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-funnel" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวกรองและค้นหา</h2>
        </div>
      </template>

      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหาส่วนลด</label>
          <UInput v-model="searchQuery" placeholder="ค้นหาตามรหัสหรือชื่อส่วนลด..." icon="i-heroicons-magnifying-glass"
            size="xl" class="w-full" />
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สถานะ</label>
          <USelect v-model="selectedStatus" :items="statuses" option-attribute="label" value-attribute="value"
            placeholder="เลือกสถานะ" size="xl" class="w-full" />
        </div>

        <!-- Type Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ประเภท</label>
          <USelect v-model="selectedType" :items="types" option-attribute="label" value-attribute="value"
            placeholder="เลือกประเภท" size="xl" class="w-full" />
        </div>

        <!-- Sort -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เรียงลำดับ</label>
          <div class="flex gap-2">
            <USelect v-model="sortBy" :items="[
              { value: 'created_at', label: 'วันที่สร้าง' },
              { value: 'usedCount', label: 'จำนวนใช้' },
              { value: 'value', label: 'มูลค่าส่วนลด' },
              { value: 'endDate', label: 'วันหมดอายุ' }
            ]" option-attribute="label" value-attribute="value" size="xl" class="flex-1" />
            <UButton @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'" variant="outline" size="sm" class="px-3">
              <Icon :name="sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down'" class="w-4 h-4" />
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Discounts Table -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <Icon name="i-heroicons-ticket" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รายการส่วนลด</h2>
            <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-400 rounded-full">
              {{ filteredDiscounts.length }} รายการ
            </span>
          </div>
        </div>
      </template>

      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="flex items-center gap-3">
          <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-blue-600" />
          <span class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</span>
        </div>
      </div>

      <div v-else-if="paginatedDiscounts.length === 0" class="text-center py-12">
        <div class="text-gray-400 dark:text-gray-500 mb-4">
          <Icon name="i-heroicons-ticket" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">ไม่พบส่วนลด</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          ยังไม่มีส่วนลดในระบบ หรือไม่ตรงกับเงื่อนไขการค้นหา
        </p>
        <UButton @click="handleCreateDiscount" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
          สร้างส่วนลดแรก
        </UButton>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">ส่วนลด</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">ประเภท</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">เงื่อนไข</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>การใช้งาน</span>
                  <UButton @click="handleSort('usedCount')" variant="ghost" size="xs" class="p-1">
                    <Icon
                      :name="sortBy === 'usedCount' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'"
                      class="w-3 h-3" />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">สถานะ</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">วันหมดอายุ</th>
              <th class="text-right py-4 px-6 font-semibold text-gray-900 dark:text-white">การดำเนินการ</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="discount in paginatedDiscounts" :key="discount.id"
              class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
              <td class="py-4 px-6">
                <div class="flex items-center gap-3">
                  <div
                    class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-semibold text-sm">
                    {{ discount.code.slice(0, 3) }}
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">{{ discount.name }}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ discount.description }}</p>
                  </div>
                </div>
              </td>
              <td class="py-4 px-6">
                <div class="space-y-1">
                  <span class="px-3 py-1 text-xs rounded-full font-medium" :class="getTypeBadgeClass(discount.type)">
                    {{ getTypeLabel(discount.type) }}
                  </span>
                  <p class="text-sm font-semibold text-gray-900 dark:text-white">
                    {{ getDiscountDisplay(discount) }}
                  </p>
                </div>
              </td>
              <td class="py-4 px-6">
                <div class="space-y-1 text-sm">
                  <p class="text-gray-600 dark:text-gray-400">
                    ขั้นต่ำ {{ formatCurrency(discount.minOrderAmount) }}
                  </p>
                  <p v-if="discount.maxDiscount > 0" class="text-gray-600 dark:text-gray-400">
                    สูงสุด {{ formatCurrency(discount.maxDiscount) }}
                  </p>
                  <p v-if="discount.firstTimeOnly" class="text-xs text-orange-600 dark:text-orange-400">
                    ลูกค้าใหม่เท่านั้น
                  </p>
                </div>
              </td>
              <td class="py-4 px-6">
                <div class="space-y-1">
                  <p class="font-semibold text-gray-900 dark:text-white">
                    {{ discount.usedCount }}/{{ discount.usageLimit }}
                  </p>
                  <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      :style="{ width: `${(discount.usedCount / discount.usageLimit) * 100}%` }"></div>
                  </div>
                </div>
              </td>
              <td class="py-4 px-6">
                <span class="px-3 py-1 text-xs rounded-full font-medium" :class="getStatusBadgeClass(discount.status)">
                  {{ getStatusLabel(discount.status) }}
                </span>
                <div v-if="isExpired(discount)" class="text-xs text-red-600 dark:text-red-400 mt-1">
                  หมดอายุแล้ว
                </div>
                <div v-else-if="!isActive(discount)" class="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  ยังไม่เริ่ม
                </div>
              </td>
              <td class="py-4 px-6">
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {{ formatDate(discount.endDate) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center justify-end gap-2">
                  <UButton @click="handleViewDiscount(discount)" variant="ghost" size="sm"
                    class="hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400">
                    <Icon name="i-heroicons-eye" class="w-4 h-4" />
                  </UButton>
                  <UButton @click="handleEditDiscount(discount)" variant="ghost" size="sm"
                    class="hover:bg-green-50 dark:hover:bg-green-900/20 text-green-600 dark:text-green-400">
                    <Icon name="i-heroicons-pencil-square" class="w-4 h-4" />
                  </UButton>
                  <UButton @click="handleToggleStatus(discount)" variant="ghost" size="sm"
                    class="hover:bg-yellow-50 dark:hover:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400">
                    <Icon :name="discount.status === 'active' ? 'i-heroicons-pause' : 'i-heroicons-play'"
                      class="w-4 h-4" />
                  </UButton>
                  <UButton @click="handleDeleteDiscount(discount)" variant="ghost" size="sm"
                    class="hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400">
                    <Icon name="i-heroicons-trash" class="w-4 h-4" />
                  </UButton>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1"
        class="flex items-center justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} ถึง {{ Math.min(currentPage * itemsPerPage,
            filteredDiscounts.length) }}
          จาก {{ filteredDiscounts.length }} รายการ
        </div>
        <div class="flex items-center gap-2">
          <UButton @click="handlePageChange(currentPage - 1)" :disabled="currentPage === 1" variant="outline" size="sm">
            <Icon name="i-heroicons-chevron-left" class="w-4 h-4" />
          </UButton>

          <div class="flex items-center gap-1">
            <UButton v-for="page in Math.min(5, totalPages)" :key="page" @click="handlePageChange(page)"
              :variant="currentPage === page ? 'solid' : 'outline'" size="sm" class="w-8 h-8 p-0">
              {{ page }}
            </UButton>
          </div>

          <UButton @click="handlePageChange(currentPage + 1)" :disabled="currentPage === totalPages" variant="outline"
            size="sm">
            <Icon name="i-heroicons-chevron-right" class="w-4 h-4" />
          </UButton>
        </div>
      </div>
    </UCard>
  </div>
</template>

<!-- <style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Line clamp utility */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>  -->