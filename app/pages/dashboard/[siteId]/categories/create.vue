<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useVuelidate } from '@vuelidate/core';
import { required, minLength, maxLength, helpers } from '@vuelidate/validators';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// Form data
const form = reactive({
  name: '',
  slug: '',
  description: '',
  parentId: '',
  image: '',
  isActive: true,
  sortOrder: 1,
  seoTitle: '',
  seoDescription: '',
  seoKeywords: '',
});

// Validation rules
const rules = computed(() => ({
  name: {
    required: helpers.withMessage('ชื่อหมวดหมู่ต้องไม่ว่าง', required),
    minLength: helpers.withMessage('ชื่อหมวดหมู่ต้องมีอย่างน้อย 2 ตัวอักษร', minLength(2)),
    maxLength: helpers.withMessage('ชื่อหมวดหมู่ต้องไม่เกิน 100 ตัวอักษร', maxLength(100)),
  },
  slug: {
    required: helpers.withMessage('Slug ต้องไม่ว่าง', required),
    minLength: helpers.withMessage('Slug ต้องมีอย่างน้อย 2 ตัวอักษร', minLength(2)),
    maxLength: helpers.withMessage('Slug ต้องไม่เกิน 100 ตัวอักษร', maxLength(100)),
  },
  description: {
    maxLength: helpers.withMessage('คำอธิบายต้องไม่เกิน 500 ตัวอักษร', maxLength(500)),
  },
  seoTitle: {
    maxLength: helpers.withMessage('SEO Title ต้องไม่เกิน 60 ตัวอักษร', maxLength(60)),
  },
  seoDescription: {
    maxLength: helpers.withMessage('SEO Description ต้องไม่เกิน 160 ตัวอักษร', maxLength(160)),
  },
}));

// Initialize Vuelidate
const v$ = useVuelidate(rules, form);

const loading = ref(false);

// Mock parent categories
const parentCategories = ref([
  { id: '', label: 'ไม่มีหมวดหมู่หลัก (หมวดหมู่รูท)' },
  { id: '1', label: 'อิเล็กทรอนิกส์' },
  { id: '4', label: 'เสื้อผ้า' },
  { id: '7', label: 'หนังสือ' },
]);

// Auto-generate slug from name
watch(() => form.name, (newName) => {
  if (newName && !form.slug) {
    form.slug = newName
      .toLowerCase()
      .replace(/[^a-z0-9ก-๙\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }
});

// Auto-generate SEO title from name
watch(() => form.name, (newName) => {
  if (newName && !form.seoTitle) {
    form.seoTitle = newName;
  }
});

// Handle image upload
const uploadedImage = ref<string | null>(null);
const handleImageUpload = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = (e) => {
    uploadedImage.value = e.target?.result as string;
    form.image = uploadedImage.value;
  };
  reader.readAsDataURL(file);
};

// Remove image
const removeImage = () => {
  uploadedImage.value = null;
  form.image = '';
};

// Handle form submission
const handleSubmit = async () => {
  // Validate form using Vuelidate
  const isValid = await v$.value.$validate();
  if (!isValid) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: 'กรุณาตรวจสอบข้อมูลที่กรอก',
      color: 'error',
    });
    return;
  }

  loading.value = true;
  try {
    // Mock API call
    const categoryData = {
      name: form.name,
      slug: form.slug,
      description: form.description,
      parentId: form.parentId || null,
      image: form.image,
      isActive: form.isActive,
      sortOrder: form.sortOrder,
      seoTitle: form.seoTitle,
      seoDescription: form.seoDescription,
      seoKeywords: form.seoKeywords ? form.seoKeywords.split(',').map(k => k.trim()) : [],
    };

    console.log('Creating category:', categoryData);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    useToast().add({
      title: 'สำเร็จ',
      description: 'สร้างหมวดหมู่เรียบร้อยแล้ว',
      color: 'success',
    });

    // Navigate back to categories page
    await navigateTo(`/dashboard/${siteId.value}/categories`);
  } catch (error: any) {
    console.error('Error creating category:', error);
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: error.message || 'ไม่สามารถสร้างหมวดหมู่ได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// Handle cancel
const handleCancel = () => {
  navigateTo(`/dashboard/${siteId.value}/categories`);
};

// Generate slug manually
const generateSlug = () => {
  if (form.name) {
    form.slug = form.name
      .toLowerCase()
      .replace(/[^a-z0-9ก-๙\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }
};

// Preview URL
const previewUrl = computed(() => {
  if (form.slug) {
    return `https://yourstore.com/categories/${form.slug}`;
  }
  return '';
});
</script>

<template>
  <div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          เพิ่มหมวดหมู่ใหม่
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          สร้างหมวดหมู่สินค้าใหม่สำหรับร้านค้า
        </p>
      </div>
      <div class="flex items-center gap-3">
        <UButton @click="handleCancel" variant="outline" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
          <Icon name="i-heroicons-arrow-left" class="w-4 h-4 mr-2" />
          ย้อนกลับ
        </UButton>
        <UButton @click="handleSubmit" :loading="loading"
          class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
          <Icon name="i-heroicons-check" class="w-5 h-5 mr-2" />
          สร้างหมวดหมู่
        </UButton>
      </div>
    </div>

    <!-- Form -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Form -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                <Icon name="i-heroicons-information-circle" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ข้อมูลพื้นฐาน</h2>
            </div>
          </template>

          <div class="space-y-6">
            <!-- Category Name -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ชื่อหมวดหมู่ <span class="text-red-500">*</span>
              </label>
              <UInput v-model="form.name" placeholder="กรอกชื่อหมวดหมู่"
                :error="v$.name.$error ? v$.name.$errors[0].$message : ''" class="w-full" />
            </div>

            <!-- Slug -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Slug (URL) <span class="text-red-500">*</span>
              </label>
              <div class="flex gap-2">
                <UInput v-model="form.slug" placeholder="category-slug"
                  :error="v$.slug.$error ? v$.slug.$errors[0].$message : ''" class="flex-1" />
                <UButton @click="generateSlug" variant="outline" size="sm">
                  สร้าง
                </UButton>
              </div>
              <p v-if="previewUrl" class="text-xs text-gray-500 mt-1">
                URL: {{ previewUrl }}
              </p>
            </div>

            <!-- Description -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                คำอธิบาย
              </label>
              <UTextarea v-model="form.description" placeholder="อธิบายหมวดหมู่นี้"
                :error="v$.description.$error ? v$.description.$errors[0].$message : ''" :rows="4" class="w-full" />
            </div>

            <!-- Parent Category -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                หมวดหมู่หลัก
              </label>
              <USelect v-model="form.parentId" :items="parentCategories" option-attribute="label" value-attribute="id"
                placeholder="เลือกหมวดหมู่หลัก" class="w-full" />
              <p class="text-xs text-gray-500 mt-1">
                เลือกหมวดหมู่หลักหากต้องการสร้างหมวดหมู่ย่อย
              </p>
            </div>

            <!-- Sort Order -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ลำดับการแสดง
              </label>
              <UInput v-model="form.sortOrder" type="number" placeholder="1" class="w-full" />
              <p class="text-xs text-gray-500 mt-1">
                ตัวเลขที่น้อยกว่าจะแสดงก่อน
              </p>
            </div>
          </div>
        </UCard>

        <!-- Image -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                <Icon name="i-heroicons-photo" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รูปภาพหมวดหมู่</h2>
            </div>
          </template>

          <div class="space-y-4">
            <!-- Image Upload -->
            <div v-if="!uploadedImage"
              class="border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-8 text-center">
              <input type="file" accept="image/*" @change="handleImageUpload" class="sr-only" id="image-upload" />
              <label for="image-upload" class="cursor-pointer flex flex-col items-center gap-4">
                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                  <Icon name="i-heroicons-camera" class="w-8 h-8 text-gray-400" />
                </div>
                <div>
                  <p class="text-lg font-medium text-gray-900 dark:text-white">อัปโหลดรูปภาพ</p>
                  <p class="text-sm text-gray-500">PNG, JPG, WEBP ขนาดไม่เกิน 5MB</p>
                </div>
              </label>
            </div>

            <!-- Image Preview -->
            <div v-else class="relative">
              <img :src="uploadedImage" alt="Category image" class="w-full h-48 object-cover rounded-lg" />
              <UButton @click="removeImage" size="sm" color="red" variant="solid" class="absolute top-2 right-2">
                <Icon name="i-heroicons-x-mark" class="w-4 h-4" />
              </UButton>
            </div>
          </div>
        </UCard>

        <!-- SEO -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg">
                <Icon name="i-heroicons-magnifying-glass" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">SEO</h2>
            </div>
          </template>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SEO Title
              </label>
              <UInput v-model="form.seoTitle" placeholder="SEO Title (ไม่เกิน 60 ตัวอักษร)"
                :error="v$.seoTitle.$error ? v$.seoTitle.$errors[0].$message : ''" class="w-full" />
              <p class="text-xs text-gray-500 mt-1">
                {{ form.seoTitle.length }}/60 ตัวอักษร
              </p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SEO Description
              </label>
              <UTextarea v-model="form.seoDescription" placeholder="SEO Description (ไม่เกิน 160 ตัวอักษร)"
                :error="v$.seoDescription.$error ? v$.seoDescription.$errors[0].$message : ''" :rows="3"
                class="w-full" />
              <p class="text-xs text-gray-500 mt-1">
                {{ form.seoDescription.length }}/160 ตัวอักษร
              </p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SEO Keywords
              </label>
              <UInput v-model="form.seoKeywords" placeholder="คำสำคัญ, คั่นด้วยจุลภาค" class="w-full" />
              <p class="text-xs text-gray-500 mt-1">
                แยกคำสำคัญด้วยจุลภาค เช่น อิเล็กทรอนิกส์, โทรศัพท์, แท็บเล็ต
              </p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Settings Sidebar -->
      <div class="space-y-6">
        <!-- Status -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-red-500 to-red-600 rounded-lg">
                <Icon name="i-heroicons-flag" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">สถานะ</h2>
            </div>
          </template>

          <div class="space-y-4">
            <UCheckbox v-model="form.isActive" label="เปิดใช้งานหมวดหมู่" />
            <p class="text-sm text-gray-600 dark:text-gray-400">
              หมวดหมู่ที่เปิดใช้งานจะแสดงในหน้าร้านค้า
            </p>
          </div>
        </UCard>

        <!-- Preview -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
                <Icon name="i-heroicons-eye" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวอย่าง</h2>
            </div>
          </template>

          <div class="space-y-4">
            <!-- Category Preview -->
            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div class="flex items-center gap-3 mb-3">
                <div class="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
                  <img v-if="uploadedImage" :src="uploadedImage" alt="Preview" class="w-full h-full object-cover" />
                  <div v-else class="w-full h-full flex items-center justify-center">
                    <Icon name="i-heroicons-folder" class="w-6 h-6 text-gray-400" />
                  </div>
                </div>
                <div class="flex-1">
                  <h3 class="font-medium text-gray-900 dark:text-white">
                    {{ form.name || 'ชื่อหมวดหมู่' }}
                  </h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ form.description || 'คำอธิบายหมวดหมู่' }}
                  </p>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <UBadge :color="form.isActive ? 'green' : 'red'" variant="subtle" size="sm">
                  {{ form.isActive ? 'เปิดใช้งาน' : 'ปิดใช้งาน' }}
                </UBadge>
                <UBadge variant="outline" size="sm">
                  ลำดับ {{ form.sortOrder }}
                </UBadge>
              </div>
            </div>

            <!-- SEO Preview -->
            <div v-if="form.seoTitle || form.seoDescription"
              class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ตัวอย่าง SEO
              </h4>
              <div class="space-y-1">
                <h5 class="text-blue-600 text-sm font-medium">
                  {{ form.seoTitle || form.name }}
                </h5>
                <p class="text-green-600 text-xs">
                  {{ previewUrl }}
                </p>
                <p class="text-gray-600 text-sm">
                  {{ form.seoDescription || form.description }}
                </p>
              </div>
            </div>
          </div>
        </UCard>

        <!-- Tips -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg">
                <Icon name="i-heroicons-light-bulb" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">เคล็ดลับ</h2>
            </div>
          </template>

          <div class="space-y-3 text-sm text-gray-600 dark:text-gray-400">
            <div class="flex items-start gap-2">
              <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
              <p>ใช้ชื่อหมวดหมู่ที่สื่อความหมายชัดเจนและเข้าใจง่าย</p>
            </div>
            <div class="flex items-start gap-2">
              <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
              <p>Slug ควรเป็นภาษาอังกฤษและไม่มีช่องว่าง</p>
            </div>
            <div class="flex items-start gap-2">
              <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
              <p>เพิ่มรูปภาพที่เกี่ยวข้องเพื่อดึงดูดลูกค้า</p>
            </div>
            <div class="flex items-start gap-2">
              <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
              <p>กรอก SEO Title และ Description เพื่อ SEO ที่ดี</p>
            </div>
          </div>
        </UCard>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8>* {
  animation: fadeInUp 0.6s ease-out forwards;
}
</style>