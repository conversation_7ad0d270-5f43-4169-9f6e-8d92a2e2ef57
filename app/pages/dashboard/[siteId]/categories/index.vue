<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// Mock data for categories
const categories = ref([
  {
    id: '1',
    name: 'อิเล็กทรอนิกส์',
    slug: 'electronics',
    description: 'สินค้าอิเล็กทรอนิกส์และอุปกรณ์เทคโนโลยี',
    image: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400',
    parentId: null,
    isActive: true,
    sortOrder: 1,
    productCount: 45,
    seoTitle: 'อิเล็กทรอนิกส์ - ร้านค้าออนไลน์',
    seoDescription: 'เลือกซื้อสินค้าอิเล็กทรอนิกส์คุณภาพดี ราคาดี',
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20',
    children: [
      {
        id: '2',
        name: 'โทรศัพท์มือถือ',
        slug: 'mobile-phones',
        description: 'โทรศัพท์มือถือและอุปกรณ์เสริม',
        image: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400',
        parentId: '1',
        isActive: true,
        sortOrder: 1,
        productCount: 25,
        seoTitle: 'โทรศัพท์มือถือ',
        seoDescription: 'โทรศัพท์มือถือรุ่นใหม่ล่าสุด',
        createdAt: '2024-01-16',
        updatedAt: '2024-01-21',
        children: []
      },
      {
        id: '3',
        name: 'คอมพิวเตอร์',
        slug: 'computers',
        description: 'คอมพิวเตอร์และอุปกรณ์คอมพิวเตอร์',
        image: 'https://images.unsplash.com/photo-1547082299-de196ea013d6?w=400',
        parentId: '1',
        isActive: true,
        sortOrder: 2,
        productCount: 20,
        seoTitle: 'คอมพิวเตอร์',
        seoDescription: 'คอมพิวเตอร์และอุปกรณ์คอมพิวเตอร์',
        createdAt: '2024-01-17',
        updatedAt: '2024-01-22',
        children: []
      }
    ]
  },
  {
    id: '4',
    name: 'เสื้อผ้า',
    slug: 'clothing',
    description: 'เสื้อผ้าและแฟชั่น',
    image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400',
    parentId: null,
    isActive: true,
    sortOrder: 2,
    productCount: 78,
    seoTitle: 'เสื้อผ้าแฟชั่น',
    seoDescription: 'เสื้อผ้าแฟชั่นสำหรับทุกเพศทุกวัย',
    createdAt: '2024-01-18',
    updatedAt: '2024-01-23',
    children: [
      {
        id: '5',
        name: 'เสื้อผ้าผู้ชาย',
        slug: 'mens-clothing',
        description: 'เสื้อผ้าสำหรับผู้ชาย',
        image: 'https://images.unsplash.com/photo-1516257984-b1b4d707412e?w=400',
        parentId: '4',
        isActive: true,
        sortOrder: 1,
        productCount: 35,
        seoTitle: 'เสื้อผ้าผู้ชาย',
        seoDescription: 'เสื้อผ้าแฟชั่นสำหรับผู้ชาย',
        createdAt: '2024-01-19',
        updatedAt: '2024-01-24',
        children: []
      },
      {
        id: '6',
        name: 'เสื้อผ้าผู้หญิง',
        slug: 'womens-clothing',
        description: 'เสื้อผ้าสำหรับผู้หญิง',
        image: 'https://images.unsplash.com/photo-1490481651871-ab68de25d43d?w=400',
        parentId: '4',
        isActive: true,
        sortOrder: 2,
        productCount: 43,
        seoTitle: 'เสื้อผ้าผู้หญิง',
        seoDescription: 'เสื้อผ้าแฟชั่นสำหรับผู้หญิง',
        createdAt: '2024-01-20',
        updatedAt: '2024-01-25',
        children: []
      }
    ]
  },
  {
    id: '7',
    name: 'หนังสือ',
    slug: 'books',
    description: 'หนังสือและสื่อการเรียนรู้',
    image: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400',
    parentId: null,
    isActive: true,
    sortOrder: 3,
    productCount: 32,
    seoTitle: 'หนังสือ',
    seoDescription: 'หนังสือและสื่อการเรียนรู้',
    createdAt: '2024-01-21',
    updatedAt: '2024-01-26',
    children: []
  }
]);

const loading = ref(false);
const searchQuery = ref('');
const selectedStatus = ref('all');
const viewMode = ref<'tree' | 'list'>('tree');

// Filter categories
const filteredCategories = computed(() => {
  let filtered = categories.value;

  if (searchQuery.value) {
    const searchRecursive = (cats: any[]): any[] => {
      return cats.filter(cat => {
        const matchesSearch = cat.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
          cat.description.toLowerCase().includes(searchQuery.value.toLowerCase());

        if (matchesSearch) return true;

        // Check children
        if (cat.children && cat.children.length > 0) {
          const filteredChildren = searchRecursive(cat.children);
          if (filteredChildren.length > 0) {
            cat.children = filteredChildren;
            return true;
          }
        }

        return false;
      });
    };

    filtered = searchRecursive(JSON.parse(JSON.stringify(filtered)));
  }

  if (selectedStatus.value !== 'all') {
    const filterByStatus = (cats: any[]): any[] => {
      return cats.filter(cat => {
        const matchesStatus = selectedStatus.value === 'active' ? cat.isActive : !cat.isActive;
        if (cat.children && cat.children.length > 0) {
          cat.children = filterByStatus(cat.children);
        }
        return matchesStatus;
      });
    };

    filtered = filterByStatus(filtered);
  }

  return filtered;
});

// Flatten categories for list view
const flattenedCategories = computed(() => {
  const flatten = (cats: any[], level = 0): any[] => {
    let result: any[] = [];
    for (const cat of cats) {
      result.push({ ...cat, level });
      if (cat.children && cat.children.length > 0) {
        result = result.concat(flatten(cat.children, level + 1));
      }
    }
    return result;
  };

  return flatten(filteredCategories.value);
});

// Status options for filter
const statusOptions = [
  { value: 'all', label: 'ทั้งหมด' },
  { value: 'active', label: 'เปิดใช้งาน' },
  { value: 'inactive', label: 'ปิดใช้งาน' },
];

// Actions
const handleCreate = () => {
  navigateTo(`/dashboard/${siteId.value}/categories/create`);
};

const handleEdit = (id: string) => {
  navigateTo(`/dashboard/${siteId.value}/categories/${id}/edit`);
};

const handleView = (id: string) => {
  navigateTo(`/dashboard/${siteId.value}/categories/${id}`);
};

const handleDuplicate = async (category: any) => {
  loading.value = true;
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    const duplicated = {
      ...category,
      id: Date.now().toString(),
      name: `${category.name} (สำเนา)`,
      slug: `${category.slug}-copy`,
      productCount: 0,
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0],
      children: []
    };

    categories.value.push(duplicated);

    useToast().add({
      title: 'สำเร็จ',
      description: 'คัดลอกหมวดหมู่เรียบร้อยแล้ว',
      color: 'success',
    });
  } catch (error) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: 'ไม่สามารถคัดลอกหมวดหมู่ได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

const handleToggleStatus = async (id: string) => {
  loading.value = true;
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 500));

    const toggleRecursive = (cats: any[]): void => {
      for (const cat of cats) {
        if (cat.id === id) {
          cat.isActive = !cat.isActive;
          cat.updatedAt = new Date().toISOString().split('T')[0];
          return;
        }
        if (cat.children && cat.children.length > 0) {
          toggleRecursive(cat.children);
        }
      }
    };

    toggleRecursive(categories.value);

    useToast().add({
      title: 'สำเร็จ',
      description: 'อัปเดตสถานะหมวดหมู่เรียบร้อยแล้ว',
      color: 'success',
    });
  } catch (error) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: 'ไม่สามารถอัปเดตสถานะได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

const handleDelete = async (id: string) => {
  const findCategory = (cats: any[], targetId: string): any => {
    for (const cat of cats) {
      if (cat.id === targetId) return cat;
      if (cat.children && cat.children.length > 0) {
        const found = findCategory(cat.children, targetId);
        if (found) return found;
      }
    }
    return null;
  };

  const category = findCategory(categories.value, id);
  if (!category) return;

  if (category.productCount > 0) {
    useToast().add({
      title: 'ไม่สามารถลบได้',
      description: `หมวดหมู่นี้มีสินค้า ${category.productCount} รายการ`,
      color: 'error',
    });
    return;
  }

  if (category.children && category.children.length > 0) {
    useToast().add({
      title: 'ไม่สามารถลบได้',
      description: 'หมวดหมู่นี้มีหมวดหมู่ย่อย กรุณาลบหมวดหมู่ย่อยก่อน',
      color: 'error',
    });
    return;
  }

  const confirmed = confirm('คุณแน่ใจหรือไม่ที่จะลบหมวดหมู่นี้?');
  if (!confirmed) return;

  loading.value = true;
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    const removeRecursive = (cats: any[], targetId: string): any[] => {
      return cats.filter(cat => {
        if (cat.id === targetId) return false;
        if (cat.children && cat.children.length > 0) {
          cat.children = removeRecursive(cat.children, targetId);
        }
        return true;
      });
    };

    categories.value = removeRecursive(categories.value, id);

    useToast().add({
      title: 'สำเร็จ',
      description: 'ลบหมวดหมู่เรียบร้อยแล้ว',
      color: 'success',
    });
  } catch (error) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: 'ไม่สามารถลบหมวดหมู่ได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// Stats
const stats = computed(() => {
  const countRecursive = (cats: any[]): { total: number; active: number; products: number } => {
    let total = 0;
    let active = 0;
    let products = 0;

    for (const cat of cats) {
      total++;
      if (cat.isActive) active++;
      products += cat.productCount;

      if (cat.children && cat.children.length > 0) {
        const childStats = countRecursive(cat.children);
        total += childStats.total;
        active += childStats.active;
        products += childStats.products;
      }
    }

    return { total, active, products };
  };

  return countRecursive(categories.value);
});
</script>

<template>
  <div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          หมวดหมู่สินค้า
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          จัดการหมวดหมู่สินค้าและโครงสร้างการจัดหมวดหมู่
        </p>
      </div>
      <div class="flex items-center gap-3">
        <UButton @click="handleCreate"
          class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
          <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
          เพิ่มหมวดหมู่ใหม่
        </UButton>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <UCard class="overflow-hidden">
        <div class="flex items-center gap-4">
          <div class="p-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
            <Icon name="i-heroicons-folder" class="w-6 h-6 text-white" />
          </div>
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">หมวดหมู่ทั้งหมด</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.total }}</p>
          </div>
        </div>
      </UCard>

      <UCard class="overflow-hidden">
        <div class="flex items-center gap-4">
          <div class="p-3 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
            <Icon name="i-heroicons-check-circle" class="w-6 h-6 text-white" />
          </div>
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">เปิดใช้งาน</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.active }}</p>
          </div>
        </div>
      </UCard>

      <UCard class="overflow-hidden">
        <div class="flex items-center gap-4">
          <div class="p-3 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-cube" class="w-6 h-6 text-white" />
          </div>
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">สินค้าทั้งหมด</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.products }}</p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Filters and View Toggle -->
    <UCard class="overflow-hidden">
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <UInput v-model="searchQuery" placeholder="ค้นหาหมวดหมู่..." class="w-full">
            <template #leading>
              <Icon name="i-heroicons-magnifying-glass" class="w-4 h-4" />
            </template>
          </UInput>
        </div>
        <div class="sm:w-48">
          <USelect v-model="selectedStatus" :items="statusOptions" option-attribute="label" value-attribute="value"
            placeholder="สถานะ" class="w-full" />
        </div>
        <div class="flex items-center gap-2">
          <UButton @click="viewMode = 'tree'" :variant="viewMode === 'tree' ? 'solid' : 'outline'" size="sm">
            <Icon name="i-heroicons-squares-2x2" class="w-4 h-4" />
          </UButton>
          <UButton @click="viewMode = 'list'" :variant="viewMode === 'list' ? 'solid' : 'outline'" size="sm">
            <Icon name="i-heroicons-list-bullet" class="w-4 h-4" />
          </UButton>
        </div>
      </div>
    </UCard>

    <!-- Tree View -->
    <div v-if="viewMode === 'tree'" class="space-y-4">
      <CategoryTreeItem v-for="category in filteredCategories" :key="category.id" :category="category"
        @edit="handleEdit" @view="handleView" @duplicate="handleDuplicate" @toggle-status="handleToggleStatus"
        @delete="handleDelete" />
    </div>

    <!-- List View -->
    <div v-else class="space-y-4">
      <UCard v-for="category in flattenedCategories" :key="category.id"
        class="overflow-hidden hover:shadow-lg transition-shadow duration-300">
        <div class="flex items-center gap-4">
          <!-- Indentation for hierarchy -->
          <div :style="{ marginLeft: `${category.level * 24}px` }" class="flex items-center gap-4 flex-1">
            <!-- Category Image -->
            <div class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden flex-shrink-0">
              <img v-if="category.image" :src="category.image" :alt="category.name"
                class="w-full h-full object-cover" />
              <div v-else class="w-full h-full flex items-center justify-center">
                <Icon name="i-heroicons-folder" class="w-6 h-6 text-gray-400" />
              </div>
            </div>

            <!-- Category Info -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2 mb-1">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                  {{ category.name }}
                </h3>
                <UBadge :color="category.isActive ? 'green' : 'red'" variant="subtle" size="sm">
                  {{ category.isActive ? 'เปิดใช้งาน' : 'ปิดใช้งาน' }}
                </UBadge>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-400 truncate mb-2">
                {{ category.description }}
              </p>
              <div class="flex items-center gap-4 text-sm text-gray-500">
                <span>{{ category.productCount }} สินค้า</span>
                <span>อัปเดต {{ category.updatedAt }}</span>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center gap-2">
              <UButton @click="handleView(category.id)" variant="ghost" size="sm">
                <Icon name="i-heroicons-eye" class="w-4 h-4" />
              </UButton>
              <UDropdown :items="[
                [
                  {
                    label: 'แก้ไข',
                    icon: 'i-heroicons-pencil-square',
                    click: () => handleEdit(category.id)
                  },
                  {
                    label: 'คัดลอก',
                    icon: 'i-heroicons-document-duplicate',
                    click: () => handleDuplicate(category)
                  },
                  {
                    label: category.isActive ? 'ปิดใช้งาน' : 'เปิดใช้งาน',
                    icon: category.isActive ? 'i-heroicons-eye-slash' : 'i-heroicons-eye',
                    click: () => handleToggleStatus(category.id)
                  }
                ],
                [
                  {
                    label: 'ลบ',
                    icon: 'i-heroicons-trash',
                    click: () => handleDelete(category.id),
                    disabled: category.productCount > 0 || (category.children && category.children.length > 0)
                  }
                ]
              ]">
                <UButton variant="ghost" size="sm">
                  <Icon name="i-heroicons-ellipsis-vertical" class="w-4 h-4" />
                </UButton>
              </UDropdown>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Empty State -->
    <div v-if="filteredCategories.length === 0" class="text-center py-12">
      <div class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
        <Icon name="i-heroicons-folder" class="w-12 h-12 text-gray-400" />
      </div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
        {{ searchQuery ? 'ไม่พบหมวดหมู่ที่ค้นหา' : 'ยังไม่มีหมวดหมู่' }}
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-6">
        {{ searchQuery ? 'ลองเปลี่ยนคำค้นหาหรือตัวกรอง' : 'เริ่มต้นสร้างหมวดหมู่แรกของคุณ' }}
      </p>
      <UButton v-if="!searchQuery" @click="handleCreate"
        class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">
        <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
        เพิ่มหมวดหมู่ใหม่
      </UButton>
    </div>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8>* {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.hover\:shadow-lg:hover {
  transform: translateY(-2px);
}
</style>