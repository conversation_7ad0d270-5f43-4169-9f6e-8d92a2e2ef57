<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useVuelidate } from '@vuelidate/core';
import { required, minLength, maxLength, helpers } from '@vuelidate/validators';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId and categoryId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);
const categoryId = computed(() => route.params.categoryId as string);

// Mock data - in real app, fetch from API
const mockCategory = {
  id: '1',
  name: 'อิเล็กทรอนิกส์',
  slug: 'electronics',
  description: 'สินค้าอิเล็กทรอนิกส์และอุปกรณ์เทคโนโลยี',
  image: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400',
  parentId: '',
  isActive: true,
  sortOrder: 1,
  productCount: 45,
  seoTitle: 'อิเล็กทรอนิกส์ - ร้านค้าออนไลน์',
  seoDescription: 'เลือกซื้อสินค้าอิเล็กทรอนิกส์คุณภาพดี ราคาดี',
  seoKeywords: 'อิเล็กทรอนิกส์, โทรศัพท์, คอมพิวเตอร์',
  createdAt: '2024-01-15',
  updatedAt: '2024-01-20',
};

// Form data
const form = reactive({
  name: mockCategory.name,
  slug: mockCategory.slug,
  description: mockCategory.description,
  parentId: mockCategory.parentId,
  image: mockCategory.image,
  isActive: mockCategory.isActive,
  sortOrder: mockCategory.sortOrder,
  seoTitle: mockCategory.seoTitle,
  seoDescription: mockCategory.seoDescription,
  seoKeywords: mockCategory.seoKeywords,
});

// Validation rules
const rules = computed(() => ({
  name: {
    required: helpers.withMessage('ชื่อหมวดหมู่ต้องไม่ว่าง', required),
    minLength: helpers.withMessage('ชื่อหมวดหมู่ต้องมีอย่างน้อย 2 ตัวอักษร', minLength(2)),
    maxLength: helpers.withMessage('ชื่อหมวดหมู่ต้องไม่เกิน 100 ตัวอักษร', maxLength(100)),
  },
  slug: {
    required: helpers.withMessage('Slug ต้องไม่ว่าง', required),
    minLength: helpers.withMessage('Slug ต้องมีอย่างน้อย 2 ตัวอักษร', minLength(2)),
    maxLength: helpers.withMessage('Slug ต้องไม่เกิน 100 ตัวอักษร', maxLength(100)),
  },
  description: {
    maxLength: helpers.withMessage('คำอธิบายต้องไม่เกิน 500 ตัวอักษร', maxLength(500)),
  },
  seoTitle: {
    maxLength: helpers.withMessage('SEO Title ต้องไม่เกิน 60 ตัวอักษร', maxLength(60)),
  },
  seoDescription: {
    maxLength: helpers.withMessage('SEO Description ต้องไม่เกิน 160 ตัวอักษร', maxLength(160)),
  },
}));

// Initialize Vuelidate
const v$ = useVuelidate(rules, form);

const loading = ref(false);
const originalData = ref({ ...form });

// Mock parent categories (excluding current category and its children)
const parentCategories = ref([
  { id: '', label: 'ไม่มีหมวดหมู่หลัก (หมวดหมู่รูท)' },
  { id: '4', label: 'เสื้อผ้า' },
  { id: '7', label: 'หนังสือ' },
]);

// Check if form has changes
const hasChanges = computed(() => {
  return JSON.stringify(form) !== JSON.stringify(originalData.value);
});

// Handle image upload
const uploadedImage = ref<string | null>(form.image);
const handleImageUpload = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = (e) => {
    uploadedImage.value = e.target?.result as string;
    form.image = uploadedImage.value;
  };
  reader.readAsDataURL(file);
};

// Remove image
const removeImage = () => {
  uploadedImage.value = null;
  form.image = '';
};

// Handle form submission
const handleSubmit = async () => {
  // Validate form using Vuelidate
  const isValid = await v$.value.$validate();
  if (!isValid) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: 'กรุณาตรวจสอบข้อมูลที่กรอก',
      color: 'error',
    });
    return;
  }

  loading.value = true;
  try {
    // Mock API call
    const categoryData = {
      id: categoryId.value,
      name: form.name,
      slug: form.slug,
      description: form.description,
      parentId: form.parentId || null,
      image: form.image,
      isActive: form.isActive,
      sortOrder: form.sortOrder,
      seoTitle: form.seoTitle,
      seoDescription: form.seoDescription,
      seoKeywords: form.seoKeywords ? form.seoKeywords.split(',').map(k => k.trim()) : [],
    };

    console.log('Updating category:', categoryData);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Update original data to reflect changes
    originalData.value = { ...form };

    useToast().add({
      title: 'สำเร็จ',
      description: 'อัปเดตหมวดหมู่เรียบร้อยแล้ว',
      color: 'success',
    });

    // Navigate back to categories page
    await navigateTo(`/dashboard/${siteId.value}/categories`);
  } catch (error: any) {
    console.error('Error updating category:', error);
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: error.message || 'ไม่สามารถอัปเดตหมวดหมู่ได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// Handle cancel
const handleCancel = () => {
  if (hasChanges.value) {
    const confirmed = confirm('คุณมีการเปลี่ยนแปลงที่ยังไม่ได้บันทึก คุณต้องการออกจากหน้านี้หรือไม่?');
    if (!confirmed) return;
  }
  navigateTo(`/dashboard/${siteId.value}/categories`);
};

// Reset form
const handleReset = () => {
  const confirmed = confirm('คุณต้องการรีเซ็ตการเปลี่ยนแปลงทั้งหมดหรือไม่?');
  if (!confirmed) return;

  Object.assign(form, originalData.value);
  uploadedImage.value = originalData.value.image;
};

// Generate slug manually
const generateSlug = () => {
  if (form.name) {
    form.slug = form.name
      .toLowerCase()
      .replace(/[^a-z0-9ก-๙\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }
};

// Preview URL
const previewUrl = computed(() => {
  if (form.slug) {
    return `https://yourstore.com/categories/${form.slug}`;
  }
  return '';
});
</script>

<template>
  <div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          แก้ไขหมวดหมู่
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          แก้ไขหมวดหมู่ "{{ form.name }}"
        </p>
      </div>
      <div class="flex items-center gap-3">
        <UButton @click="handleCancel" variant="outline" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
          <Icon name="i-heroicons-arrow-left" class="w-4 h-4 mr-2" />
          ย้อนกลับ
        </UButton>
        <UButton v-if="hasChanges" @click="handleReset" variant="outline" size="sm" color="orange">
          <Icon name="i-heroicons-arrow-path" class="w-4 h-4 mr-2" />
          รีเซ็ต
        </UButton>
        <UButton @click="handleSubmit" :loading="loading" :disabled="!hasChanges"
          class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
          <Icon name="i-heroicons-check" class="w-5 h-5 mr-2" />
          บันทึกการเปลี่ยนแปลง
        </UButton>
      </div>
    </div>

    <!-- Usage Warning -->
    <UAlert v-if="mockCategory.productCount > 0" color="amber" variant="soft" title="คำเตือน"
      :description="`หมวดหมู่นี้มีสินค้า ${mockCategory.productCount} รายการ การเปลี่ยนแปลงอาจส่งผลต่อสินค้าเหล่านั้น`" />

    <!-- Form -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Form -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                <Icon name="i-heroicons-information-circle" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ข้อมูลพื้นฐาน</h2>
            </div>
          </template>

          <div class="space-y-6">
            <!-- Category Name -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ชื่อหมวดหมู่ <span class="text-red-500">*</span>
              </label>
              <UInput v-model="form.name" placeholder="กรอกชื่อหมวดหมู่"
                :error="v$.name.$error ? v$.name.$errors[0].$message : ''" class="w-full" />
            </div>

            <!-- Slug -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Slug (URL) <span class="text-red-500">*</span>
              </label>
              <div class="flex gap-2">
                <UInput v-model="form.slug" placeholder="category-slug"
                  :error="v$.slug.$error ? v$.slug.$errors[0].$message : ''" class="flex-1" />
                <UButton @click="generateSlug" variant="outline" size="sm">
                  สร้าง
                </UButton>
              </div>
              <p v-if="previewUrl" class="text-xs text-gray-500 mt-1">
                URL: {{ previewUrl }}
              </p>
            </div>

            <!-- Description -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                คำอธิบาย
              </label>
              <UTextarea v-model="form.description" placeholder="อธิบายหมวดหมู่นี้"
                :error="v$.description.$error ? v$.description.$errors[0].$message : ''" :rows="4" class="w-full" />
            </div>

            <!-- Parent Category -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                หมวดหมู่หลัก
              </label>
              <USelect v-model="form.parentId" :items="parentCategories" option-attribute="label" value-attribute="id"
                placeholder="เลือกหมวดหมู่หลัก" class="w-full" />
              <p class="text-xs text-gray-500 mt-1">
                เลือกหมวดหมู่หลักหากต้องการสร้างหมวดหมู่ย่อย
              </p>
            </div>

            <!-- Sort Order -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ลำดับการแสดง
              </label>
              <UInput v-model="form.sortOrder" type="number" placeholder="1" class="w-full" />
              <p class="text-xs text-gray-500 mt-1">
                ตัวเลขที่น้อยกว่าจะแสดงก่อน
              </p>
            </div>
          </div>
        </UCard>

        <!-- Image -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                <Icon name="i-heroicons-photo" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รูปภาพหมวดหมู่</h2>
            </div>
          </template>

          <div class="space-y-4">
            <!-- Image Upload -->
            <div v-if="!uploadedImage"
              class="border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-8 text-center">
              <input type="file" accept="image/*" @change="handleImageUpload" class="sr-only" id="image-upload" />
              <label for="image-upload" class="cursor-pointer flex flex-col items-center gap-4">
                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                  <Icon name="i-heroicons-camera" class="w-8 h-8 text-gray-400" />
                </div>
                <div>
                  <p class="text-lg font-medium text-gray-900 dark:text-white">อัปโหลดรูปภาพ</p>
                  <p class="text-sm text-gray-500">PNG, JPG, WEBP ขนาดไม่เกิน 5MB</p>
                </div>
              </label>
            </div>

            <!-- Image Preview -->
            <div v-else class="relative">
              <img :src="uploadedImage" alt="Category image" class="w-full h-48 object-cover rounded-lg" />
              <UButton @click="removeImage" size="sm" color="red" variant="solid" class="absolute top-2 right-2">
                <Icon name="i-heroicons-x-mark" class="w-4 h-4" />
              </UButton>
            </div>
          </div>
        </UCard>

        <!-- SEO -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg">
                <Icon name="i-heroicons-magnifying-glass" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">SEO</h2>
            </div>
          </template>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SEO Title
              </label>
              <UInput v-model="form.seoTitle" placeholder="SEO Title (ไม่เกิน 60 ตัวอักษร)"
                :error="v$.seoTitle.$error ? v$.seoTitle.$errors[0].$message : ''" class="w-full" />
              <p class="text-xs text-gray-500 mt-1">
                {{ form.seoTitle.length }}/60 ตัวอักษร
              </p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SEO Description
              </label>
              <UTextarea v-model="form.seoDescription" placeholder="SEO Description (ไม่เกิน 160 ตัวอักษร)"
                :error="v$.seoDescription.$error ? v$.seoDescription.$errors[0].$message : ''" :rows="3"
                class="w-full" />
              <p class="text-xs text-gray-500 mt-1">
                {{ form.seoDescription.length }}/160 ตัวอักษร
              </p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SEO Keywords
              </label>
              <UInput v-model="form.seoKeywords" placeholder="คำสำคัญ, คั่นด้วยจุลภาค" class="w-full" />
              <p class="text-xs text-gray-500 mt-1">
                แยกคำสำคัญด้วยจุลภาค เช่น อิเล็กทรอนิกส์, โทรศัพท์, แท็บเล็ต
              </p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Settings Sidebar -->
      <div class="space-y-6">
        <!-- Status -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-red-500 to-red-600 rounded-lg">
                <Icon name="i-heroicons-flag" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">สถานะ</h2>
            </div>
          </template>

          <div class="space-y-4">
            <UCheckbox v-model="form.isActive" label="เปิดใช้งานหมวดหมู่" />
            <p class="text-sm text-gray-600 dark:text-gray-400">
              หมวดหมู่ที่เปิดใช้งานจะแสดงในหน้าร้านค้า
            </p>
          </div>
        </UCard>

        <!-- Category Info -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
                <Icon name="i-heroicons-chart-bar" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ข้อมูลหมวดหมู่</h2>
            </div>
          </template>

          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">สินค้าในหมวดหมู่</span>
              <span class="font-medium">{{ mockCategory.productCount }} รายการ</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">สร้างเมื่อ</span>
              <span class="font-medium">{{ mockCategory.createdAt }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">อัปเดตล่าสุด</span>
              <span class="font-medium">{{ mockCategory.updatedAt }}</span>
            </div>
          </div>
        </UCard>

        <!-- Preview -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
                <Icon name="i-heroicons-eye" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวอย่าง</h2>
            </div>
          </template>

          <div class="space-y-4">
            <!-- Category Preview -->
            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div class="flex items-center gap-3 mb-3">
                <div class="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
                  <img v-if="uploadedImage" :src="uploadedImage" alt="Preview" class="w-full h-full object-cover" />
                  <div v-else class="w-full h-full flex items-center justify-center">
                    <Icon name="i-heroicons-folder" class="w-6 h-6 text-gray-400" />
                  </div>
                </div>
                <div class="flex-1">
                  <h3 class="font-medium text-gray-900 dark:text-white">
                    {{ form.name }}
                  </h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ form.description }}
                  </p>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <UBadge :color="form.isActive ? 'green' : 'red'" variant="subtle" size="sm">
                  {{ form.isActive ? 'เปิดใช้งาน' : 'ปิดใช้งาน' }}
                </UBadge>
                <UBadge variant="outline" size="sm">
                  ลำดับ {{ form.sortOrder }}
                </UBadge>
              </div>
            </div>

            <!-- SEO Preview -->
            <div v-if="form.seoTitle || form.seoDescription"
              class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ตัวอย่าง SEO
              </h4>
              <div class="space-y-1">
                <h5 class="text-blue-600 text-sm font-medium">
                  {{ form.seoTitle || form.name }}
                </h5>
                <p class="text-green-600 text-xs">
                  {{ previewUrl }}
                </p>
                <p class="text-gray-600 text-sm">
                  {{ form.seoDescription || form.description }}
                </p>
              </div>
            </div>
          </div>
        </UCard>

        <!-- Changes Indicator -->
        <UCard v-if="hasChanges" class="overflow-hidden border-orange-200 dark:border-orange-800">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
                <Icon name="i-heroicons-exclamation-triangle" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-orange-900 dark:text-orange-100">มีการเปลี่ยนแปลง</h2>
            </div>
          </template>

          <p class="text-sm text-orange-700 dark:text-orange-300">
            คุณมีการเปลี่ยนแปลงที่ยังไม่ได้บันทึก อย่าลืมกดปุ่ม "บันทึกการเปลี่ยนแปลง"
          </p>
        </UCard>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8>* {
  animation: fadeInUp 0.6s ease-out forwards;
}
</style>