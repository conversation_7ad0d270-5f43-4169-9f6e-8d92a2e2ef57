<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

definePageMeta({
    layout: 'dashboard',
    middleware: ['auth'],
});

// Get siteId and categoryId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);
const categoryId = computed(() => route.params.categoryId as string);

// Mock data - in real app, fetch from API
const category = ref({
    id: '1',
    name: 'อิเล็กทรอนิกส์',
    slug: 'electronics',
    description: 'สินค้าอิเล็กทรอนิกส์และอุปกรณ์เทคโนโลยีทุกประเภท รวมถึงโทรศัพท์มือถือ คอมพิวเตอร์ แท็บเล็ต และอุปกรณ์เสริมต่างๆ',
    image: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=800',
    parentId: null,
    isActive: true,
    sortOrder: 1,
    productCount: 45,
    seoTitle: 'อิเล็กทรอนิกส์ - ร้านค้าออนไลน์',
    seoDescription: 'เลือกซื้อสินค้าอิเล็กทรอนิกส์คุณภาพดี ราคาดี จากแบรนด์ชั้นนำ พร้อมบริการหลังการขายที่ดี',
    seoKeywords: ['อิเล็กทรอนิกส์', 'โทรศัพท์', 'คอมพิวเตอร์', 'แท็บเล็ต'],
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-20T14:45:00Z',
    children: [
        {
            id: '2',
            name: 'โทรศัพท์มือถือ',
            slug: 'mobile-phones',
            productCount: 25,
            isActive: true,
        },
        {
            id: '3',
            name: 'คอมพิวเตอร์',
            slug: 'computers',
            productCount: 20,
            isActive: true,
        }
    ]
});

// Mock products in this category
const products = ref([
    {
        id: '1',
        name: 'iPhone 15 Pro',
        price: 39900,
        image: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=200',
        isActive: true,
        stock: 15,
    },
    {
        id: '2',
        name: 'Samsung Galaxy S24',
        price: 29900,
        image: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=200',
        isActive: true,
        stock: 8,
    },
    {
        id: '3',
        name: 'MacBook Pro M3',
        price: 89900,
        image: 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=200',
        isActive: true,
        stock: 5,
    },
    {
        id: '4',
        name: 'iPad Air',
        price: 24900,
        image: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=200',
        isActive: false,
        stock: 0,
    },
]);

const loading = ref(false);

// Actions
const handleEdit = () => {
    navigateTo(`/dashboard/${siteId.value}/categories/${categoryId.value}/edit`);
};

const handleBack = () => {
    navigateTo(`/dashboard/${siteId.value}/categories`);
};

const handleToggleStatus = async () => {
    loading.value = true;
    try {
        // Mock API call
        await new Promise(resolve => setTimeout(resolve, 500));

        category.value.isActive = !category.value.isActive;
        category.value.updatedAt = new Date().toISOString();

        useToast().add({
            title: 'สำเร็จ',
            description: `${category.value.isActive ? 'เปิด' : 'ปิด'}ใช้งานหมวดหมู่เรียบร้อยแล้ว`,
            color: 'success',
        });
    } catch (error) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'ไม่สามารถอัปเดตสถานะได้',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

const handleViewProduct = (productId: string) => {
    navigateTo(`/dashboard/${siteId.value}/products/${productId}`);
};

const handleViewSubcategory = (subcategoryId: string) => {
    navigateTo(`/dashboard/${siteId.value}/categories/${subcategoryId}`);
};

// Format date
const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('th-TH', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
};

// Stats
const stats = computed(() => ({
    totalProducts: category.value.productCount,
    activeProducts: products.value.filter(p => p.isActive).length,
    inactiveProducts: products.value.filter(p => !p.isActive).length,
    lowStockProducts: products.value.filter(p => p.stock <= 5).length,
    subcategories: category.value.children.length,
}));
</script>

<template>
    <div class="space-y-8">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="flex items-center gap-4">
                <UButton @click="handleBack" variant="outline" size="sm"
                    class="hover:bg-gray-100 dark:hover:bg-gray-800">
                    <Icon name="i-heroicons-arrow-left" class="w-4 h-4 mr-2" />
                    ย้อนกลับ
                </UButton>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                        {{ category.name }}
                    </h1>
                    <div class="flex items-center gap-2">
                        <UBadge :color="category.isActive ? 'green' : 'red'" variant="subtle">
                            {{ category.isActive ? 'เปิดใช้งาน' : 'ปิดใช้งาน' }}
                        </UBadge>
                        <span class="text-gray-600 dark:text-gray-400">•</span>
                        <span class="text-gray-600 dark:text-gray-400">
                            {{ category.productCount }} สินค้า
                        </span>
                    </div>
                </div>
            </div>
            <div class="flex items-center gap-3">
                <UButton @click="handleToggleStatus" :loading="loading" :color="category.isActive ? 'red' : 'green'"
                    variant="outline" size="sm">
                    <Icon :name="category.isActive ? 'i-heroicons-eye-slash' : 'i-heroicons-eye'"
                        class="w-4 h-4 mr-2" />
                    {{ category.isActive ? 'ปิดใช้งาน' : 'เปิดใช้งาน' }}
                </UButton>
                <UButton @click="handleEdit"
                    class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
                    <Icon name="i-heroicons-pencil-square" class="w-5 h-5 mr-2" />
                    แก้ไข
                </UButton>
            </div>
        </div>

        <!-- Category Overview -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Category Details -->
                <UCard class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                                <Icon name="i-heroicons-information-circle" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รายละเอียดหมวดหมู่</h2>
                        </div>
                    </template>

                    <div class="space-y-6">
                        <!-- Category Image and Info -->
                        <div class="flex flex-col md:flex-row gap-6">
                            <div
                                class="w-full md:w-48 h-48 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden flex-shrink-0">
                                <img v-if="category.image" :src="category.image" :alt="category.name"
                                    class="w-full h-full object-cover" />
                                <div v-else class="w-full h-full flex items-center justify-center">
                                    <Icon name="i-heroicons-folder" class="w-16 h-16 text-gray-400" />
                                </div>
                            </div>

                            <div class="flex-1">
                                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                                    {{ category.name }}
                                </h3>
                                <p class="text-gray-600 dark:text-gray-400 mb-4">
                                    {{ category.description }}
                                </p>

                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-500">Slug:</span>
                                        <span class="ml-2 font-mono bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                                            {{ category.slug }}
                                        </span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">ลำดับ:</span>
                                        <span class="ml-2">{{ category.sortOrder }}</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">สร้างเมื่อ:</span>
                                        <span class="ml-2">{{ formatDate(category.createdAt) }}</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">อัปเดต:</span>
                                        <span class="ml-2">{{ formatDate(category.updatedAt) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </UCard>

                <!-- SEO Information -->
                <UCard class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg">
                                <Icon name="i-heroicons-magnifying-glass" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ข้อมูล SEO</h2>
                        </div>
                    </template>

                    <div class="space-y-4">
                        <!-- SEO Preview -->
                        <div
                            class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                                ตัวอย่างการแสดงผลใน Google
                            </h4>
                            <div class="space-y-1">
                                <h5 class="text-blue-600 text-lg font-medium hover:underline cursor-pointer">
                                    {{ category.seoTitle }}
                                </h5>
                                <p class="text-green-600 text-sm">
                                    https://yourstore.com/categories/{{ category.slug }}
                                </p>
                                <p class="text-gray-600 text-sm">
                                    {{ category.seoDescription }}
                                </p>
                            </div>
                        </div>

                        <!-- SEO Details -->
                        <div class="grid grid-cols-1 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    SEO Title
                                </label>
                                <p class="text-gray-900 dark:text-white">{{ category.seoTitle }}</p>
                                <p class="text-xs text-gray-500 mt-1">{{ category.seoTitle.length }}/60 ตัวอักษร</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    SEO Description
                                </label>
                                <p class="text-gray-900 dark:text-white">{{ category.seoDescription }}</p>
                                <p class="text-xs text-gray-500 mt-1">{{ category.seoDescription.length }}/160 ตัวอักษร
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    SEO Keywords
                                </label>
                                <div class="flex flex-wrap gap-2">
                                    <UBadge v-for="keyword in category.seoKeywords" :key="keyword" variant="subtle"
                                        size="sm">
                                        {{ keyword }}
                                    </UBadge>
                                </div>
                            </div>
                        </div>
                    </div>
                </UCard>

                <!-- Subcategories -->
                <UCard v-if="category.children.length > 0" class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
                                <Icon name="i-heroicons-folder-open" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">หมวดหมู่ย่อย</h2>
                        </div>
                    </template>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div v-for="subcategory in category.children" :key="subcategory.id"
                            class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow cursor-pointer"
                            @click="handleViewSubcategory(subcategory.id)">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="font-medium text-gray-900 dark:text-white">
                                        {{ subcategory.name }}
                                    </h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        {{ subcategory.productCount }} สินค้า
                                    </p>
                                </div>
                                <div class="flex items-center gap-2">
                                    <UBadge :color="subcategory.isActive ? 'green' : 'red'" variant="subtle" size="sm">
                                        {{ subcategory.isActive ? 'เปิด' : 'ปิด' }}
                                    </UBadge>
                                    <Icon name="i-heroicons-chevron-right" class="w-4 h-4 text-gray-400" />
                                </div>
                            </div>
                        </div>
                    </div>
                </UCard>

                <!-- Products in Category -->
                <UCard class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                                    <Icon name="i-heroicons-cube" class="w-5 h-5 text-white" />
                                </div>
                                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">สินค้าในหมวดหมู่</h2>
                            </div>
                            <UButton @click="navigateTo(`/dashboard/${siteId}/products?category=${categoryId}`)"
                                variant="outline" size="sm">
                                ดูทั้งหมด
                            </UButton>
                        </div>
                    </template>

                    <div class="space-y-4">
                        <div v-for="product in products.slice(0, 5)" :key="product.id"
                            class="flex items-center gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow cursor-pointer"
                            @click="handleViewProduct(product.id)">
                            <div
                                class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden flex-shrink-0">
                                <img :src="product.image" :alt="product.name" class="w-full h-full object-cover" />
                            </div>

                            <div class="flex-1 min-w-0">
                                <h3 class="font-medium text-gray-900 dark:text-white truncate">
                                    {{ product.name }}
                                </h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    ฿{{ product.price.toLocaleString() }}
                                </p>
                            </div>

                            <div class="flex items-center gap-2">
                                <UBadge :color="product.isActive ? 'green' : 'red'" variant="subtle" size="sm">
                                    {{ product.isActive ? 'เปิดขาย' : 'ปิดขาย' }}
                                </UBadge>
                                <UBadge :color="product.stock <= 5 ? 'red' : 'blue'" variant="subtle" size="sm">
                                    {{ product.stock }} ชิ้น
                                </UBadge>
                                <Icon name="i-heroicons-chevron-right" class="w-4 h-4 text-gray-400" />
                            </div>
                        </div>

                        <div v-if="products.length === 0" class="text-center py-8">
                            <div
                                class="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                                <Icon name="i-heroicons-cube" class="w-8 h-8 text-gray-400" />
                            </div>
                            <p class="text-gray-600 dark:text-gray-400">ยังไม่มีสินค้าในหมวดหมู่นี้</p>
                        </div>
                    </div>
                </UCard>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Stats -->
                <UCard class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg">
                                <Icon name="i-heroicons-chart-bar" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">สถิติ</h2>
                        </div>
                    </template>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">สินค้าทั้งหมด</span>
                            <span class="font-semibold text-lg">{{ stats.totalProducts }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">สินค้าเปิดขาย</span>
                            <span class="font-semibold text-green-600">{{ stats.activeProducts }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">สินค้าปิดขาย</span>
                            <span class="font-semibold text-red-600">{{ stats.inactiveProducts }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">สต็อกต่ำ</span>
                            <span class="font-semibold text-orange-600">{{ stats.lowStockProducts }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">หมวดหมู่ย่อย</span>
                            <span class="font-semibold text-blue-600">{{ stats.subcategories }}</span>
                        </div>
                    </div>
                </UCard>

                <!-- Quick Actions -->
                <UCard class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
                                <Icon name="i-heroicons-bolt" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">การดำเนินการด่วน</h2>
                        </div>
                    </template>

                    <div class="space-y-3">
                        <UButton @click="navigateTo(`/dashboard/${siteId}/products/create?category=${categoryId}`)"
                            variant="outline" size="sm" class="w-full justify-start">
                            <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
                            เพิ่มสินค้าในหมวดหมู่นี้
                        </UButton>

                        <UButton @click="navigateTo(`/dashboard/${siteId}/categories/create?parent=${categoryId}`)"
                            variant="outline" size="sm" class="w-full justify-start">
                            <Icon name="i-heroicons-folder-plus" class="w-4 h-4 mr-2" />
                            เพิ่มหมวดหมู่ย่อย
                        </UButton>

                        <UButton @click="navigateTo(`/dashboard/${siteId}/products?category=${categoryId}`)"
                            variant="outline" size="sm" class="w-full justify-start">
                            <Icon name="i-heroicons-eye" class="w-4 h-4 mr-2" />
                            ดูสินค้าทั้งหมด
                        </UButton>
                    </div>
                </UCard>

                <!-- Category Hierarchy -->
                <UCard v-if="category.parentId" class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg">
                                <Icon name="i-heroicons-folder-open" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ลำดับชั้น</h2>
                        </div>
                    </template>

                    <div class="space-y-2">
                        <div class="flex items-center gap-2 text-sm">
                            <Icon name="i-heroicons-home" class="w-4 h-4 text-gray-400" />
                            <span class="text-gray-600 dark:text-gray-400">หน้าแรก</span>
                            <Icon name="i-heroicons-chevron-right" class="w-3 h-3 text-gray-400" />
                            <span class="text-gray-600 dark:text-gray-400">หมวดหมู่หลัก</span>
                            <Icon name="i-heroicons-chevron-right" class="w-3 h-3 text-gray-400" />
                            <span class="font-medium text-gray-900 dark:text-white">{{ category.name }}</span>
                        </div>
                    </div>
                </UCard>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.space-y-8>* {
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.hover\:shadow-md:hover {
    transform: translateY(-1px);
}
</style>