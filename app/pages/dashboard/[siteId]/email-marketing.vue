<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// State
const loading = ref(false);
const searchQuery = ref('');
const selectedStatus = ref('all');
const selectedType = ref('all');
const sortBy = ref('created_at');
const sortOrder = ref('desc');
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Status options
const statuses = ref([
  { value: 'all', label: 'ทุกสถานะ' },
  { value: 'draft', label: 'ร่าง' },
  { value: 'scheduled', label: 'ตั้งเวลา' },
  { value: 'sending', label: 'กำลังส่ง' },
  { value: 'sent', label: 'ส่งแล้ว' },
  { value: 'paused', label: 'หยุดชั่วคราว' },
]);

const types = ref([
  { value: 'all', label: 'ทุกประเภท' },
  { value: 'newsletter', label: 'จดหมายข่าว' },
  { value: 'promotion', label: 'โปรโมชั่น' },
  { value: 'welcome', label: 'ต้อนรับ' },
  { value: 'abandoned_cart', label: 'ตะกร้าค้าง' },
  { value: 'order_confirmation', label: 'ยืนยันคำสั่งซื้อ' },
]);

// Mock email campaigns data
const emailCampaigns = ref([
  {
    id: '1',
    name: 'โปรโมชั่นปีใหม่ 2024',
    subject: '🎉 สุขสันต์ปีใหม่! ลดสูงสุด 50% ทุกสินค้า',
    type: 'promotion',
    status: 'sent',
    recipientCount: 2500,
    sentCount: 2487,
    openedCount: 1243,
    clickedCount: 456,
    conversionCount: 89,
    openRate: 50.0,
    clickRate: 18.3,
    conversionRate: 3.6,
    scheduledAt: '2024-01-01T09:00:00Z',
    sentAt: '2024-01-01T09:05:00Z',
    createdAt: '2023-12-30T14:00:00Z',
    createdBy: 'admin',
    template: 'promotion_2024',
    segment: 'all_customers',
  },
  {
    id: '2',
    name: 'จดหมายข่าวประจำเดือน',
    subject: '📰 อัพเดทสินค้าใหม่และข่าวสารประจำเดือน',
    type: 'newsletter',
    status: 'scheduled',
    recipientCount: 3200,
    sentCount: 0,
    openedCount: 0,
    clickedCount: 0,
    conversionCount: 0,
    openRate: 0,
    clickRate: 0,
    conversionRate: 0,
    scheduledAt: '2024-02-01T10:00:00Z',
    sentAt: null,
    createdAt: '2024-01-25T16:30:00Z',
    createdBy: 'admin',
    template: 'monthly_newsletter',
    segment: 'subscribed_customers',
  },
  {
    id: '3',
    name: 'ต้อนรับลูกค้าใหม่',
    subject: '🎁 ยินดีต้อนรับ! รับส่วนลด 10% สำหรับการสั่งซื้อแรก',
    type: 'welcome',
    status: 'sent',
    recipientCount: 150,
    sentCount: 150,
    openedCount: 98,
    clickedCount: 67,
    conversionCount: 23,
    openRate: 65.3,
    clickRate: 44.7,
    conversionRate: 15.3,
    scheduledAt: '2024-01-15T08:00:00Z',
    sentAt: '2024-01-15T08:02:00Z',
    createdAt: '2024-01-15T07:30:00Z',
    createdBy: 'system',
    template: 'welcome_new_customer',
    segment: 'new_customers',
  },
  {
    id: '4',
    name: 'ตะกร้าค้าง - สินค้าลดราคา',
    subject: '⏰ อย่าพลาด! สินค้าในตะกร้าของคุณลดราคาแล้ว',
    type: 'abandoned_cart',
    status: 'sending',
    recipientCount: 85,
    sentCount: 45,
    openedCount: 12,
    clickedCount: 8,
    conversionCount: 3,
    openRate: 26.7,
    clickRate: 17.8,
    conversionRate: 6.7,
    scheduledAt: '2024-01-20T14:00:00Z',
    sentAt: '2024-01-20T14:01:00Z',
    createdAt: '2024-01-20T13:30:00Z',
    createdBy: 'system',
    template: 'abandoned_cart_discount',
    segment: 'abandoned_cart',
  },
  {
    id: '5',
    name: 'ยืนยันคำสั่งซื้อ #12345',
    subject: '✅ ยืนยันคำสั่งซื้อของคุณ - #12345',
    type: 'order_confirmation',
    status: 'sent',
    recipientCount: 1,
    sentCount: 1,
    openedCount: 1,
    clickedCount: 1,
    conversionCount: 1,
    openRate: 100.0,
    clickRate: 100.0,
    conversionRate: 100.0,
    scheduledAt: '2024-01-18T15:30:00Z',
    sentAt: '2024-01-18T15:31:00Z',
    createdAt: '2024-01-18T15:30:00Z',
    createdBy: 'system',
    template: 'order_confirmation',
    segment: 'single_customer',
  },
  {
    id: '6',
    name: 'แฟลชเซล - สินค้าอิเล็กทรอนิกส์',
    subject: '⚡ แฟลชเซล! สินค้าอิเล็กทรอนิกส์ลดราคา 24 ชั่วโมงเท่านั้น',
    type: 'promotion',
    status: 'draft',
    recipientCount: 0,
    sentCount: 0,
    openedCount: 0,
    clickedCount: 0,
    conversionCount: 0,
    openRate: 0,
    clickRate: 0,
    conversionRate: 0,
    scheduledAt: null,
    sentAt: null,
    createdAt: '2024-01-22T11:00:00Z',
    createdBy: 'admin',
    template: 'flash_sale_electronics',
    segment: 'electronics_customers',
  },
]);

// Analytics data
const analytics = ref({
  totalCampaigns: 15,
  activeCampaigns: 3,
  totalRecipients: 8950,
  totalSent: 8230,
  averageOpenRate: 45.2,
  averageClickRate: 18.7,
  averageConversionRate: 4.8,
  totalRevenue: 125000,
});

// Computed filtered campaigns
const filteredCampaigns = computed(() => {
  let filtered = emailCampaigns.value;

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(
      campaign =>
        campaign.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        campaign.subject.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  // Status filter
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(campaign => campaign.status === selectedStatus.value);
  }

  // Type filter
  if (selectedType.value !== 'all') {
    filtered = filtered.filter(campaign => campaign.type === selectedType.value);
  }

  // Sort
  filtered.sort((a, b) => {
    let aVal = a[sortBy.value as keyof typeof a];
    let bVal = b[sortBy.value as keyof typeof b];

    if (typeof aVal === 'string') {
      aVal = aVal.toLowerCase();
      bVal = bVal.toLowerCase();
    }

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });

  return filtered;
});

// Paginated campaigns
const paginatedCampaigns = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredCampaigns.value.slice(start, end);
});

// Total pages
const totalPages = computed(() => {
  return Math.ceil(filteredCampaigns.value.length / itemsPerPage.value);
});

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
  }).format(amount);
};

// Format date
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Format percentage
const formatPercentage = (value: number) => {
  return `${value.toFixed(1)}%`;
};

// Get status badge class
const getStatusBadgeClass = (status: string) => {
  const classes = {
    draft: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
    scheduled: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    sending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    sent: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    paused: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
  };
  return classes[status as keyof typeof classes] || classes.draft;
};

// Get status label
const getStatusLabel = (status: string) => {
  const labels = {
    draft: 'ร่าง',
    scheduled: 'ตั้งเวลา',
    sending: 'กำลังส่ง',
    sent: 'ส่งแล้ว',
    paused: 'หยุดชั่วคราว',
  };
  return labels[status as keyof typeof labels] || status;
};

// Get type badge class
const getTypeBadgeClass = (type: string) => {
  const classes = {
    newsletter: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    promotion: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
    welcome: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    abandoned_cart: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
    order_confirmation: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400',
  };
  return classes[type as keyof typeof classes] || classes.newsletter;
};

// Get type label
const getTypeLabel = (type: string) => {
  const labels = {
    newsletter: 'จดหมายข่าว',
    promotion: 'โปรโมชั่น',
    welcome: 'ต้อนรับ',
    abandoned_cart: 'ตะกร้าค้าง',
    order_confirmation: 'ยืนยันคำสั่งซื้อ',
  };
  return labels[type as keyof typeof labels] || type;
};

// Get color class
const getColorClass = (color: string) => {
  const classes = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    purple: 'bg-purple-500',
    orange: 'bg-orange-500',
    red: 'bg-red-500',
    yellow: 'bg-yellow-500',
  };
  return classes[color as keyof typeof classes] || classes.blue;
};

// Methods
const handleSort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = field;
    sortOrder.value = 'desc';
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handleViewCampaign = (campaign: any) => {
  navigateTo(`/dashboard/${siteId.value}/email-marketing/${campaign.id}`);
};

const handleEditCampaign = (campaign: any) => {
  navigateTo(`/dashboard/${siteId.value}/email-marketing/${campaign.id}/edit`);
};

const handleDuplicateCampaign = async (campaign: any) => {
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    const newCampaign = {
      ...campaign,
      id: Date.now().toString(),
      name: `${campaign.name} (คัดลอก)`,
      status: 'draft',
      sentCount: 0,
      openedCount: 0,
      clickedCount: 0,
      conversionCount: 0,
      openRate: 0,
      clickRate: 0,
      conversionRate: 0,
      scheduledAt: null,
      sentAt: null,
      createdAt: new Date().toISOString(),
    };

    emailCampaigns.value.unshift(newCampaign);

    useToast().add({
      title: 'สำเร็จ',
      description: `คัดลอกแคมเปญ ${campaign.name} เรียบร้อยแล้ว`,
      color: 'success',
    });
  } catch (error) {
    useToast().add({
      title: 'ผิดพลาด',
      description: 'ไม่สามารถคัดลอกแคมเปญได้',
      color: 'error',
    });
  }
};

const handleDeleteCampaign = async (campaign: any) => {
  if (confirm(`คุณต้องการลบแคมเปญ "${campaign.name}" ใช่หรือไม่?`)) {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const index = emailCampaigns.value.findIndex(c => c.id === campaign.id);
      if (index > -1) {
        emailCampaigns.value.splice(index, 1);
      }

      useToast().add({
        title: 'สำเร็จ',
        description: `ลบแคมเปญ ${campaign.name} เรียบร้อยแล้ว`,
        color: 'success',
      });
    } catch (error) {
      useToast().add({
        title: 'ผิดพลาด',
        description: 'ไม่สามารถลบแคมเปญได้',
        color: 'error',
      });
    }
  }
};

const handleCreateCampaign = () => {
  navigateTo(`/dashboard/${siteId.value}/email-marketing/create`);
};

const handleExportCampaigns = () => {
  useToast().add({
    title: 'สำเร็จ',
    description: 'ส่งออกข้อมูลแคมเปญเรียบร้อยแล้ว',
    color: 'success',
  });
};
</script>

<template>
  <div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Email Marketing
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          จัดการแคมเปญอีเมลและการตลาดผ่านอีเมล
        </p>
      </div>
      <div class="flex items-center gap-2">
        <UButton 
          @click="handleExportCampaigns"
          variant="outline"
          icon="i-heroicons-arrow-down-tray"
        >
          ส่งออก
        </UButton>
        <UButton 
          @click="handleCreateCampaign"
          class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
          สร้างแคมเปญใหม่
        </UButton>
      </div>
    </div>

    <!-- Analytics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">แคมเปญทั้งหมด</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalCampaigns }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">{{ analytics.activeCampaigns }} ใช้งาน</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-envelope" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ผู้รับรวม</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalRecipients.toLocaleString() }}</p>
            <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">{{ analytics.totalSent.toLocaleString() }} ส่งแล้ว</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-users" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">อัตราการเปิด</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.averageOpenRate }}%</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">เฉลี่ย</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-eye" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">รายได้รวม</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatCurrency(analytics.totalRevenue) }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">จาก Email Marketing</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-currency-dollar" class="w-8 h-8" />
          </div>
        </div>
      </UCard>
    </div>

    <!-- Filters -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-funnel" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวกรองและค้นหา</h2>
        </div>
      </template>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหาแคมเปญ</label>
          <UInput
            v-model="searchQuery"
            placeholder="ค้นหาตามชื่อหรือหัวข้อ..."
            icon="i-heroicons-magnifying-glass"
            size="xl"
            class="w-full"
          />
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สถานะ</label>
          <USelect
            v-model="selectedStatus"
            :items="statuses"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกสถานะ"
            size="xl"
            class="w-full"
          />
        </div>

        <!-- Type Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ประเภท</label>
          <USelect
            v-model="selectedType"
            :items="types"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกประเภท"
            size="xl"
            class="w-full"
          />
        </div>

        <!-- Sort -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เรียงลำดับ</label>
          <div class="flex gap-2">
            <USelect
              v-model="sortBy"
              :items="[
                { value: 'created_at', label: 'วันที่สร้าง' },
                { value: 'sentCount', label: 'จำนวนส่ง' },
                { value: 'openRate', label: 'อัตราการเปิด' },
                { value: 'name', label: 'ชื่อ' }
              ]"
              option-attribute="label"
              value-attribute="value"
              size="xl"
              class="flex-1"
            />
            <UButton
              @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'"
              variant="outline"
              size="sm"
              class="px-3"
            >
              <Icon 
                :name="sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down'" 
                class="w-4 h-4" 
              />
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Campaigns Table -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <Icon name="i-heroicons-envelope" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รายการแคมเปญ</h2>
            <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-400 rounded-full">
              {{ filteredCampaigns.length }} รายการ
            </span>
          </div>
        </div>
      </template>
      
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="flex items-center gap-3">
          <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-blue-600" />
          <span class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</span>
        </div>
      </div>

      <div v-else-if="paginatedCampaigns.length === 0" class="text-center py-12">
        <div class="text-gray-400 dark:text-gray-500 mb-4">
          <Icon name="i-heroicons-envelope" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">ไม่พบแคมเปญ</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          ยังไม่มีแคมเปญในระบบ หรือไม่ตรงกับเงื่อนไขการค้นหา
        </p>
        <UButton @click="handleCreateCampaign" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
          สร้างแคมเปญแรก
        </UButton>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">แคมเปญ</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">ประเภท</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">สถิติ</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>อัตราการเปิด</span>
                  <UButton
                    @click="handleSort('openRate')"
                    variant="ghost"
                    size="xs"
                    class="p-1"
                  >
                    <Icon 
                      :name="sortBy === 'openRate' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'" 
                      class="w-3 h-3" 
                    />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">สถานะ</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">วันที่ส่ง</th>
              <th class="text-right py-4 px-6 font-semibold text-gray-900 dark:text-white">การดำเนินการ</th>
            </tr>
          </thead>
          <tbody>
            <tr 
              v-for="campaign in paginatedCampaigns" 
              :key="campaign.id"
              class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
            >
              <td class="py-4 px-6">
                <div class="flex items-center gap-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-semibold text-sm">
                    {{ campaign.name.charAt(0).toUpperCase() }}
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">{{ campaign.name }}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-1">{{ campaign.subject }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-500">{{ campaign.recipientCount.toLocaleString() }} ผู้รับ</p>
                  </div>
                </div>
              </td>
              <td class="py-4 px-6">
                <span 
                  class="px-3 py-1 text-xs rounded-full font-medium"
                  :class="getTypeBadgeClass(campaign.type)"
                >
                  {{ getTypeLabel(campaign.type) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="space-y-1 text-sm">
                  <p class="text-gray-600 dark:text-gray-400">
                    ส่ง: {{ campaign.sentCount.toLocaleString() }}
                  </p>
                  <p class="text-gray-600 dark:text-gray-400">
                    เปิด: {{ campaign.openedCount.toLocaleString() }}
                  </p>
                  <p class="text-gray-600 dark:text-gray-400">
                    คลิก: {{ campaign.clickedCount.toLocaleString() }}
                  </p>
                </div>
              </td>
              <td class="py-4 px-6">
                <div class="space-y-1">
                  <p class="font-semibold text-gray-900 dark:text-white">
                    {{ formatPercentage(campaign.openRate) }}
                  </p>
                  <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      :style="{ width: `${campaign.openRate}%` }"
                    ></div>
                  </div>
                  <p class="text-xs text-gray-500 dark:text-gray-500">
                    คลิก: {{ formatPercentage(campaign.clickRate) }}
                  </p>
                </div>
              </td>
              <td class="py-4 px-6">
                <span 
                  class="px-3 py-1 text-xs rounded-full font-medium"
                  :class="getStatusBadgeClass(campaign.status)"
                >
                  {{ getStatusLabel(campaign.status) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {{ campaign.sentAt ? formatDate(campaign.sentAt) : (campaign.scheduledAt ? formatDate(campaign.scheduledAt) : '-') }}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center justify-end gap-2">
                  <UButton
                    @click="handleViewCampaign(campaign)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                  >
                    <Icon name="i-heroicons-eye" class="w-4 h-4" />
                  </UButton>
                  <UButton
                    @click="handleEditCampaign(campaign)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-green-50 dark:hover:bg-green-900/20 text-green-600 dark:text-green-400"
                  >
                    <Icon name="i-heroicons-pencil-square" class="w-4 h-4" />
                  </UButton>
                  <UButton
                    @click="handleDuplicateCampaign(campaign)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-purple-50 dark:hover:bg-purple-900/20 text-purple-600 dark:text-purple-400"
                  >
                    <Icon name="i-heroicons-document-duplicate" class="w-4 h-4" />
                  </UButton>
                  <UButton
                    @click="handleDeleteCampaign(campaign)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400"
                  >
                    <Icon name="i-heroicons-trash" class="w-4 h-4" />
                  </UButton>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex items-center justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} ถึง {{ Math.min(currentPage * itemsPerPage, filteredCampaigns.length) }} 
          จาก {{ filteredCampaigns.length }} รายการ
        </div>
        <div class="flex items-center gap-2">
          <UButton
            @click="handlePageChange(currentPage - 1)"
            :disabled="currentPage === 1"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-left" class="w-4 h-4" />
          </UButton>
          
          <div class="flex items-center gap-1">
            <UButton
              v-for="page in Math.min(5, totalPages)"
              :key="page"
              @click="handlePageChange(page)"
              :variant="currentPage === page ? 'solid' : 'outline'"
              size="sm"
              class="w-8 h-8 p-0"
            >
              {{ page }}
            </UButton>
          </div>
          
          <UButton
            @click="handlePageChange(currentPage + 1)"
            :disabled="currentPage === totalPages"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-right" class="w-4 h-4" />
          </UButton>
        </div>
      </div>
    </UCard>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Line clamp utility */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 