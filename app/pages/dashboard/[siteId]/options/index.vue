<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

definePageMeta({
    layout: 'dashboard',
    middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// Mock data for option sets
const optionSets = ref([
    {
        id: '1',
        name: 'ขนาดเสื้อผ้า',
        type: 'single',
        description: 'ขนาดมาตรฐานสำหรับเสื้อผ้า',
        options: [
            { value: 'XS', label: 'XS', position: 1 },
            { value: 'S', label: 'S', position: 2 },
            { value: 'M', label: 'M', position: 3 },
            { value: 'L', label: 'L', position: 4 },
            { value: 'XL', label: 'XL', position: 5 },
            { value: 'XXL', label: 'XXL', position: 6 },
        ],
        usageCount: 15,
        createdAt: '2024-01-15',
        updatedAt: '2024-01-20',
    },
    {
        id: '2',
        name: 'สีสินค้า',
        type: 'color',
        description: 'สีพื้นฐานสำหรับสินค้า',
        options: [
            { value: 'red', label: 'แดง', color: '#FF0000', position: 1 },
            { value: 'blue', label: 'น้ำเงิน', color: '#0000FF', position: 2 },
            { value: 'green', label: 'เขียว', color: '#00FF00', position: 3 },
            { value: 'black', label: 'ดำ', color: '#000000', position: 4 },
            { value: 'white', label: 'ขาว', color: '#FFFFFF', position: 5 },
        ],
        usageCount: 23,
        createdAt: '2024-01-10',
        updatedAt: '2024-01-18',
    },
    {
        id: '3',
        name: 'วัสดุ',
        type: 'single',
        description: 'ประเภทวัสดุที่ใช้ในการผลิต',
        options: [
            { value: 'cotton', label: 'ผ้าฝ้าย', position: 1 },
            { value: 'polyester', label: 'โพลีเอสเตอร์', position: 2 },
            { value: 'silk', label: 'ผ้าไหม', position: 3 },
            { value: 'wool', label: 'ผ้าขนสัตว์', position: 4 },
        ],
        usageCount: 8,
        createdAt: '2024-01-12',
        updatedAt: '2024-01-16',
    },
]);

const loading = ref(false);
const searchQuery = ref('');
const selectedType = ref('all');

// Filter options
const filteredOptionSets = computed(() => {
    let filtered = optionSets.value;

    if (searchQuery.value) {
        filtered = filtered.filter(set =>
            set.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            set.description.toLowerCase().includes(searchQuery.value.toLowerCase())
        );
    }

    if (selectedType.value !== 'all') {
        filtered = filtered.filter(set => set.type === selectedType.value);
    }

    return filtered;
});

// Type options for filter
const typeOptions = [
    { value: 'all', label: 'ทั้งหมด' },
    { value: 'single', label: 'ตัวเลือกเดี่ยว' },
    { value: 'color', label: 'สี' },
    { value: 'image', label: 'รูปภาพ' },
];

// Actions
const handleCreate = () => {
    navigateTo(`/dashboard/${siteId.value}/options/create`);
};

const handleEdit = (id: string) => {
    navigateTo(`/dashboard/${siteId.value}/options/${id}/edit`);
};

const handleDuplicate = async (optionSet: any) => {
    loading.value = true;
    try {
        // Mock API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        const duplicated = {
            ...optionSet,
            id: Date.now().toString(),
            name: `${optionSet.name} (สำเนา)`,
            usageCount: 0,
            createdAt: new Date().toISOString().split('T')[0],
            updatedAt: new Date().toISOString().split('T')[0],
        };

        optionSets.value.unshift(duplicated);

        useToast().add({
            title: 'สำเร็จ',
            description: 'คัดลอกชุดตัวเลือกเรียบร้อยแล้ว',
            color: 'success',
        });
    } catch (error) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'ไม่สามารถคัดลอกชุดตัวเลือกได้',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

const handleDelete = async (id: string) => {
    const optionSet = optionSets.value.find(set => set.id === id);
    if (!optionSet) return;

    if (optionSet.usageCount > 0) {
        useToast().add({
            title: 'ไม่สามารถลบได้',
            description: `ชุดตัวเลือกนี้ถูกใช้งานอยู่ใน ${optionSet.usageCount} สินค้า`,
            color: 'error',
        });
        return;
    }

    const confirmed = confirm('คุณแน่ใจหรือไม่ที่จะลบชุดตัวเลือกนี้?');
    if (!confirmed) return;

    loading.value = true;
    try {
        // Mock API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        optionSets.value = optionSets.value.filter(set => set.id !== id);

        useToast().add({
            title: 'สำเร็จ',
            description: 'ลบชุดตัวเลือกเรียบร้อยแล้ว',
            color: 'success',
        });
    } catch (error) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'ไม่สามารถลบชุดตัวเลือกได้',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

// Stats
const stats = computed(() => ({
    total: optionSets.value.length,
    totalUsage: optionSets.value.reduce((sum, set) => sum + set.usageCount, 0),
    mostUsed: optionSets.value.reduce((max, set) =>
        set.usageCount > max.usageCount ? set : max, optionSets.value[0] || { usageCount: 0 }
    ),
}));
</script>

<template>
    <div class="space-y-8">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    ตัวเลือกสินค้า
                </h1>
                <p class="text-gray-600 dark:text-gray-400">
                    จัดการชุดตัวเลือกสินค้าเพื่อใช้ซ้ำกับสินค้าต่างๆ
                </p>
            </div>
            <div class="flex items-center gap-3">
                <UButton @click="handleCreate"
                    class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
                    <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
                    สร้างชุดตัวเลือกใหม่
                </UButton>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <UCard class="overflow-hidden">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                        <Icon name="i-heroicons-squares-2x2" class="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">ชุดตัวเลือกทั้งหมด</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.total }}</p>
                    </div>
                </div>
            </UCard>

            <UCard class="overflow-hidden">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                        <Icon name="i-heroicons-chart-bar" class="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">การใช้งานรวม</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.totalUsage }}</p>
                    </div>
                </div>
            </UCard>

            <UCard class="overflow-hidden">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
                        <Icon name="i-heroicons-star" class="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">ใช้มากที่สุด</p>
                        <p class="text-lg font-bold text-gray-900 dark:text-white">{{ stats.mostUsed?.name || '-' }}</p>
                    </div>
                </div>
            </UCard>
        </div>

        <!-- Filters -->
        <UCard class="overflow-hidden">
            <div class="flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <UInput v-model="searchQuery" placeholder="ค้นหาชุดตัวเลือก..." class="w-full">
                        <template #leading>
                            <Icon name="i-heroicons-magnifying-glass" class="w-4 h-4" />
                        </template>
                    </UInput>
                </div>
                <div class="sm:w-48">
                    <USelect v-model="selectedType" :items="typeOptions" option-attribute="label"
                        value-attribute="value" placeholder="ประเภท" class="w-full" />
                </div>
            </div>
        </UCard>

        <!-- Option Sets List -->
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            <UCard v-for="optionSet in filteredOptionSets" :key="optionSet.id"
                class="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <template #header>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg">
                                <Icon :name="optionSet.type === 'color' ? 'i-heroicons-swatch' :
                                    optionSet.type === 'image' ? 'i-heroicons-photo' :
                                        'i-heroicons-list-bullet'" class="w-4 h-4 text-white" />
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                    {{ optionSet.name }}
                                </h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    {{ optionSet.options.length }} ตัวเลือก
                                </p>
                            </div>
                        </div>
                        <UDropdown :items="[
                            [
                                {
                                    label: 'แก้ไข',
                                    icon: 'i-heroicons-pencil-square',
                                    click: () => handleEdit(optionSet.id)
                                },
                                {
                                    label: 'คัดลอก',
                                    icon: 'i-heroicons-document-duplicate',
                                    click: () => handleDuplicate(optionSet)
                                }
                            ],
                            [
                                {
                                    label: 'ลบ',
                                    icon: 'i-heroicons-trash',
                                    click: () => handleDelete(optionSet.id),
                                    disabled: optionSet.usageCount > 0
                                }
                            ]
                        ]">
                            <UButton variant="ghost" size="sm">
                                <Icon name="i-heroicons-ellipsis-vertical" class="w-4 h-4" />
                            </UButton>
                        </UDropdown>
                    </div>
                </template>

                <div class="space-y-4">
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        {{ optionSet.description }}
                    </p>

                    <!-- Options Preview -->
                    <div class="space-y-2">
                        <p class="text-sm font-medium text-gray-700 dark:text-gray-300">ตัวเลือก:</p>
                        <div class="flex flex-wrap gap-2">
                            <template v-if="optionSet.type === 'color'">
                                <div v-for="option in optionSet.options.slice(0, 5)" :key="option.value"
                                    class="flex items-center gap-2">
                                    <div class="w-4 h-4 rounded-full border border-gray-300"
                                        :style="{ backgroundColor: option.color }"></div>
                                    <span class="text-xs text-gray-600 dark:text-gray-400">{{ option.label }}</span>
                                </div>
                            </template>
                            <template v-else>
                                <UBadge v-for="option in optionSet.options.slice(0, 5)" :key="option.value"
                                    variant="subtle" size="sm">
                                    {{ option.label }}
                                </UBadge>
                            </template>
                            <UBadge v-if="optionSet.options.length > 5" variant="outline" size="sm">
                                +{{ optionSet.options.length - 5 }}
                            </UBadge>
                        </div>
                    </div>

                    <!-- Usage Stats -->
                    <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                        <div class="flex items-center gap-2">
                            <Icon name="i-heroicons-cube" class="w-4 h-4 text-gray-400" />
                            <span class="text-sm text-gray-600 dark:text-gray-400">
                                ใช้ใน {{ optionSet.usageCount }} สินค้า
                            </span>
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-500">
                            อัปเดต {{ optionSet.updatedAt }}
                        </div>
                    </div>
                </div>
            </UCard>
        </div>

        <!-- Empty State -->
        <div v-if="filteredOptionSets.length === 0" class="text-center py-12">
            <div
                class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                <Icon name="i-heroicons-squares-2x2" class="w-12 h-12 text-gray-400" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {{ searchQuery ? 'ไม่พบชุดตัวเลือกที่ค้นหา' : 'ยังไม่มีชุดตัวเลือก' }}
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                {{ searchQuery ? 'ลองเปลี่ยนคำค้นหาหรือตัวกรอง' : 'เริ่มต้นสร้างชุดตัวเลือกแรกของคุณ' }}
            </p>
            <UButton v-if="!searchQuery" @click="handleCreate"
                class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">
                <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
                สร้างชุดตัวเลือกใหม่
            </UButton>
        </div>
    </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.space-y-8>* {
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.hover\:shadow-lg:hover {
    transform: translateY(-2px);
}
</style>