<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useVuelidate } from '@vuelidate/core';
import { required, minLength, maxLength, helpers } from '@vuelidate/validators';

definePageMeta({
    layout: 'dashboard',
    middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// Form data
const form = reactive({
    name: '',
    type: 'single' as 'single' | 'color' | 'image',
    description: '',
    options: [] as Array<{
        id: string;
        value: string;
        label: string;
        color?: string;
        image?: string;
        position: number;
    }>,
});

// Validation rules
const rules = computed(() => ({
    name: {
        required: helpers.withMessage('ชื่อชุดตัวเลือกต้องไม่ว่าง', required),
        minLength: helpers.withMessage('ชื่อชุดตัวเลือกต้องมีอย่างน้อย 2 ตัวอักษร', minLength(2)),
        maxLength: helpers.withMessage('ชื่อชุดตัวเลือกต้องไม่เกิน 100 ตัวอักษร', maxLength(100)),
    },
    type: {
        required: helpers.withMessage('ต้องเลือกประเภทตัวเลือก', required),
    },
    description: {
        maxLength: helpers.withMessage('คำอธิบายต้องไม่เกิน 500 ตัวอักษร', maxLength(500)),
    },
}));

// Initialize Vuelidate
const v$ = useVuelidate(rules, form);

const loading = ref(false);

// Type options
const typeOptions = [
    { value: 'single', label: 'ตัวเลือกเดี่ยว', description: 'ข้อความธรรมดา เช่น ขนาด, วัสดุ' },
    { value: 'color', label: 'สี', description: 'ตัวเลือกสีพร้อมแสดงสี' },
    { value: 'image', label: 'รูปภาพ', description: 'ตัวเลือกพร้อมรูปภาพ' },
];

// Add new option
const addOption = () => {
    const newOption = {
        id: Date.now().toString(),
        value: '',
        label: '',
        position: form.options.length + 1,
    };

    if (form.type === 'color') {
        newOption.color = '#000000';
    } else if (form.type === 'image') {
        newOption.image = '';
    }

    form.options.push(newOption);
};

// Remove option
const removeOption = (id: string) => {
    form.options = form.options.filter(option => option.id !== id);
    // Reorder positions
    form.options.forEach((option, index) => {
        option.position = index + 1;
    });
};

// Move option up/down
const moveOption = (id: string, direction: 'up' | 'down') => {
    const index = form.options.findIndex(option => option.id === id);
    if (index === -1) return;

    const newIndex = direction === 'up' ? index - 1 : index + 1;
    if (newIndex < 0 || newIndex >= form.options.length) return;

    // Swap positions
    [form.options[index], form.options[newIndex]] = [form.options[newIndex], form.options[index]];

    // Update position numbers
    form.options.forEach((option, idx) => {
        option.position = idx + 1;
    });
};

// Handle image upload
const handleImageUpload = (optionId: string, event: Event) => {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
        const option = form.options.find(opt => opt.id === optionId);
        if (option) {
            option.image = e.target?.result as string;
        }
    };
    reader.readAsDataURL(file);
};

// Validate options
const validateOptions = () => {
    if (form.options.length === 0) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'ต้องมีตัวเลือกอย่างน้อย 1 ตัว',
            color: 'error',
        });
        return false;
    }

    for (const option of form.options) {
        if (!option.value.trim() || !option.label.trim()) {
            useToast().add({
                title: 'ข้อผิดพลาด',
                description: 'กรุณากรอกค่าและป้ายกำกับให้ครบทุกตัวเลือก',
                color: 'error',
            });
            return false;
        }
    }

    // Check for duplicate values
    const values = form.options.map(opt => opt.value);
    const uniqueValues = new Set(values);
    if (values.length !== uniqueValues.size) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'ค่าตัวเลือกต้องไม่ซ้ำกัน',
            color: 'error',
        });
        return false;
    }

    return true;
};

// Handle form submission
const handleSubmit = async () => {
    // Validate form using Vuelidate
    const isValid = await v$.value.$validate();
    if (!isValid) {
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: 'กรุณาตรวจสอบข้อมูลที่กรอก',
            color: 'error',
        });
        return;
    }

    // Validate options
    if (!validateOptions()) return;

    loading.value = true;
    try {
        // Mock API call
        const optionSetData = {
            name: form.name,
            type: form.type,
            description: form.description,
            options: form.options.map(option => ({
                value: option.value,
                label: option.label,
                color: option.color,
                image: option.image,
                position: option.position,
            })),
        };

        console.log('Creating option set:', optionSetData);

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        useToast().add({
            title: 'สำเร็จ',
            description: 'สร้างชุดตัวเลือกเรียบร้อยแล้ว',
            color: 'success',
        });

        // Navigate back to options page
        await navigateTo(`/dashboard/${siteId.value}/options`);
    } catch (error: any) {
        console.error('Error creating option set:', error);
        useToast().add({
            title: 'ข้อผิดพลาด',
            description: error.message || 'ไม่สามารถสร้างชุดตัวเลือกได้',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

// Handle cancel
const handleCancel = () => {
    navigateTo(`/dashboard/${siteId.value}/options`);
};

// Add some default options when type changes
watch(() => form.type, (newType) => {
    if (form.options.length === 0) {
        if (newType === 'single') {
            // Add some default size options
            const defaultOptions = ['S', 'M', 'L', 'XL'];
            defaultOptions.forEach((size, index) => {
                form.options.push({
                    id: Date.now().toString() + index,
                    value: size.toLowerCase(),
                    label: size,
                    position: index + 1,
                });
            });
        } else if (newType === 'color') {
            // Add some default colors
            const defaultColors = [
                { value: 'red', label: 'แดง', color: '#FF0000' },
                { value: 'blue', label: 'น้ำเงิน', color: '#0000FF' },
                { value: 'green', label: 'เขียว', color: '#00FF00' },
            ];
            defaultColors.forEach((color, index) => {
                form.options.push({
                    id: Date.now().toString() + index,
                    value: color.value,
                    label: color.label,
                    color: color.color,
                    position: index + 1,
                });
            });
        }
    }
});

// Initialize with one empty option
onMounted(() => {
    if (form.options.length === 0) {
        addOption();
    }
});
</script>

<template>
    <div class="space-y-8">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    สร้างชุดตัวเลือกใหม่
                </h1>
                <p class="text-gray-600 dark:text-gray-400">
                    สร้างชุดตัวเลือกสินค้าเพื่อใช้ซ้ำกับสินค้าต่างๆ
                </p>
            </div>
            <div class="flex items-center gap-3">
                <UButton @click="handleCancel" variant="outline" size="sm"
                    class="hover:bg-gray-100 dark:hover:bg-gray-800">
                    <Icon name="i-heroicons-arrow-left" class="w-4 h-4 mr-2" />
                    ย้อนกลับ
                </UButton>
                <UButton @click="handleSubmit" :loading="loading"
                    class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
                    <Icon name="i-heroicons-check" class="w-5 h-5 mr-2" />
                    สร้างชุดตัวเลือก
                </UButton>
            </div>
        </div>

        <!-- Form -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Form -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <UCard class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                                <Icon name="i-heroicons-information-circle" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ข้อมูลพื้นฐาน</h2>
                        </div>
                    </template>

                    <div class="space-y-6">
                        <!-- Option Set Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                ชื่อชุดตัวเลือก <span class="text-red-500">*</span>
                            </label>
                            <UInput v-model="form.name" placeholder="เช่น ขนาดเสื้อผ้า, สีสินค้า"
                                :error="v$.name.$error ? v$.name.$errors[0].$message : ''" class="w-full" />
                        </div>

                        <!-- Type Selection -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                ประเภทตัวเลือก <span class="text-red-500">*</span>
                            </label>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div v-for="typeOption in typeOptions" :key="typeOption.value" class="relative">
                                    <input :id="typeOption.value" v-model="form.type" :value="typeOption.value"
                                        type="radio" class="sr-only" />
                                    <label :for="typeOption.value"
                                        class="flex flex-col p-4 border-2 rounded-lg cursor-pointer transition-all duration-200"
                                        :class="form.type === typeOption.value
                                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                                            : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'">
                                        <div class="flex items-center gap-2 mb-2">
                                            <Icon :name="typeOption.value === 'color' ? 'i-heroicons-swatch' :
                                                typeOption.value === 'image' ? 'i-heroicons-photo' :
                                                    'i-heroicons-list-bullet'" class="w-5 h-5"
                                                :class="form.type === typeOption.value ? 'text-blue-600' : 'text-gray-400'" />
                                            <span class="font-medium"
                                                :class="form.type === typeOption.value ? 'text-blue-900 dark:text-blue-100' : 'text-gray-900 dark:text-white'">
                                                {{ typeOption.label }}
                                            </span>
                                        </div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">
                                            {{ typeOption.description }}
                                        </p>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                คำอธิบาย
                            </label>
                            <UTextarea v-model="form.description" placeholder="อธิบายการใช้งานชุดตัวเลือกนี้"
                                :error="v$.description.$error ? v$.description.$errors[0].$message : ''" :rows="3"
                                class="w-full" />
                        </div>
                    </div>
                </UCard>

                <!-- Options Management -->
                <UCard class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                                    <Icon name="i-heroicons-squares-2x2" class="w-5 h-5 text-white" />
                                </div>
                                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวเลือก</h2>
                            </div>
                            <UButton @click="addOption" size="sm" variant="outline">
                                <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
                                เพิ่มตัวเลือก
                            </UButton>
                        </div>
                    </template>

                    <div class="space-y-4">
                        <div v-for="(option, index) in form.options" :key="option.id"
                            class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="flex items-start gap-4">
                                <!-- Move buttons -->
                                <div class="flex flex-col gap-1">
                                    <UButton @click="moveOption(option.id, 'up')" :disabled="index === 0" size="xs"
                                        variant="outline">
                                        <Icon name="i-heroicons-chevron-up" class="w-3 h-3" />
                                    </UButton>
                                    <UButton @click="moveOption(option.id, 'down')"
                                        :disabled="index === form.options.length - 1" size="xs" variant="outline">
                                        <Icon name="i-heroicons-chevron-down" class="w-3 h-3" />
                                    </UButton>
                                </div>

                                <!-- Option fields -->
                                <div class="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            ค่า (Value) <span class="text-red-500">*</span>
                                        </label>
                                        <UInput v-model="option.value" placeholder="เช่น red, large" class="w-full" />
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            ป้ายกำกับ <span class="text-red-500">*</span>
                                        </label>
                                        <UInput v-model="option.label" placeholder="เช่น แดง, ใหญ่" class="w-full" />
                                    </div>
                                </div>

                                <!-- Color picker for color type -->
                                <div v-if="form.type === 'color'" class="flex flex-col items-center gap-2">
                                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">สี</label>
                                    <input v-model="option.color" type="color"
                                        class="w-12 h-8 rounded border border-gray-300 cursor-pointer" />
                                </div>

                                <!-- Image upload for image type -->
                                <div v-if="form.type === 'image'" class="flex flex-col items-center gap-2">
                                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">รูปภาพ</label>
                                    <div class="relative">
                                        <input type="file" accept="image/*"
                                            @change="handleImageUpload(option.id, $event)" class="sr-only"
                                            :id="`image-${option.id}`" />
                                        <label :for="`image-${option.id}`"
                                            class="flex items-center justify-center w-16 h-16 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400 transition-colors">
                                            <img v-if="option.image" :src="option.image" alt="Option image"
                                                class="w-full h-full object-cover rounded-lg" />
                                            <Icon v-else name="i-heroicons-camera" class="w-6 h-6 text-gray-400" />
                                        </label>
                                    </div>
                                </div>

                                <!-- Remove button -->
                                <UButton @click="removeOption(option.id)" size="sm" color="red" variant="outline">
                                    <Icon name="i-heroicons-trash" class="w-4 h-4" />
                                </UButton>
                            </div>
                        </div>

                        <!-- Empty state -->
                        <div v-if="form.options.length === 0" class="text-center py-8">
                            <div
                                class="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                                <Icon name="i-heroicons-squares-2x2" class="w-8 h-8 text-gray-400" />
                            </div>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">ยังไม่มีตัวเลือก</p>
                            <UButton @click="addOption" variant="outline">
                                <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
                                เพิ่มตัวเลือกแรก
                            </UButton>
                        </div>
                    </div>
                </UCard>
            </div>

            <!-- Preview Sidebar -->
            <div class="space-y-6">
                <!-- Preview -->
                <UCard class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
                                <Icon name="i-heroicons-eye" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวอย่าง</h2>
                        </div>
                    </template>

                    <div class="space-y-4">
                        <div v-if="form.name">
                            <h3 class="font-medium text-gray-900 dark:text-white">{{ form.name }}</h3>
                            <p v-if="form.description" class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {{ form.description }}
                            </p>
                        </div>

                        <div v-if="form.options.length > 0">
                            <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ตัวเลือก:</p>
                            <div class="space-y-2">
                                <template v-if="form.type === 'color'">
                                    <div v-for="option in form.options" :key="option.id"
                                        class="flex items-center gap-2 p-2 border border-gray-200 dark:border-gray-700 rounded">
                                        <div class="w-4 h-4 rounded-full border border-gray-300"
                                            :style="{ backgroundColor: option.color }"></div>
                                        <span class="text-sm">{{ option.label || 'ไม่มีป้ายกำกับ' }}</span>
                                    </div>
                                </template>
                                <template v-else-if="form.type === 'image'">
                                    <div v-for="option in form.options" :key="option.id"
                                        class="flex items-center gap-2 p-2 border border-gray-200 dark:border-gray-700 rounded">
                                        <div
                                            class="w-8 h-8 bg-gray-100 dark:bg-gray-800 rounded flex items-center justify-center">
                                            <img v-if="option.image" :src="option.image" alt="Option"
                                                class="w-full h-full object-cover rounded" />
                                            <Icon v-else name="i-heroicons-photo" class="w-4 h-4 text-gray-400" />
                                        </div>
                                        <span class="text-sm">{{ option.label || 'ไม่มีป้ายกำกับ' }}</span>
                                    </div>
                                </template>
                                <template v-else>
                                    <div class="flex flex-wrap gap-2">
                                        <UBadge v-for="option in form.options" :key="option.id" variant="subtle"
                                            size="sm">
                                            {{ option.label || 'ไม่มีป้ายกำกับ' }}
                                        </UBadge>
                                    </div>
                                </template>
                            </div>
                        </div>

                        <div v-else class="text-center py-4">
                            <Icon name="i-heroicons-squares-2x2" class="w-8 h-8 text-gray-400 mx-auto mb-2" />
                            <p class="text-sm text-gray-500">เพิ่มตัวเลือกเพื่อดูตัวอย่าง</p>
                        </div>
                    </div>
                </UCard>

                <!-- Tips -->
                <UCard class="overflow-hidden">
                    <template #header>
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg">
                                <Icon name="i-heroicons-light-bulb" class="w-5 h-5 text-white" />
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">เคล็ดลับ</h2>
                        </div>
                    </template>

                    <div class="space-y-3 text-sm text-gray-600 dark:text-gray-400">
                        <div class="flex items-start gap-2">
                            <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <p>ใช้ชื่อที่สื่อความหมายชัดเจน เช่น "ขนาดเสื้อผ้า" แทน "ขนาด"</p>
                        </div>
                        <div class="flex items-start gap-2">
                            <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <p>ค่า (Value) ควรเป็นภาษาอังกฤษหรือตัวเลข เช่น "red", "large"</p>
                        </div>
                        <div class="flex items-start gap-2">
                            <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <p>ป้ายกำกับควรเป็นภาษาไทยที่ลูกค้าเข้าใจง่าย</p>
                        </div>
                        <div class="flex items-start gap-2">
                            <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <p>เรียงลำดับตัวเลือกตามความสำคัญหรือขนาด</p>
                        </div>
                    </div>
                </UCard>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.space-y-8>* {
    animation: fadeInUp 0.6s ease-out forwards;
}
</style>