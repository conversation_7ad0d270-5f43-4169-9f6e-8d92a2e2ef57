<script setup lang="ts"> 

// Types
interface LogEntry {
  id: string
  timestamp: string
  level: 'info' | 'warning' | 'error' | 'debug'
  category: string
  message: string
  details?: string
  user?: string
  stackTrace?: string
  context?: any
}

// Route and utilities
const route = useRoute()
const toast = useToast()

// Get siteId from route
const siteId = computed(() => route.params.siteId as string)

// State
const logs = ref<LogEntry[]>([])
const filteredLogs = ref<LogEntry[]>([])
const searchQuery = ref('')
const selectedLevel = ref('')
const selectedCategory = ref('')
const selectedTimeRange = ref('today')
const currentPage = ref(1)
const pageSize = ref(50)
const isAutoRefresh = ref(false)
const lastUpdate = ref('')
const showLogModal = ref(false)
const selectedLog = ref<LogEntry | null>(null)

// Auto refresh interval
let refreshInterval: NodeJS.Timeout | null = null

// Log stats
const logStats = computed(() => {
  return {
    info: logs.value.filter(log => log.level === 'info').length,
    warning: logs.value.filter(log => log.level === 'warning').length,
    error: logs.value.filter(log => log.level === 'error').length,
    debug: logs.value.filter(log => log.level === 'debug').length
  }
})

// Options
const levelOptions = [
  { label: 'ทั้งหมด', value: 'all' },
  { label: 'Info', value: 'info' },
  { label: 'Warning', value: 'warning' },
  { label: 'Error', value: 'error' },
  { label: 'Debug', value: 'debug' }
]

const categoryOptions = [
  { label: 'ทั้งหมด', value: 'all' },
  { label: 'Authentication', value: 'auth' },
  { label: 'Database', value: 'database' },
  { label: 'API', value: 'api' },
  { label: 'Payment', value: 'payment' },
  { label: 'Email', value: 'email' },
  { label: 'System', value: 'system' }
]

const timeRangeOptions = [
  { label: 'วันนี้', value: 'today' },
  { label: '7 วันที่ผ่านมา', value: '7days' },
  { label: '30 วันที่ผ่านมา', value: '30days' },
  { label: 'ทั้งหมด', value: 'all' }
]

// Methods
const loadLogs = async () => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Mock data
    const mockLogs: LogEntry[] = [
      {
        id: '1',
        timestamp: new Date().toISOString(),
        level: 'info',
        category: 'auth',
        message: 'User logged in successfully',
        details: 'User ID: 123, IP: ***********',
        user: '<EMAIL>'
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 60000).toISOString(),
        level: 'warning',
        category: 'database',
        message: 'Database connection slow',
        details: 'Query took 2.5 seconds to execute',
        context: { query: 'SELECT * FROM products', duration: 2500 }
      },
      {
        id: '3',
        timestamp: new Date(Date.now() - 120000).toISOString(),
        level: 'error',
        category: 'payment',
        message: 'Payment processing failed',
        details: 'Credit card declined',
        stackTrace: 'PaymentError: Card declined\n  at processPayment (/app/payment.js:45)\n  at handleCheckout (/app/checkout.js:123)',
        context: { orderId: 'ORD-12345', amount: 1500 }
      },
      {
        id: '4',
        timestamp: new Date(Date.now() - 180000).toISOString(),
        level: 'debug',
        category: 'api',
        message: 'API request received',
        details: 'GET /api/products',
        context: { method: 'GET', path: '/api/products', userAgent: 'Mozilla/5.0' }
      }
    ]
    
    logs.value = mockLogs
    filterLogs()
    lastUpdate.value = new Date().toLocaleString('th-TH')
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถโหลดบันทึกได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  }
}

const filterLogs = () => {
  let filtered = logs.value

  // Filter by search query
  if (searchQuery.value) {
    filtered = filtered.filter(log =>
      log.message.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      log.category.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      (log.details && log.details.toLowerCase().includes(searchQuery.value.toLowerCase()))
    )
  }

  // Filter by level
  if (selectedLevel.value) {
    filtered = filtered.filter(log => log.level === selectedLevel.value)
  }

  // Filter by category
  if (selectedCategory.value) {
    filtered = filtered.filter(log => log.category === selectedCategory.value)
  }

  // Filter by time range
  if (selectedTimeRange.value !== 'all') {
    const now = new Date()
    let cutoff: Date

    switch (selectedTimeRange.value) {
      case 'today':
        cutoff = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        break
      case '7days':
        cutoff = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30days':
        cutoff = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      default:
        cutoff = new Date(0)
    }

    filtered = filtered.filter(log => new Date(log.timestamp) >= cutoff)
  }

  filteredLogs.value = filtered
  currentPage.value = 1
}

const formatDateTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('th-TH')
}

const getLevelColor = (level: string) => {
  switch (level) {
    case 'info': return 'info'
    case 'warning': return 'warning'
    case 'error': return 'error'
    case 'debug': return 'neutral'
    default: return 'neutral'
  }
}

const viewLogDetails = (log: LogEntry) => {
  selectedLog.value = log
  showLogModal.value = true
}

const copyLog = async (log: LogEntry) => {
  try {
    const logText = `[${formatDateTime(log.timestamp)}] ${log.level.toUpperCase()} ${log.category}: ${log.message}`
    await navigator.clipboard.writeText(logText)
    toast.add({
      title: 'คัดลอกแล้ว',
      description: 'บันทึกถูกคัดลอกไปยังคลิปบอร์ด',
      icon: 'i-heroicons-clipboard',
      color: 'info'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถคัดลอกได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  }
}

const copyLogDetails = async () => {
  if (!selectedLog.value) return

  try {
    const logDetails = JSON.stringify(selectedLog.value, null, 2)
    await navigator.clipboard.writeText(logDetails)
    toast.add({
      title: 'คัดลอกแล้ว',
      description: 'รายละเอียดบันทึกถูกคัดลอกไปยังคลิปบอร์ด',
      icon: 'i-heroicons-clipboard',
      color: 'info'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถคัดลอกได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  }
}

const toggleAutoRefresh = () => {
  isAutoRefresh.value = !isAutoRefresh.value

  if (isAutoRefresh.value) {
    refreshInterval = setInterval(loadLogs, 5000) // Refresh every 5 seconds
    toast.add({
      title: 'เปิด Auto Refresh',
      description: 'บันทึกจะอัพเดทอัตโนมัติทุก 5 วินาที',
      icon: 'i-heroicons-play',
      color: 'success'
    })
  } else {
    if (refreshInterval) {
      clearInterval(refreshInterval)
      refreshInterval = null
    }
    toast.add({
      title: 'ปิด Auto Refresh',
      description: 'หยุดการอัพเดทอัตโนมัติ',
      icon: 'i-heroicons-pause',
      color: 'warning'
    })
  }
}

const exportLogs = () => {
  try {
    const csvContent = [
      ['เวลา', 'ระดับ', 'หมวดหมู่', 'ข้อความ', 'รายละเอียด'].join(','),
      ...filteredLogs.value.map(log => [
        formatDateTime(log.timestamp),
        log.level,
        log.category,
        `"${log.message}"`,
        `"${log.details || ''}"`
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `logs_${siteId.value}_${new Date().toISOString().split('T')[0]}.csv`
    link.click()

    toast.add({
      title: 'ดาวน์โหลดสำเร็จ',
      description: 'ไฟล์บันทึกได้รับการดาวน์โหลดแล้ว',
      icon: 'i-heroicons-arrow-down-tray',
      color: 'success'
    })
  } catch (error) {
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถดาวน์โหลดไฟล์ได้',
      icon: 'i-heroicons-x-circle',
      color: 'error'
    })
  }
}

const clearLogs = () => {
  if (confirm('คุณต้องการล้างบันทึกทั้งหมดหรือไม่?')) {
    logs.value = []
    filteredLogs.value = []
    toast.add({
      title: 'ล้างบันทึกแล้ว',
      description: 'บันทึกทั้งหมดได้รับการลบแล้ว',
      icon: 'i-heroicons-trash',
      color: 'success'
    })
  }
}

// Lifecycle
onMounted(() => {
  loadLogs()
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})

// Page meta
definePageMeta({
  layout: 'dashboard',
  middleware: 'auth'
})
</script> 
<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">บันทึกระบบ</h1>
        <p class="text-gray-600 dark:text-gray-400">ตรวจสอบและจัดการบันทึกการทำงานของระบบ</p>
      </div>
      <div class="flex items-center gap-3">
        <UButton
          variant="outline"
          icon="i-heroicons-arrow-down-tray"
          @click="exportLogs"
        >
          ดาวน์โหลด
        </UButton>
        <UButton
          variant="outline"
          icon="i-heroicons-trash"
          color="error"
          @click="clearLogs"
        >
          ล้างบันทึก
        </UButton>
        <UButton
          :icon="isAutoRefresh ? 'i-heroicons-pause' : 'i-heroicons-play'"
          :variant="isAutoRefresh ? 'solid' : 'outline'"
          @click="toggleAutoRefresh"
        >
          {{ isAutoRefresh ? 'หยุด' : 'เริ่ม' }} Auto Refresh
        </UButton>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Icon name="i-heroicons-information-circle" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ logStats.info }}</div>
            <div class="text-sm text-gray-500">ข้อมูล</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
            <Icon name="i-heroicons-exclamation-triangle" class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ logStats.warning }}</div>
            <div class="text-sm text-gray-500">คำเตือน</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-red-100 dark:bg-red-900/20 rounded-lg">
            <Icon name="i-heroicons-x-circle" class="w-6 h-6 text-red-600 dark:text-red-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ logStats.error }}</div>
            <div class="text-sm text-gray-500">ข้อผิดพลาด</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gray-100 dark:bg-gray-900/20 rounded-lg">
            <Icon name="i-heroicons-bug-ant" class="w-6 h-6 text-gray-600 dark:text-gray-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ logStats.debug }}</div>
            <div class="text-sm text-gray-500">Debug</div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Filters -->
    <UCard>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <UFormField label="ค้นหา">
          <UInput
            v-model="searchQuery"
            placeholder="ค้นหาในบันทึก..."
            icon="i-heroicons-magnifying-glass"
            @input="filterLogs"
          />
        </UFormField>

        <UFormField label="ระดับ">
          <USelect
            v-model="selectedLevel"
            :items="levelOptions"
            placeholder="เลือกระดับ"
            @change="filterLogs"
          />
        </UFormField>

        <UFormField label="หมวดหมู่">
          <USelect
            v-model="selectedCategory"
            :items="categoryOptions"
            placeholder="เลือกหมวดหมู่"
            @change="filterLogs"
          />
        </UFormField>

        <UFormField label="ช่วงเวลา">
          <USelect
            v-model="selectedTimeRange"
            :items="timeRangeOptions"
            placeholder="เลือกช่วงเวลา"
            @change="filterLogs"
          />
        </UFormField>
      </div>
    </UCard>

    <!-- Log Table -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold">บันทึกระบบ</h3>
          <div class="flex items-center gap-2">
            <UBadge
              :color="isAutoRefresh ? 'success' : 'neutral'"
              :label="isAutoRefresh ? 'Live' : 'Static'"
            />
            <span class="text-sm text-gray-500">
              อัพเดทล่าสุด: {{ lastUpdate }}
            </span>
          </div>
        </div>
      </template>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                เวลา
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                ระดับ
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                หมวดหมู่
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                ข้อความ
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                การดำเนินการ
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            <tr
              v-for="log in filteredLogs"
              :key="log.id"
              class="hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ formatDateTime(log.timestamp) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <UBadge
                    :color="getLevelColor(log.level)"
                  :label="log.level.toUpperCase()"
                />
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ log.category }}
              </td>
              <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                <div class="max-w-md">
                  <div class="truncate">{{ log.message }}</div>
                  <div v-if="log.details" class="text-xs text-gray-500 mt-1">
                    {{ log.details }}
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center gap-2">
                  <UButton
                    variant="ghost"
                    size="sm"
                    icon="i-heroicons-eye"
                    @click="viewLogDetails(log)"
                  />
                  <UButton
                    variant="ghost"
                    size="sm"
                    icon="i-heroicons-clipboard"
                    @click="copyLog(log)"
                  />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="flex items-center justify-between mt-4">
        <div class="text-sm text-gray-700 dark:text-gray-300">
          แสดง {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredLogs.length) }} 
          จาก {{ filteredLogs.length }} รายการ
        </div>
        <UPagination
          v-model="currentPage"
          :page-count="pageSize"
          :total="filteredLogs.length"
        />
      </div>
    </UCard>

    <!-- Log Details Modal -->
    <UModal 
      v-model:open="showLogModal"
      title="รายละเอียดบันทึก"
      description="ข้อมูลรายละเอียดของบันทึกระบบ"
    >
      <template #body>
        <div v-if="selectedLog" class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">เวลา</label>
              <div class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ formatDateTime(selectedLog.timestamp) }}
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">ระดับ</label>
              <div class="mt-1">
                <UBadge
                  :color="getLevelColor(selectedLog.level)"
                  :label="selectedLog.level.toUpperCase()"
                />
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">หมวดหมู่</label>
              <div class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ selectedLog.category }}
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">ผู้ใช้</label>
              <div class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ selectedLog.user || 'ระบบ' }}
              </div>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">ข้อความ</label>
            <div class="mt-1 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg text-sm text-gray-900 dark:text-white">
              {{ selectedLog.message }}
            </div>
          </div>

          <div v-if="selectedLog.details">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">รายละเอียด</label>
            <div class="mt-1 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg text-sm text-gray-900 dark:text-white">
              {{ selectedLog.details }}
            </div>
          </div>

          <div v-if="selectedLog.stackTrace">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Stack Trace</label>
            <div class="mt-1 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg text-sm text-gray-900 dark:text-white font-mono">
              <pre class="whitespace-pre-wrap">{{ selectedLog.stackTrace }}</pre>
            </div>
          </div>

          <div v-if="selectedLog.context">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Context</label>
            <div class="mt-1 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg text-sm text-gray-900 dark:text-white">
              <pre class="whitespace-pre-wrap">{{ JSON.stringify(selectedLog.context, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </template>

      <template #footer>
        <div class="flex justify-end gap-2">
          <UButton
            variant="outline"
            @click="showLogModal = false"
          >
            ปิด
          </UButton>
          <UButton
            icon="i-heroicons-clipboard"
            @click="copyLogDetails"
          >
            คัดลอก
          </UButton>
        </div>
      </template>
    </UModal>
  </div>
</template>
