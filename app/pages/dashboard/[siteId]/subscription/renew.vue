<script setup lang="ts">
import { z } from 'zod';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// State
const loading = ref(false);
const site = ref<any>(null);
const subscription = ref<any>(null);
const { getSiteSubscription, updateSubscription } = useSubscriptionApi();

// Form schema
const renewalSchema = z.object({
  plan: z.enum(['free', 'basic', 'premium', 'enterprise']),
  duration: z.enum(['1', '3', '6', '12']),
  autoRenew: z.boolean().default(true),
  paymentMethod: z.string().optional(),
});

type RenewalForm = z.infer<typeof renewalSchema>;

// Form state
const state = reactive<RenewalForm>({
  plan: 'basic',
  duration: '1',
  autoRenew: true,
  paymentMethod: '',
});

// Load data
const loadData = async () => {
  loading.value = true;
  try {
    // Load site data
    const siteResponse = await $fetch(`/api/v1/site/${siteId.value}`);
    site.value = siteResponse.data;

    // Load subscription data
    const subscriptionResponse = await getSiteSubscription(siteId.value);
    subscription.value = subscriptionResponse.data;

    // Set current plan as default
    if (subscription.value?.plan) {
      state.plan = subscription.value.plan;
    }
  } catch (error) {
    console.error('Error loading data:', error);
    useToast().add({
      title: 'ผิดพลาด',
      description: 'ไม่สามารถโหลดข้อมูลได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// Get plan price
const getPlanPrice = (plan: string) => {
  const prices = {
    free: 0,
    basic: 299,
    premium: 599,
    enterprise: 1499,
  };
  return prices[plan as keyof typeof prices] || 0;
};

// Get plan name
const getPlanName = (plan: string) => {
  const names = {
    free: 'ฟรี',
    basic: 'เบสิค',
    premium: 'พรีเมียม',
    enterprise: 'เอ็นเตอร์ไพรส์',
  };
  return names[plan as keyof typeof prices] || plan;
};

// Calculate total price
const totalPrice = computed(() => {
  const basePrice = getPlanPrice(state.plan);
  const duration = parseInt(state.duration);
  const discount = duration >= 12 ? 0.2 : duration >= 6 ? 0.1 : duration >= 3 ? 0.05 : 0;

  return basePrice * duration * (1 - discount);
});

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
  }).format(amount);
};

// Get duration discount
const getDurationDiscount = (duration: string) => {
  const discounts = {
    '1': 0,
    '3': 5,
    '6': 10,
    '12': 20,
  };
  return discounts[duration as keyof typeof discounts] || 0;
};

// Handle form submission
const handleSubmit = async (event: any) => {
  loading.value = true;
  try {
    const response = await updateSubscription(siteId.value, {
      plan: event.data.plan,
      duration: parseInt(event.data.duration),
      autoRenew: event.data.autoRenew,
      paymentMethod: event.data.paymentMethod,
    });

    if (response?.success) {
      useToast().add({
        title: 'สำเร็จ',
        description: 'ต่ออายุเรียบร้อยแล้ว',
        color: 'success',
      });

      navigateTo(`/dashboard/${siteId.value}/subscription`);
    }
  } catch (error: any) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: error.message || 'ไม่สามารถต่ออายุได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// Handle cancel
const handleCancel = () => {
  navigateTo(`/dashboard/${siteId.value}/subscription`);
};

// Load data when component mounts
onMounted(() => {
  loadData();
});

// SEO
useSeoMeta({
  title: 'ต่ออายุการสมัครสมาชิก - ระบบจัดการร้านค้า',
  description: 'ต่ออายุการสมัครสมาชิกและเลือกแพ็คเกจใหม่',
  keywords: 'ต่ออายุ, การสมัครสมาชิก, แพ็คเกจ, การชำระเงิน',
  ogTitle: 'ต่ออายุการสมัครสมาชิก - ระบบจัดการร้านค้า',
  ogDescription: 'ต่ออายุการสมัครสมาชิกและเลือกแพ็คเกจใหม่',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-3">
        <UButton
          @click="handleCancel"
          variant="ghost"
          icon="i-heroicons-arrow-left"
          size="sm"
        />
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            ต่ออายุการสมัครสมาชิก
          </h1>
          <p class="text-gray-600 dark:text-gray-400">
            เลือกแพ็คเกจและระยะเวลาการสมัครสมาชิกใหม่
          </p>
        </div>
      </div>
    </div>

    <div v-if="loading" class="flex items-center justify-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="w-8 h-8 animate-spin text-gray-400" />
      <span class="ml-2 text-gray-600 dark:text-gray-400">กำลังโหลด...</span>
    </div>

    <div v-else-if="!site" class="text-center py-12">
      <UIcon name="i-heroicons-exclamation-triangle" class="w-12 h-12 mx-auto text-gray-400" />
      <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">ไม่พบเว็บไซต์</h3>
      <p class="mt-2 text-gray-600 dark:text-gray-400">ไม่สามารถโหลดข้อมูลเว็บไซต์ได้</p>
    </div>

    <div v-else class="space-y-6">
      <!-- Current Status -->
      <UCard>
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Icon name="i-heroicons-information-circle" class="w-5 h-5 text-blue-600" />
            </div>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
              สถานะปัจจุบัน
            </h2>
          </div>
        </template>

        <div class="space-y-4">
          <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <div class="text-sm text-gray-600 dark:text-gray-400">แผนปัจจุบัน</div>
              <div class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ getPlanName(subscription?.plan || 'free') }}
              </div>
            </div>
            <div class="text-right">
              <div class="text-sm text-gray-600 dark:text-gray-400">ราคา</div>
              <div class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ formatCurrency(getPlanPrice(subscription?.plan || 'free')) }}/เดือน
              </div>
            </div>
          </div>
        </div>
      </UCard>

      <!-- Renewal Form -->
      <UCard>
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <Icon name="i-heroicons-credit-card" class="w-5 h-5 text-green-600" />
            </div>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
              เลือกแพ็คเกจใหม่
            </h2>
          </div>
        </template>

        <UForm :schema="renewalSchema" :state="state" @submit="handleSubmit" class="space-y-6">
          <!-- Plan Selection -->
          <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">เลือกแผน</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div 
                v-for="plan in ['basic', 'premium', 'enterprise']" 
                :key="plan"
                class="border-2 rounded-lg p-4 cursor-pointer transition-colors"
                :class="[
                  state.plan === plan 
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                ]"
                @click="state.plan = plan"
              >
                <div class="flex items-center justify-between">
                  <div>
                    <div class="font-semibold text-gray-900 dark:text-white">
                      {{ getPlanName(plan) }}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                      {{ formatCurrency(getPlanPrice(plan)) }}/เดือน
                    </div>
                  </div>
                  <div v-if="state.plan === plan" class="text-blue-600">
                    <Icon name="i-heroicons-check-circle" class="w-5 h-5" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Duration Selection -->
          <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">ระยะเวลา</h3>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div 
                v-for="duration in ['1', '3', '6', '12']" 
                :key="duration"
                class="border-2 rounded-lg p-4 cursor-pointer transition-colors text-center"
                :class="[
                  state.duration === duration 
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                ]"
                @click="state.duration = duration"
              >
                <div class="font-semibold text-gray-900 dark:text-white">
                  {{ duration }} {{ parseInt(duration) === 1 ? 'เดือน' : 'เดือน' }}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  {{ getDurationDiscount(duration) > 0 ? `ลด ${getDurationDiscount(duration)}%` : 'ไม่มีส่วนลด' }}
                </div>
                <div v-if="state.duration === duration" class="text-blue-600 mt-2">
                  <Icon name="i-heroicons-check-circle" class="w-5 h-5 mx-auto" />
                </div>
              </div>
            </div>
          </div>

          <!-- Auto Renew -->
          <UFormField label="การต่ออายุอัตโนมัติ" name="autoRenew">
            <UCheckbox
              v-model="state.autoRenew"
              label="ต่ออายุอัตโนมัติเมื่อหมดอายุ"
              help="ระบบจะเรียกเก็บเงินอัตโนมัติเมื่อใกล้หมดอายุ"
            />
          </UFormField>

          <!-- Price Summary -->
          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">สรุปราคา</h3>
            
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">ราคาแพ็คเกจ</span>
                <span class="text-gray-900 dark:text-white">
                  {{ formatCurrency(getPlanPrice(state.plan)) }} × {{ state.duration }} เดือน
                </span>
              </div>
              
              <div v-if="getDurationDiscount(state.duration) > 0" class="flex justify-between text-green-600">
                <span>ส่วนลด</span>
                <span>-{{ getDurationDiscount(state.duration) }}%</span>
              </div>
              
              <div class="border-t border-gray-200 dark:border-gray-700 pt-2">
                <div class="flex justify-between">
                  <span class="font-semibold text-gray-900 dark:text-white">รวมทั้งหมด</span>
                  <span class="font-semibold text-gray-900 dark:text-white">
                    {{ formatCurrency(totalPrice) }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex justify-end gap-3">
            <UButton
              @click="handleCancel"
              variant="outline"
              :disabled="loading"
            >
              ยกเลิก
            </UButton>
            <UButton
              type="submit"
              :loading="loading"
              color="primary"
            >
              ต่ออายุ
            </UButton>
          </div>
        </UForm>
      </UCard>

      <!-- Help Section -->
      <UCard>
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
              <Icon name="i-heroicons-light-bulb" class="w-5 h-5 text-yellow-600" />
            </div>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
              ข้อมูลเพิ่มเติม
            </h2>
          </div>
        </template>

        <div class="space-y-3 text-sm text-gray-600 dark:text-gray-400">
          <div class="flex items-start gap-2">
            <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <span>การชำระเงินจะถูกเรียกเก็บทันทีเมื่อยืนยัน</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <span>ระยะเวลานานขึ้นจะได้รับส่วนลดมากขึ้น</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <span>สามารถยกเลิกการต่ออายุอัตโนมัติได้ทุกเมื่อ</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <span>การอัปเกรดจะเริ่มใช้งานทันที</span>
          </div>
        </div>
      </UCard>
    </div>
  </div>
</template> 