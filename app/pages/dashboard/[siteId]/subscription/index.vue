<script setup lang="ts">
definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// State
const loading = ref(false);
const site = ref<any>(null);
const subscription = ref<any>(null);
const { getSiteSubscription, updateSubscription } = useSubscriptionApi();

// Load site and subscription data
const loadData = async () => {
  loading.value = true;
  try {
    // Load site data
    const siteResponse = await $fetch(`/api/v1/site/${siteId.value}`);
    site.value = siteResponse.data;

    // Load subscription data
    const subscriptionResponse = await getSiteSubscription(siteId.value);
    subscription.value = subscriptionResponse.data;
  } catch (error) {
    console.error('Error loading data:', error);
    useToast().add({
      title: 'ผิดพลาด',
      description: 'ไม่สามารถโหลดข้อมูลได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
  }).format(amount);
};

// Get plan price
const getPlanPrice = (plan: string) => {
  const prices = {
    free: 0,
    basic: 299,
    premium: 599,
    enterprise: 1499,
  };
  return prices[plan as keyof typeof prices] || 0;
};

// Get plan name
const getPlanName = (plan: string) => {
  const names = {
    free: 'ฟรี',
    basic: 'เบสิค',
    premium: 'พรีเมียม',
    enterprise: 'เอ็นเตอร์ไพรส์',
  };
  return names[plan as keyof typeof names] || plan;
};

// Get plan features
const getPlanFeatures = (plan: string) => {
  const features = {
    free: ['เว็บไซต์ 1 หน้า', 'สินค้า 10 รายการ', 'ลูกค้า 50 คน', 'การสนับสนุนอีเมล'],
    basic: [
      'เว็บไซต์ 3 หน้า',
      'สินค้า 100 รายการ',
      'ลูกค้า 500 คน',
      'การสนับสนุนอีเมล',
      'รายงานพื้นฐาน',
      'การชำระเงินออนไลน์',
    ],
    premium: [
      'เว็บไซต์ไม่จำกัด',
      'สินค้าไม่จำกัด',
      'ลูกค้าไม่จำกัด',
      'การสนับสนุน 24/7',
      'รายงานขั้นสูง',
      'การชำระเงินออนไลน์',
      'การตลาดอีเมล',
      'SEO ขั้นสูง',
    ],
    enterprise: [
      'ทุกฟีเจอร์ของ Premium',
      'API ใช้งาน',
      'การสนับสนุนส่วนตัว',
      'การตั้งค่าขั้นสูง',
      'การสำรองข้อมูลอัตโนมัติ',
      'การวิเคราะห์ขั้นสูง',
      'การจัดการทีม',
    ],
  };
  return features[plan as keyof typeof features] || [];
};

// Check if subscription is expired
const isSubscriptionExpired = (subscription: any): boolean => {
  if (!subscription) return false;

  if (subscription.endDate) {
    return new Date() > new Date(subscription.endDate);
  }

  if (subscription.status === 'expired' || subscription.status === 'past_due') {
    return true;
  }

  return !subscription.isActive;
};

// Check if subscription is expiring soon (7 days)
const isSubscriptionExpiringSoon = (subscription: any): boolean => {
  if (!subscription) return false;

  const sevenDaysFromNow = new Date();
  sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);

  if (subscription.endDate) {
    const endDate = new Date(subscription.endDate);
    return endDate <= sevenDaysFromNow && endDate > new Date();
  }

  return false;
};

// Calculate days remaining
const getDaysRemaining = (subscription: any): number => {
  if (!subscription?.endDate) return 0;

  const endDate = new Date(subscription.endDate);
  const now = new Date();
  const diffTime = endDate.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return Math.max(0, diffDays);
};

// Format date
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

// Handle plan upgrade
const handleUpgrade = async (newPlan: string) => {
  try {
    await updateSubscription(siteId.value, {
      plan: newPlan,
      autoRenew: true,
    });

    useToast().add({
      title: 'สำเร็จ',
      description: 'อัปเกรดแผนเรียบร้อยแล้ว',
      color: 'success',
    });

    await loadData();
  } catch (error: any) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: error.message || 'ไม่สามารถอัปเกรดแผนได้',
      color: 'error',
    });
  }
};

// Handle renewal
const handleRenewal = () => {
  navigateTo(`/dashboard/${siteId.value}/subscription/renew`);
};

// Load data when component mounts
onMounted(() => {
  loadData();
});

// SEO
useSeoMeta({
  title: 'การสมัครสมาชิก - ระบบจัดการร้านค้า',
  description: 'จัดการการสมัครสมาชิก แพ็คเกจ และการต่ออายุ',
  keywords: 'การสมัครสมาชิก, แพ็คเกจ, การต่ออายุ, การชำระเงิน',
  ogTitle: 'การสมัครสมาชิก - ระบบจัดการร้านค้า',
  ogDescription: 'จัดการการสมัครสมาชิก แพ็คเกจ และการต่ออายุ',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script>

<template>
  <div class="max-w-7xl mx-auto space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          การสมัครสมาชิก
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          จัดการแพ็คเกจและการต่ออายุสำหรับ {{ site?.name }}
        </p>
      </div>
    </div>

    <div v-if="loading" class="flex items-center justify-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="w-8 h-8 animate-spin text-gray-400" />
      <span class="ml-2 text-gray-600 dark:text-gray-400">กำลังโหลด...</span>
    </div>

    <div v-else-if="!site" class="text-center py-12">
      <UIcon name="i-heroicons-exclamation-triangle" class="w-12 h-12 mx-auto text-gray-400" />
      <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">ไม่พบเว็บไซต์</h3>
      <p class="mt-2 text-gray-600 dark:text-gray-400">ไม่สามารถโหลดข้อมูลเว็บไซต์ได้</p>
    </div>

    <div v-else class="space-y-6">
      <!-- Current Subscription Status -->
      <UCard>
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Icon name="i-heroicons-credit-card" class="w-5 h-5 text-blue-600" />
            </div>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
              สถานะการสมัครสมาชิกปัจจุบัน
            </h2>
          </div>
        </template>

        <div class="space-y-4">
          <!-- Plan Info -->
          <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div class="flex items-center gap-4">
              <div class="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                <Icon name="i-heroicons-crown" class="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                  {{ getPlanName(subscription?.plan || 'free') }}
                </h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  {{ formatCurrency(getPlanPrice(subscription?.plan || 'free')) }}/เดือน
                </p>
              </div>
            </div>
            
            <div class="text-right">
              <div class="text-sm text-gray-600 dark:text-gray-400">สถานะ</div>
              <div 
                class="text-sm font-medium"
                :class="{
                  'text-green-600': subscription?.isActive && !isSubscriptionExpired(subscription),
                  'text-red-600': isSubscriptionExpired(subscription),
                  'text-orange-600': isSubscriptionExpiringSoon(subscription),
                  'text-gray-600': !subscription?.isActive
                }"
              >
                {{ 
                  isSubscriptionExpired(subscription) ? 'หมดอายุ' :
                  isSubscriptionExpiringSoon(subscription) ? 'ใกล้หมดอายุ' :
                  subscription?.isActive ? 'ใช้งาน' : 'ไม่ใช้งาน'
                }}
              </div>
            </div>
          </div>

          <!-- Subscription Details -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="text-sm text-gray-600 dark:text-gray-400">วันเริ่มต้น</div>
              <div class="text-sm font-medium text-gray-900 dark:text-white">
                {{ subscription?.startDate ? formatDate(subscription.startDate) : 'ไม่ระบุ' }}
              </div>
            </div>
            
            <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="text-sm text-gray-600 dark:text-gray-400">วันหมดอายุ</div>
              <div class="text-sm font-medium text-gray-900 dark:text-white">
                {{ subscription?.endDate ? formatDate(subscription.endDate) : 'ไม่ระบุ' }}
              </div>
            </div>
            
            <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="text-sm text-gray-600 dark:text-gray-400">วันเหลือ</div>
              <div class="text-sm font-medium text-gray-900 dark:text-white">
                {{ getDaysRemaining(subscription) }} วัน
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex gap-3">
            <UButton
              v-if="isSubscriptionExpired(subscription)"
              color="error"
              @click="handleRenewal"
            >
              <Icon name="i-heroicons-arrow-path" class="w-4 h-4 mr-2" />
              ต่ออายุ
            </UButton>
            
            <UButton
              v-else-if="subscription?.isActive"
              variant="outline"
              @click="handleRenewal"
            >
              <Icon name="i-heroicons-pencil-square" class="w-4 h-4 mr-2" />
              แก้ไข
            </UButton>
            
            <UButton
              v-if="subscription?.plan === 'free'"
              color="primary"
              @click="handleUpgrade('basic')"
            >
              <Icon name="i-heroicons-arrow-up" class="w-4 h-4 mr-2" />
              อัปเกรด
            </UButton>
          </div>
        </div>
      </UCard>

      <!-- Available Plans -->
      <UCard>
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <Icon name="i-heroicons-gift" class="w-5 h-5 text-green-600" />
            </div>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
              แพ็คเกจที่ใช้ได้
            </h2>
          </div>
        </template>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- Free Plan -->
          <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <div class="text-center">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">ฟรี</h3>
              <div class="text-3xl font-bold text-gray-900 dark:text-white mt-2">฿0</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">/เดือน</div>
            </div>
            
            <div class="mt-6 space-y-3">
              <div v-for="feature in getPlanFeatures('free')" :key="feature" class="flex items-center gap-2">
                <Icon name="i-heroicons-check" class="w-4 h-4 text-green-500" />
                <span class="text-sm text-gray-600 dark:text-gray-400">{{ feature }}</span>
              </div>
            </div>
            
            <UButton
              v-if="subscription?.plan !== 'free'"
              variant="outline"
              class="w-full mt-6"
              @click="handleUpgrade('free')"
            >
              เลือกแผน
            </UButton>
            <UButton
              v-else
              variant="outline"
              class="w-full mt-6"
              disabled
            >
              แผนปัจจุบัน
            </UButton>
          </div>

          <!-- Basic Plan -->
          <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <div class="text-center">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">เบสิค</h3>
              <div class="text-3xl font-bold text-gray-900 dark:text-white mt-2">฿299</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">/เดือน</div>
            </div>
            
            <div class="mt-6 space-y-3">
              <div v-for="feature in getPlanFeatures('basic')" :key="feature" class="flex items-center gap-2">
                <Icon name="i-heroicons-check" class="w-4 h-4 text-green-500" />
                <span class="text-sm text-gray-600 dark:text-gray-400">{{ feature }}</span>
              </div>
            </div>
            
            <UButton
              v-if="subscription?.plan !== 'basic'"
              color="primary"
              class="w-full mt-6"
              @click="handleUpgrade('basic')"
            >
              {{ subscription?.plan === 'free' ? 'อัปเกรด' : 'เปลี่ยนแผน' }}
            </UButton>
            <UButton
              v-else
              variant="outline"
              class="w-full mt-6"
              disabled
            >
              แผนปัจจุบัน
            </UButton>
          </div>

          <!-- Premium Plan -->
          <div class="border-2 border-blue-500 rounded-lg p-6 relative">
            <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">แนะนำ</span>
            </div>
            <div class="text-center">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">พรีเมียม</h3>
              <div class="text-3xl font-bold text-gray-900 dark:text-white mt-2">฿599</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">/เดือน</div>
            </div>
            
            <div class="mt-6 space-y-3">
              <div v-for="feature in getPlanFeatures('premium')" :key="feature" class="flex items-center gap-2">
                <Icon name="i-heroicons-check" class="w-4 h-4 text-green-500" />
                <span class="text-sm text-gray-600 dark:text-gray-400">{{ feature }}</span>
              </div>
            </div>
            
            <UButton
              v-if="subscription?.plan !== 'premium'"
              color="blue"
              class="w-full mt-6"
              @click="handleUpgrade('premium')"
            >
              {{ subscription?.plan === 'free' ? 'อัปเกรด' : 'เปลี่ยนแผน' }}
            </UButton>
            <UButton
              v-else
              variant="outline"
              class="w-full mt-6"
              disabled
            >
              แผนปัจจุบัน
            </UButton>
          </div>

          <!-- Enterprise Plan -->
          <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <div class="text-center">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">เอ็นเตอร์ไพรส์</h3>
              <div class="text-3xl font-bold text-gray-900 dark:text-white mt-2">฿1,499</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">/เดือน</div>
            </div>
            
            <div class="mt-6 space-y-3">
              <div v-for="feature in getPlanFeatures('enterprise')" :key="feature" class="flex items-center gap-2">
                <Icon name="i-heroicons-check" class="w-4 h-4 text-green-500" />
                <span class="text-sm text-gray-600 dark:text-gray-400">{{ feature }}</span>
              </div>
            </div>
            
            <UButton
              v-if="subscription?.plan !== 'enterprise'"
              color="primary"
              class="w-full mt-6"
              @click="handleUpgrade('enterprise')"
            >
              {{ subscription?.plan === 'free' ? 'อัปเกรด' : 'เปลี่ยนแผน' }}
            </UButton>
            <UButton
              v-else
              variant="outline"
              class="w-full mt-6"
              disabled
            >
              แผนปัจจุบัน
            </UButton>
          </div>
        </div>
      </UCard>

      <!-- Payment History -->
      <UCard>
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
              <Icon name="i-heroicons-clock" class="w-5 h-5 text-purple-600" />
            </div>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
              ประวัติการชำระเงิน
            </h2>
          </div>
        </template>

        <div class="text-center py-8">
          <Icon name="i-heroicons-clock" class="w-12 h-12 mx-auto text-gray-400" />
          <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">ยังไม่มีประวัติ</h3>
          <p class="mt-2 text-gray-600 dark:text-gray-400">ประวัติการชำระเงินจะแสดงที่นี่</p>
        </div>
      </UCard>

      <!-- Help Section -->
      <UCard>
        <template #header>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
              <Icon name="i-heroicons-information-circle" class="w-5 h-5 text-yellow-600" />
            </div>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
              ข้อมูลเพิ่มเติม
            </h2>
          </div>
        </template>

        <div class="space-y-4 text-sm text-gray-600 dark:text-gray-400">
          <div class="flex items-start gap-2">
            <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <span>การชำระเงินจะถูกเรียกเก็บอัตโนมัติทุกเดือน</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <span>สามารถยกเลิกการสมัครสมาชิกได้ทุกเมื่อ</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <span>เมื่อหมดอายุ เว็บไซต์จะไม่สามารถใช้งานได้</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <span>สามารถต่ออายุได้ก่อนหมดอายุ 7 วัน</span>
          </div>
        </div>
      </UCard>
    </div>
  </div>
</template> 