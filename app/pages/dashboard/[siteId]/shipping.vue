<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// State
const loading = ref(false);
const searchQuery = ref('');
const selectedStatus = ref('all');
const selectedZone = ref('all');
const sortBy = ref('created_at');
const sortOrder = ref('desc');
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Status options
const statuses = ref([
  { value: 'all', label: 'ทุกสถานะ' },
  { value: 'active', label: 'ใช้งาน' },
  { value: 'inactive', label: 'ไม่ใช้งาน' },
]);

const zones = ref([
  { value: 'all', label: 'ทุกเขต' },
  { value: 'bangkok', label: 'กรุงเทพฯ' },
  { value: 'central', label: 'ภาคกลาง' },
  { value: 'north', label: 'ภาคเหนือ' },
  { value: 'northeast', label: 'ภาคอีสาน' },
  { value: 'south', label: 'ภาคใต้' },
  { value: 'international', label: 'ต่างประเทศ' },
]);

// Mock shipping methods data
const shippingMethods = ref([
  {
    id: '1',
    name: 'จัดส่งด่วน (EMS)',
    description: 'จัดส่งด่วนภายใน 1-2 วันทำการ',
    carrier: 'Thailand Post',
    trackingCode: 'EMS',
    status: 'active',
    zone: 'bangkok',
    basePrice: 50,
    pricePerKg: 20,
    maxWeight: 20,
    deliveryTime: '1-2 วันทำการ',
    createdAt: '2024-01-01T09:00:00Z',
    updatedAt: '2024-01-15T14:30:00Z',
    icon: 'i-heroicons-truck',
    color: 'blue',
  },
  {
    id: '2',
    name: 'จัดส่งปกติ (ลงทะเบียน)',
    description: 'จัดส่งปกติภายใน 3-5 วันทำการ',
    carrier: 'Thailand Post',
    trackingCode: 'REG',
    status: 'active',
    zone: 'bangkok',
    basePrice: 30,
    pricePerKg: 15,
    maxWeight: 30,
    deliveryTime: '3-5 วันทำการ',
    createdAt: '2024-01-01T09:00:00Z',
    updatedAt: '2024-01-10T11:20:00Z',
    icon: 'i-heroicons-envelope',
    color: 'green',
  },
  {
    id: '3',
    name: 'Kerry Express',
    description: 'จัดส่งด่วนด้วย Kerry Express',
    carrier: 'Kerry Express',
    trackingCode: 'KEX',
    status: 'active',
    zone: 'central',
    basePrice: 60,
    pricePerKg: 25,
    maxWeight: 25,
    deliveryTime: '1-2 วันทำการ',
    createdAt: '2024-01-05T10:00:00Z',
    updatedAt: '2024-01-12T16:45:00Z',
    icon: 'i-heroicons-truck',
    color: 'orange',
  },
  {
    id: '4',
    name: 'Flash Express',
    description: 'จัดส่งด่วนด้วย Flash Express',
    carrier: 'Flash Express',
    trackingCode: 'FEX',
    status: 'active',
    zone: 'central',
    basePrice: 55,
    pricePerKg: 22,
    maxWeight: 30,
    deliveryTime: '1-2 วันทำการ',
    createdAt: '2024-01-08T13:00:00Z',
    updatedAt: '2024-01-14T09:15:00Z',
    icon: 'i-heroicons-bolt',
    color: 'yellow',
  },
  {
    id: '5',
    name: 'จัดส่งต่างประเทศ (DHL)',
    description: 'จัดส่งไปต่างประเทศด้วย DHL',
    carrier: 'DHL',
    trackingCode: 'DHL',
    status: 'active',
    zone: 'international',
    basePrice: 500,
    pricePerKg: 200,
    maxWeight: 50,
    deliveryTime: '3-7 วันทำการ',
    createdAt: '2024-01-01T09:00:00Z',
    updatedAt: '2024-01-20T12:00:00Z',
    icon: 'i-heroicons-globe-alt',
    color: 'purple',
  },
  {
    id: '6',
    name: 'จัดส่งฟรี (ขั้นต่ำ 1000 บาท)',
    description: 'จัดส่งฟรีเมื่อสั่งซื้อขั้นต่ำ 1000 บาท',
    carrier: 'Thailand Post',
    trackingCode: 'FREE',
    status: 'active',
    zone: 'bangkok',
    basePrice: 0,
    pricePerKg: 0,
    maxWeight: 10,
    deliveryTime: '3-5 วันทำการ',
    createdAt: '2024-01-01T09:00:00Z',
    updatedAt: '2024-01-15T14:30:00Z',
    icon: 'i-heroicons-gift',
    color: 'pink',
  },
]);

// Analytics data
const analytics = ref({
  totalMethods: 6,
  activeMethods: 6,
  totalOrders: 1247,
  totalRevenue: 45600,
  averageDeliveryTime: 2.5,
  customerSatisfaction: 4.2,
});

// Computed filtered shipping methods
const filteredShippingMethods = computed(() => {
  let filtered = shippingMethods.value;

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(
      method =>
        method.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        method.description.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        method.carrier.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  // Status filter
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(method => method.status === selectedStatus.value);
  }

  // Zone filter
  if (selectedZone.value !== 'all') {
    filtered = filtered.filter(method => method.zone === selectedZone.value);
  }

  // Sort
  filtered.sort((a, b) => {
    let aVal = a[sortBy.value as keyof typeof a];
    let bVal = b[sortBy.value as keyof typeof b];

    if (typeof aVal === 'string') {
      aVal = aVal.toLowerCase();
      bVal = bVal.toLowerCase();
    }

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });

  return filtered;
});

// Paginated shipping methods
const paginatedShippingMethods = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredShippingMethods.value.slice(start, end);
});

// Total pages
const totalPages = computed(() => {
  return Math.ceil(filteredShippingMethods.value.length / itemsPerPage.value);
});

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
  }).format(amount);
};

// Format date
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

// Get status badge class
const getStatusBadgeClass = (status: string) => {
  const classes = {
    active: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
  };
  return classes[status as keyof typeof classes] || classes.inactive;
};

// Get status label
const getStatusLabel = (status: string) => {
  const labels = {
    active: 'ใช้งาน',
    inactive: 'ไม่ใช้งาน',
  };
  return labels[status as keyof typeof labels] || status;
};

// Get zone badge class
const getZoneBadgeClass = (zone: string) => {
  const classes = {
    bangkok: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    central: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    north: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
    northeast: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
    south: 'bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-400',
    international: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
  };
  return classes[zone as keyof typeof classes] || classes.bangkok;
};

// Get zone label
const getZoneLabel = (zone: string) => {
  const labels = {
    bangkok: 'กรุงเทพฯ',
    central: 'ภาคกลาง',
    north: 'ภาคเหนือ',
    northeast: 'ภาคอีสาน',
    south: 'ภาคใต้',
    international: 'ต่างประเทศ',
  };
  return labels[zone as keyof typeof labels] || zone;
};

// Get color class
const getColorClass = (color: string) => {
  const classes = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    orange: 'bg-orange-500',
    yellow: 'bg-yellow-500',
    purple: 'bg-purple-500',
    pink: 'bg-pink-500',
  };
  return classes[color as keyof typeof classes] || classes.blue;
};

// Methods
const handleSort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = field;
    sortOrder.value = 'desc';
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handleViewMethod = (method: any) => {
  navigateTo(`/dashboard/${siteId.value}/shipping/${method.id}`);
};

const handleEditMethod = (method: any) => {
  navigateTo(`/dashboard/${siteId.value}/shipping/${method.id}/edit`);
};

const handleToggleStatus = async (method: any) => {
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    method.status = method.status === 'active' ? 'inactive' : 'active';

    useToast().add({
      title: 'สำเร็จ',
      description: `อัปเดตสถานะวิธีการจัดส่ง ${method.name} เรียบร้อยแล้ว`,
      color: 'success',
    });
  } catch (error) {
    useToast().add({
      title: 'ผิดพลาด',
      description: 'ไม่สามารถอัปเดตสถานะได้',
      color: 'error',
    });
  }
};

const handleDeleteMethod = async (method: any) => {
  if (confirm(`คุณต้องการลบวิธีการจัดส่ง "${method.name}" ใช่หรือไม่?`)) {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const index = shippingMethods.value.findIndex(m => m.id === method.id);
      if (index > -1) {
        shippingMethods.value.splice(index, 1);
      }

      useToast().add({
        title: 'สำเร็จ',
        description: `ลบวิธีการจัดส่ง ${method.name} เรียบร้อยแล้ว`,
        color: 'success',
      });
    } catch (error) {
      useToast().add({
        title: 'ผิดพลาด',
        description: 'ไม่สามารถลบวิธีการจัดส่งได้',
        color: 'error',
      });
    }
  }
};

const handleCreateMethod = () => {
  navigateTo(`/dashboard/${siteId.value}/shipping/create`);
};

const handleExportMethods = () => {
  useToast().add({
    title: 'สำเร็จ',
    description: 'ส่งออกข้อมูลวิธีการจัดส่งเรียบร้อยแล้ว',
    color: 'success',
  });
};
</script>

<template>
  <div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          การจัดส่ง
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          จัดการวิธีการจัดส่งและค่าขนส่ง
        </p>
      </div>
      <div class="flex items-center gap-2">
        <UButton 
          @click="handleExportMethods"
          variant="outline"
          icon="i-heroicons-arrow-down-tray"
        >
          ส่งออก
        </UButton>
        <UButton 
          @click="handleCreateMethod"
          class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
          เพิ่มวิธีการจัดส่ง
        </UButton>
      </div>
    </div>

    <!-- Analytics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">วิธีการจัดส่ง</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalMethods }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">{{ analytics.activeMethods }} ใช้งาน</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-truck" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">คำสั่งซื้อรวม</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalOrders }}</p>
            <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">คำสั่งซื้อ</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-shopping-cart" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">รายได้ค่าจัดส่ง</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatCurrency(analytics.totalRevenue) }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">เฉลี่ย {{ formatCurrency(analytics.totalRevenue / analytics.totalOrders) }}/คำสั่งซื้อ</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-currency-dollar" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">เวลาจัดส่งเฉลี่ย</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.averageDeliveryTime }}</p>
            <p class="text-xs text-orange-600 dark:text-orange-400 mt-1">วันทำการ</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-clock" class="w-8 h-8" />
          </div>
        </div>
      </UCard>
    </div>

    <!-- Filters -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-funnel" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวกรองและค้นหา</h2>
        </div>
      </template>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหาวิธีการจัดส่ง</label>
          <UInput
            v-model="searchQuery"
            placeholder="ค้นหาตามชื่อหรือผู้ให้บริการ..."
            icon="i-heroicons-magnifying-glass"
            size="xl"
            class="w-full"
          />
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สถานะ</label>
          <USelect
            v-model="selectedStatus"
            :items="statuses"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกสถานะ"
            size="xl"
            class="w-full"
          />
        </div>

        <!-- Zone Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เขตการจัดส่ง</label>
          <USelect
            v-model="selectedZone"
            :items="zones"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกเขต"
            size="xl"
            class="w-full"
          />
        </div>

        <!-- Sort -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เรียงลำดับ</label>
          <div class="flex gap-2">
            <USelect
              v-model="sortBy"
              :items="[
                { value: 'created_at', label: 'วันที่สร้าง' },
                { value: 'basePrice', label: 'ราคาพื้นฐาน' },
                { value: 'name', label: 'ชื่อ' }
              ]"
              option-attribute="label"
              value-attribute="value"
              size="xl"
              class="flex-1"
            />
            <UButton
              @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'"
              variant="outline"
              size="sm"
              class="px-3"
            >
              <Icon 
                :name="sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down'" 
                class="w-4 h-4" 
              />
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Shipping Methods Table -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <Icon name="i-heroicons-truck" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รายการวิธีการจัดส่ง</h2>
            <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-400 rounded-full">
              {{ filteredShippingMethods.length }} รายการ
            </span>
          </div>
        </div>
      </template>
      
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="flex items-center gap-3">
          <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-blue-600" />
          <span class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</span>
        </div>
      </div>

      <div v-else-if="paginatedShippingMethods.length === 0" class="text-center py-12">
        <div class="text-gray-400 dark:text-gray-500 mb-4">
          <Icon name="i-heroicons-truck" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">ไม่พบวิธีการจัดส่ง</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          ยังไม่มีวิธีการจัดส่งในระบบ หรือไม่ตรงกับเงื่อนไขการค้นหา
        </p>
        <UButton @click="handleCreateMethod" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
          เพิ่มวิธีการจัดส่งแรก
        </UButton>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">วิธีการจัดส่ง</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">ผู้ให้บริการ</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">เขตการจัดส่ง</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>ราคา</span>
                  <UButton
                    @click="handleSort('basePrice')"
                    variant="ghost"
                    size="xs"
                    class="p-1"
                  >
                    <Icon 
                      :name="sortBy === 'basePrice' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'" 
                      class="w-3 h-3" 
                    />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">เวลาจัดส่ง</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">สถานะ</th>
              <th class="text-right py-4 px-6 font-semibold text-gray-900 dark:text-white">การดำเนินการ</th>
            </tr>
          </thead>
          <tbody>
            <tr 
              v-for="method in paginatedShippingMethods" 
              :key="method.id"
              class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
            >
              <td class="py-4 px-6">
                <div class="flex items-center gap-4">
                  <div class="w-12 h-12 rounded-lg flex items-center justify-center text-white" :class="getColorClass(method.color)">
                    <Icon :name="method.icon" class="w-6 h-6" />
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">{{ method.name }}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-1">{{ method.description }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-500">รหัส: {{ method.trackingCode }}</p>
                  </div>
                </div>
              </td>
              <td class="py-4 px-6">
                <span class="font-medium text-gray-900 dark:text-white">{{ method.carrier }}</span>
              </td>
              <td class="py-4 px-6">
                <span 
                  class="px-3 py-1 text-xs rounded-full font-medium"
                  :class="getZoneBadgeClass(method.zone)"
                >
                  {{ getZoneLabel(method.zone) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="space-y-1">
                  <p class="font-semibold text-gray-900 dark:text-white">
                    {{ formatCurrency(method.basePrice) }}
                  </p>
                  <p v-if="method.pricePerKg > 0" class="text-xs text-gray-600 dark:text-gray-400">
                    +{{ formatCurrency(method.pricePerKg) }}/กก.
                  </p>
                  <p class="text-xs text-gray-500 dark:text-gray-500">
                    สูงสุด {{ method.maxWeight }} กก.
                  </p>
                </div>
              </td>
              <td class="py-4 px-6">
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {{ method.deliveryTime }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span 
                  class="px-3 py-1 text-xs rounded-full font-medium"
                  :class="getStatusBadgeClass(method.status)"
                >
                  {{ getStatusLabel(method.status) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center justify-end gap-2">
                  <UButton
                    @click="handleViewMethod(method)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                  >
                    <Icon name="i-heroicons-eye" class="w-4 h-4" />
                  </UButton>
                  <UButton
                    @click="handleEditMethod(method)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-green-50 dark:hover:bg-green-900/20 text-green-600 dark:text-green-400"
                  >
                    <Icon name="i-heroicons-pencil-square" class="w-4 h-4" />
                  </UButton>
                  <UButton
                    @click="handleToggleStatus(method)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-yellow-50 dark:hover:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400"
                  >
                    <Icon :name="method.status === 'active' ? 'i-heroicons-pause' : 'i-heroicons-play'" class="w-4 h-4" />
                  </UButton>
                  <UButton
                    @click="handleDeleteMethod(method)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400"
                  >
                    <Icon name="i-heroicons-trash" class="w-4 h-4" />
                  </UButton>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex items-center justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} ถึง {{ Math.min(currentPage * itemsPerPage, filteredShippingMethods.length) }} 
          จาก {{ filteredShippingMethods.length }} รายการ
        </div>
        <div class="flex items-center gap-2">
          <UButton
            @click="handlePageChange(currentPage - 1)"
            :disabled="currentPage === 1"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-left" class="w-4 h-4" />
          </UButton>
          
          <div class="flex items-center gap-1">
            <UButton
              v-for="page in Math.min(5, totalPages)"
              :key="page"
              @click="handlePageChange(page)"
              :variant="currentPage === page ? 'solid' : 'outline'"
              size="sm"
              class="w-8 h-8 p-0"
            >
              {{ page }}
            </UButton>
          </div>
          
          <UButton
            @click="handlePageChange(currentPage + 1)"
            :disabled="currentPage === totalPages"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-right" class="w-4 h-4" />
          </UButton>
        </div>
      </div>
    </UCard>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Line clamp utility */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 