<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// State
const loading = ref(false);
const searchQuery = ref('');
const selectedStatus = ref('all');
const selectedMethod = ref('all');
const sortBy = ref('created_at');
const sortOrder = ref('desc');
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Status options
const statuses = ref([
  { value: 'all', label: 'ทุกสถานะ' },
  { value: 'active', label: 'ใช้งาน' },
  { value: 'inactive', label: 'ไม่ใช้งาน' },
  { value: 'maintenance', label: 'บำรุงรักษา' },
]);

const methods = ref([
  { value: 'all', label: 'ทุกวิธี' },
  { value: 'credit_card', label: 'บัตรเครดิต' },
  { value: 'bank_transfer', label: 'โอนเงินธนาคาร' },
  { value: 'mobile_banking', label: 'Mobile Banking' },
  { value: 'e_wallet', label: 'E-Wallet' },
  { value: 'cod', label: 'เก็บเงินปลายทาง' },
]);

// Mock payment methods data
const paymentMethods = ref([
  {
    id: '1',
    name: 'บัตรเครดิต/เดบิต',
    description: 'รับชำระด้วยบัตร Visa, MasterCard, JCB',
    type: 'credit_card',
    status: 'active',
    processingFee: 2.5,
    settlementTime: '1-2 วันทำการ',
    minAmount: 100,
    maxAmount: 50000,
    supportedCards: ['Visa', 'MasterCard', 'JCB', 'UnionPay'],
    createdAt: '2024-01-01T09:00:00Z',
    updatedAt: '2024-01-15T14:30:00Z',
    icon: 'i-heroicons-credit-card',
    color: 'blue',
    logo: 'https://images.unsplash.com/photo-**********-824ae1b704d3?w=150&h=100&fit=crop',
  },
  {
    id: '2',
    name: 'โอนเงินผ่านธนาคาร',
    description: 'โอนเงินผ่านธนาคารไทยพาณิชย์, กสิกรไทย, กรุงเทพ',
    type: 'bank_transfer',
    status: 'active',
    processingFee: 0,
    settlementTime: '1 วันทำการ',
    minAmount: 100,
    maxAmount: 100000,
    supportedBanks: ['SCB', 'KBANK', 'BBL', 'BAY', 'TMB'],
    createdAt: '2024-01-01T09:00:00Z',
    updatedAt: '2024-01-10T11:20:00Z',
    icon: 'i-heroicons-building-library',
    color: 'green',
    logo: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=150&h=100&fit=crop',
  },
  {
    id: '3',
    name: 'Mobile Banking',
    description: 'ชำระผ่าน Mobile Banking ต่างๆ',
    type: 'mobile_banking',
    status: 'active',
    processingFee: 1.5,
    settlementTime: 'ทันที',
    minAmount: 50,
    maxAmount: 25000,
    supportedApps: ['SCB Easy', 'K PLUS', 'mBanking', 'Krungthai NEXT'],
    createdAt: '2024-01-05T10:00:00Z',
    updatedAt: '2024-01-12T16:45:00Z',
    icon: 'i-heroicons-device-phone-mobile',
    color: 'purple',
    logo: 'https://images.unsplash.com/photo-*************-5f897ff02aa9?w=150&h=100&fit=crop',
  },
  {
    id: '4',
    name: 'E-Wallet',
    description: 'ชำระผ่าน E-Wallet ต่างๆ',
    type: 'e_wallet',
    status: 'active',
    processingFee: 2.0,
    settlementTime: 'ทันที',
    minAmount: 10,
    maxAmount: 10000,
    supportedWallets: ['TrueMoney', 'Rabbit LINE Pay', 'GrabPay', 'ShopeePay'],
    createdAt: '2024-01-08T13:00:00Z',
    updatedAt: '2024-01-14T09:15:00Z',
    icon: 'i-heroicons-wallet',
    color: 'orange',
    logo: 'https://images.unsplash.com/photo-**********-6726b3ff858f?w=150&h=100&fit=crop',
  },
  {
    id: '5',
    name: 'เก็บเงินปลายทาง (COD)',
    description: 'ชำระเงินเมื่อได้รับสินค้า',
    type: 'cod',
    status: 'active',
    processingFee: 30,
    settlementTime: 'ทันที',
    minAmount: 100,
    maxAmount: 5000,
    supportedAreas: ['กรุงเทพฯ', 'ปริมณฑล', 'เมืองใหญ่'],
    createdAt: '2024-01-01T09:00:00Z',
    updatedAt: '2024-01-20T12:00:00Z',
    icon: 'i-heroicons-banknotes',
    color: 'red',
    logo: 'https://images.unsplash.com/photo-*************-2fceff292cdc?w=150&h=100&fit=crop',
  },
  {
    id: '6',
    name: 'PayPal',
    description: 'ชำระผ่าน PayPal สำหรับลูกค้าต่างชาติ',
    type: 'credit_card',
    status: 'inactive',
    processingFee: 3.5,
    settlementTime: '2-3 วันทำการ',
    minAmount: 100,
    maxAmount: 10000,
    supportedCards: ['PayPal'],
    createdAt: '2024-01-01T09:00:00Z',
    updatedAt: '2024-01-15T14:30:00Z',
    icon: 'i-heroicons-globe-alt',
    color: 'yellow',
    logo: 'https://images.unsplash.com/photo-**********-824ae1b704d3?w=150&h=100&fit=crop',
  },
]);

// Analytics data
const analytics = ref({
  totalMethods: 6,
  activeMethods: 5,
  totalTransactions: 2847,
  totalRevenue: 1250000,
  averageTransactionValue: 439,
  successRate: 98.5,
});

// Computed filtered payment methods
const filteredPaymentMethods = computed(() => {
  let filtered = paymentMethods.value;

  // Search filter
  if (searchQuery.value) {
    filtered = filtered.filter(
      method =>
        method.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        method.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  // Status filter
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(method => method.status === selectedStatus.value);
  }

  // Method filter
  if (selectedMethod.value !== 'all') {
    filtered = filtered.filter(method => method.type === selectedMethod.value);
  }

  // Sort
  filtered.sort((a, b) => {
    let aVal = a[sortBy.value as keyof typeof a];
    let bVal = b[sortBy.value as keyof typeof b];

    if (typeof aVal === 'string') {
      aVal = aVal.toLowerCase();
      bVal = bVal.toLowerCase();
    }

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });

  return filtered;
});

// Paginated payment methods
const paginatedPaymentMethods = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredPaymentMethods.value.slice(start, end);
});

// Total pages
const totalPages = computed(() => {
  return Math.ceil(filteredPaymentMethods.value.length / itemsPerPage.value);
});

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
  }).format(amount);
};

// Format percentage
const formatPercentage = (value: number) => {
  return `${value}%`;
};

// Get status badge class
const getStatusBadgeClass = (status: string) => {
  const classes = {
    active: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
    maintenance: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
  };
  return classes[status as keyof typeof classes] || classes.inactive;
};

// Get status label
const getStatusLabel = (status: string) => {
  const labels = {
    active: 'ใช้งาน',
    inactive: 'ไม่ใช้งาน',
    maintenance: 'บำรุงรักษา',
  };
  return labels[status as keyof typeof labels] || status;
};

// Get method badge class
const getMethodBadgeClass = (type: string) => {
  const classes = {
    credit_card: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    bank_transfer: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    mobile_banking: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
    e_wallet: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
    cod: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
  };
  return classes[type as keyof typeof classes] || classes.credit_card;
};

// Get method label
const getMethodLabel = (type: string) => {
  const labels = {
    credit_card: 'บัตรเครดิต',
    bank_transfer: 'โอนเงินธนาคาร',
    mobile_banking: 'Mobile Banking',
    e_wallet: 'E-Wallet',
    cod: 'เก็บเงินปลายทาง',
  };
  return labels[type as keyof typeof labels] || type;
};

// Get color class
const getColorClass = (color: string) => {
  const classes = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    purple: 'bg-purple-500',
    orange: 'bg-orange-500',
    red: 'bg-red-500',
    yellow: 'bg-yellow-500',
  };
  return classes[color as keyof typeof classes] || classes.blue;
};

// Methods
const handleSort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = field;
    sortOrder.value = 'desc';
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handleViewMethod = (method: any) => {
  navigateTo(`/dashboard/${siteId.value}/payments/${method.id}`);
};

const handleEditMethod = (method: any) => {
  navigateTo(`/dashboard/${siteId.value}/payments/${method.id}/edit`);
};

const handleToggleStatus = async (method: any) => {
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    method.status = method.status === 'active' ? 'inactive' : 'active';

    useToast().add({
      title: 'สำเร็จ',
      description: `อัปเดตสถานะวิธีการชำระเงิน ${method.name} เรียบร้อยแล้ว`,
      color: 'success',
    });
  } catch (error) {
    useToast().add({
      title: 'ผิดพลาด',
      description: 'ไม่สามารถอัปเดตสถานะได้',
      color: 'error',
    });
  }
};

const handleDeleteMethod = async (method: any) => {
  if (confirm(`คุณต้องการลบวิธีการชำระเงิน "${method.name}" ใช่หรือไม่?`)) {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const index = paymentMethods.value.findIndex(m => m.id === method.id);
      if (index > -1) {
        paymentMethods.value.splice(index, 1);
      }

      useToast().add({
        title: 'สำเร็จ',
        description: `ลบวิธีการชำระเงิน ${method.name} เรียบร้อยแล้ว`,
        color: 'success',
      });
    } catch (error) {
      useToast().add({
        title: 'ผิดพลาด',
        description: 'ไม่สามารถลบวิธีการชำระเงินได้',
        color: 'error',
      });
    }
  }
};

const handleCreateMethod = () => {
  navigateTo(`/dashboard/${siteId.value}/payments/create`);
};

const handleExportMethods = () => {
  useToast().add({
    title: 'สำเร็จ',
    description: 'ส่งออกข้อมูลวิธีการชำระเงินเรียบร้อยแล้ว',
    color: 'success',
  });
};
</script>

<template>
  <div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          การชำระเงิน
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          จัดการวิธีการชำระเงินและค่าธรรมเนียม
        </p>
      </div>
      <div class="flex items-center gap-2">
        <UButton 
          @click="handleExportMethods"
          variant="outline"
          icon="i-heroicons-arrow-down-tray"
        >
          ส่งออก
        </UButton>
        <UButton 
          @click="handleCreateMethod"
          class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <Icon name="i-heroicons-plus" class="w-5 h-5 mr-2" />
          เพิ่มวิธีการชำระเงิน
        </UButton>
      </div>
    </div>

    <!-- Analytics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">วิธีการชำระเงิน</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalMethods }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">{{ analytics.activeMethods }} ใช้งาน</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-credit-card" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">ธุรกรรมรวม</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.totalTransactions }}</p>
            <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">ธุรกรรม</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-arrow-path" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">รายได้รวม</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ formatCurrency(analytics.totalRevenue) }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">เฉลี่ย {{ formatCurrency(analytics.averageTransactionValue) }}/ธุรกรรม</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-currency-dollar" class="w-8 h-8" />
          </div>
        </div>
      </UCard>

      <UCard class="group hover:shadow-lg transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">อัตราความสำเร็จ</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ analytics.successRate }}%</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">ธุรกรรมสำเร็จ</p>
          </div>
          <div class="p-4 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl text-white shadow-lg">
            <Icon name="i-heroicons-check-circle" class="w-8 h-8" />
          </div>
        </div>
      </UCard>
    </div>

    <!-- Filters -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <Icon name="i-heroicons-funnel" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวกรองและค้นหา</h2>
        </div>
      </template>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหาวิธีการชำระเงิน</label>
          <UInput
            v-model="searchQuery"
            placeholder="ค้นหาตามชื่อหรือคำอธิบาย..."
            icon="i-heroicons-magnifying-glass"
            size="xl"
            class="w-full"
          />
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สถานะ</label>
          <USelect
            v-model="selectedStatus"
            :items="statuses"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกสถานะ"
            size="xl"
            class="w-full"
          />
        </div>

        <!-- Method Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ประเภท</label>
          <USelect
            v-model="selectedMethod"
            :items="methods"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกประเภท"
            size="xl"
            class="w-full"
          />
        </div>

        <!-- Sort -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เรียงลำดับ</label>
          <div class="flex gap-2">
            <USelect
              v-model="sortBy"
              :items="[
                { value: 'created_at', label: 'วันที่สร้าง' },
                { value: 'processingFee', label: 'ค่าธรรมเนียม' },
                { value: 'name', label: 'ชื่อ' }
              ]"
              option-attribute="label"
              value-attribute="value"
              size="xl"
              class="flex-1"
            />
            <UButton
              @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'"
              variant="outline"
              size="sm"
              class="px-3"
            >
              <Icon 
                :name="sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down'" 
                class="w-4 h-4" 
              />
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Payment Methods Table -->
    <UCard class="overflow-hidden">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <Icon name="i-heroicons-credit-card" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รายการวิธีการชำระเงิน</h2>
            <span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-400 rounded-full">
              {{ filteredPaymentMethods.length }} รายการ
            </span>
          </div>
        </div>
      </template>
      
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="flex items-center gap-3">
          <UIcon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin text-blue-600" />
          <span class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</span>
        </div>
      </div>

      <div v-else-if="paginatedPaymentMethods.length === 0" class="text-center py-12">
        <div class="text-gray-400 dark:text-gray-500 mb-4">
          <Icon name="i-heroicons-credit-card" class="w-12 h-12 mx-auto" />
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">ไม่พบวิธีการชำระเงิน</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          ยังไม่มีวิธีการชำระเงินในระบบ หรือไม่ตรงกับเงื่อนไขการค้นหา
        </p>
        <UButton @click="handleCreateMethod" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
          เพิ่มวิธีการชำระเงินแรก
        </UButton>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">วิธีการชำระเงิน</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">ประเภท</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">
                <div class="flex items-center gap-2">
                  <span>ค่าธรรมเนียม</span>
                  <UButton
                    @click="handleSort('processingFee')"
                    variant="ghost"
                    size="xs"
                    class="p-1"
                  >
                    <Icon 
                      :name="sortBy === 'processingFee' ? (sortOrder === 'asc' ? 'i-heroicons-arrow-up' : 'i-heroicons-arrow-down') : 'i-heroicons-arrows-up-down'" 
                      class="w-3 h-3" 
                    />
                  </UButton>
                </div>
              </th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">เงื่อนไข</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">เวลาชำระ</th>
              <th class="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">สถานะ</th>
              <th class="text-right py-4 px-6 font-semibold text-gray-900 dark:text-white">การดำเนินการ</th>
            </tr>
          </thead>
          <tbody>
            <tr 
              v-for="method in paginatedPaymentMethods" 
              :key="method.id"
              class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
            >
              <td class="py-4 px-6">
                <div class="flex items-center gap-4">
                  <div class="w-12 h-12 rounded-lg flex items-center justify-center text-white" :class="getColorClass(method.color)">
                    <Icon :name="method.icon" class="w-6 h-6" />
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">{{ method.name }}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-1">{{ method.description }}</p>
                  </div>
                </div>
              </td>
              <td class="py-4 px-6">
                <span 
                  class="px-3 py-1 text-xs rounded-full font-medium"
                  :class="getMethodBadgeClass(method.type)"
                >
                  {{ getMethodLabel(method.type) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="space-y-1">
                  <p class="font-semibold text-gray-900 dark:text-white">
                    {{ formatPercentage(method.processingFee) }}
                  </p>
                  <p class="text-xs text-gray-600 dark:text-gray-400">
                    ของมูลค่าธุรกรรม
                  </p>
                </div>
              </td>
              <td class="py-4 px-6">
                <div class="space-y-1 text-sm">
                  <p class="text-gray-600 dark:text-gray-400">
                    ขั้นต่ำ {{ formatCurrency(method.minAmount) }}
                  </p>
                  <p class="text-gray-600 dark:text-gray-400">
                    สูงสุด {{ formatCurrency(method.maxAmount) }}
                  </p>
                </div>
              </td>
              <td class="py-4 px-6">
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {{ method.settlementTime }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span 
                  class="px-3 py-1 text-xs rounded-full font-medium"
                  :class="getStatusBadgeClass(method.status)"
                >
                  {{ getStatusLabel(method.status) }}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center justify-end gap-2">
                  <UButton
                    @click="handleViewMethod(method)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                  >
                    <Icon name="i-heroicons-eye" class="w-4 h-4" />
                  </UButton>
                  <UButton
                    @click="handleEditMethod(method)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-green-50 dark:hover:bg-green-900/20 text-green-600 dark:text-green-400"
                  >
                    <Icon name="i-heroicons-pencil-square" class="w-4 h-4" />
                  </UButton>
                  <UButton
                    @click="handleToggleStatus(method)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-yellow-50 dark:hover:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400"
                  >
                    <Icon :name="method.status === 'active' ? 'i-heroicons-pause' : 'i-heroicons-play'" class="w-4 h-4" />
                  </UButton>
                  <UButton
                    @click="handleDeleteMethod(method)"
                    variant="ghost"
                    size="sm"
                    class="hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400"
                  >
                    <Icon name="i-heroicons-trash" class="w-4 h-4" />
                  </UButton>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex items-center justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} ถึง {{ Math.min(currentPage * itemsPerPage, filteredPaymentMethods.length) }} 
          จาก {{ filteredPaymentMethods.length }} รายการ
        </div>
        <div class="flex items-center gap-2">
          <UButton
            @click="handlePageChange(currentPage - 1)"
            :disabled="currentPage === 1"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-left" class="w-4 h-4" />
          </UButton>
          
          <div class="flex items-center gap-1">
            <UButton
              v-for="page in Math.min(5, totalPages)"
              :key="page"
              @click="handlePageChange(page)"
              :variant="currentPage === page ? 'solid' : 'outline'"
              size="sm"
              class="w-8 h-8 p-0"
            >
              {{ page }}
            </UButton>
          </div>
          
          <UButton
            @click="handlePageChange(currentPage + 1)"
            :disabled="currentPage === totalPages"
            variant="outline"
            size="sm"
          >
            <Icon name="i-heroicons-chevron-right" class="w-4 h-4" />
          </UButton>
        </div>
      </div>
    </UCard>
  </div>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Line clamp utility */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 