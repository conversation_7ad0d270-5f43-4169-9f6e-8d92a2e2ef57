<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">แคมเปญการตลาด</h1>
        <p class="text-gray-600 dark:text-gray-400">จัดการแคมเปญการตลาดและโปรโมชัน</p>
      </div>
      <div class="flex items-center gap-3">
        <UButton @click="loadCampaigns" :loading="loading" variant="outline" icon="i-heroicons-arrow-path">
          รีเฟรช
        </UButton>
        <UButton @click="showCreateModal = true" icon="i-heroicons-plus">
          สร้างแคมเปญใหม่
        </UButton>
      </div>
    </div>

    <!-- Analytics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Icon name="i-heroicons-megaphone" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ analytics.totalCampaigns }}</div>
            <div class="text-sm text-gray-500">แคมเปญทั้งหมด</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <Icon name="i-heroicons-play-circle" class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ analytics.activeCampaigns }}</div>
            <div class="text-sm text-gray-500">แคมเปญที่ใช้งาน</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Icon name="i-heroicons-users" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ analytics.totalParticipants }}</div>
            <div class="text-sm text-gray-500">ผู้เข้าร่วมทั้งหมด</div>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center gap-3">
          <div class="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
            <Icon name="i-heroicons-chart-bar" class="w-6 h-6 text-orange-600 dark:text-orange-400" />
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ analytics.conversionRate }}%</div>
            <div class="text-sm text-gray-500">อัตราการแปลง</div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Filters -->
    <UCard>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ค้นหา</label>
          <UInput
            v-model="searchQuery"
            placeholder="ค้นหาแคมเปญ..."
            icon="i-heroicons-magnifying-glass"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">สถานะ</label>
          <USelect
            v-model="statusFilter"
            :items="statusOptions"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกสถานะ"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ประเภท</label>
          <USelect
            v-model="typeFilter"
            :items="typeOptions"
            option-attribute="label"
            value-attribute="value"
            placeholder="เลือกประเภท"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">วันที่</label>
          <UInput
            v-model="dateFilter"
            type="date"
            placeholder="เลือกวันที่"
          />
        </div>
      </div>
    </UCard>

    <!-- Campaigns List -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold">แคมเปญทั้งหมด</h3>
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-500">
              แสดง {{ filteredCampaigns.length }} รายการ
            </span>
          </div>
        </div>
      </template>

      <div v-if="loading" class="flex justify-center py-8">
        <Icon name="i-heroicons-arrow-path" class="w-6 h-6 animate-spin" />
      </div>
      
      <div v-else-if="filteredCampaigns.length === 0" class="text-center py-12">
        <Icon name="i-heroicons-megaphone" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">ไม่พบแคมเปญ</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">ยังไม่มีแคมเปญในระบบ</p>
        <UButton @click="showCreateModal = true" icon="i-heroicons-plus">
          สร้างแคมเปญใหม่
        </UButton>
      </div>
      
      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                แคมเปญ
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                ประเภท
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                สถานะ
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                ผู้เข้าร่วม
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                วันที่เริ่ม
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                วันที่สิ้นสุด
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                อัตราการแปลง
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                การดำเนินการ
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="campaign in paginatedCampaigns" :key="campaign.id" class="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="max-w-xs">
                  <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {{ campaign.name }}
                  </p>
                  <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {{ campaign.description }}
                  </p>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <UBadge :color="getTypeColor(campaign.type)" :variant="getTypeVariant(campaign.type)">
                  {{ getTypeText(campaign.type) }}
                </UBadge>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <UBadge :color="getStatusColor(campaign.status)" :variant="getStatusVariant(campaign.status)">
                  {{ getStatusText(campaign.status) }}
                </UBadge>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">
                  {{ campaign.participants }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  {{ campaign.targetParticipants }} เป้าหมาย
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ formatDate(campaign.startDate) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ formatDate(campaign.endDate) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                    <div 
                      class="bg-green-600 h-2 rounded-full" 
                      :style="{ width: `${campaign.conversionRate}%` }"
                    ></div>
                  </div>
                  <span class="text-sm text-gray-900 dark:text-white">{{ campaign.conversionRate }}%</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <UDropdownMenu :items="getActionItems(campaign)">
                  <UButton variant="ghost" icon="i-heroicons-ellipsis-vertical" />
                </UDropdownMenu>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex items-center justify-between mt-4">
        <div class="text-sm text-gray-700 dark:text-gray-300">
          แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} ถึง {{ Math.min(currentPage * itemsPerPage, filteredCampaigns.length) }}
          จาก {{ filteredCampaigns.length }} รายการ
        </div>
        <UPagination
          v-model="currentPage"
          :total="totalPages"
        />
      </div>
    </UCard>

    <!-- Create Campaign Modal -->
    <UModal 
      v-model:open="showCreateModal"
      title="สร้างแคมเปญใหม่"
      description="สร้างแคมเปญการตลาดใหม่สำหรับเว็บไซต์ของคุณ"
    >
      <template #body>
        <form @submit.prevent="handleCreateCampaign" class="space-y-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ชื่อแคมเปญ <span class="text-red-500">*</span></label>
            <UInput
              v-model="newCampaign.name"
              placeholder="ชื่อแคมเปญ"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">คำอธิบาย</label>
            <UTextarea
              v-model="newCampaign.description"
              placeholder="คำอธิบายแคมเปญ..."
              :rows="3"
            />
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ประเภท <span class="text-red-500">*</span></label>
              <USelect
                v-model="newCampaign.type"
                :items="typeOptions.filter(t => t.value)"
                option-attribute="label"
                value-attribute="value"
                placeholder="เลือกประเภท"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">เป้าหมายผู้เข้าร่วม <span class="text-red-500">*</span></label>
              <UInput
                v-model="newCampaign.targetParticipants"
                type="number"
                placeholder="จำนวนเป้าหมาย"
              />
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">วันที่เริ่ม <span class="text-red-500">*</span></label>
              <UInput
                v-model="newCampaign.startDate"
                type="date"
                placeholder="เลือกวันที่เริ่ม"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">วันที่สิ้นสุด <span class="text-red-500">*</span></label>
              <UInput
                v-model="newCampaign.endDate"
                type="date"
                placeholder="เลือกวันที่สิ้นสุด"
              />
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">งบประมาณ</label>
            <UInput
              v-model="newCampaign.budget"
              type="number"
              placeholder="งบประมาณ (บาท)"
            />
          </div>
        </form>
      </template>

      <template #footer>
        <div class="flex justify-end gap-3">
          <UButton @click="showCreateModal = false" variant="outline">
            ยกเลิก
          </UButton>
          <UButton @click="handleCreateCampaign" :loading="creating">
            สร้างแคมเปญ
          </UButton>
        </div>
      </template>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';

definePageMeta({
  layout: 'dashboard',
  middleware: 'auth',
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// Use campaigns composable
const {
  getCampaigns,
  getCampaign,
  createCampaign,
  updateCampaign,
  deleteCampaign,
  startCampaign,
  stopCampaign,
  duplicateCampaign,
  getCampaignAnalytics,
} = useCampaigns();

// Types
interface Campaign {
  id: string;
  name: string;
  description: string;
  type: 'promotional' | 'seasonal' | 'loyalty' | 'referral' | 'launch';
  status: 'active' | 'inactive' | 'completed' | 'draft';
  participants: number;
  targetParticipants: number;
  startDate: string;
  endDate: string;
  conversionRate: number;
  budget?: number;
}

interface Analytics {
  totalCampaigns: number;
  activeCampaigns: number;
  totalParticipants: number;
  conversionRate: number;
}

// State
const campaigns = ref<Campaign[]>([]);
const analytics = ref<Analytics>({
  totalCampaigns: 0,
  activeCampaigns: 0,
  totalParticipants: 0,
  conversionRate: 0,
});
const loading = ref(false);
const creating = ref(false);
const searchQuery = ref('');
const statusFilter = ref('');
const typeFilter = ref('');
const dateFilter = ref('');
const currentPage = ref(1);
const itemsPerPage = ref(10);
const showCreateModal = ref(false);

// New campaign form
const newCampaign = ref({
  name: '',
  description: '',
  type: '',
  targetParticipants: '',
  startDate: '',
  endDate: '',
  budget: '',
});

// Options
const statusOptions = [
  { value: '', label: 'ทั้งหมด' },
  { value: 'active', label: 'ใช้งาน' },
  { value: 'inactive', label: 'ไม่ใช้งาน' },
  { value: 'completed', label: 'เสร็จสิ้น' },
  { value: 'draft', label: 'ร่าง' },
];

const typeOptions = [
  { value: '', label: 'ทั้งหมด' },
  { value: 'promotional', label: 'โปรโมชัน' },
  { value: 'seasonal', label: 'ตามฤดูกาล' },
  { value: 'loyalty', label: 'สมาชิก' },
  { value: 'referral', label: 'แนะนำเพื่อน' },
  { value: 'launch', label: 'เปิดตัวสินค้า' },
];

// Load campaigns
const loadCampaigns = async () => {
  loading.value = true;
  try {
    const response = await getCampaigns(siteId.value, {
      search: searchQuery.value,
      status: statusFilter.value,
      type: typeFilter.value,
      dateRange: dateFilter.value,
      page: currentPage.value,
      limit: itemsPerPage.value,
    });
    campaigns.value = (response as any)?.data || [];
  } catch (err: any) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: err.message || 'ไม่สามารถโหลดข้อมูลแคมเปญได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// Load analytics
const loadAnalytics = async () => {
  try {
    const response = await getCampaignAnalytics(siteId.value);
    analytics.value = (response as any)?.data || {
      totalCampaigns: 0,
      activeCampaigns: 0,
      totalParticipants: 0,
      conversionRate: 0,
    };
  } catch (err: any) {
    console.error('Error loading analytics:', err);
  }
};

// Load data on mount
onMounted(async () => {
  await Promise.all([loadCampaigns(), loadAnalytics()]);
});

// Watch for filter changes
watch([searchQuery, statusFilter, typeFilter, dateFilter], async () => {
  currentPage.value = 1;
  await loadCampaigns();
});

// Watch for route changes
watch(
  () => siteId.value,
  async newSiteId => {
    if (newSiteId) {
      await Promise.all([loadCampaigns(), loadAnalytics()]);
    }
  }
);

// Computed filtered campaigns
const filteredCampaigns = computed(() => {
  let filtered = campaigns.value || [];

  if (searchQuery.value) {
    filtered = filtered.filter(
      (c: any) =>
        c.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        c.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  if (statusFilter.value) {
    filtered = filtered.filter((c: any) => c.status === statusFilter.value);
  }

  if (typeFilter.value) {
    filtered = filtered.filter((c: any) => c.type === typeFilter.value);
  }

  if (dateFilter.value) {
    filtered = filtered.filter(
      (c: any) => c.startDate.startsWith(dateFilter.value) || c.endDate.startsWith(dateFilter.value)
    );
  }

  return filtered;
});

// Paginated campaigns
const paginatedCampaigns = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredCampaigns.value.slice(start, end);
});

const totalPages = computed(() => {
  return Math.ceil(filteredCampaigns.value.length / itemsPerPage.value);
});

// Create campaign
const handleCreateCampaign = async () => {
  creating.value = true;
  try {
    const campaignData = {
      name: newCampaign.value.name,
      description: newCampaign.value.description,
      type: newCampaign.value.type as
        | 'promotional'
        | 'seasonal'
        | 'loyalty'
        | 'referral'
        | 'launch',
      status: 'draft' as 'active' | 'inactive' | 'completed' | 'draft',
      targetParticipants: parseInt(newCampaign.value.targetParticipants),
      startDate: newCampaign.value.startDate,
      endDate: newCampaign.value.endDate,
      budget: newCampaign.value.budget ? parseInt(newCampaign.value.budget) : 0,
    };

    await createCampaign(siteId.value, campaignData);
    showCreateModal.value = false;

    // Reset form
    newCampaign.value = {
      name: '',
      description: '',
      type: '',
      targetParticipants: '',
      startDate: '',
      endDate: '',
      budget: '',
    };

    // Reload campaigns
    await loadCampaigns();

    useToast().add({
      title: 'สร้างแคมเปญสำเร็จ',
      description: 'แคมเปญถูกสร้างแล้ว',
      icon: 'i-heroicons-check-circle',
      color: 'success',
    });
  } catch (err: any) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: err.message || 'ไม่สามารถสร้างแคมเปญได้',
      color: 'error',
    });
  } finally {
    creating.value = false;
  }
};

// Edit campaign
const handleEditCampaign = async (campaignId: string) => {
  try {
    // Navigate to edit page or open edit modal
    navigateTo(`/dashboard/${siteId.value}/campaigns/${campaignId}/edit`);
  } catch (err: any) {
    console.error('Error editing campaign:', err);
  }
};

// View campaign details
const handleViewCampaign = async (campaignId: string) => {
  try {
    navigateTo(`/dashboard/${siteId.value}/campaigns/${campaignId}`);
  } catch (err: any) {
    console.error('Error viewing campaign:', err);
  }
};

// Toggle campaign status
const handleToggleCampaign = async (campaign: Campaign) => {
  try {
    loading.value = true;
    if (campaign.status === 'active') {
      await stopCampaign(siteId.value, campaign.id);
      useToast().add({
        title: 'หยุดแคมเปญแล้ว',
        description: `แคมเปญ "${campaign.name}" ถูกหยุดแล้ว`,
        color: 'info',
      });
    } else {
      await startCampaign(siteId.value, campaign.id);
      useToast().add({
        title: 'เริ่มแคมเปญแล้ว',
        description: `แคมเปญ "${campaign.name}" เริ่มทำงานแล้ว`,
        color: 'success',
      });
    }
    // Reload campaigns
    await loadCampaigns();
  } catch (err: any) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: err.message || 'ไม่สามารถเปลี่ยนสถานะแคมเปญได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// Duplicate campaign
const handleDuplicateCampaign = async (campaign: Campaign) => {
  try {
    loading.value = true;
    await duplicateCampaign(siteId.value, campaign.id);
    await loadCampaigns();
    useToast().add({
      title: 'คัดลอกแคมเปญสำเร็จ',
      description: `คัดลอกแคมเปญ "${campaign.name}" เรียบร้อยแล้ว`,
      color: 'success',
    });
  } catch (err: any) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: err.message || 'ไม่สามารถคัดลอกแคมเปญได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// Delete campaign
const handleDeleteCampaign = async (campaign: Campaign) => {
  if (confirm(`คุณต้องการลบแคมเปญ "${campaign.name}" ใช่หรือไม่?`)) {
    try {
      loading.value = true;
      await deleteCampaign(siteId.value, campaign.id);
      await loadCampaigns();
      useToast().add({
        title: 'ลบแคมเปญสำเร็จ',
        description: `ลบแคมเปญ "${campaign.name}" เรียบร้อยแล้ว`,
        color: 'success',
      });
    } catch (err: any) {
      useToast().add({
        title: 'ข้อผิดพลาด',
        description: err.message || 'ไม่สามารถลบแคมเปญได้',
        color: 'error',
      });
    } finally {
      loading.value = false;
    }
  }
};

// Utility functions
const getStatusColor = (
  status: string
): 'success' | 'primary' | 'secondary' | 'info' | 'warning' | 'error' | 'neutral' => {
  switch (status) {
    case 'active':
      return 'success';
    case 'inactive':
      return 'neutral';
    case 'completed':
      return 'info';
    case 'draft':
      return 'warning';
    default:
      return 'neutral';
  }
};

const getStatusVariant = (status: string) => {
  return status === 'draft' ? 'soft' : 'solid';
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'active':
      return 'ใช้งาน';
    case 'inactive':
      return 'ไม่ใช้งาน';
    case 'completed':
      return 'เสร็จสิ้น';
    case 'draft':
      return 'ร่าง';
    default:
      return status;
  }
};

const getTypeColor = (
  type: string
): 'success' | 'primary' | 'secondary' | 'info' | 'warning' | 'error' | 'neutral' => {
  switch (type) {
    case 'promotional':
      return 'primary';
    case 'seasonal':
      return 'warning';
    case 'loyalty':
      return 'info';
    case 'referral':
      return 'success';
    case 'launch':
      return 'error';
    default:
      return 'neutral';
  }
};

const getTypeVariant = (type: string): 'outline' | 'solid' | 'soft' | 'subtle' => {
  return 'soft';
};

const getTypeText = (type: string) => {
  switch (type) {
    case 'promotional':
      return 'โปรโมชัน';
    case 'seasonal':
      return 'ตามฤดูกาล';
    case 'loyalty':
      return 'สมาชิก';
    case 'referral':
      return 'แนะนำเพื่อน';
    case 'launch':
      return 'เปิดตัวสินค้า';
    default:
      return type;
  }
};

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

const getActionItems = (campaign: Campaign) => [
  [
    {
      label: 'แก้ไข',
      icon: 'i-heroicons-pencil-square',
      click: () => handleEditCampaign(campaign.id),
    },
    {
      label: 'ดูรายละเอียด',
      icon: 'i-heroicons-eye',
      click: () => handleViewCampaign(campaign.id),
    },
    {
      label: 'คัดลอก',
      icon: 'i-heroicons-document-duplicate',
      click: () => handleDuplicateCampaign(campaign),
    },
  ],
  [
    {
      label: campaign.status === 'active' ? 'หยุด' : 'เริ่ม',
      icon: campaign.status === 'active' ? 'i-heroicons-pause' : 'i-heroicons-play',
      click: () => handleToggleCampaign(campaign),
    },
  ],
  [
    {
      label: 'ลบ',
      icon: 'i-heroicons-trash',
      click: () => handleDeleteCampaign(campaign),
    },
  ],
];

// SEO
useSeoMeta({
  title: 'แคมเปญการตลาด - ระบบจัดการร้านค้า',
  description: 'จัดการแคมเปญการตลาดและโปรโมชัน',
  keywords: 'แคมเปญ, การตลาด, โปรโมชัน, ส่วนลด',
  ogTitle: 'แคมเปญการตลาด - ระบบจัดการร้านค้า',
  ogDescription: 'จัดการแคมเปญการตลาดและโปรโมชัน',
  ogType: 'website',
  twitterCard: 'summary_large_image',
});
</script> 