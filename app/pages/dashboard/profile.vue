<script setup lang="ts">
import { useAuthStore } from '~/stores/auth';
import { useMyImage } from '~/composables/useMyImage';

// Import useMyImage for avatar
const { imageUrl } = useMyImage();

definePageMeta({
  layout: 'manage',
  middleware: ['auth'],
});

const authStore = useAuthStore();

// State
const loading = ref(false);

// User data
const user = computed(() => authStore.getUser);
const isAuthenticated = computed(() => authStore.isAuthenticated);

// Reload user data
const handleReloadUser = async () => {
  loading.value = true;
  try {
    await authStore.loadUserDetail();
    useToast().add({
      title: 'สำเร็จ',
      description: 'โหลดข้อมูลผู้ใช้เรียบร้อยแล้ว',
      icon: 'i-heroicons-check-circle',
      color: 'success',
      duration: 1000,
    });
  } catch (error) {
    useToast().add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถโหลดข้อมูลผู้ใช้ได้',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'error',
      duration: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// Format date
const formatDate = (date: string | Date) => {
  return new Date(date).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};
</script>

<template>
  <UContainer>
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            ข้อมูลผู้ใช้
          </h1>
          <p class="text-gray-600 dark:text-gray-400 mt-1">
            จัดการข้อมูลส่วนตัวและการตั้งค่าบัญชี
          </p>
        </div>

        <div class="flex gap-2">
          <UButton @click="handleReloadUser" :loading="loading" variant="outline" icon="i-heroicons-arrow-path">
            รีโหลดข้อมูล
          </UButton>
        </div>
      </div>

      <!-- Authentication Status -->
      <UCard>
        <template #header>
          <div class="flex items-center gap-2">
            <Icon name="i-heroicons-shield-check" class="w-5 h-5" />
            <h2 class="text-lg font-semibold">สถานะการเข้าสู่ระบบ</h2>
          </div>
        </template>

        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
              สถานะ
            </span>
            <UBadge :color="isAuthenticated ? 'success' : 'error'" variant="subtle">
              {{ isAuthenticated ? 'เข้าสู่ระบบแล้ว' : 'ไม่ได้เข้าสู่ระบบ' }}
            </UBadge>
          </div>

        </div>
      </UCard>

      <!-- User Information -->
      <UCard v-if="user">
        <template #header>
          <div class="flex items-center gap-2">
            <Icon name="i-heroicons-user" class="w-5 h-5" />
            <h2 class="text-lg font-semibold">ข้อมูลส่วนตัว</h2>
          </div>
        </template>

        <div class="space-y-4">
          <!-- Avatar -->
          <div class="flex items-center gap-4">
            <ClientOnly>
              <UiImage :src="imageUrl(user.avatar, { width: 200, height: 200 })"
                :alt="user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.email"
                fallback-icon="solar:user-circle-bold-duotone" class="rounded" />
            </ClientOnly> 
          </div>

          <!-- User Details -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-3">
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  อีเมล
                </label>
                <p class="text-sm text-gray-900 dark:text-white">
                  {{ user.email }}
                </p>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  บทบาท
                </label>
                <UBadge :color="user.role === 'admin' ? 'primary' : 'neutral'" variant="subtle">
                  {{ user.role === 'admin' ? 'ผู้ดูแลระบบ' : 'ผู้ใช้ทั่วไป' }}
                </UBadge>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  สถานะอีเมล
                </label>
                <UBadge :color="user.isEmailVerified ? 'success' : 'warning'" variant="subtle">
                  {{ user.isEmailVerified ? 'ยืนยันแล้ว' : 'ยังไม่ยืนยัน' }}
                </UBadge>
              </div>
            </div>

            <div class="space-y-3">
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  คะแนนเงิน
                </label>
                <p class="text-sm text-gray-900 dark:text-white">
                  {{ user.moneyPoint?.toLocaleString() || 0 }} คะแนน
                </p>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  คะแนนทอง
                </label>
                <p class="text-sm text-gray-900 dark:text-white">
                  {{ user.goldPoint?.toLocaleString() || 0 }} คะแนน
                </p>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  สมาชิกเมื่อ
                </label>
                <p class="text-sm text-gray-900 dark:text-white">
                  {{ formatDate(user.createdAt) }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </UCard>

      <!-- Debug Information (เฉพาะ development) -->
      <UCard v-if="$config.public.dev">
        <template #header>
          <div class="flex items-center gap-2">
            <Icon name="i-heroicons-bug-ant" class="w-5 h-5" />
            <h2 class="text-lg font-semibold">ข้อมูลการ Debug</h2>
          </div>
        </template>

        <div class="space-y-4">
          <div>
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
              Access Token
            </label>
            <code class="block text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded mt-1 break-all">
        ไม่มี access token (ใช้ session-based authentication)
      </code>
          </div>

          <div>
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
              Refresh Token (ตัวอย่าง 50 ตัวอักษรแรก)
            </label>
            <code class="block text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded mt-1 break-all">
        ไม่มี refresh token (ใช้ session-based authentication)
      </code>
          </div>

          <div>
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
              Raw User Data
            </label>
            <pre class="text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded mt-1 overflow-auto max-h-40">{{ JSON.stringify(user,
              null, 2) }}</pre>
          </div>
        </div>
      </UCard>
    </div>
  </UContainer>
</template>