<script setup lang="ts">
definePageMeta({
  layout: 'manage',
  middleware: ['auth'],
});

const { getTransactionHistory, topupPoints } = useUser();
const authStore = useAuthStore();

// State
const loading = ref(false);
const processingPayment = ref(false);
const selectedPackage = ref<any>(null);
const selectedPaymentMethod = ref('');
const showPaymentModal = ref(false);

// Loading states
const balanceLoading = ref(false);
const transactionLoading = ref(false);

// Transaction history
const transactions = ref<any[]>([]);

// Load user balance
onMounted(async () => {
  await loadTransactionHistory();
});

const loadTransactionHistory = async () => {
  transactionLoading.value = true;
  try {
    const response = await getTransactionHistory('topup', 10);
    if (response?.success && response?.data) {
      transactions.value = response.data;
    }
  } catch (error) {
    console.error('Error loading transaction history:', error);
    useToast().add({
      title: 'ผิดพลาด',
      description: 'ไม่สามารถโหลดประวัติการทำรายการได้',
      color: 'error',
    });
  } finally {
    transactionLoading.value = false;
  }
};

// Point packages
const moneyPointPackages = [
  {
    id: 'money_100',
    name: '100 Money Points',
    points: 100,
    price: 100,
    bonus: 0,
    popular: false,
  },
  {
    id: 'money_500',
    name: '500 Money Points',
    points: 500,
    price: 500,
    bonus: 25,
    popular: true,
  },
  {
    id: 'money_1000',
    name: '1,000 Money Points',
    points: 1000,
    price: 1000,
    bonus: 100,
    popular: false,
  },
  {
    id: 'money_2000',
    name: '2,000 Money Points',
    points: 2000,
    price: 2000,
    bonus: 300,
    popular: false,
  },
];

const goldPointPackages = [
  {
    id: 'gold_50',
    name: '50 Gold Points',
    points: 50,
    price: 150,
    bonus: 0,
    popular: false,
  },
  {
    id: 'gold_100',
    name: '100 Gold Points',
    points: 100,
    price: 300,
    bonus: 10,
    popular: true,
  },
  {
    id: 'gold_250',
    name: '250 Gold Points',
    points: 250,
    price: 750,
    bonus: 50,
    popular: false,
  },
  {
    id: 'gold_500',
    name: '500 Gold Points',
    points: 500,
    price: 1500,
    bonus: 150,
    popular: false,
  },
];

// Payment methods
const paymentMethods = [
  {
    id: 'credit_card',
    name: 'บัตรเครดิต/เดบิต',
    icon: 'solar:card-line-duotone',
    description: 'Visa, MasterCard, JCB',
  },
  {
    id: 'bank_transfer',
    name: 'โอนเงินผ่านธนาคาร',
    icon: 'solar:bank-line-duotone',
    description: 'ธนาคารไทยพาณิชย์, กสิกรไทย',
  },
  {
    id: 'mobile_banking',
    name: 'Mobile Banking',
    icon: 'solar:phone-line-duotone',
    description: 'SCB Easy, K PLUS',
  },
  {
    id: 'e_wallet',
    name: 'E-Wallet',
    icon: 'solar:wallet-line-duotone',
    description: 'TrueMoney, Rabbit LINE Pay',
  },
];

// Select package
const selectPackage = (pkg: any) => {
  selectedPackage.value = pkg;
  showPaymentModal.value = true;
};

// Process payment
const processPayment = async () => {
  if (!selectedPackage.value || !selectedPaymentMethod.value) return;

  processingPayment.value = true;
  try {
    const response = await topupPoints({
      type: selectedPackage.value.id.includes('money') ? 'moneyPoint' : 'goldPoint',
      amount: selectedPackage.value.points + selectedPackage.value.bonus,
      packageId: selectedPackage.value.id,
      paymentMethod: selectedPaymentMethod.value,
    });

    if (response?.success && response?.data) {
      // Update user balance
      //   userBalance.value = response.data.newBalance;

      useToast().add({
        title: 'สำเร็จ',
        description: `เติม ${selectedPackage.value.name} เรียบร้อยแล้ว`,
        color: 'success',
      });

      // Close modal and refresh data
      showPaymentModal.value = false;
      selectedPackage.value = null;
      selectedPaymentMethod.value = '';
      await loadTransactionHistory();
    } else {
      throw new Error(response?.message || 'ไม่สามารถประมวลผลการชำระเงินได้');
    }
  } catch (error: any) {
    console.error('Payment error:', error);

    // Extract error message from API response
    let errorMessage = 'ไม่สามารถประมวลผลการชำระเงินได้';
    if (error?.data?.message) {
      errorMessage = error.data.message;
    } else if (error?.message) {
      errorMessage = error.message;
    }

    // Show error toast
    useToast().add({
      title: 'ผิดพลาด',
      description: errorMessage,
      color: 'error',
    });
  } finally {
    processingPayment.value = false;
  }
};

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
  }).format(amount);
};

// Format date
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Get transaction status color
const getStatusColor = (status: string): 'success' | 'warning' | 'error' | 'neutral' => {
  const colors: Record<string, 'success' | 'warning' | 'error' | 'neutral'> = {
    completed: 'success',
    pending: 'warning',
    failed: 'error',
    cancelled: 'neutral',
  };
  return colors[status] || 'neutral';
};

// Get transaction status text
const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    completed: 'สำเร็จ',
    pending: 'รอดำเนินการ',
    failed: 'ล้มเหลว',
    cancelled: 'ยกเลิก',
  };
  return texts[status] || status;
};

// Get payment method name
const getPaymentMethodName = (method: string) => {
  const names: Record<string, string> = {
    credit_card: 'บัตรเครดิต',
    bank_transfer: 'โอนเงินธนาคาร',
    mobile_banking: 'Mobile Banking',
    e_wallet: 'E-Wallet',
  };
  return names[method] || method;
};
</script>

<template>
  <div class="max-w-6xl mx-auto space-y-6">
    <!-- Header -->
    <div class="text-center">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
        เติมเงิน
      </h1>
      <p class="text-gray-600 dark:text-gray-400 mt-2">
        เติม Money Points และ Gold Points สำหรับใช้งานในระบบ
      </p>
    </div>

    <!-- Current Balance -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <UCard>
        <div class="text-center">
          <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon name="solar:wallet-money-line-duotone" class="w-8 h-8 text-blue-500" />
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            Money Points
          </h3>
          <div v-if="balanceLoading" class="mt-2">
            <div class="flex items-center justify-center gap-2">
              <Icon name="eos-icons:bubble-loading" class="w-5 h-5 animate-spin text-blue-500" />
              <span class="text-sm text-gray-600 dark:text-gray-400">กำลังโหลด...</span>
            </div>
          </div>
          <p v-else class="text-3xl font-bold text-blue-600 dark:text-blue-400 mt-2">
            {{ authStore.userDetail?.moneyPoint.toLocaleString() }}
          </p>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            ใช้สำหรับซื้อแพ็คเกจและบริการ
          </p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-16 h-16 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon name="solar:star-line-duotone" class="w-8 h-8 text-yellow-500" />
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            Gold Points
          </h3>
          <div v-if="balanceLoading" class="mt-2">
            <div class="flex items-center justify-center gap-2">
              <Icon name="eos-icons:bubble-loading" class="w-5 h-5 animate-spin text-yellow-500" />
              <span class="text-sm text-gray-600 dark:text-gray-400">กำลังโหลด...</span>
            </div>
          </div>
          <p v-else class="text-3xl font-bold text-yellow-600 dark:text-yellow-400 mt-2">
            {{ authStore.userDetail?.goldPoint.toLocaleString() }}
          </p>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            ใช้สำหรับฟีเจอร์พิเศษและโบนัส
          </p>
        </div>
      </UCard>
    </div>

    <!-- Money Point Packages -->
    <UCard>
      <template #header>
        <div class="flex items-center gap-3">
          <Icon name="solar:wallet-money-line-duotone" class="w-6 h-6 text-blue-500" />
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            แพ็คเกจ Money Points
          </h2>
        </div>
      </template>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div 
          v-for="pkg in moneyPointPackages" 
          :key="pkg.id"
          :class="[
            'border-2 rounded-lg p-4 cursor-pointer transition-all relative',
            'hover:border-blue-300 dark:hover:border-blue-600',
            'border-gray-200 dark:border-gray-700'
          ]"
          @click="selectPackage(pkg)"
        >
          <div v-if="pkg.popular" class="absolute -top-2 left-1/2 transform -translate-x-1/2">
            <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
              แนะนำ
            </span>
          </div>
          
          <div class="text-center">
            <h3 class="font-semibold text-gray-900 dark:text-white">
              {{ pkg.points.toLocaleString() }}
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              Money Points
            </p>
            
            <div v-if="pkg.bonus > 0" class="mt-2">
              <span class="text-xs bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 px-2 py-1 rounded">
                +{{ pkg.bonus }} โบนัส
              </span>
            </div>
            
            <div class="mt-4">
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {{ formatCurrency(pkg.price) }}
              </p>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                ({{ (pkg.price / pkg.points).toFixed(2) }} บาท/พอยต์)
              </p>
            </div>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Gold Point Packages -->
    <UCard>
      <template #header>
        <div class="flex items-center gap-3">
          <Icon name="solar:star-line-duotone" class="w-6 h-6 text-yellow-500" />
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            แพ็คเกจ Gold Points
          </h2>
        </div>
      </template>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div 
          v-for="pkg in goldPointPackages" 
          :key="pkg.id"
          :class="[
            'border-2 rounded-lg p-4 cursor-pointer transition-all relative',
            'hover:border-yellow-300 dark:hover:border-yellow-600',
            'border-gray-200 dark:border-gray-700'
          ]"
          @click="selectPackage(pkg)"
        >
          <div v-if="pkg.popular" class="absolute -top-2 left-1/2 transform -translate-x-1/2">
            <span class="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">
              แนะนำ
            </span>
          </div>
          
          <div class="text-center">
            <h3 class="font-semibold text-gray-900 dark:text-white">
              {{ pkg.points.toLocaleString() }}
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              Gold Points
            </p>
            
            <div v-if="pkg.bonus > 0" class="mt-2">
              <span class="text-xs bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 px-2 py-1 rounded">
                +{{ pkg.bonus }} โบนัส
              </span>
            </div>
            
            <div class="mt-4">
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {{ formatCurrency(pkg.price) }}
              </p>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                ({{ (pkg.price / pkg.points).toFixed(2) }} บาท/พอยต์)
              </p>
            </div>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Transaction History -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            ประวัติการทำรายการ
          </h2>
          <UButton variant="outline" size="sm" @click="loadTransactionHistory">
            <Icon name="solar:refresh-line-duotone" class="w-4 h-4" />
            รีเฟรช
          </UButton>
        </div>
      </template>

      <div v-if="transactionLoading" class="text-center py-8">
        <Icon name="eos-icons:bubble-loading" class="w-8 h-8 animate-spin mx-auto mb-2" />
        <p class="text-gray-600 dark:text-gray-400">กำลังโหลด...</p>
      </div>

      <div v-else-if="transactions.length === 0" class="text-center py-8">
        <Icon name="solar:history-line-duotone" class="w-12 h-12 mx-auto mb-2 opacity-50" />
        <p class="text-gray-600 dark:text-gray-400">ไม่มีประวัติการทำรายการ</p>
      </div>

      <div v-else class="space-y-3">
        <div 
          v-for="transaction in transactions" 
          :key="transaction.id"
          class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
              <Icon 
                :name="transaction.pointType === 'moneyPoint' ? 'solar:wallet-money-line-duotone' : 'solar:star-line-duotone'" 
                class="w-5 h-5 text-blue-500" 
              />
            </div>
            <div>
              <p class="font-medium text-gray-900 dark:text-white">
                เติม {{ transaction.pointType === 'moneyPoint' ? 'Money Points' : 'Gold Points' }}
              </p>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ getPaymentMethodName(transaction.method) }} • {{ formatDate(transaction.createdAt) }}
              </p>
            </div>
          </div>
          
          <div class="text-right">
            <p class="font-semibold text-gray-900 dark:text-white">
              +{{ transaction.amount.toLocaleString() }}
            </p>
            <UBadge 
              :color="getStatusColor(transaction.status)"
              variant="subtle"
              size="xs"
            >
              {{ getStatusText(transaction.status) }}
            </UBadge>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Payment Modal -->
    <UModal 
      v-model:open="showPaymentModal"
      title="ชำระเงิน"
      description="กรุณาเลือกวิธีการชำระเงินและยืนยันการเติมเงิน"
    >
      <template #body>
        <div v-if="selectedPackage" class="space-y-4">
          <!-- Package Summary -->
          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <h4 class="font-medium text-gray-900 dark:text-white mb-2">
              {{ selectedPackage.name }}
            </h4>
            <div class="space-y-1 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">จำนวน:</span>
                <span class="text-gray-900 dark:text-white">{{ selectedPackage.points.toLocaleString() }}</span>
              </div>
              <div v-if="selectedPackage.bonus > 0" class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">โบนัส:</span>
                <span class="text-green-600 dark:text-green-400">+{{ selectedPackage.bonus.toLocaleString() }}</span>
              </div>
              <div class="flex justify-between font-semibold">
                <span class="text-gray-600 dark:text-gray-400">ราคา:</span>
                <span class="text-gray-900 dark:text-white">{{ formatCurrency(selectedPackage.price) }}</span>
              </div>
            </div>
          </div>

          <!-- Payment Methods -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              วิธีการชำระเงิน
            </label>
            <div class="space-y-2">
              <div 
                v-for="method in paymentMethods" 
                :key="method.id"
                :class="[
                  'border-2 rounded-lg p-3 cursor-pointer transition-all',
                  selectedPaymentMethod === method.id
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                ]"
                @click="selectedPaymentMethod = method.id"
              >
                <div class="flex items-center gap-3">
                  <Icon :name="method.icon" class="w-5 h-5 text-gray-500" />
                  <div>
                    <p class="font-medium text-gray-900 dark:text-white">{{ method.name }}</p>
                    <p class="text-xs text-gray-600 dark:text-gray-400">{{ method.description }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <template #footer>
        <div class="flex gap-3">
          <UButton 
            variant="outline" 
            class="flex-1"
            @click="showPaymentModal = false"
            :disabled="processingPayment"
          >
            ยกเลิก
          </UButton>
          <UButton 
            :loading="processingPayment"
            :disabled="!selectedPaymentMethod"
            class="flex-1"
            @click="processPayment"
          >
            {{ processingPayment ? 'กำลังประมวลผล...' : 'ชำระเงิน' }}
          </UButton>
        </div>
      </template>
    </UModal>
  </div>
</template>
