<script setup lang="ts">
definePageMeta({
  layout: 'manage',
  middleware: ['auth']
});

import { useMyImage } from '~/composables/useMyImage'

// ใช้ useSites สำหรับจัดการข้อมูลเว็บไซต์
const { getSites } = useSites();

// State สำหรับ pagination
const pagination = ref({
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 1,
});

// ดึงข้อมูลเว็บไซต์
const { data: sitesData, pending: loading, refresh: refreshSites } = await useLazyAsyncData(
  'dashboard-sites',
  () => getSites({ page: pagination.value.page, limit: pagination.value.limit }),
  {
    watch: [() => pagination.value.page]
  }
);

const sites = computed(() => sitesData.value?.data?.sites || []);

// Update pagination when data changes
watch(sitesData, (newData) => {
  if (newData?.data) {
    pagination.value = {
      page: newData.data.page,
      limit: newData.data.limit,
      total: newData.data.total,
      totalPages: newData.data.totalPages,
    };
  }
}, { immediate: true });


// Open site in new tab
const openSite = (domain: string) => {
  if (typeof window !== 'undefined') {
    window.open(`https://${domain}`, '_blank');
  }
};

// Format date
const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('th-TH');
};

// Get role label
const getRoleLabel = (role: string) => {
  switch (role) {
    case 'owner': return 'เจ้าของ';
    case 'admin': return 'ผู้ดูแล';
    case 'editor': return 'บรรณาธิการ';
    case 'viewer': return 'ผู้ดู';
    default: return role;
  }
};

// Check if site is expired
const isExpired = (expiredAt: string) => {
  return new Date(expiredAt) < new Date();
};

// Get days until expiration
const getDaysUntilExpiration = (expiredAt: string) => {
  const now = new Date();
  const expiry = new Date(expiredAt);
  const diffTime = expiry.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

// เพิ่ม computed สำหรับ logoUrl
const { imageUrl } = useMyImage()
// สมมุติว่า site คือ ref หลัก ถ้าไม่ใช่ ให้เปลี่ยนเป็นตัวแปรที่ถูกต้อง
// ลบ logoUrl เดี่ยวออก ใช้ imageUrl(site?.seoSettings?.logo, {...}) ใน template แทน

// เพิ่มฟังก์ชัน reloadSites สำหรับรีเฟรชข้อมูลเว็บไซต์
function reloadSites() {
  refreshSites && refreshSites();
}

onMounted(() => {
  reloadSites();
});
</script>

<template>
  <div class="p-3 max-w-(--ui-container) mx-auto">

    <!-- Header Actions -->
    <div class="flex justify-end mb-4 gap-2">
      <UButton @click="reloadSites" :loading="loading" color="secondary" icon="i-heroicons-arrow-path" variant="soft"
        :disabled="loading" title="รีเฟรชรายการเว็บไซต์" />
      <UButton to="/dashboard/create" icon="i-heroicons-plus" size="md" color="primary">
        สร้างเว็บไซต์ใหม่
      </UButton>
    </div>

    <!-- Sites Grid -->
    <div v-if="!loading && sites.length > 0" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
      <NuxtLink :to="`/dashboard/${site._id}`" v-for="site in sites as any" :key="site._id"
        class="relative mt-16 hover:scale-101 transition-all duration-300 ease-in-out">
        <div class="absolute -mt-16 w-full flex justify-center">
          <div class="h-32 w-32">
            <img v-if="!!imageUrl(site?.seoSettings?.logo, { width: 200, height: 200, crop: 'fit' })"
              :src="imageUrl(site?.seoSettings?.logo, { width: 200, height: 200, crop: 'fit' }) || undefined"
              :alt="site?.seoSettings?.logo || 'โลโก้'"
              class="w-32 h-32 rounded-full object-cover bg-gray-100 dark:bg-gray-800" />
            <div v-else class="w-32 h-32 bg-[var(--ui-bg-1)] rounded-full flex items-center justify-center">
              <Icon name="solar:global-line-duotone" class="size-32 text-[var(--ui-text-1)]" />
            </div>
          </div>
        </div>
        <div class="rounded-xl overflow-hidden shadow text-base-content h-full bg-[var(--ui-bg-1)]">
          <div class="mt-15 flex flex-col gap-3 text-center p-3">
            <h1 class="font-semibold text-xl md:text-2xl">
              {{ site?.title || site?.fullDomain || 'ไม่มีชื่อ' }}
            </h1>
            <span
              class="absolute flex items-center gap-1 left-2 top-2 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 font-medium rounded-lg">
              <UIcon name="i-heroicons-user-group" class="size-6" /> {{ getRoleLabel(site.userRole) }}
            </span>
            <span @click="openSite(site.fullDomain)"
              class="absolute z-10 flex items-center gap-2 right-2 top-2 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 font-medium rounded-lg">
              <UIcon name="i-heroicons-globe-alt" class="size-6" /> ดูเว็บไซต์
            </span>
            <p v-if="site.description" class="text-lg truncate max-w-xl mx-auto">
              {{ site.description }}
            </p>
            <div class="flex items-center justify-between p-2 rounded-xl" :class="isExpired(site.expiredAt)
              ? 'bg-error/10 text-error'
              : 'bg-success/10 text-success'">
              <div class="flex justify-center items-center gap-2">
                <Icon name="i-heroicons-clock" class="size-6"
                  :class="isExpired(site.expiredAt) ? 'text-red-500' : 'text-success'" />
                <span class="font-medium">
                  {{ isExpired(site.expiredAt) ? 'หมดอายุแล้ว' : `เหลือ ${getDaysUntilExpiration(site.expiredAt)} วัน`
                  }}
                </span>
              </div>
              <span class="opacity-75">
                {{ formatDate(site.expiredAt) }}
              </span>
            </div>
            <div v-if="site.keyword?.length > 0" class="card-actions justify-center">
              <div v-for="(keyword, i) in site.keyword" :key="i" class="btn btn-sm btn-outline">
                {{ keyword }}
              </div>
            </div>
          </div>
        </div>
      </NuxtLink>
    </div>


    <!-- Empty State -->
    <UCard variant="soft" v-else-if="!loading && sites.length === 0" class="text-center py-12">
      <div class="max-w-md mx-auto">
        <Icon name="i-heroicons-globe-alt" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          ยังไม่มีเว็บไซต์
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          เริ่มต้นสร้างเว็บไซต์แรกของคุณและเริ่มต้นการเดินทางออนไลน์
        </p>
        <UButton to="/dashboard/create" icon="i-heroicons-plus" size="lg">
          สร้างเว็บไซต์ใหม่
        </UButton>
      </div>
    </UCard>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center py-12">
      <div class="flex flex-col items-center gap-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        <p class="text-sm text-gray-600 dark:text-gray-400">กำลังโหลดเว็บไซต์...</p>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="!loading && pagination.totalPages > 1" class="flex justify-center mt-6">
      <UPagination v-model:page="pagination.page" :total="pagination.total" :items-per-page="pagination.limit"
        show-edges color="primary" size="md" />
    </div>
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
