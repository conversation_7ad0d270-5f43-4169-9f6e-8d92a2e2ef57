<script setup lang="ts">
definePageMeta({
  layout: 'manage',
  middleware: ['auth'],
});

const { joinTeam, getInvitationDetails } = useTeam();
const route = useRoute();

// State
const loading = ref(false);
const checkingInvitation = ref(false);
const invitationCode = ref('');
const invitationDetails = ref<any>(null); 

// Check if there's an invitation token in the URL
onMounted(() => {
  const token = route.query.token as string;
  if (token) { 
    invitationCode.value = token;
    checkInvitation();
  }
});

// Check invitation details
const checkInvitation = async () => {
  if (!invitationCode.value.trim()) return;

  checkingInvitation.value = true;
  try {
    const response = await getInvitationDetails(invitationCode.value);
    if (response.success && response.data) {
      invitationDetails.value = response.data;
    } else {
      useToast().add({
        title: 'ผิดพลาด',
        description: 'ไม่พบคำเชิญหรือคำเชิญหมดอายุแล้ว',
        color: 'error',
      });
    }
  } catch (error: any) {
    useToast().add({
      title: 'ผิดพลาด',
      description: error.message || 'ไม่สามารถตรวจสอบคำเชิญได้',
      color: 'error',
    });
  } finally {
    checkingInvitation.value = false;
  }
};

// Join team
const handleJoinTeam = async () => {
  if (!invitationCode.value.trim()) return;

  loading.value = true;
  try {
    const response = await joinTeam(invitationCode.value);
    if (response.success && response.data) {
      useToast().add({
        title: 'สำเร็จ',
        description: 'เข้าร่วมทีมเรียบร้อยแล้ว',
        color: 'success',
      });

      // Redirect to the site dashboard
      if (response.data.siteId) {
        await navigateTo(`/dashboard/${response.data.siteId}`);
      } else {
        await navigateTo('/dashboard');
      }
    }
  } catch (error: any) {
    useToast().add({
      title: 'ผิดพลาด',
      description: error.message || 'ไม่สามารถเข้าร่วมทีมได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// Format date
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

// Get role color
const getRoleColor = (role: string): 'error' | 'warning' | 'primary' | 'neutral' => {
  const colors: Record<string, 'error' | 'warning' | 'primary' | 'neutral'> = {
    owner: 'error',
    admin: 'warning',
    editor: 'primary',
    viewer: 'neutral',
  };
  return colors[role] || 'neutral';
};

// Get role name in Thai
const getRoleName = (role: string) => {
  const names: Record<string, string> = {
    owner: 'เจ้าของ',
    admin: 'ผู้ดูแล',
    editor: 'บรรณาธิการ',
    viewer: 'ผู้ดู',
  };
  return names[role] || role;
};
</script>

<template>
  <UContainer>
    <!-- Header -->
    <div class="text-center">
      <h1 class="text-xl font-bold text-gray-900 dark:text-white">
        เข้าร่วมทีมงาน
      </h1>
      <p class="text-gray-600 dark:text-gray-400 mt-2">
        เข้าร่วมทีมงานเว็บไซต์ผ่านรหัสเชิญหรือลิงก์
      </p>
    </div>

    <!-- Join Methods -->
    <UCard v-if="!invitationDetails">
      <div class="space-y-6 max-w-md mx-auto">

        <!-- Code Input -->
        <div class="space-y-4">
          <UFormField label="รหัสเชิญ" required>
            <UInput size="xl" v-model="invitationCode" placeholder="กรอกรหัสเชิญ 6 หลัก" maxlength="6"
              class="text-center text-lg tracking-wider w-full" />
          </UFormField>

          <div class="flex justify-center">
            <UButton label="ตรวจสอบรหัสเชิญ" size="xl" :loading="checkingInvitation"
              :disabled="invitationCode.length !== 6" @click="checkInvitation" />

          </div>
        </div> 

        <!-- Help Text -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div class="flex items-start gap-3">
            <Icon name="solar:info-circle-line-duotone" class="w-5 h-5 text-blue-500 mt-0.5" />
            <div class="text-sm text-blue-700 dark:text-blue-300">
              <p class="font-medium mb-1">วิธีเข้าร่วมทีม:</p>
              <ul class="space-y-1 text-xs">
                <li>• ใส่รหัสเชิญ 6 หลักที่ได้รับจากเจ้าของเว็บไซต์</li>
                <li>• คลิกลิงก์เชิญที่ส่งมาทาง Email หรือ SMS</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Invitation Details -->
    <UCard v-if="invitationDetails">
      <div class="space-y-6">
        <!-- Site Info -->
        <div class="text-center">
          <div
            class="w-16 h-16 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon name="solar:global-line-duotone" class="w-8 h-8 text-primary-500" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            {{ invitationDetails.site?.name || 'ไม่ระบุชื่อเว็บไซต์' }}
          </h2>
          <p class="text-gray-600 dark:text-gray-400">
            {{ invitationDetails.site?.domain || 'ไม่ระบุโดเมน' }}
          </p>
        </div>

        <!-- Invitation Details -->
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
              เชิญโดย:
            </span>
            <span class="text-sm text-gray-900 dark:text-white">
              {{ invitationDetails.invitedBy?.name || 'ไม่ระบุชื่อ' }}
            </span>
          </div>

          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
              บทบาท:
            </span>
            <UBadge :color="getRoleColor(invitationDetails.role)" variant="subtle">
              {{ getRoleName(invitationDetails.role) }}
            </UBadge>
          </div>

          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
              วันที่เชิญ:
            </span>
            <span class="text-sm text-gray-900 dark:text-white">
              {{ formatDate(invitationDetails.createdAt) }}
            </span>
          </div>

          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
              หมดอายุ:
            </span>
            <span class="text-sm text-gray-900 dark:text-white">
              {{ formatDate(invitationDetails.expiresAt) }}
            </span>
          </div>
        </div>

        <!-- Role Permissions -->
        <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <h3 class="font-medium text-green-800 dark:text-green-200 mb-2">
            สิทธิ์ที่จะได้รับ:
          </h3>
          <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
            <li v-if="invitationDetails.role === 'owner'">
              • จัดการเว็บไซต์ทั้งหมด
            </li>
            <li v-if="['owner', 'admin'].includes(invitationDetails.role)">
              • จัดการสมาชิกและบทบาท
            </li>
            <li v-if="['owner', 'admin', 'editor'].includes(invitationDetails.role)">
              • แก้ไขเนื้อหาและสินค้า
            </li>
            <li v-if="['owner', 'admin', 'editor'].includes(invitationDetails.role)">
              • เขียนและจัดการบล็อก
            </li>
            <li>• ดูสถิติและรายงาน</li>
          </ul>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-3">
          <UButton size="xl" variant="outline" class="flex-1" @click="invitationDetails = null">
            ยกเลิก
          </UButton>
          <UButton size="xl" :loading="loading" class="flex-1" @click="handleJoinTeam">
            เข้าร่วมทีม
          </UButton>
        </div>
      </div>
    </UCard>


  </UContainer>
</template>
