<script setup lang="ts">
import { useAuthStore } from '~/stores/auth';

definePageMeta({
  layout: 'empty',
  middleware: ['auth'],
});

const authStore = useAuthStore();
const { refreshSession } = useSession();

// State
const refreshing = ref(false);
const checking = ref(false);
const testResults = ref<any[]>([]);

// User data
const user = computed(() => authStore.getUser);

// Test refresh session
const testRefreshToken = async () => {
  refreshing.value = true;
  const startTime = Date.now();

  try {
    const success = await refreshSession();
    const endTime = Date.now();

    testResults.value.unshift({
      type: 'refresh',
      success,
      duration: endTime - startTime,
      timestamp: new Date(),
      message: success ? 'Refresh session สำเร็จ' : 'Refresh session ล้มเหลว',
    });

    if (success) {
      useToast().add({
        title: 'สำเร็จ',
        description: 'Refresh session เรียบร้อยแล้ว',
        color: 'success',
      });
    } else {
      useToast().add({
        title: 'ล้มเหลว',
        description: 'ไม่สามารถ refresh session ได้',
        color: 'error',
      });
    }
  } catch (error: any) {
    const endTime = Date.now();
    testResults.value.unshift({
      type: 'refresh',
      success: false,
      duration: endTime - startTime,
      timestamp: new Date(),
      message: `Error: ${error.message}`,
      error: error,
    });

    useToast().add({
      title: 'เกิดข้อผิดพลาด',
      description: error.message,
      color: 'error',
    });
  } finally {
    refreshing.value = false;
  }
};

// Test API call
const testApiCall = async () => {
  checking.value = true;
  const startTime = Date.now();

  try {
    const response = await $fetch('/api/v1/user/profile');
    const endTime = Date.now();

    testResults.value.unshift({
      type: 'api',
      success: true,
      duration: endTime - startTime,
      timestamp: new Date(),
      message: 'API call สำเร็จ',
      data: response,
    });

    useToast().add({
      title: 'สำเร็จ',
      description: 'เรียก API เรียบร้อยแล้ว',
      color: 'success',
    });
  } catch (error: any) {
    const endTime = Date.now();
    testResults.value.unshift({
      type: 'api',
      success: false,
      duration: endTime - startTime,
      timestamp: new Date(),
      message: `API Error: ${error.message}`,
      error: error,
    });

    useToast().add({
      title: 'เกิดข้อผิดพลาด',
      description: error.message,
      color: 'error',
    });
  } finally {
    checking.value = false;
  }
};

// Clear test results
const clearResults = () => {
  testResults.value = [];
};

// Format duration
const formatDuration = (ms: number) => {
  return `${ms}ms`;
};

// Format date
const formatDate = (date: Date) => {
  return date.toLocaleString('th-TH');
};
</script>

<template>
  <UContainer>
    <div class="space-y-6">
      <!-- Header -->
      <div class="text-center">
        <h1 class="text-3xl font-bold mb-4">ทดสอบระบบ Authentication</h1>
        <p class="text-gray-600 mb-8">ทดสอบการทำงานของ token และ refresh token</p>
      </div>

      <!-- User Info -->
      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold">ข้อมูลผู้ใช้ปัจจุบัน</h2>
        </template>
        
        <div v-if="user" class="space-y-3">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="text-sm font-medium text-gray-700">อีเมล</label>
              <p class="text-sm">{{ user.email }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700">บทบาท</label>
              <UBadge :color="user.role === 'admin' ? 'primary' : 'neutral'">
                {{ user.role }}
              </UBadge>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700">คะแนนเงิน</label>
              <p class="text-sm">{{ user.moneyPoint?.toLocaleString() || 0 }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700">คะแนนทอง</label>
              <p class="text-sm">{{ user.goldPoint?.toLocaleString() || 0 }}</p>
            </div>
          </div>
        </div>
        
        <div v-else class="text-center py-4">
          <p class="text-gray-500">ไม่พบข้อมูลผู้ใช้</p>
        </div>
      </UCard>

      <!-- Session Info -->
      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold">ข้อมูล Session</h2>
        </template>
        
        <div class="text-center py-4">
          <UBadge color="success">Session-based Authentication</UBadge>
          <p class="text-sm text-gray-600 mt-2">
            ระบบใช้ session-based authentication แทน token-based<br>
            Session จะถูกจัดการโดย server อัตโนมัติ
          </p>
        </div>
      </UCard>

      <!-- Test Controls -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold">การทดสอบ</h2>
            <UButton @click="clearResults" variant="ghost" size="sm" icon="i-heroicons-trash">
              ล้างผลลัพธ์
            </UButton>
          </div>
        </template>
        
        <div class="flex flex-wrap gap-3">
          <UButton 
            @click="testRefreshToken" 
            :loading="refreshing"
            icon="i-heroicons-arrow-path"
            color="primary"
          >
            ทดสอบ Refresh Token
          </UButton>
          
          <UButton 
            @click="testApiCall" 
            :loading="checking"
            icon="i-heroicons-cloud"
            color="info"
          >
            ทดสอบ API Call
          </UButton>
          
          <UButton 
            @click="refreshSession" 
            icon="i-heroicons-shield-check"
            color="success"
          >
            ตรวจสอบ Session
          </UButton>
          
          <UButton 
            @click="authStore.loadUserDetail()" 
            icon="i-heroicons-user"
            color="warning"
          >
            โหลดข้อมูลผู้ใช้
          </UButton>
        </div>
      </UCard>

      <!-- Test Results -->
      <UCard v-if="testResults.length > 0">
        <template #header>
          <h2 class="text-lg font-semibold">ผลการทดสอบ ({{ testResults.length }})</h2>
        </template>
        
        <div class="space-y-3 max-h-96 overflow-y-auto">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            class="border rounded-lg p-3"
            :class="result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'"
          >
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center gap-2">
                <UBadge :color="result.success ? 'success' : 'error'">
                  {{ result.type === 'refresh' ? 'Refresh' : 'API' }}
                </UBadge>
                <span class="text-sm font-medium">{{ result.message }}</span>
              </div>
              <div class="flex items-center gap-2 text-xs text-gray-500">
                <span>{{ formatDuration(result.duration) }}</span>
                <span>{{ formatDate(result.timestamp) }}</span>
              </div>
            </div>
            
            <div v-if="result.data" class="mt-2">
              <details class="text-xs">
                <summary class="cursor-pointer text-gray-600">ดูข้อมูลตอบกลับ</summary>
                <pre class="mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-32">{{ JSON.stringify(result.data, null, 2) }}</pre>
              </details>
            </div>
            
            <div v-if="result.error" class="mt-2">
              <details class="text-xs">
                <summary class="cursor-pointer text-red-600">ดูข้อผิดพลาด</summary>
                <pre class="mt-2 bg-red-100 p-2 rounded overflow-auto max-h-32">{{ JSON.stringify(result.error, null, 2) }}</pre>
              </details>
            </div>
          </div>
        </div>
      </UCard>

      <!-- Navigation -->
      <div class="flex justify-center space-x-4">
        <UButton to="/dashboard" size="lg" icon="i-heroicons-home">
          Dashboard
        </UButton>
        <UButton to="/dashboard/profile" variant="outline" size="lg" icon="i-heroicons-user">
          Profile
        </UButton>
        <UButton @click="authStore.doLogout()" variant="outline" size="lg" icon="i-heroicons-arrow-right-on-rectangle" color="red">
          Logout
        </UButton>
      </div>
    </div>
  </UContainer>
</template>