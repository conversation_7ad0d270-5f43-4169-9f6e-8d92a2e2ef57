<script setup lang="ts">
import type { FormSubmitEvent } from '@nuxt/ui';
import { z } from 'zod';
import { useAuthStore } from '~/stores/auth';
import { emailSchema, passwordSchema } from '~/utils/validationSchemas';
import type { ApiResponse } from '../../shared/types/respone';

const { t } = useI18n();

// ใช้ SEO meta สำหรับหน้า signin
useAuthSeo(true);

// Page Meta
definePageMeta({
  layout: 'empty',
  middleware: ['guest'],
});

// Router & Store
const authStore = useAuthStore();

// State
const isLoading = ref(false);
const rememberMe = ref(false);
const showPassword = ref(false);

// Form Schema (ใช้ Zod โดยตรง)
const schema = z.object({
  email: emailSchema,
  password: passwordSchema,
});

// Form State (ใช้ Reactive)
type Schema = z.output<typeof schema>;
const state = reactive<Partial<Schema>>({
  email: '',
  password: '',
});

// Lifecycle Hooks
onMounted(() => {
  if (import.meta.client) {
    // โหลดค่า rememberMe จาก localStorage
    const savedRememberMe = localStorage.getItem('rememberMe');
    const savedEmail = localStorage.getItem('rememberedEmail');

    if (savedRememberMe === 'true') {
      rememberMe.value = true;

      // โหลด email ที่บันทึกไว้
      if (savedEmail) {
        state.email = savedEmail;
      }
    }
  }
});

// Watchers
watch(rememberMe, newValue => {
  if (!newValue && import.meta.client) {
    // ถ้าไม่ได้เลือกจดจำ ให้ลบข้อมูลเก่าออก
    localStorage.removeItem('rememberMe');
    localStorage.removeItem('rememberedEmail');
  }
});

// Computed property เพื่อตรวจสอบความถูกต้องของฟอร์มด้วย Zod
const isFormValid = computed(() => {
  const result = schema.safeParse(state);
  return result.success;
});

// Methods
async function onSubmit(event: FormSubmitEvent<Schema>) {
  try {
    isLoading.value = true;

    const response = await $fetch<
      ApiResponse<{
        user: User;
      }>
    >('/api/v1/auth/signin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: {
        email: event.data.email,
        password: event.data.password,
      },
    });

    if (!response.success || !response.data) {
      throw new Error(response.message || 'เกิดข้อผิดพลาดในการเข้าสู่ระบบ');
    }

    const { user } = response.data;
    console.log('user', user);

    if (!user) {
      throw new Error('Invalid response data');
    }

    // อัปเดต store (ไม่ต้องจัดการ token เพราะเก็บใน session แล้ว)
    authStore.setUser(user as any);

    // บันทึก rememberMe preference และ email
    if (import.meta.client) {
      if (rememberMe.value) {
        localStorage.setItem('rememberMe', 'true');
        localStorage.setItem('rememberedEmail', event.data.email);
      } else {
        // ถ้าไม่ได้เลือกจดจำ ให้ลบข้อมูลเก่าออก
        localStorage.removeItem('rememberMe');
        localStorage.removeItem('rememberedEmail');
      }
    }

    // ส่งข้อความผ่าน Broadcast Channel
    await authStore.handleLoginSuccess(user as any);

    // แสดง toast ก่อน navigate
    useToast().add({
      title: 'เข้าสู่ระบบสำเร็จ',
      description: 'ยินดีต้อนรับกลับมา!',
      icon: 'i-heroicons-check-circle',
      color: 'success',
      duration: 1500,
    });

    // Navigate หลังจาก toast
    await navigateTo('/dashboard', {
      replace: true,
    });
  } catch (err: any) {
    console.dir(err);

    // error.response มีข้อมูล response จาก server
    const statusCode = err.response?.status;
    const responseBody = err.response?._data;

    useToast().add({
      title: responseBody?.statusMessage || 'ล้มเหลว',
      description:
        responseBody?.message ||
        responseBody?.statusMessage ||
        `เกิดข้อผิดพลาดในการเข้าสู่ระบบ${statusCode ? ` (${statusCode})` : ''}`,
      icon: 'i-heroicons-exclamation-triangle',
      color: 'error',
      duration: 3000,
    });
  } finally {
    isLoading.value = false;
  }
}
</script>

<template>
  <UContainer class="max-w-lg">
    <UForm :schema="schema" :state="state" class="space-y-5" @submit="onSubmit">
      <div class="text-center space-y-2">
        <h2 class="text-xl sm:text-2xl font-bold tracking-tight">{{ $t('auth.signin.label') }}</h2>
        <p class="max-sm:text-sm">
          กรอกอีเมลของคุณและรหัสผ่านของคุณเพื่อเข้าสู่ระบบ
        </p>
      </div>

      <UFormField size="xl" :label="$t('form.email')" name="email" required>

        <UInput icon="solar:letter-line-duotone" placeholder="<EMAIL>" autocomplete="email" id="email"
          v-model="state.email" name="email" type="email" class="w-full" />
      </UFormField>

      <UFormField size="xl" :label="$t('form.password')" name="password" required>

        <UInput icon="solar:password-minimalistic-input-line-duotone" id="password" v-model="state.password"
          name="password" :type="showPassword ? 'text' : 'password'" autocomplete="current-password"
          placeholder="••••••••" class="w-full" :ui="{ trailing: 'pe-1' }">
          <template #trailing>
            <UButton color="neutral" variant="ghost"
              :icon="showPassword ? 'solar:eye-closed-line-duotone' : 'solar:eye-line-duotone'"
              :aria-label="showPassword ? 'ซ่อนรหัสผ่าน' : 'แสดงรหัสผ่าน'" :aria-pressed="showPassword"
              @click="showPassword = !showPassword" />
          </template>
        </UInput>
      </UFormField>

      <div class="flex items-center justify-between">
        <UCheckbox size="xl" v-model="rememberMe" name="remember-me" label="จดจำฉัน" />

        <div class="text-sm">
          <NuxtLink to="/forgot-password" class="font-medium text-primary-600 hover:text-primary-500">
            ลืมรหัสผ่าน?
          </NuxtLink>
        </div>
      </div>

      <!-- UForm จะจัดการ disabled state จาก validation status เมื่อใช้ :schema -->
      <UButton size="xl" type="submit" icon="solar:key-minimalistic-square-2-line-duotone" color="primary" block
        :loading="isLoading" :disabled="!isFormValid">
        {{ $t('auth.signin.label') }}
      </UButton>
    </UForm>
    <!-- Navigation Buttons -->
    <div class="flex flex-row gap-3 p-1">
      <UButton block to="/signup" :label="$t('auth.signup.label')" color="info" variant="soft" />
      <UButton block to="/" :label="$t('main')" color="warning" variant="soft" />
    </div>
  </UContainer>
</template>