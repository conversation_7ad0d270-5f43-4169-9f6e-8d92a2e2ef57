<script setup lang="ts">
definePageMeta({
  layout: 'default',
  middleware: ['guest'],
});

const { t } = useI18n();

// ใช้ SEO meta สำหรับหน้า help
useHelpSeo();

// Help sections
const helpSections = ref([
  {
    id: 'getting-started',
    title: 'เริ่มต้นใช้งาน',
    icon: 'i-heroicons-rocket-launch',
    color: 'blue',
    items: [
      {
        title: 'การสร้างเว็บไซต์ใหม่',
        description: 'เรียนรู้วิธีการสร้างเว็บไซต์ใหม่และตั้งค่าพื้นฐาน',
        steps: [
          'คลิกปุ่ม "สร้างเว็บไซต์ใหม่" ในหน้า Dashboard',
          'กรอกข้อมูลพื้นฐานของเว็บไซต์ เช่น ชื่อเว็บไซต์ และโดเมน',
          'เลือกธีมและเทมเพลตที่ต้องการ',
          'คลิก "สร้างเว็บไซต์" เพื่อเริ่มต้นใช้งาน',
        ],
      },
      {
        title: 'การเข้าสู่ระบบ',
        description: 'วิธีการเข้าสู่ระบบและจัดการบัญชีผู้ใช้',
        steps: [
          'ไปที่หน้า "เข้าสู่ระบบ"',
          'กรอกอีเมลและรหัสผ่าน',
          'คลิก "เข้าสู่ระบบ"',
          'หากลืมรหัสผ่าน สามารถใช้ฟีเจอร์ "ลืมรหัสผ่าน" ได้',
        ],
      },
      {
        title: 'การตั้งค่าโปรไฟล์',
        description: 'วิธีการแก้ไขข้อมูลส่วนตัวและการตั้งค่าบัญชี',
        steps: [
          'ไปที่หน้า "โปรไฟล์" ในเมนูผู้ใช้',
          'แก้ไขข้อมูลส่วนตัว เช่น ชื่อ อีเมล และรูปโปรไฟล์',
          'เปลี่ยนรหัสผ่านหากต้องการ',
          'คลิก "บันทึก" เพื่อบันทึกการเปลี่ยนแปลง',
        ],
      },
    ],
  },
  {
    id: 'website-management',
    title: 'การจัดการเว็บไซต์',
    icon: 'i-heroicons-globe-alt',
    color: 'green',
    items: [
      {
        title: 'การแก้ไขข้อมูลเว็บไซต์',
        description: 'วิธีการแก้ไขข้อมูลพื้นฐานของเว็บไซต์',
        steps: [
          'ไปที่หน้า "ตั้งค่า" ของเว็บไซต์',
          'แก้ไขข้อมูลต่างๆ เช่น ชื่อเว็บไซต์ คำอธิบาย และโลโก้',
          'ตั้งค่าสถานะเว็บไซต์ (ออนไลน์/ออฟไลน์)',
          'คลิก "บันทึก" เพื่อบันทึกการเปลี่ยนแปลง',
        ],
      },
      {
        title: 'การจัดการโดเมน',
        description: 'วิธีการเชื่อมต่อโดเมนกับเว็บไซต์',
        steps: [
          'ไปที่หน้า "ตั้งค่า" > "โดเมน"',
          'เพิ่มโดเมนที่ต้องการใช้',
          'ตั้งค่า DNS records ตามคำแนะนำ',
          'รอการยืนยันโดเมน (อาจใช้เวลาสูงสุด 24 ชั่วโมง)',
        ],
      },
      {
        title: 'การตั้งค่า SEO',
        description: 'วิธีการปรับแต่ง SEO เพื่อเพิ่มการเข้าชม',
        steps: [
          'ไปที่หน้า "SEO" ในเมนูจัดการ',
          'ตั้งค่า Meta title และ Meta description',
          'เพิ่ม Keywords ที่เกี่ยวข้อง',
          'ตั้งค่า Open Graph สำหรับโซเชียลมีเดีย',
        ],
      },
    ],
  },
  {
    id: 'product-management',
    title: 'การจัดการสินค้า',
    icon: 'i-heroicons-shopping-bag',
    color: 'purple',
    items: [
      {
        title: 'การเพิ่มสินค้าใหม่',
        description: 'วิธีการเพิ่มสินค้าใหม่ลงในร้านค้า',
        steps: [
          'ไปที่หน้า "สินค้า" > "เพิ่มสินค้า"',
          'กรอกข้อมูลสินค้า เช่น ชื่อ ราคา และคำอธิบาย',
          'อัปโหลดรูปภาพสินค้า',
          'ตั้งค่าสถานะสินค้า (เปิด/ปิดการขาย)',
          'คลิก "บันทึก" เพื่อเพิ่มสินค้า',
        ],
      },
      {
        title: 'การจัดการหมวดหมู่สินค้า',
        description: 'วิธีการจัดหมวดหมู่สินค้าเพื่อให้ลูกค้าค้นหาได้ง่าย',
        steps: [
          'ไปที่หน้า "สินค้า" > "หมวดหมู่"',
          'สร้างหมวดหมู่ใหม่หรือแก้ไขหมวดหมู่ที่มีอยู่',
          'กำหนดสินค้าให้อยู่ในหมวดหมู่ที่เหมาะสม',
          'ตั้งค่าลำดับการแสดงผลของหมวดหมู่',
        ],
      },
      {
        title: 'การจัดการสต็อกสินค้า',
        description: 'วิธีการติดตามและจัดการสต็อกสินค้า',
        steps: [
          'ไปที่หน้า "สินค้า" > "สต็อก"',
          'ดูรายการสินค้าที่มีสต็อกต่ำ',
          'อัปเดตจำนวนสต็อกตามการขาย',
          'ตั้งค่าการแจ้งเตือนเมื่อสต็อกต่ำ',
        ],
      },
    ],
  },
  {
    id: 'order-management',
    title: 'การจัดการออเดอร์',
    icon: 'i-heroicons-shopping-cart',
    color: 'orange',
    items: [
      {
        title: 'การดูรายการออเดอร์',
        description: 'วิธีการดูและจัดการออเดอร์จากลูกค้า',
        steps: [
          'ไปที่หน้า "ออเดอร์" ในเมนูจัดการ',
          'ดูรายการออเดอร์ทั้งหมดเรียงตามวันที่',
          'กรองออเดอร์ตามสถานะ เช่น รอดำเนินการ ชำระแล้ว',
          'ค้นหาออเดอร์ตามหมายเลขออเดอร์หรือชื่อลูกค้า',
        ],
      },
      {
        title: 'การอัปเดตสถานะออเดอร์',
        description: 'วิธีการอัปเดตสถานะการจัดส่งและชำระเงิน',
        steps: [
          'คลิกที่ออเดอร์ที่ต้องการอัปเดต',
          'เปลี่ยนสถานะออเดอร์ เช่น "กำลังจัดส่ง" "จัดส่งแล้ว"',
          'เพิ่มหมายเลขพัสดุหากมีการจัดส่ง',
          'บันทึกการเปลี่ยนแปลง',
        ],
      },
      {
        title: 'การจัดการการคืนสินค้า',
        description: 'วิธีการจัดการการคืนสินค้าและการชดเชย',
        steps: [
          'ดูคำขอคืนสินค้าในหน้า "ออเดอร์" > "การคืนสินค้า"',
          'ตรวจสอบเหตุผลการคืนสินค้า',
          'อนุมัติหรือปฏิเสธคำขอคืนสินค้า',
          'ดำเนินการคืนเงินหากอนุมัติ',
        ],
      },
    ],
  },
  {
    id: 'content-management',
    title: 'การจัดการเนื้อหา',
    icon: 'i-heroicons-document-text',
    color: 'indigo',
    items: [
      {
        title: 'การเขียนบทความ',
        description: 'วิธีการเขียนและเผยแพร่บทความในบล็อก',
        steps: [
          'ไปที่หน้า "บล็อก" > "เขียนบทความ"',
          'กรอกหัวข้อและเนื้อหาบทความ',
          'เพิ่มรูปภาพและจัดรูปแบบข้อความ',
          'ตั้งค่า SEO และคำสำคัญ',
          'เผยแพร่หรือบันทึกเป็นฉบับร่าง',
        ],
      },
      {
        title: 'การจัดการหน้าเว็บ',
        description: 'วิธีการสร้างและแก้ไขหน้าต่างๆ ของเว็บไซต์',
        steps: [
          'ไปที่หน้า "หน้าเว็บ" ในเมนูจัดการ',
          'สร้างหน้าใหม่หรือแก้ไขหน้าที่มีอยู่',
          'ใช้เครื่องมือแก้ไขข้อความและรูปภาพ',
          'ตั้งค่าการแสดงผลและเมนูนำทาง',
        ],
      },
      {
        title: 'การจัดการเมนู',
        description: 'วิธีการจัดระเบียบเมนูนำทางของเว็บไซต์',
        steps: [
          'ไปที่หน้า "เมนู" ในเมนูจัดการ',
          'สร้างเมนูใหม่หรือแก้ไขเมนูที่มีอยู่',
          'เพิ่มลิงก์ไปยังหน้าต่างๆ',
          'จัดลำดับการแสดงผลของเมนู',
        ],
      },
    ],
  },
  {
    id: 'team-management',
    title: 'การจัดการทีม',
    icon: 'i-heroicons-users',
    color: 'teal',
    items: [
      {
        title: 'การเชิญสมาชิกทีม',
        description: 'วิธีการเชิญสมาชิกใหม่เข้าร่วมทีม',
        steps: [
          'ไปที่หน้า "ทีม" > "เชิญสมาชิก"',
          'กรอกอีเมลของสมาชิกที่ต้องการเชิญ',
          'เลือกบทบาทและสิทธิ์การเข้าถึง',
          'ส่งคำเชิญทางอีเมล',
        ],
      },
      {
        title: 'การจัดการสิทธิ์',
        description: 'วิธีการกำหนดสิทธิ์การเข้าถึงสำหรับสมาชิกทีม',
        steps: [
          'ไปที่หน้า "ทีม" > "สิทธิ์"',
          'สร้างบทบาทใหม่หรือแก้ไขบทบาทที่มีอยู่',
          'กำหนดสิทธิ์การเข้าถึงแต่ละฟีเจอร์',
          'มอบหมายบทบาทให้สมาชิกทีม',
        ],
      },
      {
        title: 'การติดตามกิจกรรม',
        description: 'วิธีการดูกิจกรรมและการเปลี่ยนแปลงในระบบ',
        steps: [
          'ไปที่หน้า "ทีม" > "กิจกรรม"',
          'ดูรายการกิจกรรมทั้งหมดเรียงตามเวลา',
          'กรองกิจกรรมตามสมาชิกหรือประเภท',
          'ดูรายละเอียดการเปลี่ยนแปลงแต่ละครั้ง',
        ],
      },
    ],
  },
  {
    id: 'analytics',
    title: 'การวิเคราะห์ข้อมูล',
    icon: 'i-heroicons-chart-bar',
    color: 'pink',
    items: [
      {
        title: 'การดูสถิติการเข้าชม',
        description: 'วิธีการดูสถิติการเข้าชมและพฤติกรรมผู้ใช้',
        steps: [
          'ไปที่หน้า "การวิเคราะห์" ในเมนูจัดการ',
          'ดูสถิติการเข้าชมรายวัน สัปดาห์ และเดือน',
          'วิเคราะห์แหล่งที่มาของผู้เข้าชม',
          'ดูพฤติกรรมการใช้งานของลูกค้า',
        ],
      },
      {
        title: 'การดูรายงานการขาย',
        description: 'วิธีการดูรายงานการขายและรายได้',
        steps: [
          'ไปที่หน้า "รายงาน" > "การขาย"',
          'ดูยอดขายรายวัน สัปดาห์ และเดือน',
          'วิเคราะห์สินค้าขายดีและสินค้าขายไม่ดี',
          'ดูรายงานลูกค้าและการซื้อซ้ำ',
        ],
      },
      {
        title: 'การตั้งค่าการติดตาม',
        description: 'วิธีการตั้งค่าการติดตามและ Google Analytics',
        steps: [
          'ไปที่หน้า "การวิเคราะห์" > "ตั้งค่า"',
          'เพิ่ม Google Analytics Tracking ID',
          'ตั้งค่าการติดตามอีเวนต์ต่างๆ',
          'เชื่อมต่อกับ Google Search Console',
        ],
      },
    ],
  },
  {
    id: 'settings',
    title: 'การตั้งค่าต่างๆ',
    icon: 'i-heroicons-cog-6-tooth',
    color: 'gray',
    items: [
      {
        title: 'การตั้งค่าการชำระเงิน',
        description: 'วิธีการตั้งค่าช่องทางการชำระเงิน',
        steps: [
          'ไปที่หน้า "ตั้งค่า" > "การชำระเงิน"',
          'เพิ่มบัญชีธนาคารหรือบัญชี PayPal',
          'ตั้งค่าค่าธรรมเนียมการชำระเงิน',
          'ทดสอบการชำระเงิน',
        ],
      },
      {
        title: 'การตั้งค่าการจัดส่ง',
        description: 'วิธีการตั้งค่าค่าจัดส่งและวิธีการจัดส่ง',
        steps: [
          'ไปที่หน้า "ตั้งค่า" > "การจัดส่ง"',
          'กำหนดค่าจัดส่งตามน้ำหนักหรือระยะทาง',
          'ตั้งค่าวิธีการจัดส่งที่รองรับ',
          'กำหนดพื้นที่จัดส่งฟรี',
        ],
      },
      {
        title: 'การตั้งค่าการแจ้งเตือน',
        description: 'วิธีการตั้งค่าการแจ้งเตือนอีเมลและ SMS',
        steps: [
          'ไปที่หน้า "ตั้งค่า" > "การแจ้งเตือน"',
          'เปิด/ปิดการแจ้งเตือนอีเมล',
          'ตั้งค่าการแจ้งเตือน SMS (หากมี)',
          'ทดสอบการส่งการแจ้งเตือน',
        ],
      },
    ],
  },
]);

// Search functionality
const searchQuery = ref('');
const filteredSections = computed(() => {
  if (!searchQuery.value) return helpSections.value;

  return helpSections.value
    .map(section => ({
      ...section,
      items: section.items.filter(
        item =>
          item.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
          item.description.toLowerCase().includes(searchQuery.value.toLowerCase())
      ),
    }))
    .filter(section => section.items.length > 0);
});

// FAQ
const faqs = ref([
  {
    question: 'ฉันสามารถสร้างเว็บไซต์ได้กี่เว็บไซต์?',
    answer:
      'ขึ้นอยู่กับแพ็กเกจที่คุณเลือก โดยแพ็กเกจพื้นฐานสามารถสร้างได้ 1 เว็บไซต์ แพ็กเกจมาตรฐาน 3 เว็บไซต์ และแพ็กเกจพรีเมียมไม่จำกัดจำนวนเว็บไซต์',
  },
  {
    question: 'ฉันสามารถเปลี่ยนธีมได้หรือไม่?',
    answer: 'ได้ครับ คุณสามารถเปลี่ยนธีมได้ตลอดเวลาโดยไปที่หน้า "ธีม" ในเมนูจัดการ และเลือกธีมที่ต้องการ',
  },
  {
    question: 'ระบบรองรับการชำระเงินแบบใดบ้าง?',
    answer: 'ระบบรองรับการชำระเงินผ่านบัตรเครดิต/เดบิต PayPal และการโอนเงินผ่านธนาคาร',
  },
  {
    question: 'ฉันสามารถเพิ่มสมาชิกทีมได้กี่คน?',
    answer:
      'จำนวนสมาชิกทีมขึ้นอยู่กับแพ็กเกจ โดยแพ็กเกจพื้นฐาน 2 คน แพ็กเกจมาตรฐาน 5 คน และแพ็กเกจพรีเมียมไม่จำกัดจำนวน',
  },
  {
    question: 'ระบบมีฟีเจอร์ SEO หรือไม่?',
    answer:
      'มีครับ ระบบมีฟีเจอร์ SEO ครบถ้วน รวมถึงการตั้งค่า Meta tags, Sitemap, และการเชื่อมต่อกับ Google Search Console',
  },
  {
    question: 'ฉันสามารถย้ายเว็บไซต์ไปยังโดเมนอื่นได้หรือไม่?',
    answer: 'ได้ครับ คุณสามารถเปลี่ยนโดเมนได้ตลอดเวลาโดยไปที่หน้า "ตั้งค่า" > "โดเมน" และเพิ่มโดเมนใหม่',
  },
]);

// Contact support
const contactInfo = ref({
  email: '<EMAIL>',
  phone: '************',
  line: '@asphum',
  workingHours: 'จันทร์-ศุกร์ 9:00-18:00 น.',
});
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <NuxtLink to="/" class="flex items-center gap-2 text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              <Icon name="i-heroicons-arrow-left" class="w-5 h-5" />
              <span class="font-medium">กลับหน้าหลัก</span>
            </NuxtLink>
          </div> 
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <!-- Hero Section -->
      <div class="text-center mb-16">
        <div class="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-full text-sm font-medium mb-6">
          <Icon name="i-heroicons-question-mark-circle" class="w-4 h-4" />
          คู่มือการใช้งาน
        </div>
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
          วิธีใช้งานระบบเว็บไซต์
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto mb-8">
          คู่มือการใช้งานระบบเว็บไซต์แบบครบถ้วน ตั้งแต่การสร้างเว็บไซต์ การจัดการสินค้า ไปจนถึงการวิเคราะห์ข้อมูล
        </p>
        
        <!-- Search -->
        <div class="max-w-2xl mx-auto">
          <div class="relative">
            <Icon name="i-heroicons-magnifying-glass" class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <UInput
              v-model="searchQuery"
              placeholder="ค้นหาวิธีการใช้งาน..."
              class="pl-12 pr-4 py-3 text-lg"
              size="lg"
            />
          </div>
        </div>
      </div>

      <!-- Quick Navigation -->
      <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 mb-16">
        <NuxtLink
          v-for="section in helpSections"
          :key="section.id"
          :to="`#${section.id}`"
          class="group p-4 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 hover:shadow-lg transition-all duration-200 text-center"
        >
          <div class="p-3 bg-gradient-to-br rounded-lg text-white shadow-lg mb-3 mx-auto w-fit"
               :class="`from-${section.color}-500 to-${section.color}-600`">
            <Icon :name="section.icon" class="w-6 h-6" />
          </div>
          <h3 class="font-semibold text-gray-900 dark:text-white text-sm group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
            {{ section.title }}
          </h3>
        </NuxtLink>
      </div>

      <!-- Help Sections -->
      <div class="space-y-16">
        <div
          v-for="section in filteredSections"
          :key="section.id"
          :id="section.id"
          class="scroll-mt-20"
        >
          <div class="flex items-center gap-4 mb-8">
            <div class="p-3 bg-gradient-to-br rounded-xl text-white shadow-lg"
                 :class="`from-${section.color}-500 to-${section.color}-600`">
              <Icon :name="section.icon" class="w-8 h-8" />
            </div>
            <div>
              <h2 class="text-3xl font-bold text-gray-900 dark:text-white">{{ section.title }}</h2>
              <p class="text-gray-600 dark:text-gray-400 mt-2">เรียนรู้วิธีการใช้งานฟีเจอร์ต่างๆ</p>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <UCard
              v-for="item in section.items"
              :key="item.title"
              class="group hover:shadow-xl transition-all duration-300 hover:scale-105"
            >
              <template #header>
                <div class="flex items-center gap-3">
                  <div class="p-2 bg-gradient-to-br rounded-lg text-white shadow-lg"
                       :class="`from-${section.color}-500 to-${section.color}-600`">
                    <Icon name="i-heroicons-check-circle" class="w-5 h-5" />
                  </div>
                  <h3 class="text-xl font-semibold text-gray-900 dark:text-white">{{ item.title }}</h3>
                </div>
              </template>
              
              <div class="space-y-4">
                <p class="text-gray-600 dark:text-gray-400">{{ item.description }}</p>
                
                <div class="space-y-3">
                  <h4 class="font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                    <Icon name="i-heroicons-list-bullet" class="w-4 h-4 text-blue-600" />
                    ขั้นตอนการใช้งาน:
                  </h4>
                  <ol class="space-y-2">
                    <li
                      v-for="(step, index) in item.steps"
                      :key="index"
                      class="flex items-start gap-3 text-sm text-gray-700 dark:text-gray-300"
                    >
                      <span class="flex-shrink-0 w-6 h-6 bg-gradient-to-br rounded-full text-white text-xs font-bold flex items-center justify-center"
                            :class="`from-${section.color}-500 to-${section.color}-600`">
                        {{ index + 1 }}
                      </span>
                      <span>{{ step }}</span>
                    </li>
                  </ol>
                </div>
              </div>
            </UCard>
          </div>
        </div>
      </div>

      <!-- FAQ Section -->
      <div class="mt-20">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">คำถามที่พบบ่อย</h2>
          <p class="text-gray-600 dark:text-gray-400">คำตอบสำหรับคำถามที่พบบ่อยเกี่ยวกับระบบเว็บไซต์</p>
        </div>

        <div class="max-w-4xl mx-auto">
          <UAccordion :items="faqs.map(faq => ({
            label: faq.question,
            content: faq.answer,
            icon: 'i-heroicons-question-mark-circle'
          }))" />
        </div>
      </div>

      <!-- Contact Support -->
      <div class="mt-20 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-2xl p-8 text-white">
        <div class="text-center mb-8">
          <div class="inline-flex items-center gap-2 px-4 py-2 bg-white/20 rounded-full text-sm font-medium mb-4">
            <Icon name="i-heroicons-chat-bubble-left-right" class="w-4 h-4" />
            ต้องการความช่วยเหลือเพิ่มเติม?
          </div>
          <h2 class="text-3xl font-bold mb-4">ติดต่อทีมสนับสนุน</h2>
          <p class="text-blue-100 text-lg">เราพร้อมช่วยเหลือคุณตลอด 24 ชั่วโมง</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center p-6 bg-white/10 rounded-xl backdrop-blur-sm">
            <Icon name="i-heroicons-envelope" class="w-8 h-8 mx-auto mb-4" />
            <h3 class="font-semibold mb-2">อีเมล</h3>
            <p class="text-blue-100">{{ contactInfo.email }}</p>
          </div>
          <div class="text-center p-6 bg-white/10 rounded-xl backdrop-blur-sm">
            <Icon name="i-heroicons-phone" class="w-8 h-8 mx-auto mb-4" />
            <h3 class="font-semibold mb-2">โทรศัพท์</h3>
            <p class="text-blue-100">{{ contactInfo.phone }}</p>
          </div>
          <div class="text-center p-6 bg-white/10 rounded-xl backdrop-blur-sm">
            <Icon name="i-heroicons-clock" class="w-8 h-8 mx-auto mb-4" />
            <h3 class="font-semibold mb-2">เวลาทำการ</h3>
            <p class="text-blue-100">{{ contactInfo.workingHours }}</p>
          </div>
        </div>

        <div class="text-center mt-8">
          <UButton
            :to="`mailto:${contactInfo.email}?subject=ขอความช่วยเหลือ - ระบบเว็บไซต์`"
            color="neutral"
            variant="outline"
            size="lg"
            class="hover:bg-white hover:text-blue-600"
          >
            <Icon name="i-heroicons-envelope" class="w-5 h-5 mr-2" />
            ส่งอีเมลหาเรา
          </UButton>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Gradient animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Hover effects */
.group:hover {
  transform: translateY(-2px);
}

/* Card animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-16 > * {
  animation: fadeInUp 0.6s ease-out forwards;
}
</style> 