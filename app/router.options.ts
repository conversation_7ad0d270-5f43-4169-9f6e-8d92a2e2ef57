import type { RouterConfig } from '@nuxt/schema';

// https://router.vuejs.org/api/interfaces/routeroptions.html
export default (<RouterConfig>{
  routes: _routes => {
    return _routes;
  },
  scrollBehavior(to, from, savedPosition) {
    // ถ้ามีตำแหน่งที่บันทึกไว้ (เช่น กดปุ่ม back) ให้เลื่อนไปยังตำแหน่งนั้น
    if (savedPosition) {
      return savedPosition;
    }

    // ถ้ามีการระบุ hash ใน URL (เช่น #section1) ให้เลื่อนไปยัง element นั้น
    if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth',
        top: 30, // offset จากด้านบน (เผื่อมี header)
      };
    }

    // ถ้าเปลี่ยนหน้า ให้เลื่อนไปด้านบนสุด
    if (from.path !== to.path) {
      return { top: 0, behavior: 'smooth' };
    }

    // ไม่ต้องเลื่อนหน้าจอ
    return {};
  },
});
