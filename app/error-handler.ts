export default function (error: any, event: any) {
  // Handle EPIPE and connection errors
  if (error.code === 'EPIPE' || error.code === 'ECONNRESET' || error.code === 'ENOTFOUND') {
    console.log('Connection error handled:', error.code);
    return;
  }

  // Handle other errors
  console.error('Server error:', error);
  
  // Don't expose internal errors to client
  if (event.node.res.writableEnded) {
    return;
  }

  return createError({
    statusCode: 500,
    statusMessage: 'Internal Server Error',
    message: 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง',
  });
} 