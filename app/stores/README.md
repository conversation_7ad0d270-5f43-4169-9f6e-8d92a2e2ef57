# Stores

## ภาพรวม
โปรเจคนี้ใช้ **Composables** แทน Pinia stores สำหรับการจัดการข้อมูล เพื่อความเรียบง่ายและ performance ที่ดีกว่า

## การจัดการข้อมูลเว็บไซต์

ใช้ `useSites()` composable สำหรับการจัดการข้อมูลเว็บไซต์:

### การใช้งานพื้นฐาน

```vue
<script setup>
// Import composable
const { getSite, updateSite, getSites } = useSites();

// ดึงข้อมูลเว็บไซต์เดี่ยว
const { data: siteData } = await useLazyAsyncData(
  `site-${siteId}`,
  () => getSite(siteId)
);

const currentSite = computed(() => siteData.value?.data);

// ดึงรายการเว็บไซต์
const { data: sitesData } = await useLazyAsyncData(
  'sites-list',
  () => getSites({ page: 1, limit: 20 })
);

const sites = computed(() => sitesData.value?.data?.sites || []);
</script>

<template>
  <div v-if="currentSite">
    <h1>{{ currentSite.name }}</h1>
    <p v-if="currentSite.isPremium">เว็บไซต์ Premium</p>
    <p v-if="currentSite.isActive">เปิดใช้งานอยู่</p>
  </div>
</template>
```

### ฟังก์ชันที่มีใน useSites()

- `createSite(siteData)` - สร้างเว็บไซต์ใหม่
- `getSites(params)` - ดึงรายการเว็บไซต์
- `getSite(siteId)` - ดึงข้อมูลเว็บไซต์เดี่ยว
- `updateSite(siteId, siteData)` - อัปเดตเว็บไซต์
- `deleteSite(siteId)` - ลบเว็บไซต์
- `toggleSiteStatus(siteId, isActive)` - เปิด/ปิดเว็บไซต์
- `updateSEOSettings(siteId, seoData)` - อัปเดต SEO
- `updateSettings(siteId, settingsData)` - อัปเดตการตั้งค่า
- `getAnalytics(siteId, period)` - ดึงข้อมูลวิเคราะห์

### ตัวอย่างการอัปเดตข้อมูล

```vue
<script setup>
const { updateSite, updateSEOSettings } = useSites();

// อัปเดตข้อมูลพื้นฐาน
const saveSiteInfo = async () => {
  await updateSite(siteId.value, {
    name: 'ชื่อใหม่',
    description: 'คำอธิบายใหม่'
  });
  
  // Refresh ข้อมูล
  await refresh();
};

// อัปเดต SEO
const saveSEO = async () => {
  await updateSEOSettings(siteId.value, {
    title: 'หัวข้อ SEO',
    description: 'คำอธิบาย SEO'
  });
  
  await refresh();
};
</script>
```

## ข้อดีของ Composables แทน Store

1. **เรียบง่าย** - ไม่ต้อง manage global state
2. **Performance** - ไม่มี reactive overhead
3. **Auto-caching** - `useLazyAsyncData` จัดการ caching ให้
4. **SSR-friendly** - ทำงานได้ดีกับ server-side rendering
5. **Type-safe** - TypeScript support เต็มรูปแบบ

## Migration จาก Store

หากมีโค้ดเก่าที่ใช้ store สามารถแปลงได้ดังนี้:

### Before (Store)
```typescript
const { loadCurrentSiteFromRoute, quickUpdate } = useSiteStoreComposable();

onMounted(async () => {
  await loadCurrentSiteFromRoute();
});

await quickUpdate({ name: 'ชื่อใหม่' });
```

### After (Composable)
```typescript
const { getSite, updateSite } = useSites();

const { data: siteData, refresh } = await useLazyAsyncData(
  `site-${siteId}`,
  () => getSite(siteId)
);

await updateSite(siteId, { name: 'ชื่อใหม่' });
await refresh();
```