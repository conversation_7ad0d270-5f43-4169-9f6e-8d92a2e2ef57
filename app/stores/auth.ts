import { defineStore } from 'pinia';

// สร้าง Broadcast Channel สำหรับการสื่อสารระหว่างแท็บ (เฉพาะ Client-side)
const authChannel = import.meta.client ? new BroadcastChannel('auth_channel') : null;

// Define User type ให้ตรงกับ Backend Model
type User = {
  _id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: 'user' | 'admin' | 'moderator';
  avatar?: string;
  cover?: string;
  isEmailVerified: boolean;
  moneyPoint: number;
  goldPoint: number;
  createdAt: Date;
  updatedAt: Date;
};

export const useAuthStore = defineStore(
  'auth',
  () => {
    // state
    const isLogged = ref(false);
    const userDetail = ref<User | null>(null);
    const isLoading = ref(false);
    const error = ref<string | null>(null);

    // getters
    const isAuthenticated = computed(() => isLogged.value);
    const getUser = computed(() => userDetail.value);

    // ฟังก์ชันโหลดข้อมูลผู้ใช้
    async function loadUserDetail() {
      try {
        const { getCurrentUser } = useSession();

        const data = await getCurrentUser();
        const user = data?.user;
        console.log('getCurrentUser', user);

        if (user) {
          isLogged.value = true;
          userDetail.value = user;
          return user;
        }

        await clearAuth();
        return null;
      } catch (error: any) {
        console.error('Load user detail error:', error);

        // ถ้าเป็น 401 error หรือ authentication error ให้ clear auth
        if (error?.statusCode === 401 || error?.status === 401 ||
          error?.data?.shouldRedirect || error?.message?.includes('Authentication failed')) {
          console.log('Authentication error detected, clearing auth');
          await clearAuth();
        }

        return null;
      }
    }

    // ฟังก์ชัน logout
    async function doLogout() {
      // const router = useRouter();

      try {
        if (import.meta.client) {
          const { logout } = useSession();
          await logout();
          console.log('ออกจากระบบสำเร็จ');
        }
      } catch (error) {
        console.error('Logout error:', error);
        // ดำเนินการ logout ต่อไปแม้ว่า API call จะล้มเหลว
      } finally {
        // ล้าง state ทั้งหมดใน Store
        isLogged.value = false;
        userDetail.value = null;
        isLoading.value = false;
        error.value = null;

        if (import.meta.client) {
          // ล้าง localStorage (เก็บไว้เพื่อ backward compatibility)
          // localStorage.removeItem('token');
          // localStorage.removeItem('user');
          // localStorage.removeItem('refreshToken');
          localStorage.removeItem('auth'); // Pinia Persist key

          // ส่งข้อความผ่าน Broadcast Channel เพื่อแจ้งแท็บอื่นว่ามีการล็อกเอาต์
          authChannel?.postMessage({ type: 'loggedOut' });
        }

        // if (router.currentRoute.value.path !== '/signin') {
        //   await router.push('/signin');
        // }
      }
    }

    // ฟังก์ชัน setter
    const setUser = (user: User) => {
      userDetail.value = user;
      isLogged.value = true;
    };

    const setError = (newError: string | null) => {
      error.value = newError;
    };

    // ฟังก์ชันสำหรับการล็อกอินสำเร็จ
    async function handleLoginSuccess(newUser: User) {
      // อัปเดต state ใน Store
      setUser(newUser);

      // ส่งข้อความผ่าน Broadcast Channel เพื่อแจ้งแท็บอื่นว่ามีการล็อกอิน
      if (import.meta.client) {
        authChannel?.postMessage({
          type: 'loggedIn',
          payload: { user: newUser },
        });
      }
    }

    // ฟัง Listeners สำหรับ Broadcast Channel เพื่อ sync สถานะระหว่างแท็บ
    if (import.meta.client && authChannel) {
      authChannel.onmessage = (event: any) => {
        console.log('Broadcast Channel message received:', event.data);
        const { type, payload } = event.data;

        switch (type) {
          case 'loggedIn': {
            console.log('Received loggedIn message, updating state.');
            if (payload?.user) {
              setUser(payload.user);

              // Redirect ไปหน้า dashboard ถ้ายังอยู่หน้า signin
              const router = useRouter();
              if (router.currentRoute.value.path === '/signin') {
                navigateTo('/dashboard', { replace: true, redirectCode: 301 });
              }
            } else {
              // ถ้าไม่มี payload หรือ payload ไม่สมบูรณ์ อาจจะต้องโหลด user detail ใหม่
              console.warn(
                'LoggedIn message received without complete payload, attempting to load user detail.'
              );
              loadUserDetail();
              // และตรวจสอบ redirect เหมือนเดิม
              const router = useRouter();
              if (router.currentRoute.value.path === '/signin' && isLogged.value) {
                navigateTo('/dashboard', { replace: true, redirectCode: 301 });
              }
            }
            break;
          }
          case 'loggedOut': {
            console.log('Received loggedOut message, clearing state.');
            // เคลียร์ state ใน store
            isLogged.value = false;
            userDetail.value = null;
            isLoading.value = false;
            error.value = null;

            // Redirect ไปหน้า signin ถ้ายังไม่ได้อยู่ที่นั่น
            const router = useRouter();
            if (router.currentRoute.value.path !== '/signin') {
              router.push('/signin');
            }
            break;
          }
          default:
            console.log(`Unhandled message type: ${type}`);
            break;
        }
      };
    }

    // ฟังก์ชัน clearAuth ที่ไม่เรียก API (สำหรับ auth error handling)
    async function clearAuth() {
      const router = useRouter();

      console.log('Clearing auth state without API call');

      // ล้าง state ทั้งหมดใน Store
      isLogged.value = false;
      userDetail.value = null;
      isLoading.value = false;
      error.value = null;

      if (import.meta.client) {
        // ล้าง localStorage
        localStorage.removeItem('auth'); // Pinia Persist key

        // ส่งข้อความผ่าน Broadcast Channel เพื่อแจ้งแท็บอื่นว่ามีการล็อกเอาต์
        authChannel?.postMessage({ type: 'loggedOut' });
      }

      // Redirect ไป signin
      if (router.currentRoute.value.path !== '/signin') {
        await router.push('/signin');
      }
    }

    return {
      // state
      isLogged,
      userDetail,
      isLoading,
      error,

      // getters
      isAuthenticated,
      getUser,

      // actions
      loadUserDetail,
      doLogout,
      clearAuth, // ฟังก์ชันใหม่ที่ไม่เรียก API

      // setters
      setUser,
      setError,

      // new functions
      handleLoginSuccess,
    };
  },
  {
    persist: true,
    // อาจจะพิจารณาไม่ persist บาง state เช่น isLoading, error
    // paths: ['isLogged', 'userDetail', 'token', 'refreshToken'],
  }
);
