export default defineAppConfig({
  ui: {
    fonts: {
      default: 'Sarabun',
    },
    colors: {
      primary: 'blue',
      neutral: 'zinc',
    },
    // Color mode configuration
    colorMode: {
      preference: 'system',
      fallback: 'light',
    },
    container: {
      base: 'w-full !p-3 sm:!p-4 md:!p-5 lg:!p-6 max-w-(--ui-container) mx-auto bg-[var(--ui-bg-1)] text-[var(--ui-text-1)] md:rounded-lg space-y-3',
    },
    // card: {
    //   slots: {
    //     base: 'bg-[var(--ui-bg-2)]',
    //   },
    // },
    navigationMenu: {
      slots: {
        linkLeadingIcon: 'size-6',
        link: 'text-base font-medium',
        // childLink: '!text-xl font-normal',
      },
    },
    dropdownMenu: {
      slots: {
        content: 'border-none',
        group: 'border-none',
        itemLabel: 'text-base font-normal',
      },
    },
    accordion: {
      slots: {
        label: 'text-base font-semibold',
      },
    },
    formField: {
      slots: {
        error: 'mt-0 p-1 text-sm',
      },
    },
    button: {
      slots: {
        base: 'cursor-pointer font-semibold',
      },
    },
    input: {
      slots: {
        leadingIcon: 'text-primary',
        // base: 'p-2'
      },
    },
    select: {
      slots: {
        leadingIcon: 'text-primary',
        base: 'cursor-pointer font-semibold',
      },
    },
    inputNumber: {
      slots: {
        leadingIcon: 'text-primary',
        base: 'cursor-pointer font-semibold',
      },
    },
    checkbox: {
      slots: {
        label: 'text-base font-normal',
      },
    },
    dropdown: {
      slots: {
        trigger: 'text-base font-normal',
      },
    },
    notifications: {
      // Global notifications configuration
      position: 'top-right',
    },
    toast: {
      slots: {
        base: 'ring-0 border-none bg-[var(--ui-bg-1)] text-[var(--ui-text-1)]',
        icon: 'size-12 sm:size-16',
        title: 'text-base sm:text-lg font-semibold',
        description: 'text-sm sm:text-base font-normal',
      },
    },
  },
});
