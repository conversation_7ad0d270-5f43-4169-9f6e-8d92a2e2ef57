<script setup lang="ts">
import { ModalLogout } from '#components'; 

const authStore = useAuthStore();
const isLoading = ref(false);
const overlay = useOverlay();
const toast = useToast();
const router = useRouter();

const handleSignOut = async () => {
  const modal = overlay.create(ModalLogout, {
    props: {
      isLoading: isLoading.value,
    },
  });

  const instance = modal.open();
  const shouldLogout = await instance.result;

  if (shouldLogout) {
    try {
      isLoading.value = true;
      await authStore.doLogout();
      toast.add({
        title: 'สำเร็จ!',
        description: 'ออกจากระบบเรียบร้อย',
        icon: 'i-heroicons-check-circle',
        color: 'success',
        duration: 1500,
      });
      await router.push('/signin');
    } catch (error: any) {
      console.error('Signout error:', error);
      toast.add({
        title: 'ล้มเหลว!',
        description: error.message || 'เกิดข้อผิดพลาดในการออกจากระบบ',
        icon: 'i-heroicons-exclamation-triangle',
        color: 'error',
        duration: 3000,
      });
    } finally {
      isLoading.value = false;
    }
  }
};

const UserMenuLists: any[][] = [
  [
    {
      label: authStore.userDetail?.email,
      icon: 'solar:letter-line-duotone',
      to: '/dashboard/profile',
      slot: 'profile' as const,
    },
  ],
  [
    {
      label: 'เติมเงิน',
      color: 'success',
      to: '/dashboard/topup',
      icon: 'solar:wallet-line-duotone',
    },
  ],
  [
    {
      label: 'ตั้งค่า',
      to: '/dashboard/settings',
      icon: 'solar:settings-line-duotone',
    },
  ],
  [
    {
      label: 'ออกจากระบบ',
      color: 'error',
      to: '#',
      icon: 'solar:logout-3-line-duotone',
      onSelect: handleSignOut,
    },
  ],
];

const MainMenuLists: any[][] = [
  [
    {
      label: 'จัดการเว็บไซต์',
      icon: 'solar:home-angle-line-duotone',
      to: '/dashboard',
    },
    {
      label: 'สร้างเว็บไซต์',
      icon: 'solar:home-angle-line-duotone',
      to: '/dashboard/create',
    },
    {
      label: 'เข้าร่วมเว็บไซต์',
      icon: 'solar:users-group-two-rounded-line-duotone',
      to: '/dashboard/join',
    },
  ],
];
</script>

<template>
  <div class="flex flex-col">
    <header>
      <div class="bg-[var(--ui-bg-1)] py-3 px-2">
        <div class="w-full mx-auto flex items-center justify-between max-w-(--ui-container)">
          <!-- Site Selector & Navigation -->
          <div>
            <div class="flex items-center gap-4">

              <!-- Desktop Menu -->
              <UNavigationMenu color="primary" :items="MainMenuLists" class="hidden md:block" :ui="{
                list: 'gap-2',
                item: 'p-0',
                link: 'text-base'
              }">
              </UNavigationMenu>
            </div>

            <!-- Mobile Menu -->
            <UDropdownMenu size="xl" :items="MainMenuLists?.[0]" :content="{
              align: 'start',
              side: 'bottom',
              sideOffset: 8
            }" :ui="{
              content: 'w-32 min-w-fit'
            }" class="md:hidden">
              <UIcon name="solar:siderbar-line-duotone" class="size-8" />
            </UDropdownMenu>
          </div>

          <!-- Header Actions Component -->
          <LayoutHeaderActions :custom-menu-items="UserMenuLists" avatar-size="md" />
        </div>
      </div>
    </header>

    <!-- Main content -->
    <main role="main">
      <div class="md:p-3">

        <NuxtPage />
      </div>
    </main>


    <footer>
      <div class="text-center">
        <p>© {{ new Date().getFullYear() }} เรื่องเว็บไซต์ เป็นเรื่องง่าย</p>
      </div>
    </footer>
  </div>
</template>
