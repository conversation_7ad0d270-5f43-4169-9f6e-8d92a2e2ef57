<script setup lang="ts">
import { ModalLogout } from '#components';

const authStore = useAuthStore();
const isLoading = ref(false);
const overlay = useOverlay();
const toast = useToast();
const router = useRouter();
const route: any = useRoute();

// Mobile drawer state
const isDrawerOpen = ref(false);


// Debug function
const toggleDrawer = () => {
  console.log('Button clicked! Current state:', isDrawerOpen.value);
  isDrawerOpen.value = !isDrawerOpen.value;
  console.log('New state:', isDrawerOpen.value);
};

const currentSiteId = computed(() => {
  return route.params.siteId;
});

// Dynamic page title based on route
const pageTitle = computed(() => {
  const path = route.path;
  const siteId = currentSiteId.value;

  if (!siteId) return 'เลือกเว็บไซต์';

  if (path.includes('/analytics')) return 'สถิติ';
  if (path.includes('/blogs')) return 'บล็อก';
  if (path.includes('/products')) return 'สินค้า';
  if (path.includes('/categories')) return 'หมวดหมู่';
  if (path.includes('/types')) return 'ประเภท';
  if (path.includes('/discounts')) return 'ส่วนลด';
  if (path.includes('/shipping')) return 'การจัดส่ง';
  if (path.includes('/payments')) return 'การชำระเงิน';
  if (path.includes('/news')) return 'ข่าวสาร';
  if (path.includes('/teams')) return 'ทีมงาน';
  if (path.includes('/subscription')) return 'แพ็คเกจใช้งาน';
  if (path.includes('/settings')) return 'ตั้งค่า';
  if (path.includes('/themes')) return 'ธีม';
  if (path.includes('/pages')) return 'หน้าเพจ';
  if (path.includes('/menus')) return 'เมนู';
  if (path.includes('/variants')) return 'รุ่นสินค้า';
  if (path.includes('/galleries')) return 'แกลเลอรี่';
  if (path.includes('/comments')) return 'ความคิดเห็น';
  if (path.includes('/reviews')) return 'รีวิว';


  return 'หน้าแรก';
});

// Close drawer when route changes
watch(route, () => {
  isDrawerOpen.value = false;
  if (!currentSiteId.value) {
    router.push('/dashboard');
  }
});

const handleSignOut = async () => {
  const modal = overlay.create(ModalLogout, {
    props: {
      isLoading: isLoading.value,
    },
  });

  const instance = modal.open();
  const shouldLogout = await instance.result;

  if (shouldLogout) {
    try {
      isLoading.value = true;
      await authStore.doLogout();
      toast.add({
        title: 'สำเร็จ!',
        description: 'ออกจากระบบเรียบร้อย',
        icon: 'i-heroicons-check-circle',
        color: 'success',
        duration: 1500,
      });
      await router.push('/signin');
    } catch (error: any) {
      console.error('Signout error:', error);
      toast.add({
        title: 'ล้มเหลว!',
        description: error.message || 'เกิดข้อผิดพลาดในการออกจากระบบ',
        icon: 'i-heroicons-exclamation-triangle',
        color: 'error',
        duration: 3000,
      });
    } finally {
      isLoading.value = false;
    }
  }
};

const UserMenuLists: any[][] = [
  [
    {
      label: authStore.userDetail?.email,
      icon: 'solar:letter-line-duotone',
      to: '/dashboard/profile',
      slot: 'profile' as const,
    },
  ],
  [
    {
      label: 'เติมเงิน',
      color: 'success',
      to: '/dashboard/topup',
      icon: 'solar:wallet-line-duotone',
    },
  ],
  [
    {
      label: 'ตั้งค่า',
      to: '/dashboard/settings',
      icon: 'solar:settings-line-duotone',
    },
  ],
  [
    {
      label: 'ออกจากระบบ',
      color: 'error',
      to: '#',
      icon: 'solar:logout-3-line-duotone',
      onSelect: handleSignOut,
    },
  ],
];

</script>

<template>
  <div class="min-h-screen bg-[var(--ui-bg-1)]">
    
    <!-- Desktop Layout -->
    <div class="hidden lg:flex">
      <!-- Sidebar -->
      <div class="w-64 fixed inset-y-0 left-0 z-50">
        <DashboardSidebar :site-id="currentSiteId" />
      </div>

      <!-- Main Content -->
      <div class="flex-1 ml-64">
        <!-- Header -->
        <header class="bg-[var(--ui-bg-2)] sticky top-0 z-40">
          <div class="px-3 sm:px-6 py-2 sm:py-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-4">
                <h1 class="text-xl font-semibold text-gray-900 dark:text-white">
                  {{ pageTitle }}
                </h1>
              </div>

              <!-- Header Actions Component -->
              <LayoutHeaderActions :custom-menu-items="UserMenuLists" avatar-size="md" />
            </div>
          </div>
        </header>

        <!-- Page Content -->
        <main>
          <UContainer>
            <slot />
          </UContainer>
        </main>
      </div>
    </div>

    <!-- Mobile Layout -->
    <div class="lg:hidden">
      <!-- Mobile Header -->
      <header class="bg-[var(--ui-bg-2)] sticky top-0 z-40">
        <div class="flex items-center justify-between p-4">
          <div class="flex items-center gap-3">
            <UIcon name="solar:hamburger-menu-bold-duotone" @click="toggleDrawer" class="size-10 text-primary hover:text-primary-500" />
            <h1 class="text-lg font-semibold text-gray-900 dark:text-white">
              {{ pageTitle }}
            </h1>
          </div>

          <!-- Header Actions Component -->
          <LayoutHeaderActions :custom-menu-items="UserMenuLists" avatar-size="md" />
        </div>
      </header>

      <!-- Mobile Content -->
      <main class="p-4">
        <slot />
      </main>
    </div>

    <!-- Mobile Drawer - Simple Version -->
    <div v-if="isDrawerOpen" class="fixed inset-0 z-50 lg:hidden">
      <!-- Backdrop -->
      <div class="absolute inset-0 bg-black/50" @click="isDrawerOpen = false"></div>

      <!-- Drawer -->
      <div class="relative w-80 max-w-[80vw] h-full bg-white dark:bg-gray-900 shadow-xl">
        <div class="flex flex-col h-full">

          <!-- Content -->
          <div class="flex-1 overflow-y-auto">
            <DashboardSidebar :site-id="currentSiteId" :is-mobile="true">
              <template #header>
                <UIcon name="solar:close-square-bold" @click="isDrawerOpen = false" class="size-8 text-error"
                  />
              </template>
            </DashboardSidebar>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>