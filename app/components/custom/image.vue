<script setup lang="ts"> 
const props = defineProps({
  publicId: {
    type: String,
    required: true,
  },
  restricted: {
    type: Boolean,
    default: false,
  },
  width: {
    type: [Number, String],
    default: 'auto',
  },
  height: {
    type: [Number, String],
    default: 'auto',
  },
  alt: {
    type: String,
    default: 'Image',
  },
});

const imageUrl = ref('');
const loading = ref(true);
const error = ref('');

// สำหรับรูปภาพ public ใช้ URL ตรงๆ จาก Cloudinary
const getPublicImageUrl = (publicId: string) => {
  const config = useRuntimeConfig();
  const cloudName = config.public.cloudinaryCloudName || 'dsoy8tgfc';
  return `https://res.cloudinary.com/${cloudName}/image/upload/q_auto,f_auto/${publicId}`;
};

const fetchSignedUrl = async () => {
  try {
    loading.value = true;
    error.value = '';
    
    const response = await fetch('/api/signed-url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        publicId: props.publicId,
        restricted: props.restricted
      })
    });
    
    const data = await response.json();
    
    if (data.error) {
      error.value = data.error;
      console.error('API Error:', data.error);
      
      // Fallback: ลองใช้ public URL
      console.log('Trying fallback to public URL...');
      imageUrl.value = getPublicImageUrl(props.publicId);
      
    } else if (data.url) {
      imageUrl.value = data.url;
      console.log('Successfully loaded restricted image:', data.url);
    } else {
      error.value = 'ไม่สามารถโหลดรูปภาพได้';
    }
  } catch (err) {
    error.value = 'เกิดข้อผิดพลาดในการโหลดรูปภาพ';
    console.error('Error fetching signed URL:', err);
    
    // Fallback: ลองใช้ public URL
    console.log('Network error, trying fallback to public URL...');
    imageUrl.value = getPublicImageUrl(props.publicId);
    
  } finally {
    loading.value = false;
  }
};

// ถ้าเป็นรูปภาพ public ใช้ URL ตรงๆ ไม่ต้องผ่าน API
onMounted(async () => {
  if (!props.restricted) {
    // สำหรับรูปภาพ public ใช้ URL ตรงๆ
    imageUrl.value = getPublicImageUrl(props.publicId);
    loading.value = false;
  } else {
    // สำหรับรูปภาพ restricted ใช้ API
    await fetchSignedUrl();
  }
});
</script>

<template>
  <div class="custom-image-container">
    <img 
      v-if="imageUrl && !loading && !error" 
      :src="imageUrl" 
      :alt="alt"
      :style="{ width: width === 'auto' ? 'auto' : `${width}px`, height: height === 'auto' ? 'auto' : `${height}px` }"
      class="custom-image"
      @error="error = 'ไม่สามารถโหลดรูปภาพได้'"
    />
    <div v-else-if="loading" class="loading-placeholder">
      <div class="loading-spinner"></div>
      <p>กำลังโหลดรูปภาพ...</p>
    </div>
    <div v-else-if="error" class="error-placeholder">
      <p>{{ error }}</p>
      <button @click="props.restricted ? fetchSignedUrl() : (imageUrl = getPublicImageUrl(props.publicId), loading = false, error = '')" class="retry-btn">
        ลองใหม่
      </button>
    </div>
  </div>
</template>

<style scoped>
.custom-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
}

.custom-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.loading-placeholder, .error-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #6b7280;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-btn {
  margin-top: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background-color: #2563eb;
}

.error-placeholder p {
  color: #ef4444;
  margin-bottom: 0.5rem;
}
</style>