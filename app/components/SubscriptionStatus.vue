<script setup lang="ts">
interface Props {
  site: {
    _id: string;
    name: string;
    subscription?: {
      plan: 'free' | 'basic' | 'premium' | 'enterprise';
      isActive: boolean;
      endDate?: string;
      startDate?: string;
      price: number;
      billingCycle: 'monthly' | 'yearly';
    };
  };
}

const props = defineProps<Props>();
const router = useRouter();

// ตรวจสอบสถานะ subscription
const subscriptionStatus = computed(() => {
  if (!props.site.subscription) {
    return { status: 'free', label: 'ฟรี', color: 'neutral' };
  }

  const subscription = props.site.subscription;

  // ตรวจสอบว่า subscription หมดอายุหรือไม่
  const isExpired = (sub: Props['site']['subscription'] | undefined): boolean => {
    if (!sub) return false;

    if (sub.endDate) {
      return new Date() > new Date(sub.endDate);
    }
    // ลบการเช็ค sub.status เพราะไม่มีใน type จริง
    return !sub.isActive;
  };

  // ตรวจสอบว่า subscription จะหมดอายุในอีก 7 วันหรือไม่
  const isExpiringSoon = (sub: Props['site']['subscription'] | undefined): boolean => {
    if (!sub) return false;

    const sevenDaysFromNow = new Date();
    sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);

    if (sub.endDate) {
      const endDate = new Date(sub.endDate);
      return endDate <= sevenDaysFromNow && endDate > new Date();
    }

    return false;
  };

  if (isExpired(subscription)) {
    return { status: 'expired', label: 'หมดอายุ', color: 'error' };
  }

  if (isExpiringSoon(subscription)) {
    return { status: 'expiring', label: 'จะหมดอายุเร็วๆ นี้', color: 'warning' };
  }

  return { status: 'active', label: 'ใช้งาน', color: 'success' };
});

// คำนวณจำนวนวันที่เหลือ
const daysLeft = computed(() => {
  if (!props.site.subscription?.endDate) return null;

  const endDate = new Date(props.site.subscription.endDate);

  if (isNaN(endDate.getTime())) return null;

  const now = new Date();
  const diffTime = endDate.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays > 0 ? diffDays : 0;
});

// ไปหน้าต่ออายุ
const goToRenew = () => {
  router.push(`/dashboard/subscription/renew?siteId=${props.site._id}`);
};

// Format date
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

// Get plan name
const getPlanName = (plan: string) => {
  const names = {
    free: 'ฟรี',
    basic: 'เบสิค',
    premium: 'พรีเมียม',
    enterprise: 'เอ็นเตอร์ไพรส์',
  };
  return names[plan as keyof typeof names] || plan;
};
</script>

<template>
  <div class="subscription-status">
    <!-- Status Badge -->
    <div class="flex items-center gap-2 mb-2">
      <UBadge 
        :color="subscriptionStatus.color as any" 
        variant="subtle"
        size="sm"
      >
        {{ subscriptionStatus.label }}
      </UBadge>
      
      <span v-if="site.subscription?.plan && site.subscription.plan !== 'free'" class="text-xs text-gray-600 dark:text-gray-400">
        {{ getPlanName(site.subscription.plan) }}
      </span>
    </div>

    <!-- Expiration Info -->
    <div v-if="site.subscription?.endDate" class="text-xs text-gray-600 dark:text-gray-400">
      <div v-if="subscriptionStatus.status === 'expired'" class="text-red-600 dark:text-red-400">
        หมดอายุเมื่อ: {{ formatDate(site.subscription.endDate) }}
      </div>
      <div v-else-if="subscriptionStatus.status === 'expiring'" class="text-orange-600 dark:text-orange-400">
        หมดอายุใน: {{ daysLeft }} วัน
      </div>
      <div v-else class="text-gray-600 dark:text-gray-400">
        หมดอายุ: {{ formatDate(site.subscription.endDate) }}
      </div>
    </div>

    <!-- Action Button -->
    <div v-if="subscriptionStatus.status === 'expired'" class="mt-2">
      <UButton
        size="xs"
        color="error"
        @click="goToRenew"
      >
        <Icon name="i-heroicons-arrow-path" class="w-3 h-3 mr-1" />
        ต่ออายุ
      </UButton>
    </div>

    <div v-else-if="subscriptionStatus.status === 'expiring'" class="mt-2">
      <UButton
        size="xs"
        color="warning"
        variant="outline"
        @click="goToRenew"
      >
        <Icon name="i-heroicons-arrow-path" class="w-3 h-3 mr-1" />
        ต่ออายุ
      </UButton>
    </div>
  </div>
</template>

<style scoped>

@reference "@/assets/css/main.css";

.subscription-status {
  @apply p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700;
}
</style> 