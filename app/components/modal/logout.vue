<script setup lang="ts">
defineProps<{
  isLoading: boolean;
}>();

const emit = defineEmits<{
  close: [result: boolean];
}>();

const handleConfirm = () => {
  emit('close', true);
};

const handleCancel = () => {
  emit('close', false);
};
</script>

<template>
    <UModal 
        title="ยืนยันการออกจากระบบ" 
        description="คุณต้องการออกจากระบบใช่หรือไม่?"
    >
        <template #footer>
            <div class="w-full flex justify-end gap-3">
                <UButton color="neutral" variant="outline" label="ยกเลิก" @click="handleCancel" />
                <UButton label="ออกจากระบบ" color="error" :loading="isLoading" :disabled="isLoading"
                    @click="handleConfirm" />
            </div>
        </template>
    </UModal>
</template>