<script setup lang="ts">
import { useMyImage } from '~/composables/useMyImage'

interface Props {
    // Image source
    src?: string | null | undefined
    alt?: string

    // Dimensions
    width?: number | string
    height?: number | string
    aspectRatio?: string // e.g., "16/9", "1/1", "4/3"

    // Processing options
    processWidth?: number
    processHeight?: number
    crop?: 'fit' | 'thumb' | 'fill' | 'scale' | 'crop'
    quality?: number
    format?: 'auto' | 'webp' | 'jpg' | 'png'

    // Layout & styling
    objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
    objectPosition?: string

    // Fallback options
    fallbackSrc?: string
    showFallback?: boolean
    fallbackIcon?: string
    fallbackText?: string
    fallbackBg?: string

    // Behavior
    lazy?: boolean
    eager?: boolean
    clickable?: boolean
    zoomable?: boolean

    // Custom classes
    class?: string
    imageClass?: string
    fallbackClass?: string

    // Accessibility
    title?: string
    loading?: 'lazy' | 'eager'

    // Advanced
    sizes?: string
    srcset?: string
    crossorigin?: 'anonymous' | 'use-credentials'
}

const props = withDefaults(defineProps<Props>(), {
    alt: '',
    crop: 'fit',
    quality: 80,
    format: 'auto',
    objectFit: 'cover',
    objectPosition: 'center',
    showFallback: true,
    fallbackIcon: 'i-solar-image-bold-duotone',
    fallbackBg: 'bg-gray-100 dark:bg-gray-800',
    lazy: true,
    eager: false,
    clickable: false,
    zoomable: false,
    loading: 'lazy'
})

const emit = defineEmits<{
    click: [event: MouseEvent]
    load: [event: Event]
    error: [event: Event]
    zoom: [event: MouseEvent]
}>()

const { imageUrl } = useMyImage()

// State
const imageLoaded = ref(false)
const imageError = ref(false)
const isZoomed = ref(false)
const loadingTimeout = ref<NodeJS.Timeout | null>(null)

// Computed styles
const containerStyles = computed(() => {
    const styles: Record<string, string> = {}

    if (props.width) {
        styles.width = typeof props.width === 'number' ? `${props.width}px` : props.width
    }

    if (props.height) {
        styles.height = typeof props.height === 'number' ? `${props.height}px` : props.height
    }

    if (props.aspectRatio) {
        styles.aspectRatio = props.aspectRatio
    }

    return styles
})

const imageStyles = computed(() => {
    const styles: Record<string, string> = {}

    if (props.objectFit) {
        styles.objectFit = props.objectFit
    }

    if (props.objectPosition) {
        styles.objectPosition = props.objectPosition
    }

    return styles
})

// Computed classes
const containerClasses = computed(() => {
    const classes = ['relative', 'overflow-hidden']

    // Interactive
    if (props.clickable || props.zoomable) {
        classes.push('cursor-pointer', 'transition-transform', 'hover:scale-105')
    }


    if (props.class) {
        classes.push(props.class)
    }

    return classes
})

const imageClasses = computed(() => {
    const classes = ['w-full', 'h-full', 'transition-all', 'duration-300']

    if (props.imageClass) {
        classes.push(props.imageClass)
    }

    if (isZoomed.value) {
        classes.push('scale-110')
    }

    return classes
})

const fallbackClasses = computed(() => {
    const classes = [
        'w-full', 'h-full', 'flex', 'items-center', 'justify-center',
        'text-gray-400', 'dark:text-gray-500'
    ]

    if (props.fallbackBg) {
        classes.push(props.fallbackBg)
    }

    if (props.fallbackClass) {
        classes.push(props.fallbackClass)
    }

    return classes
})

// Processed image URL
const processedImageUrl = computed(() => {
    if (!props.src) return null

    try {
        const options: any = {
            crop: props.crop,
            quality: props.quality
        }

        if (props.processWidth) options.width = props.processWidth
        if (props.processHeight) options.height = props.processHeight
        if (props.format !== 'auto') options.format = props.format

        return imageUrl(props.src, options)
    } catch (error) {
        console.warn('Error processing image URL:', error)
        return props.src
    }
})

// Final image source (with fallback)
const finalImageSrc = computed(() => {
    if (imageError.value && props.fallbackSrc) {
        return props.fallbackSrc
    }
    return processedImageUrl.value || props.src
})

// Loading attribute
const loadingAttr = computed(() => {
    if (props.eager) return 'eager'
    if (props.lazy) return 'lazy'
    return props.loading
})

// Event handlers
const handleClick = (event: MouseEvent) => {
    if (props.clickable) {
        emit('click', event)
    }

    if (props.zoomable) {
        isZoomed.value = !isZoomed.value
        emit('zoom', event)
    }
}

const handleLoad = (event: Event) => {
    imageLoaded.value = true
    imageError.value = false
    clearLoadingTimeout()
    emit('load', event)
}

const handleError = (event: Event) => {
    imageError.value = true
    imageLoaded.value = false
    clearLoadingTimeout()
    emit('error', event)
}

// Clear loading timeout
const clearLoadingTimeout = () => {
    if (loadingTimeout.value) {
        clearTimeout(loadingTimeout.value)
        loadingTimeout.value = null
    }
}

// Set loading timeout
const setLoadingTimeout = () => {
    clearLoadingTimeout()
    // Set timeout to 10 seconds
    loadingTimeout.value = setTimeout(() => {
        if (!imageLoaded.value && !imageError.value) {
            console.warn('Image loading timeout:', finalImageSrc.value)
            imageError.value = true
        }
    }, 5000)
}

// Reset states when src changes
watch(() => props.src, () => {
    imageLoaded.value = false
    imageError.value = false
    isZoomed.value = false
    clearLoadingTimeout()

    // Start timeout when new image starts loading
    if (props.src) {
        nextTick(() => {
            if (!imageLoaded.value && !imageError.value) {
                setLoadingTimeout()
            }
        })
    }
})

// Cleanup on unmount
onUnmounted(() => {
    clearLoadingTimeout()
})
</script>

<template>
    <div :class="containerClasses" :style="containerStyles" @click="handleClick"
        :role="clickable || zoomable ? 'button' : undefined" :tabindex="clickable || zoomable ? 0 : undefined"
        :title="title">
        <!-- Main Image -->
        <img v-if="finalImageSrc && !imageError" :src="finalImageSrc" :alt="alt" :class="imageClasses"
            :style="imageStyles" :loading="loadingAttr" :sizes="sizes" :srcset="srcset" :crossorigin="crossorigin"
            @load="handleLoad" @error="handleError" />

        <!-- Fallback Content -->
        <div v-else-if="showFallback" :class="fallbackClasses">
            <!-- Custom fallback slot -->
            <slot name="fallback">
                <!-- Fallback icon -->
                <div v-if="fallbackIcon" class="text-center">
                    <Icon :name="fallbackIcon" class="w-8 h-8 mx-auto mb-2" />
                    <p v-if="fallbackText" class="text-sm">{{ fallbackText }}</p>
                </div>

                <!-- Fallback text only -->
                <div v-else-if="fallbackText" class="text-center">
                    <p class="text-sm">{{ fallbackText }}</p>
                </div>

                <!-- Default fallback -->
                <div v-else class="text-center">
                    <Icon name="solar:image-bold-duotone" class="w-8 h-8 mx-auto mb-2" />
                    <p class="text-sm">ไม่สามารถโหลดรูปภาพได้</p>
                </div>
            </slot>
        </div>

        <!-- Loading indicator -->
        <div v-if="!imageLoaded && !imageError && finalImageSrc"
            class="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
            <slot name="loading">
                <div class="animate-pulse">
                    <Icon name="solar:refresh-circle-bold-duotone" class="w-6 h-6 text-gray-400 animate-spin" />
                </div>
            </slot>
        </div>

        <!-- Overlay slot -->
        <slot name="overlay" />

        <!-- Zoom indicator -->
        <div v-if="zoomable && !isZoomed" class="absolute top-2 right-2 bg-black bg-opacity-50 text-white p-1 rounded">
            <Icon name="solar:magnifer-zoom-in-bold" class="w-4 h-4" />
        </div>
    </div>
</template>

<style scoped>
/* Custom animations */
@keyframes zoomIn {
    from {
        transform: scale(1);
    }

    to {
        transform: scale(1.1);
    }
}

@keyframes zoomOut {
    from {
        transform: scale(1.1);
    }

    to {
        transform: scale(1);
    }
}

.zoom-enter-active {
    animation: zoomIn 0.3s ease-out;
}

.zoom-leave-active {
    animation: zoomOut 0.3s ease-out;
}
</style>