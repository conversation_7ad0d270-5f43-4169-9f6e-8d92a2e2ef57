<script setup lang="ts">
interface Props {
  modelValue?: string | string[];
  placeholder?: string;
  accept?: string;
  maxSize?: number; // in MB
  aspectRatio?: number; // width/height
  showPreview?: boolean;
  showRemove?: boolean;
  disabled?: boolean;
  loading?: boolean;
  error?: string;
  multiple?: boolean;
  maxFiles?: number;
}

interface Emits {
  (e: 'update:modelValue', value: string | string[]): void;
  (e: 'upload', file: File, base64: string): void;
  (e: 'error', error: string): void;
  (e: 'remove', index?: number): void;
  (e: 'reorder', newOrder: string[]): void;
}

interface UploadResponse {
  success: boolean;
  data: {
    url: string;
  };
  message?: string;
}

interface ImageItem {
  id: string;
  url: string;
  file?: File;
  base64?: string;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'คลิกเพื่อเลือกรูปภาพ หรือลากและวางที่นี่',
  accept: 'image/*',
  maxSize: 5, // 5MB
  aspectRatio: 16 / 9,
  showPreview: true,
  showRemove: true,
  disabled: false,
  loading: false,
  multiple: false,
  maxFiles: 10,
});

const emit = defineEmits<Emits>();

// State
const isDragOver = ref(false);
const isUploading = ref(false);
const uploadProgress = ref(0);
const images = ref<ImageItem[]>([]);
const draggedItem = ref<ImageItem | null>(null);
const draggedOverItem = ref<ImageItem | null>(null);

// File input ref
const fileInput = ref<HTMLInputElement>();

// Computed
const hasImages = computed(() => {
  return images.value.length > 0;
});

const isImageValid = computed(() => {
  return !props.error;
});

const canAddMore = computed(() => {
  return props.multiple && images.value.length < props.maxFiles;
});

// Methods
const validateFile = (file: File): string | null => {
  // Check file type
  if (!file.type.startsWith('image/')) {
    return 'ไฟล์ต้องเป็นรูปภาพเท่านั้น';
  }

  // Check file size
  if (file.size > props.maxSize * 1024 * 1024) {
    return `ขนาดไฟล์ต้องไม่เกิน ${props.maxSize}MB`;
  }

  // Check max files for multiple mode
  if (props.multiple && images.value.length >= props.maxFiles) {
    return `สามารถอัปโหลดได้สูงสุด ${props.maxFiles} ไฟล์`;
  }

  return null;
};

const convertToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      resolve(reader.result as string);
    };
    reader.onerror = () => {
      reject(new Error('ไม่สามารถอ่านไฟล์ได้'));
    };
    reader.readAsDataURL(file);
  });
};

const resizeImage = (
  base64: string,
  maxWidth: number = 1920,
  maxHeight: number = 1080
): Promise<string> => {
  return new Promise(resolve => {
    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;

      // Calculate new dimensions
      let { width, height } = img;
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      // Set canvas dimensions
      canvas.width = width;
      canvas.height = height;

      // Draw resized image
      ctx.drawImage(img, 0, 0, width, height);

      // Convert to base64 with quality 0.8
      const resizedBase64 = canvas.toDataURL('image/jpeg', 0.8);
      resolve(resizedBase64);
    };
    img.src = base64;
  });
};

const uploadToServer = async (file: File, base64: string): Promise<string> => {
  try {
    // Create FormData
    const formData = new FormData();
    formData.append('image', file);
    formData.append('base64', base64);

    // Upload to server
    const response = await $fetch<UploadResponse>('/v1/upload/image', {
      method: 'POST',
      body: formData,
    });

    if (response.success) {
      return response.data.url;
    } else {
      throw new Error(response.message || 'อัปโหลดไม่สำเร็จ');
    }
  } catch (error: any) {
    throw new Error(error.message || 'เกิดข้อผิดพลาดในการอัปโหลด');
  }
};

const handleFileSelect = async (file: File) => {
  try {
    isUploading.value = true;
    uploadProgress.value = 0;

    // Validate file
    const validationError = validateFile(file);
    if (validationError) {
      emit('error', validationError);
      return;
    }

    // Convert to base64
    const base64 = await convertToBase64(file);

    // Resize image if needed
    const resizedBase64 = await resizeImage(base64);

    // Create image item
    const imageItem: ImageItem = {
      id: Date.now().toString(),
      url: resizedBase64,
      file,
      base64: resizedBase64,
    };

    if (props.multiple) {
      // Add to multiple images
      images.value.push(imageItem);
      emit(
        'update:modelValue',
        images.value.map(img => img.url)
      );
    } else {
      // Single image mode
      images.value = [imageItem];
      emit('update:modelValue', imageItem.url);
    }

    // Upload to server
    const serverUrl = await uploadToServer(file, resizedBase64);

    // Update with server URL
    if (props.multiple) {
      const index = images.value.findIndex(img => img.id === imageItem.id);
      if (index !== -1 && images.value[index]) {
        images.value[index].url = serverUrl;
        emit(
          'update:modelValue',
          images.value.map(img => img.url)
        );
      }
    } else {
      if (images.value[0]) {
        images.value[0].url = serverUrl;
        emit('update:modelValue', serverUrl);
      }
    }

    emit('upload', file, resizedBase64);
  } catch (error: any) {
    emit('error', error.message);
  } finally {
    isUploading.value = false;
    uploadProgress.value = 0;
  }
};

const handleFilesSelect = async (files: FileList) => {
  if (!props.multiple) {
    const file = files[0];
    if (file) {
      await handleFileSelect(file);
    }
    return;
  }

  // Handle multiple files
  const fileArray = Array.from(files);
  const validFiles = fileArray.filter(file => {
    const error = validateFile(file);
    if (error) {
      emit('error', error);
      return false;
    }
    return true;
  });

  for (const file of validFiles) {
    await handleFileSelect(file);
  }
};

const handleFileInput = (event: Event) => {
  const input = event.target as HTMLInputElement;
  const files = input.files;

  if (files && files.length > 0) {
    handleFilesSelect(files);
  }

  // Reset input
  input.value = '';
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  isDragOver.value = false;

  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    handleFilesSelect(files);
  }
};

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  isDragOver.value = true;
};

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault();
  isDragOver.value = false;
};

const handleClick = () => {
  if (!props.disabled && !isUploading.value) {
    fileInput.value?.click();
  }
};

const handleRemove = (index?: number) => {
  if (props.multiple) {
    if (index !== undefined) {
      images.value.splice(index, 1);
      emit(
        'update:modelValue',
        images.value.map(img => img.url)
      );
      emit('remove', index);
    }
  } else {
    images.value = [];
    emit('update:modelValue', '');
    emit('remove');
  }
};

// Drag and drop reordering
const handleDragStart = (event: DragEvent, item: ImageItem) => {
  draggedItem.value = item;
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move';
  }
};

const handleDragOverItem = (event: DragEvent, item: ImageItem) => {
  event.preventDefault();
  if (draggedItem.value && draggedItem.value.id !== item.id) {
    draggedOverItem.value = item;
  }
};

const handleDragLeaveItem = () => {
  draggedOverItem.value = null;
};

const handleDropItem = (event: DragEvent, targetItem: ImageItem) => {
  event.preventDefault();

  if (draggedItem.value && draggedItem.value.id !== targetItem.id) {
    const draggedIndex = images.value.findIndex(img => img.id === draggedItem.value!.id);
    const targetIndex = images.value.findIndex(img => img.id === targetItem.id);

    if (draggedIndex !== -1 && targetIndex !== -1) {
      // Reorder images
      const newImages = [...images.value];
      const draggedImage = newImages.splice(draggedIndex, 1)[0];
      if (draggedImage) {
        newImages.splice(targetIndex, 0, draggedImage);

        images.value = newImages;
        emit(
          'update:modelValue',
          newImages.map(img => img.url)
        );
        emit(
          'reorder',
          newImages.map(img => img.url)
        );
      }
    }
  }

  draggedItem.value = null;
  draggedOverItem.value = null;
};

// Initialize images from modelValue
const initializeImages = () => {
  if (props.modelValue) {
    if (Array.isArray(props.modelValue)) {
      images.value = props.modelValue.map((url, index) => ({
        id: `existing-${index}`,
        url,
      }));
    } else {
      images.value = [
        {
          id: 'existing-0',
          url: props.modelValue,
        },
      ];
    }
  }
};

// Watch for modelValue changes
watch(
  () => props.modelValue,
  () => {
    initializeImages();
  },
  { immediate: true }
);

// Watch for error changes
watch(
  () => props.error,
  error => {
    if (error) {
      console.error('Image upload error:', error);
    }
  }
);
</script>

<template>
  <div class="image-upload">
    <!-- File Input (Hidden) -->
    <input
      ref="fileInput"
      type="file"
      :accept="accept"
      :multiple="multiple"
      class="hidden"
      @change="handleFileInput"
    />

    <!-- Upload Area -->
    <div
      v-if="!hasImages || !showPreview"
      :class="[
        'upload-area',
        'border-2 border-dashed rounded-lg p-6 text-center transition-all cursor-pointer',
        'hover:border-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20',
        isDragOver ? 'border-primary-500 bg-primary-100 dark:bg-primary-900/30' : 'border-gray-300 dark:border-gray-600',
        props.disabled ? 'opacity-50 cursor-not-allowed' : '',
        isUploading ? 'pointer-events-none' : ''
      ]"
      @click="handleClick"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
    >
      <!-- Loading State -->
      <div v-if="isUploading" class="space-y-4">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
        <div class="space-y-2">
          <p class="text-sm font-medium text-gray-900 dark:text-white">
            กำลังอัปโหลด...
          </p>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="bg-primary-500 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${uploadProgress}%` }"
            ></div>
          </div>
          <p class="text-xs text-gray-500 dark:text-gray-400">
            {{ uploadProgress }}%
          </p>
        </div>
      </div>

      <!-- Default State -->
      <div v-else class="space-y-4">
        <div class="mx-auto w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
          <Icon name="i-heroicons-photo" class="w-6 h-6 text-gray-400" />
        </div>
        <div>
          <p class="text-sm font-medium text-gray-900 dark:text-white mb-1">
            {{ props.placeholder }}
          </p>
          <p class="text-xs text-gray-500 dark:text-gray-400">
            รองรับไฟล์ PNG, JPG, GIF ขนาดไม่เกิน {{ maxSize }}MB
            <span v-if="multiple"> (สูงสุด {{ maxFiles }} ไฟล์)</span>
          </p>
        </div>
      </div>
    </div>

    <!-- Multiple Images Preview -->
    <div v-else-if="multiple && hasImages" class="space-y-4">
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <div
          v-for="(image, index) in images"
          :key="image.id"
          :class="[
            'relative group cursor-move',
            draggedOverItem?.id === image.id ? 'ring-2 ring-primary-500' : ''
          ]"
          draggable="true"
          @dragstart="handleDragStart($event, image)"
          @dragover="handleDragOverItem($event, image)"
          @dragleave="handleDragLeaveItem"
          @drop="handleDropItem($event, image)"
        >
          <div
            :class="[
              'preview-container',
              'relative overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700',
              'group-hover:border-primary-300 dark:group-hover:border-primary-600'
            ]"
            :style="{ aspectRatio: aspectRatio.toString() }"
          >
            <img
              :src="image.url"
              :alt="`Preview ${index + 1}`"
              class="w-full h-full object-cover"
            />
            
            <!-- Overlay -->
            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center gap-2">
                <UButton
                  @click="handleClick"
                  variant="solid"
                  size="sm"
                  class="bg-white text-gray-900 hover:bg-gray-100"
                >
                  <Icon name="i-heroicons-pencil" class="w-4 h-4 mr-1" />
                  แก้ไข
                </UButton>
                <UButton
                  v-if="showRemove"
                  @click="handleRemove(index)"
                  variant="solid"
                  size="sm"
                  color="error"
                  class="bg-red-500 text-white hover:bg-red-600"
                >
                  <Icon name="i-heroicons-trash" class="w-4 h-4 mr-1" />
                  ลบ
                </UButton>
              </div>
            </div>

            <!-- Loading Overlay -->
            <div
              v-if="isUploading"
              class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center"
            >
              <div class="text-center text-white">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
                <p class="text-sm">กำลังอัปโหลด...</p>
              </div>
            </div>
          </div>

          <!-- Image Number Badge -->
          <div class="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
            {{ index + 1 }}
          </div>
        </div>

        <!-- Add More Button -->
        <div
          v-if="canAddMore"
          :class="[
            'border-2 border-dashed rounded-lg p-4 text-center transition-all cursor-pointer',
            'hover:border-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20',
            'border-gray-300 dark:border-gray-600'
          ]"
          :style="{ aspectRatio: aspectRatio.toString() }"
          @click="handleClick"
        >
          <div class="flex flex-col items-center justify-center h-full">
            <Icon name="i-heroicons-plus" class="w-8 h-8 text-gray-400 mb-2" />
            <p class="text-sm text-gray-500 dark:text-gray-400">เพิ่มรูปภาพ</p>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="mt-2 text-sm text-red-600 dark:text-red-400">
        {{ error }}
      </div>
    </div>

    <!-- Single Image Preview -->
    <div v-else-if="!multiple && hasImages" class="relative group">
      <div
        :class="[
          'preview-container',
          'relative overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700',
          'group-hover:border-primary-300 dark:group-hover:border-primary-600'
        ]"
        :style="{ aspectRatio: aspectRatio.toString() }"
      >
        <img
          :src="images[0]?.url"
          :alt="'Preview'"
          class="w-full h-full object-cover"
        />
        
        <!-- Overlay -->
        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
          <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center gap-2">
            <UButton
              @click="handleClick"
              variant="solid"
              size="sm"
              class="bg-white text-gray-900 hover:bg-gray-100"
            >
              <Icon name="i-heroicons-pencil" class="w-4 h-4 mr-1" />
              แก้ไข
            </UButton>
            <UButton
              v-if="showRemove"
              @click="handleRemove()"
              variant="solid"
              size="sm"
              color="error"
              class="bg-red-500 text-white hover:bg-red-600"
            >
              <Icon name="i-heroicons-trash" class="w-4 h-4 mr-1" />
              ลบ
            </UButton>
          </div>
        </div>

        <!-- Loading Overlay -->
        <div
          v-if="isUploading"
          class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center"
        >
          <div class="text-center text-white">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p class="text-sm">กำลังอัปโหลด...</p>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="mt-2 text-sm text-red-600 dark:text-red-400">
        {{ error }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.image-upload {
  @apply w-full;
}

.upload-area {
  @apply transition-all duration-200;
}

.upload-area:hover {
  @apply transform scale-[1.02];
}

.preview-container {
  @apply transition-all duration-200;
}

.preview-container:hover {
  @apply transform scale-[1.02];
}
</style> 