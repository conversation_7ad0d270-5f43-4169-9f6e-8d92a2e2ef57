<template>
	<div class="p-6 space-y-8">
		<!-- Primary Colors -->
		<div class="space-y-4">
			<h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Primary Colors</h2>
			<div class="grid grid-cols-2 md:grid-cols-5 gap-4">
				<div 
					v-for="color in primaryColors" 
					:key="color.name"
					class="p-4 rounded-lg border"
					:style="{ backgroundColor: color.value, color: getContrastColor(color.value) }"
				>
					<div class="font-semibold">{{ color.name }}</div>
					<div class="text-sm opacity-80">{{ color.value }}</div>
				</div>
			</div>
		</div>

		<!-- Semantic Colors -->
		<div class="space-y-4">
			<h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Semantic Colors</h2>
			<div class="grid grid-cols-2 md:grid-cols-3 gap-4">
				<div 
					v-for="color in semanticColorList" 
					:key="color.name"
					class="p-4 rounded-lg border"
					:style="{ backgroundColor: color.value, color: getContrastColor(color.value) }"
				>
					<div class="font-semibold">{{ color.name }}</div>
					<div class="text-sm opacity-80">{{ color.value }}</div>
				</div>
			</div>
		</div>

		<!-- Color Palettes -->
		<div class="space-y-4">
			<h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Color Palettes</h2>
			<div class="space-y-6">
				<div 
					v-for="palette in colorPalettes" 
					:key="palette.name"
					class="space-y-2"
				>
					<h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">{{ palette.name }}</h3>
					<div class="grid grid-cols-10 gap-1">
						<div 
							v-for="(color, index) in palette.colors" 
							:key="index"
							class="h-12 rounded border flex items-center justify-center"
							:style="{ backgroundColor: color }"
							:title="`${palette.name} ${index * 100}`"
						>
							<span 
								class="text-xs font-bold"
								:style="{ color: getContrastColor(color) }"
							>
								{{ index * 100 }}
							</span>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Interactive Color Picker -->
		<div class="space-y-4">
			<h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Interactive Color Picker</h2>
			<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
				<div class="space-y-4">
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
						Select Color Family
					</label>
					<select 
						v-model="selectedFamily" 
						class="w-full p-2 border rounded-lg bg-white dark:bg-gray-800"
					>
						<option value="">Choose a color family</option>
						<option v-for="family in colorFamilies" :key="family" :value="family">
							{{ family.charAt(0).toUpperCase() + family.slice(1) }}
						</option>
					</select>
				</div>
				
				<div v-if="selectedFamily" class="space-y-4">
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
						Select Color Shade
					</label>
					<select 
						v-model="selectedShade" 
						class="w-full p-2 border rounded-lg bg-white dark:bg-gray-800"
					>
						<option value="">Choose a shade</option>
						<option v-for="shade in availableShades" :key="shade" :value="shade">
							{{ shade }}
						</option>
					</select>
				</div>
			</div>

			<div v-if="selectedColor" class="p-6 rounded-lg border" :style="{ backgroundColor: selectedColor }">
				<div class="text-center">
					<h3 class="text-xl font-bold" :style="{ color: selectedContrastColor }">
						Selected Color
					</h3>
					<p class="text-sm opacity-80" :style="{ color: selectedContrastColor }">
						{{ selectedColor }}
					</p>
					<div class="mt-4 space-y-2">
						<div class="flex items-center justify-between">
							<span class="text-sm" :style="{ color: selectedContrastColor }">RGB:</span>
							<span class="text-sm font-mono" :style="{ color: selectedContrastColor }">
								{{ selectedRgbValues }}
							</span>
						</div>
						<div class="flex items-center justify-between">
							<span class="text-sm" :style="{ color: selectedContrastColor }">Contrast:</span>
							<span class="text-sm" :style="{ color: selectedContrastColor }">
								{{ selectedContrastType }}
							</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
const { 
	getColorByName, 
	getColorWithOpacityByName, 
	getSemanticColor, 
	hexToRgb, 
	hexToRgba, 
	getColorPalette, 
	getAllColors, 
	getAllSemanticColors, 
	isLightColor, 
	getContrastColor, 
	generateColorVariants,
	colors,
	semanticColors 
} = useColors()

// Primary colors
const primaryColors = computed(() => [
	{ name: 'Primary', value: colors.primary },
	{ name: 'Secondary', value: semanticColors.secondary },
	{ name: 'Success', value: semanticColors.success },
	{ name: 'Warning', value: semanticColors.warning },
	{ name: 'Error', value: semanticColors.error },
	{ name: 'Info', value: semanticColors.info },
	{ name: 'Neutral', value: semanticColors.neutral },
	{ name: 'Text Primary', value: semanticColors.textPrimary },
	{ name: 'Text Secondary', value: semanticColors.textSecondary },
	{ name: 'Background', value: semanticColors.bgPrimary }
])

// Semantic colors
const semanticColorList = computed(() => 
	Object.entries(semanticColors).map(([name, value]) => ({
		name: name.charAt(0).toUpperCase() + name.slice(1),
		value
	}))
)

// Color palettes
const colorPalettes = computed(() => [
	{
		name: 'Blue',
		colors: [
			colors.blue50, colors.blue100, colors.blue200, colors.blue300, colors.blue400,
			colors.blue500, colors.blue600, colors.blue700, colors.blue800, colors.blue900
		]
	},
	{
		name: 'Green',
		colors: [
			colors.green50, colors.green100, colors.green200, colors.green300, colors.green400,
			colors.green500, colors.green600, colors.green700, colors.green800, colors.green900
		]
	},
	{
		name: 'Red',
		colors: [
			colors.red50, colors.red100, colors.red200, colors.red300, colors.red400,
			colors.red500, colors.red600, colors.red700, colors.red800, colors.red900
		]
	},
	{
		name: 'Purple',
		colors: [
			colors.grape50, colors.grape100, colors.grape200, colors.grape300, colors.grape400,
			colors.grape500, colors.grape600, colors.grape700, colors.grape800, colors.grape900
		]
	},
	{
		name: 'Gray',
		colors: [
			colors.gray50, colors.gray100, colors.gray200, colors.gray300, colors.gray400,
			colors.gray500, colors.gray600, colors.gray700, colors.gray800, colors.gray900
		]
	}
])

// Interactive color picker
const selectedFamily = ref('')
const selectedShade = ref('')

const colorFamilies = computed(() => [
	'blue', 'green', 'red', 'grape', 'violet', 'indigo', 'cyan', 'teal', 'lime', 'yellow', 'orange', 'pink', 'gray', 'dark'
])

const availableShades = computed(() => {
	if (!selectedFamily.value) return []
	return ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900']
})

const selectedColor = computed(() => {
	if (!selectedFamily.value || !selectedShade.value) return null
	const colorKey = `${selectedFamily.value}${selectedShade.value}` as keyof typeof colors
	return colors[colorKey] || null
})

const getRgbValues = (hex: string) => {
	const rgb = hexToRgb(hex)
	return rgb ? `${rgb.r}, ${rgb.g}, ${rgb.b}` : 'Invalid color'
}

// Computed properties for selected color
const selectedContrastColor = computed(() => {
	if (!selectedColor.value) return '#000000'
	return getContrastColor(selectedColor.value)
})

const selectedRgbValues = computed(() => {
	if (!selectedColor.value) return ''
	return getRgbValues(selectedColor.value)
})

const selectedContrastType = computed(() => {
	if (!selectedColor.value) return ''
	return isLightColor(selectedColor.value) ? 'Light' : 'Dark'
})
</script> 