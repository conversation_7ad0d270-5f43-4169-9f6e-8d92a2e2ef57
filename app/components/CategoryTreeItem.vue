<script setup lang="ts">
interface Category {
    id: string;
    name: string;
    slug: string;
    description: string;
    image?: string;
    parentId?: string | null;
    isActive: boolean;
    sortOrder: number;
    productCount: number;
    seoTitle?: string;
    seoDescription?: string;
    createdAt: string;
    updatedAt: string;
    children?: Category[];
}

interface Props {
    category: Category;
    level?: number;
}

interface Emits {
    (e: 'edit', id: string): void;
    (e: 'view', id: string): void;
    (e: 'duplicate', category: Category): void;
    (e: 'toggle-status', id: string): void;
    (e: 'delete', id: string): void;
}

const props = withDefaults(defineProps<Props>(), {
    level: 0
});

const emit = defineEmits<Emits>();

const expanded = ref(true);

const toggleExpanded = () => {
    expanded.value = !expanded.value;
};

const hasChildren = computed(() => {
    return props.category.children && props.category.children.length > 0;
});
</script>

<template>
    <div class="space-y-2">
        <!-- Category Item -->
        <UCard class="overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <div class="flex items-center gap-4">
                <!-- Indentation and Expand/Collapse -->
                <div :style="{ marginLeft: `${level * 24}px` }" class="flex items-center gap-2">
                    <UButton v-if="hasChildren" @click="toggleExpanded" variant="ghost" size="xs" class="p-1">
                        <Icon :name="expanded ? 'i-heroicons-chevron-down' : 'i-heroicons-chevron-right'"
                            class="w-4 h-4" />
                    </UButton>
                    <div v-else class="w-6"></div>
                </div>

                <!-- Category Image -->
                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden flex-shrink-0">
                    <img v-if="category.image" :src="category.image" :alt="category.name"
                        class="w-full h-full object-cover" />
                    <div v-else class="w-full h-full flex items-center justify-center">
                        <Icon name="i-heroicons-folder" class="w-6 h-6 text-gray-400" />
                    </div>
                </div>

                <!-- Category Info -->
                <div class="flex-1 min-w-0">
                    <div class="flex items-center gap-2 mb-1">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                            {{ category.name }}
                        </h3>
                        <UBadge :color="category.isActive ? 'green' : 'red'" variant="subtle" size="sm">
                            {{ category.isActive ? 'เปิดใช้งาน' : 'ปิดใช้งาน' }}
                        </UBadge>
                        <UBadge v-if="hasChildren" color="blue" variant="subtle" size="sm">
                            {{ category.children!.length }} หมวดหมู่ย่อย
                        </UBadge>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400 truncate mb-2">
                        {{ category.description }}
                    </p>
                    <div class="flex items-center gap-4 text-sm text-gray-500">
                        <span class="flex items-center gap-1">
                            <Icon name="i-heroicons-cube" class="w-4 h-4" />
                            {{ category.productCount }} สินค้า
                        </span>
                        <span class="flex items-center gap-1">
                            <Icon name="i-heroicons-clock" class="w-4 h-4" />
                            อัปเดต {{ category.updatedAt }}
                        </span>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex items-center gap-2">
                    <UButton @click="emit('view', category.id)" variant="ghost" size="sm">
                        <Icon name="i-heroicons-eye" class="w-4 h-4" />
                    </UButton>
                    <UDropdown :items="[
                        [
                            {
                                label: 'แก้ไข',
                                icon: 'i-heroicons-pencil-square',
                                click: () => emit('edit', category.id)
                            },
                            {
                                label: 'คัดลอก',
                                icon: 'i-heroicons-document-duplicate',
                                click: () => emit('duplicate', category)
                            },
                            {
                                label: category.isActive ? 'ปิดใช้งาน' : 'เปิดใช้งาน',
                                icon: category.isActive ? 'i-heroicons-eye-slash' : 'i-heroicons-eye',
                                click: () => emit('toggle-status', category.id)
                            }
                        ],
                        [
                            {
                                label: 'ลบ',
                                icon: 'i-heroicons-trash',
                                click: () => emit('delete', category.id),
                                disabled: category.productCount > 0 || hasChildren
                            }
                        ]
                    ]">
                        <UButton variant="ghost" size="sm">
                            <Icon name="i-heroicons-ellipsis-vertical" class="w-4 h-4" />
                        </UButton>
                    </UDropdown>
                </div>
            </div>
        </UCard>

        <!-- Children -->
        <div v-if="hasChildren && expanded" class="space-y-2">
            <CategoryTreeItem v-for="child in category.children" :key="child.id" :category="child" :level="level + 1"
                @edit="emit('edit', $event)" @view="emit('view', $event)" @duplicate="emit('duplicate', $event)"
                @toggle-status="emit('toggle-status', $event)" @delete="emit('delete', $event)" />
        </div>
    </div>
</template>

<style scoped>
/* Smooth transitions for expand/collapse */
.space-y-2>div {
    transition: all 0.3s ease;
}
</style>