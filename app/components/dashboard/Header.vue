<template>
  <header class=" ">
    <div class="flex items-center justify-between px-4 py-3 lg:px-6">
      <!-- Left Section -->
      <div class="flex items-center gap-4">
        <!-- Mobile Menu Button -->
        <UButton
          @click="$emit('toggle-sidebar')"
          variant="ghost"
          size="sm"
          icon="i-heroicons-bars-3"
          class="lg:hidden"
        />
        
        <!-- Page Title -->
        <div>
          <h1 class="text-xl font-semibold text-gray-900 dark:text-white">
            {{ pageTitle }}
          </h1>
          <p v-if="pageDescription" class="text-sm text-gray-500 dark:text-gray-400">
            {{ pageDescription }}
          </p>
        </div>
      </div>

      <!-- Right Section -->
      <div class="flex items-center gap-3">
        <!-- Search Bar -->
        <DashboardSearchBar class="hidden md:block" />
        
        <!-- Notifications -->
        <DashboardNotificationBell />
        
        <!-- Quick Actions -->
        <UDropdownMenu :items="quickActionItems">
          <UButton
            variant="outline"
            size="sm"
            icon="i-heroicons-plus"
            label="สร้าง"
          />
        </UDropdownMenu>

        <!-- User Menu -->
        <UDropdownMenu :items="userMenuItems">
          <UAvatar 
            :src="authStore.userDetail?.avatar || 'https://github.com/benjamincanac.png'"
            size="sm"
            class="cursor-pointer"
          />
        </UDropdownMenu>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ModalLogout } from '#components';
import { useAuthStore } from '~/stores/auth';

const authStore = useAuthStore();
const route = useRoute();
const overlay = useOverlay();
const toast = useToast();

// Props
interface Props {
  pageTitle?: string;
  pageDescription?: string;
  siteId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  pageTitle: 'Dashboard',
  pageDescription: '',
});

// Emits
defineEmits<{
  'toggle-sidebar': [];
}>();

// Get current siteId
const currentSiteId = computed(() => props.siteId || route.params);

// Quick action items
const quickActionItems = computed(() => [
  [
    {
      label: 'สินค้าใหม่',
      icon: 'i-heroicons-shopping-bag',
      to: `/dashboard/${currentSiteId.value}/products/create`,
    },
    {
      label: 'บล็อกใหม่',
      icon: 'i-heroicons-document-text',
      to: `/dashboard/${currentSiteId.value}/blog/create`,
    },
    {
      label: 'หน้าเพจใหม่',
      icon: 'i-heroicons-document',
      to: `/dashboard/${currentSiteId.value}/pages/create`,
    },
  ],
  [
    {
      label: 'คูปองใหม่',
      icon: 'i-heroicons-ticket',
      to: `/dashboard/${currentSiteId.value}/coupons/create`,
    },
    {
      label: 'แคมเปญใหม่',
      icon: 'i-heroicons-megaphone',
      to: `/dashboard/${currentSiteId.value}/campaigns/create`,
    },
  ],
]);

// User menu items
const userMenuItems = computed(() => [
  [
    {
      label: 'โปรไฟล์',
      icon: 'i-heroicons-user',
      to: '/profile',
    },
    {
      label: 'ตั้งค่า',
      icon: 'i-heroicons-cog-6-tooth',
      to: '/settings',
    },
  ],
  [
    {
      label: 'ออกจากระบบ',
      icon: 'i-heroicons-arrow-left-on-rectangle',
      click: handleSignOut,
    },
  ],
]);

// Handle sign out
const handleSignOut = async () => {
  const modal = overlay.create(ModalLogout, {
    props: {
      isLoading: false,
    },
  });

  const instance = modal.open();
  const shouldLogout = await instance.result;

  if (shouldLogout) {
    try {
      await authStore.doLogout();
      toast.add({
        title: 'ออกจากระบบสำเร็จ',
        description: 'ขอบคุณที่ใช้งาน',
        icon: 'i-heroicons-check-circle',
        color: 'success',
        duration: 1000,
      });
      await navigateTo('/signin');
    } catch (error: unknown) {
      console.error('Signout error:', error);
      toast.add({
        title: 'ล้มเหลว',
        description: (error as any).message || 'เกิดข้อผิดพลาดในการออกจากระบบ',
        icon: 'i-heroicons-exclamation-triangle',
        color: 'error',
        duration: 3000,
      });
    }
  }
};
</script>