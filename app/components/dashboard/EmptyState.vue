<template>
  <div class="text-center py-12">
    <div class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
      <Icon :name="icon" class="w-12 h-12 text-gray-400" />
    </div>
    
    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
      {{ title }}
    </h3>
    
    <p class="text-gray-500 dark:text-gray-400 mb-6 max-w-sm mx-auto">
      {{ description }}
    </p>
    
    <div class="flex flex-col sm:flex-row gap-3 justify-center">
      <UButton
        v-if="primaryAction"
        :to="primaryAction.to"
        :icon="primaryAction.icon"
        size="lg"
        @click="primaryAction.click"
      >
        {{ primaryAction.label }}
      </UButton>
      
      <UButton
        v-if="secondaryAction"
        :to="secondaryAction.to"
        :icon="secondaryAction.icon"
        variant="outline"
        size="lg"
        @click="secondaryAction.click"
      >
        {{ secondaryAction.label }}
      </UButton>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Action {
  label: string;
  icon?: string;
  to?: string;
  click?: () => void;
}

interface Props {
  title: string;
  description: string;
  icon: string;
  primaryAction?: Action;
  secondaryAction?: Action;
}

defineProps<Props>();
</script>