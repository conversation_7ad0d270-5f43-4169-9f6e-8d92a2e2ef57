<template>
  <UDropdownMenu :items="notificationItems" :popper="{ placement: 'bottom-end' }">
    <UButton
      variant="ghost"
      size="sm"
      class="relative"
    >
      <Icon name="i-heroicons-bell" class="w-5 h-5" />
      
      <!-- Notification Badge -->
      <span
        v-if="unreadCount > 0"
        class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium"
      >
        {{ unreadCount > 99 ? '99+' : unreadCount }}
      </span>
    </UButton>

    <template #item="{ item }">
      <div v-if="item.type === 'header'" class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-sm font-semibold text-gray-900 dark:text-white">
            การแจ้งเตือน
          </h3>
          <UButton
            v-if="unreadCount > 0"
            @click="markAllAsRead"
            variant="ghost"
            size="xs"
            label="อ่านทั้งหมด"
          />
        </div>
      </div>
      
      <div v-else-if="item.type === 'notification'" class="px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer">
        <div class="flex items-start gap-3">
          <div :class="[
            'flex items-center justify-center w-8 h-8 rounded-full flex-shrink-0',
            getNotificationColor(item.category)
          ]">
            <Icon :name="getNotificationIcon(item.category)" class="w-4 h-4" />
          </div>
          
          <div class="flex-1 min-w-0">
            <p :class="[
              'text-sm',
              item.read ? 'text-gray-600 dark:text-gray-400' : 'text-gray-900 dark:text-white font-medium'
            ]">
              {{ item.title }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {{ item.message }}
            </p>
            <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
              {{ formatTime(item.timestamp) }}
            </p>
          </div>
          
          <div v-if="!item.read" class="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-2"></div>
        </div>
      </div>
      
      <div v-else-if="item.type === 'empty'" class="px-4 py-8 text-center">
        <Icon name="i-heroicons-bell-slash" class="w-12 h-12 text-gray-400 mx-auto mb-3" />
        <p class="text-gray-500 dark:text-gray-400">ไม่มีการแจ้งเตือน</p>
      </div>
      
      <div v-else-if="item.type === 'footer'" class="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
        <UButton
          to="/dashboard/notifications"
          variant="ghost"
          size="sm"
          label="ดูการแจ้งเตือนทั้งหมด"
          class="w-full justify-center"
        />
      </div>
    </template>
  </UDropdownMenu>
</template>

<script setup lang="ts">
interface Notification {
  id: string;
  title: string;
  message: string;
  category: 'order' | 'system' | 'user' | 'payment' | 'security';
  timestamp: Date;
  read: boolean;
  link?: string;
}

// Mock notifications data - should come from API
const notifications = ref<Notification[]>([
  {
    id: '1',
    title: 'คำสั่งซื้อใหม่',
    message: 'มีคำสั่งซื้อใหม่จาก สมชาย ใจดี',
    category: 'order',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    read: false,
    link: '/dashboard/orders/12345',
  },
  {
    id: '2',
    title: 'การชำระเงินสำเร็จ',
    message: 'ได้รับการชำระเงินสำหรับคำสั่งซื้อ #12344',
    category: 'payment',
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
    read: false,
  },
  {
    id: '3',
    title: 'สมาชิกใหม่',
    message: 'สมหญิง รักดี ได้สมัครสมาชิกใหม่',
    category: 'user',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    read: true,
  },
  {
    id: '4',
    title: 'อัปเดตระบบ',
    message: 'ระบบได้รับการอัปเดตเป็นเวอร์ชัน 2.1.0',
    category: 'system',
    timestamp: new Date(Date.now() - 60 * 60 * 1000),
    read: true,
  },
]);

// Computed
const unreadCount = computed(() => notifications.value.filter(n => !n.read).length);

const notificationItems = computed(() => {
  const items = [{ type: 'header' }];

  if (notifications.value.length === 0) {
    items.push({ type: 'empty' });
  } else {
    notifications.value.slice(0, 5).forEach(notification => {
      items.push({
        type: 'notification',
        ...notification,
      });
    });
  }

  if (notifications.value.length > 0) {
    items.push({ type: 'footer' });
  }

  return items;
});

// Methods
const getNotificationIcon = (category: Notification['category']) => {
  const icons = {
    order: 'i-heroicons-shopping-cart',
    system: 'i-heroicons-cog-6-tooth',
    user: 'i-heroicons-user-plus',
    payment: 'i-heroicons-credit-card',
    security: 'i-heroicons-shield-exclamation',
  };
  return icons[category];
};

const getNotificationColor = (category: Notification['category']) => {
  const colors = {
    order: 'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400',
    system: 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400',
    user: 'bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400',
    payment: 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400',
    security: 'bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400',
  };
  return colors[category];
};

const formatTime = (timestamp: Date) => {
  const now = new Date();
  const diff = now.getTime() - timestamp.getTime();
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 1) return 'เมื่อสักครู่';
  if (minutes < 60) return `${minutes} นาทีที่แล้ว`;
  if (hours < 24) return `${hours} ชั่วโมงที่แล้ว`;
  if (days < 7) return `${days} วันที่แล้ว`;

  return timestamp.toLocaleDateString('th-TH');
};

const markAllAsRead = () => {
  notifications.value.forEach(notification => {
    notification.read = true;
  });
};
</script>