<template>
  <UCard variant="soft">
    <template #header>
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          กิจกรรมล่าสุด
        </h3>
        <UButton
          to="/dashboard/activity"
          variant="ghost"
          size="sm"
          label="ดูทั้งหมด"
        />
      </div>
    </template>

    <div class="space-y-4">
      <div
        v-for="activity in activities"
        :key="activity.id"
        class="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
      >
        <!-- Activity Icon -->
        <div :class="[
          'flex items-center justify-center w-8 h-8 rounded-full flex-shrink-0',
          getActivityColor(activity.type)
        ]">
          <Icon :name="getActivityIcon(activity.type)" class="w-4 h-4" />
        </div>

        <!-- Activity Content -->
        <div class="flex-1 min-w-0">
          <p class="text-sm text-gray-900 dark:text-white">
            <span class="font-medium">{{ activity.user }}</span>
            {{ activity.action }}
            <span v-if="activity.target" class="font-medium">{{ activity.target }}</span>
          </p>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {{ formatTime(activity.timestamp) }}
          </p>
        </div>

        <!-- Activity Status -->
        <div v-if="activity.status" :class="[
          'px-2 py-1 text-xs font-medium rounded-full',
          getStatusColor(activity.status)
        ]">
          {{ getStatusText(activity.status) }}
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="activities.length === 0" class="text-center py-8">
        <Icon name="i-heroicons-clock" class="w-12 h-12 text-gray-400 mx-auto mb-3" />
        <p class="text-gray-500 dark:text-gray-400">ยังไม่มีกิจกรรมล่าสุด</p>
      </div>
    </div>
  </UCard>
</template>

<script setup lang="ts">
interface Activity {
  id: string;
  type: 'order' | 'product' | 'user' | 'system' | 'payment' | 'blog';
  user: string;
  action: string;
  target?: string;
  timestamp: Date;
  status?: 'success' | 'pending' | 'failed';
}

interface Props {
  siteId?: string;
  limit?: number;
}

const props = withDefaults(defineProps<Props>(), {
  limit: 5,
});

// Mock activities data - should come from API
const activities = ref<Activity[]>([
  {
    id: '1',
    type: 'order',
    user: 'สมชาย ใจดี',
    action: 'สั่งซื้อสินค้า',
    target: 'iPhone 15 Pro',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    status: 'success',
  },
  {
    id: '2',
    type: 'product',
    user: 'ผู้ดูแลระบบ',
    action: 'เพิ่มสินค้าใหม่',
    target: 'MacBook Air M3',
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
    status: 'success',
  },
  {
    id: '3',
    type: 'user',
    user: 'สมหญิง รักดี',
    action: 'สมัครสมาชิกใหม่',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    status: 'success',
  },
  {
    id: '4',
    type: 'payment',
    user: 'ระบบ',
    action: 'ประมวลผลการชำระเงิน',
    target: 'คำสั่งซื้อ #12345',
    timestamp: new Date(Date.now() - 45 * 60 * 1000),
    status: 'pending',
  },
  {
    id: '5',
    type: 'blog',
    user: 'ผู้ดูแลเนื้อหา',
    action: 'เผยแพร่บทความ',
    target: 'วิธีเลือกซื้อสมาร์ทโฟน',
    timestamp: new Date(Date.now() - 60 * 60 * 1000),
    status: 'success',
  },
]);

// Get activity icon
const getActivityIcon = (type: Activity['type']) => {
  const icons = {
    order: 'i-heroicons-shopping-cart',
    product: 'i-heroicons-shopping-bag',
    user: 'i-heroicons-user-plus',
    system: 'i-heroicons-cog-6-tooth',
    payment: 'i-heroicons-credit-card',
    blog: 'i-heroicons-document-text',
  };
  return icons[type];
};

// Get activity color
const getActivityColor = (type: Activity['type']) => {
  const colors = {
    order: 'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400',
    product: 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400',
    user: 'bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400',
    system: 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400',
    payment: 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400',
    blog: 'bg-indigo-100 text-indigo-600 dark:bg-indigo-900/20 dark:text-indigo-400',
  };
  return colors[type];
};

// Get status color
const getStatusColor = (status: Activity['status']) => {
  const colors = {
    success: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    failed: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
  };
  return colors[status || 'success'];
};

// Get status text
const getStatusText = (status: Activity['status']) => {
  const texts = {
    success: 'สำเร็จ',
    pending: 'รอดำเนินการ',
    failed: 'ล้มเหลว',
  };
  return texts[status || 'success'];
};

// Format time
const formatTime = (timestamp: Date) => {
  const now = new Date();
  const diff = now.getTime() - timestamp.getTime();
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 1) return 'เมื่อสักครู่';
  if (minutes < 60) return `${minutes} นาทีที่แล้ว`;
  if (hours < 24) return `${hours} ชั่วโมงที่แล้ว`;
  if (days < 7) return `${days} วันที่แล้ว`;

  return timestamp.toLocaleDateString('th-TH');
};
</script>