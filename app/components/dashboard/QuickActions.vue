<script setup lang="ts">
interface Props {
  siteId?: string;
}

const props = defineProps<Props>();
const route: any = useRoute();

// Get current siteId
const currentSiteId = computed(() => props.siteId || route.params.siteId);

// Quick actions data
const quickActions = computed(() => [
  {
    id: 'new-product',
    label: 'เพิ่มสินค้า',
    description: 'สร้างสินค้าใหม่',
    icon: 'i-heroicons-shopping-bag',
    to: `/dashboard/${currentSiteId.value}/products/create`,
  },
  {
    id: 'new-order',
    label: 'คำสั่งซื้อ',
    description: 'ดูคำสั่งซื้อใหม่',
    icon: 'i-heroicons-shopping-cart',
    to: `/dashboard/${currentSiteId.value}/orders`,
  },
  {
    id: 'new-blog',
    label: 'เขียนบล็อก',
    description: 'สร้างบทความใหม่',
    icon: 'i-heroicons-document-text',
    to: `/dashboard/${currentSiteId.value}/blog/create`,
  },
  {
    id: 'customers',
    label: 'ลูกค้า',
    description: 'จัดการลูกค้า',
    icon: 'i-heroicons-users',
    to: `/dashboard/${currentSiteId.value}/customers`,
  },
  {
    id: 'analytics',
    label: 'สถิติ',
    description: 'ดูรายงานการขาย',
    icon: 'i-heroicons-chart-bar',
    to: `/dashboard/${currentSiteId.value}/analytics`,
  },
  {
    id: 'settings',
    label: 'ตั้งค่า',
    description: 'ตั้งค่าเว็บไซต์',
    icon: 'i-heroicons-cog-6-tooth',
    to: `/dashboard/${currentSiteId.value}/settings`,
  },
  {
    id: 'themes',
    label: 'ธีม',
    description: 'เปลี่ยนธีมเว็บไซต์',
    icon: 'i-heroicons-paint-brush',
    to: `/dashboard/${currentSiteId.value}/themes`,
  },
  {
    id: 'backup',
    label: 'สำรองข้อมูล',
    description: 'สำรองข้อมูลเว็บไซต์',
    icon: 'i-heroicons-cloud-arrow-down',
    to: `/dashboard/${currentSiteId.value}/backups`,
  },
]);
</script>

<template>
  <UCard variant="soft">
    <template #header>
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          การดำเนินการด่วน
        </h3>
        <UButton variant="ghost" size="sm" icon="i-heroicons-ellipsis-horizontal" />
      </div>
    </template>

    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      <UButton v-for="action in quickActions" :key="action.id" :to="action.to" variant="soft" size="lg"
        class="flex flex-col items-center gap-2 h-auto py-4 hover:bg-gray-50 dark:hover:bg-gray-800">
        <Icon :name="action.icon" class="w-6 h-6" />
        <span class="text-sm font-medium">{{ action.label }}</span>
        <span v-if="action.description" class="text-xs text-gray-500 dark:text-gray-400 text-center">
          {{ action.description }}
        </span>
      </UButton>
    </div>
  </UCard>
</template>
