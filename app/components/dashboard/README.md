# Dashboard Components

คอมโพเนนต์สำหรับสร้าง Dashboard ที่สมบูรณ์ด้วย Nuxt UI 3 และ Tailwind CSS

## คอมโพเนนต์ที่มี

### 1. Layout Components
- **DashboardLayout** - เลย์เอาต์หลักของ dashboard (รวม mobile navigation)
- **DashboardHeader** - ส่วนหัวของ dashboard
- **DashboardSidebar** - แถบเมนูด้านข้าง (รองรับทั้ง desktop และ mobile)

### 2. Navigation Components
- **DashboardSiteSelector** - เลือกเว็บไซต์
- **DashboardSearchBar** - แถบค้นหา
- **DashboardBreadcrumb** - เส้นทางการนำทาง

### 3. Content Components
- **DashboardStatsCard** - การ์ดแสดงสถิติ
- **DashboardQuickActions** - ปุ่มลัดต่างๆ
- **DashboardRecentActivity** - กิจกรรมล่าสุด
- **DashboardNotificationBell** - ระฆังแจ้งเตือน
- **DashboardEmptyState** - สถานะเมื่อไม่มีข้อมูล

## การใช้งาน

### Basic Layout
```vue
<template>
  <DashboardLayout
    page-title="หน้าแรก"
    page-description="ภาพรวมของเว็บไซต์"
    :site-id="siteId"
  >
    <!-- เนื้อหาของหน้า -->
  </DashboardLayout>
</template>
```

### Stats Cards
```vue
<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <DashboardStatsCard
      title="ยอดขายวันนี้"
      :value="25000"
      icon="i-heroicons-currency-dollar"
      :change="12.5"
      format="currency"
      color="green"
    />
    
    <DashboardStatsCard
      title="คำสั่งซื้อใหม่"
      :value="48"
      icon="i-heroicons-shopping-cart"
      :change="-5.2"
      color="blue"
    />
  </div>
</template>
```

### Quick Actions
```vue
<template>
  <DashboardQuickActions :site-id="siteId" />
</template>
```

### Recent Activity
```vue
<template>
  <DashboardRecentActivity :site-id="siteId" :limit="10" />
</template>
```

### Empty State
```vue
<template>
  <DashboardEmptyState
    title="ยังไม่มีสินค้า"
    description="เริ่มต้นด้วยการเพิ่มสินค้าแรกของคุณ"
    icon="i-heroicons-shopping-bag"
    :primary-action="{
      label: 'เพิ่มสินค้า',
      icon: 'i-heroicons-plus',
      to: '/dashboard/products/create'
    }"
    :secondary-action="{
      label: 'นำเข้าสินค้า',
      icon: 'i-heroicons-arrow-up-tray',
      to: '/dashboard/products/import'
    }"
  />
</template>
```

## Features

### 🎨 Design System
- ใช้ Nuxt UI 3 components
- รองรับ Dark Mode
- Responsive Design
- Tailwind CSS

### 🌐 Internationalization
- ข้อความเป็นภาษาไทย
- รองรับการแปลภาษา
- Format ตัวเลขแบบไทย

### 📱 Mobile Friendly
- Mobile Navigation
- Touch Friendly
- Responsive Grid

### 🔍 Search & Navigation
- Global Search
- Breadcrumb Navigation
- Keyboard Shortcuts (⌘K)

### 🔔 Notifications
- Real-time Notifications
- Notification Bell
- Activity Feed

### 📊 Analytics Ready
- Stats Cards
- Chart Integration Ready
- Performance Metrics

## Customization

### Colors
คอมโพเนนต์รองรับสีต่างๆ:
- `primary` (default)
- `green`
- `blue` 
- `yellow`
- `red`
- `purple`

### Icons
ใช้ Heroicons และ Solar Icons:
```vue
<DashboardStatsCard
  icon="i-heroicons-shopping-cart"
  // หรือ
  icon="solar:bag-line-duotone"
/>
```

## Dependencies

- Nuxt 4
- Nuxt UI 3
- Tailwind CSS
- Heroicons
- Solar Icons (optional)

## File Structure

```
app/components/dashboard/
├── Layout.vue              # เลย์เอาต์หลัก (รวม mobile nav)
├── Header.vue              # ส่วนหัว
├── Sidebar.vue             # แถบเมนู (รองรับ mobile)
├── SiteSelector.vue        # เลือกเว็บไซต์
├── SearchBar.vue           # แถบค้นหา
├── NotificationBell.vue    # แจ้งเตือน
├── QuickActions.vue        # ปุ่มลัด
├── StatsCard.vue           # การ์ดสถิติ
├── RecentActivity.vue      # กิจกรรมล่าสุด
├── Breadcrumb.vue          # เส้นทาง
├── EmptyState.vue          # สถานะว่าง
├── index.ts                # Export ทั้งหมด
└── README.md               # เอกสารนี้
```