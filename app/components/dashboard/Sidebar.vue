<script setup lang="ts">
import { useMyImage } from '~/composables/useMyImage';

const route = useRoute();

// Import useMyImage for UI images
const { imageUrl } = useMyImage();

// Props
interface Props {
  siteId?: string;
  isMobile?: boolean;
  onClose?: () => void; // เพิ่ม prop สำหรับ event ปิด drawer
}

const emit = defineEmits(['close']); // เพิ่ม emit event

const props = withDefaults(defineProps<Props>(), {
  isMobile: false,
});

// Get current siteId from route or props
const currentSiteId = computed(() => {
  const params = route.params as Record<string, any>;
  return typeof params.siteId === 'string' ? params.siteId : '';
});

// ใช้ useSites สำหรับดึงข้อมูลเว็บไซต์
const { getSite } = useSites();

// ดึงข้อมูลเว็บไซต์
const { data: siteData }: any = await useLazyAsyncData(
  `sidebar-site-${currentSiteId.value}`,
  () => getSite(currentSiteId.value),
  {
    watch: [currentSiteId]
  }
);

const currentSite: any = computed(() => siteData.value?.data?.site);
const isPremium = computed(() => currentSite.value?.isPremium || false);
const isActive = computed(() => currentSite.value?.isActive || false);

// Navigation items for UNavigationMenu
const isChildActive = (children: any[]) => {
  return children.some(child => child.to && route.path.startsWith(child.to));
};

const navigationItems = computed(() => {
  return [
    {
      label: 'หน้าแรก',
      icon: 'solar:home-angle-line-duotone',
      to: `/dashboard/${currentSiteId.value}`,
    },
    {
      label: 'ตั้งค่า',
      icon: 'solar:settings-line-duotone',
      to: `/dashboard/${currentSiteId.value}/settings`,
    },
    {
      label: 'ปรับแต่ง',
      icon: 'solar:palette-line-duotone',
      open: isChildActive([
        { to: `/dashboard/${currentSiteId.value}/pages` },
        { to: `/dashboard/${currentSiteId.value}/menus` },
        { to: `/dashboard/${currentSiteId.value}/themes` },
      ]),
      children: [
        {
          label: 'หน้าเพจ',
          icon: 'solar:documents-line-duotone',
          to: `/dashboard/${currentSiteId.value}/pages`,
        },
        {
          label: 'เมนู',
          icon: 'solar:hamburger-menu-line-duotone',
          to: `/dashboard/${currentSiteId.value}/menus`,
        },
        {
          label: 'ธีม',
          icon: 'solar:palette-line-duotone',
          to: `/dashboard/${currentSiteId.value}/themes`,
        },
      ],
    },
    {
      label: 'จัดการเนื้อหา',
      icon: 'solar:document-text-line-duotone',
      open: isChildActive([
        { to: `/dashboard/${currentSiteId.value}/products` },
        { to: `/dashboard/${currentSiteId.value}/categories` },
        { to: `/dashboard/${currentSiteId.value}/types` },
        { to: `/dashboard/${currentSiteId.value}/galleries` },
        { to: `/dashboard/${currentSiteId.value}/variants` },
        { to: `/dashboard/${currentSiteId.value}/comments` },
        { to: `/dashboard/${currentSiteId.value}/reviews` },
        { to: `/dashboard/${currentSiteId.value}/blog` },
        { to: `/dashboard/${currentSiteId.value}/news` },
      ]),
      children: [
        {
          label: 'สินค้า',
          icon: 'solar:bag-line-duotone',
          to: `/dashboard/${currentSiteId.value}/products`,
        },
        {
          label: 'หมวดหมู่',
          icon: 'solar:folder-line-duotone',
          to: `/dashboard/${currentSiteId.value}/categories`,
        },
        {
          label: 'ประเภท',
          icon: 'solar:tag-line-duotone',
          to: `/dashboard/${currentSiteId.value}/types`,
        },
        {
          label: 'แกลเลอรี่',
          icon: 'solar:gallery-wide-line-duotone',
          to: `/dashboard/${currentSiteId.value}/galleries`,
        },
        {
          label: 'ตัวเลือก',
          icon: 'solar:tag-price-line-duotone',
          to: `/dashboard/${currentSiteId.value}/variants`,
        },
        {
          label: 'ความคิดเห็น',
          icon: 'solar:chat-square-like-line-duotone',
          to: `/dashboard/${currentSiteId.value}/comments`,
        },
        {
          label: 'รีวิว',
          icon: 'solar:like-line-duotone',
          to: `/dashboard/${currentSiteId.value}/reviews`,
        },
        {
          label: 'บล็อก',
          icon: 'solar:document-text-line-duotone',
          to: `/dashboard/${currentSiteId.value}/blogs`,
        },
        {
          label: 'ข่าวสาร',
          icon: 'solar:notebook-minimalistic-line-duotone',
          to: `/dashboard/${currentSiteId.value}/news`,
        },
      ],
    },
    {
      label: 'การขาย',
      icon: 'solar:cart-line-duotone',
      open: isChildActive([
        { to: `/dashboard/${currentSiteId.value}/orders` },
        { to: `/dashboard/${currentSiteId.value}/customers` },
        { to: `/dashboard/${currentSiteId.value}/discounts` },
        { to: `/dashboard/${currentSiteId.value}/shipping` },
        { to: `/dashboard/${currentSiteId.value}/payments` },
      ]),
      children: [
        {
          label: 'คำสั่งซื้อ',
          icon: 'solar:cart-line-duotone',
          to: `/dashboard/${currentSiteId.value}/orders`,
        },
        {
          label: 'ลูกค้า',
          icon: 'solar:users-group-rounded-line-duotone',
          to: `/dashboard/${currentSiteId.value}/customers`,
        },
        {
          label: 'ส่วนลด',
          icon: 'solar:ticket-line-duotone',
          to: `/dashboard/${currentSiteId.value}/discounts`,
        },
        {
          label: 'การจัดส่ง',
          icon: 'solar:delivery-line-duotone',
          to: `/dashboard/${currentSiteId.value}/shipping`,
        },
        {
          label: 'การชำระเงิน',
          icon: 'solar:card-line-duotone',
          to: `/dashboard/${currentSiteId.value}/payments`,
        },
      ],
    },
    {
      label: 'การจัดการ',
      icon: 'solar:users-group-two-rounded-line-duotone',
      open: isChildActive([
        { to: `/dashboard/${currentSiteId.value}/teams` },
        { to: `/dashboard/${currentSiteId.value}/subscription` },
      ]),
      children: [
        {
          label: 'ทีมงาน',
          icon: 'solar:users-group-two-rounded-line-duotone',
          to: `/dashboard/${currentSiteId.value}/teams`,
        },
        {
          label: 'แพ็คเกจใช้งาน',
          icon: 'solar:card-line-duotone',
          to: `/dashboard/${currentSiteId.value}/subscription`,
        }
      ]
    } 
  ];
});

// Check if route is active
const isActiveRoute = (to: string) => {
  return route.path === to;
};
</script>
<template>
  <div class="flex flex-col h-full bg-[var(--ui-bg-2)]">

    <!-- Site Header -->
    <div class="p-3 relative">
      <div class="flex items-center gap-3">
        <div class="flex-shrink-0">
          <ClientOnly>
            <UiImage :src="imageUrl(currentSite?.seoSettings?.logo, { width: 50, height: 50, crop: 'fit' })"
              :alt="currentSite?.name || 'โลโก้เว็บไซต์'" width="50" height="50" rounded="lg"
              fallback-icon="solar:global-line-duotone" class="bg-gray-100 dark:bg-gray-800" />
          </ClientOnly>
        </div>
        <div class="flex-1 min-w-0">
          <h2 class="text-sm font-semibold text-gray-900 dark:text-white truncate">
            {{ currentSite?.fullDomain || 'กำลังโหลด...' }}
          </h2>
          <div class="flex items-center gap-2 mt-1">
            <span :class="[
              'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium',
              isActive
                ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
            ]">
              <span class="w-1.5 h-1.5 rounded-full mr-1" :class="isActive ? 'bg-green-400' : 'bg-red-400'"></span>
              {{ isActive ? 'เปิดใช้งาน' : 'ปิดใช้งาน' }}
            </span>
            <span v-if="isPremium"
              class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
              <Icon name="solar:crown-line-duotone" class="w-3 h-3 mr-1" />
              Premium
            </span>
          </div>
          <div class="absolute top-2 right-2">
            <template v-if="props.isMobile">
              <slot name="header" />
            </template>
          </div>
        </div>
      </div>
      <!-- Site Actions -->
      <div class="mt-3 flex gap-2">
        <UButton v-if="currentSite?.fullDomain" icon="solar:global-line-duotone"
          :to="`http://${currentSite?.fullDomain}`" target="_blank" size="xs" label="ดูเว็บไซต์" block />
        <UButton :to="`/dashboard/${currentSiteId}/settings`" size="xs" variant="outline"
          icon="solar:settings-line-duotone" label="ตั้งค่า" block />
      </div>
    </div>

    <!-- Navigation Menu -->
    <div :class="[
      'flex-1 p-1 overflow-y-auto',
      props.isMobile ? 'max-h-[calc(100vh-160px)]' : 'max-h-[calc(100vh-200px)]'
    ]">
      <UNavigationMenu :active="route.path" :items="navigationItems" orientation="vertical" variant="pill" color="primary" highlight />
    </div>

    <!-- Back Button Section -->
    <div class="p-3">
      <UButton block to="/dashboard" icon="solar:undo-left-line-duotone" label="ย้อนกลับ" />
    </div>
  </div>
</template>
