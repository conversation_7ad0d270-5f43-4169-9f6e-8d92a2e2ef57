<template>
  <div class="relative">
    <UInput
      v-model="searchQuery"
      placeholder="ค้นหา..."
      icon="i-heroicons-magnifying-glass"
      size="sm"
      :ui="{ 
        icon: { trailing: { pointer: '' } },
        wrapper: 'relative w-64'
      }"
      @keydown.enter="handleSearch"
      @input="handleInput"
    >
      <template #trailing>
        <UKbd v-if="!searchQuery" size="xs">⌘K</UKbd>
        <UButton
          v-else
          @click="clearSearch"
          variant="ghost"
          size="2xs"
          icon="i-heroicons-x-mark"
        />
      </template>
    </UInput>

    <!-- Search Results Dropdown -->
    <div
      v-if="showResults && (searchResults.length > 0 || isLoading)"
      class="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto"
    >
      <!-- Loading State -->
      <div v-if="isLoading" class="p-4 text-center">
        <Icon name="i-heroicons-arrow-path" class="w-5 h-5 animate-spin mx-auto text-gray-400" />
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">กำลังค้นหา...</p>
      </div>

      <!-- Search Results -->
      <div v-else-if="searchResults.length > 0" class="py-2">
        <div
          v-for="(group, groupName) in groupedResults"
          :key="groupName"
          class="mb-2 last:mb-0"
        >
          <div class="px-3 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            {{ getGroupTitle(groupName) }}
          </div>
          <NuxtLink
            v-for="result in group"
            :key="result.id"
            :to="result.link"
            class="flex items-center gap-3 px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            @click="handleResultClick(result)"
          >
            <Icon :name="getResultIcon(result.type)" class="w-4 h-4 text-gray-400" />
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                {{ result.title }}
              </p>
              <p v-if="result.description" class="text-xs text-gray-500 dark:text-gray-400 truncate">
                {{ result.description }}
              </p>
            </div>
          </NuxtLink>
        </div>
      </div>

      <!-- No Results -->
      <div v-else class="p-4 text-center">
        <Icon name="i-heroicons-magnifying-glass" class="w-8 h-8 text-gray-400 mx-auto mb-2" />
        <p class="text-sm text-gray-500 dark:text-gray-400">ไม่พบผลการค้นหา</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface SearchResult {
  id: string;
  title: string;
  description?: string;
  type: 'product' | 'order' | 'customer' | 'page' | 'blog' | 'setting';
  link: string;
}

const route = useRoute();
const router = useRouter();

// State
const searchQuery = ref('');
const searchResults = ref<SearchResult[]>([]);
const isLoading = ref(false);
const showResults = ref(false);

// Get current siteId
const currentSiteId = computed(() => route.params.siteId as string);

// Mock search function - should call API
const searchData = async (query: string): Promise<SearchResult[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));

  const mockData: SearchResult[] = [
    {
      id: '1',
      title: 'iPhone 15 Pro',
      description: 'สมาร์ทโฟนรุ่นล่าสุด',
      type: 'product',
      link: `/dashboard/${currentSiteId.value}/products/1`,
    },
    {
      id: '2',
      title: 'คำสั่งซื้อ #12345',
      description: 'สมชาย ใจดี - ฿25,000',
      type: 'order',
      link: `/dashboard/${currentSiteId.value}/orders/12345`,
    },
    {
      id: '3',
      title: 'สมหญิง รักดี',
      description: '<EMAIL>',
      type: 'customer',
      link: `/dashboard/${currentSiteId.value}/customers/3`,
    },
    {
      id: '4',
      title: 'วิธีเลือกซื้อสมาร์ทโฟน',
      description: 'บทความแนะนำการเลือกซื้อ',
      type: 'blog',
      link: `/dashboard/${currentSiteId.value}/blog/4`,
    },
    {
      id: '5',
      title: 'ตั้งค่าการชำระเงิน',
      description: 'จัดการวิธีการชำระเงิน',
      type: 'setting',
      link: `/dashboard/${currentSiteId.value}/settings/payments`,
    },
  ];

  return mockData.filter(
    item =>
      item.title.toLowerCase().includes(query.toLowerCase()) ||
      (item.description && item.description.toLowerCase().includes(query.toLowerCase()))
  );
};

// Group results by type
const groupedResults = computed(() => {
  const groups: Record<string, SearchResult[]> = {};

  searchResults.value.forEach(result => {
    if (!groups[result.type]) {
      groups[result.type] = [];
    }
    groups[result.type].push(result);
  });

  return groups;
});

// Debounced search
let searchTimeout: NodeJS.Timeout;

const handleInput = () => {
  clearTimeout(searchTimeout);

  if (searchQuery.value.trim().length < 2) {
    showResults.value = false;
    searchResults.value = [];
    return;
  }

  searchTimeout = setTimeout(async () => {
    isLoading.value = true;
    showResults.value = true;

    try {
      searchResults.value = await searchData(searchQuery.value);
    } catch (error) {
      console.error('Search error:', error);
      searchResults.value = [];
    } finally {
      isLoading.value = false;
    }
  }, 300);
};

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push(
      `/dashboard/${currentSiteId.value}/search?q=${encodeURIComponent(searchQuery.value)}`
    );
    showResults.value = false;
  }
};

const handleResultClick = (result: SearchResult) => {
  showResults.value = false;
  searchQuery.value = '';
};

const clearSearch = () => {
  searchQuery.value = '';
  showResults.value = false;
  searchResults.value = [];
};

// Helper functions
const getGroupTitle = (type: string) => {
  const titles = {
    product: 'สินค้า',
    order: 'คำสั่งซื้อ',
    customer: 'ลูกค้า',
    page: 'หน้าเพจ',
    blog: 'บล็อก',
    setting: 'ตั้งค่า',
  };
  return titles[type as keyof typeof titles] || type;
};

const getResultIcon = (type: SearchResult['type']) => {
  const icons = {
    product: 'i-heroicons-shopping-bag',
    order: 'i-heroicons-shopping-cart',
    customer: 'i-heroicons-user',
    page: 'i-heroicons-document',
    blog: 'i-heroicons-document-text',
    setting: 'i-heroicons-cog-6-tooth',
  };
  return icons[type];
};

// Close results when clicking outside
onMounted(() => {
  document.addEventListener('click', e => {
    const target = e.target as HTMLElement;
    if (!target.closest('.relative')) {
      showResults.value = false;
    }
  });
});

// Keyboard shortcuts
onMounted(() => {
  const handleKeydown = (e: KeyboardEvent) => {
    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
      e.preventDefault();
      const input = document.querySelector('input[placeholder="ค้นหา..."]') as HTMLInputElement;
      input?.focus();
    }
  };

  document.addEventListener('keydown', handleKeydown);

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown);
  });
});
</script>