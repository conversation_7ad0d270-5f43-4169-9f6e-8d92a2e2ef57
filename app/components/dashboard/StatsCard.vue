
<script setup lang="ts">
interface Props {
  title: string;
  value: number | string;
  icon: string;
  change?: number;
  description?: string;
  color?: 'primary' | 'green' | 'blue' | 'yellow' | 'red' | 'purple';
  showChart?: boolean;
  format?: 'number' | 'currency' | 'percentage';
}

const props = withDefaults(defineProps<Props>(), {
  color: 'primary',
  showChart: true,
  format: 'number',
});

// Format value based on type
const formattedValue = computed(() => {
  if (typeof props.value === 'string') return props.value;

  switch (props.format) {
    case 'currency':
      return new Intl.NumberFormat('th-TH', {
        style: 'currency',
        currency: 'THB',
      }).format(props.value);
    case 'percentage':
      return `${props.value}%`;
    default:
      return new Intl.NumberFormat('th-TH').format(props.value);
  }
});

// Icon color class
const iconColorClass = computed(() => {
  const colors = {
    primary: 'text-primary-600 dark:text-primary-400',
    green: 'text-green-600 dark:text-green-400',
    blue: 'text-blue-600 dark:text-blue-400',
    yellow: 'text-yellow-600 dark:text-yellow-400',
    red: 'text-red-600 dark:text-red-400',
    purple: 'text-purple-600 dark:text-purple-400',
  };
  return colors[props.color];
});

// Change color and icon
const changeColorClass = computed(() => {
  if (props.change === undefined) return '';

  return props.change >= 0
    ? 'text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/20'
    : 'text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900/20';
});

const changeIcon = computed(() => {
  if (props.change === undefined) return '';
  return props.change >= 0 ? 'i-heroicons-arrow-trending-up' : 'i-heroicons-arrow-trending-down';
});
</script>

<template>
  <UCard variant="soft">
    <div class="flex items-center justify-between">
      <div class="flex-1">
        <div class="flex items-center gap-2 mb-1">
          <Icon 
            :name="icon" 
            :class="[
              'w-5 h-5',
              iconColorClass
            ]"
          />
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
            {{ title }}
          </p>
        </div>
        
        <div class="flex items-baseline gap-2">
          <p class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ formattedValue }}
          </p>
          
          <div 
            v-if="change !== undefined"
            :class="[
              'flex items-center gap-1 text-xs font-medium px-2 py-1 rounded-full',
              changeColorClass
            ]"
          >
            <Icon 
              :name="changeIcon" 
              class="w-3 h-3"
            />
            <span>{{ Math.abs(change) }}%</span>
          </div>
        </div>
        
        <p v-if="description" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
          {{ description }}
        </p>
      </div>
      
      <!-- Chart or additional content -->
      <div v-if="showChart" class="ml-4">
        <div class="w-16 h-12 bg-gradient-to-r from-primary-100 to-primary-200 dark:from-primary-900/20 dark:to-primary-800/20 rounded-lg flex items-center justify-center">
          <Icon :name="icon" class="w-6 h-6 text-primary-600 dark:text-primary-400" />
        </div>
      </div>
    </div>
  </UCard>
</template>
