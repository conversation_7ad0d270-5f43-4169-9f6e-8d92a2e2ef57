<template>
  <nav class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-4">
    <NuxtLink
      to="/dashboard"
      class="hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
    >
      <Icon name="i-heroicons-home" class="w-4 h-4" />
    </NuxtLink>
    
    <template v-for="(item, index) in breadcrumbItems" :key="index">
      <Icon name="i-heroicons-chevron-right" class="w-4 h-4" />
      
      <NuxtLink
        v-if="item.to && index < breadcrumbItems.length - 1"
        :to="item.to"
        class="hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
      >
        {{ item.label }}
      </NuxtLink>
      
      <span
        v-else
        class="text-gray-900 dark:text-white font-medium"
      >
        {{ item.label }}
      </span>
    </template>
  </nav>
</template>

<script setup lang="ts">
interface BreadcrumbItem {
  label: string;
  to?: string;
}

interface Props {
  items?: BreadcrumbItem[];
}

const props = defineProps<Props>();
const route = useRoute();

// Auto-generate breadcrumb from route if not provided
const breadcrumbItems = computed(() => {
  if (props.items) return props.items;

  const pathSegments = route.path.split('/').filter(Boolean);
  const items: BreadcrumbItem[] = [];

  // Skip 'dashboard' segment
  const relevantSegments = pathSegments.slice(1);

  relevantSegments.forEach((segment, index) => {
    const isLast = index === relevantSegments.length - 1;
    const path = `/${pathSegments.slice(0, index + 2).join('/')}`;

    items.push({
      label: formatSegmentLabel(segment),
      to: isLast ? undefined : path,
    });
  });

  return items;
});

// Format segment label
const formatSegmentLabel = (segment: string) => {
  // Handle common dashboard routes
  const labels: Record<string, string> = {
    products: 'สินค้า',
    orders: 'คำสั่งซื้อ',
    customers: 'ลูกค้า',
    blog: 'บล็อก',
    pages: 'หน้าเพจ',
    settings: 'ตั้งค่า',
    analytics: 'สถิติ',
    reports: 'รายงาน',
    users: 'ผู้ใช้',
    teams: 'ทีม',
    roles: 'บทบาท',
    permissions: 'สิทธิ์',
    discounts: 'ส่วนลด',
    campaigns: 'แคมเปญ',
    themes: 'ธีม',
    plugins: 'ปลั๊กอิน',
    addons: 'แอปเสริม',
    shipping: 'การจัดส่ง',
    payments: 'การชำระเงิน',
    'email-marketing': 'อีเมลการตลาด',
    sms: 'SMS',
    seo: 'SEO',
    menus: 'เมนู',
    subscription: 'การสมัครสมาชิก',
    tracking: 'การติดตาม',
    logs: 'บันทึก',
    backups: 'การสำรองข้อมูล',
    maintenance: 'การบำรุงรักษา',
    api: 'API',
    create: 'สร้างใหม่',
    edit: 'แก้ไข',
    view: 'ดู',
  };

  return labels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
};
</script>