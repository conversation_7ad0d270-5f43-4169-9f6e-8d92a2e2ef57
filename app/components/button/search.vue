<script setup lang="ts">
import { ref } from 'vue'

const show = ref(false)
const value = ref({})

const groups = ref([
  {
    id: 'pages',
    label: 'หน้าเว็บไซต์',
    items: [
      {
        label: 'หน้าหลัก',
        icon: 'i-heroicons-home',
        onSelect: () => navigateTo('/dashboard')
      },
      {
        label: 'ผลิตภัณฑ์',
        icon: 'i-heroicons-shopping-bag',
        onSelect: () => navigateTo('/dashboard/products')
      },
      {
        label: 'ออเดอร์',
        icon: 'i-heroicons-shopping-cart',
        onSelect: () => navigateTo('/dashboard/orders')
      },
      {
        label: 'ลูกค้า',
        icon: 'i-heroicons-users',
        onSelect: () => navigateTo('/dashboard/customers')
      },
      {
        label: 'บล็อก',
        icon: 'i-heroicons-document-text',
        onSelect: () => navigateTo('/dashboard/blog')
      }
    ]
  },
  {
    id: 'actions',
    label: 'การดำเนินการ',
    items: [
      {
        label: 'เพิ่มผลิตภัณฑ์ใหม่',
        icon: 'i-heroicons-plus-circle',
        onSelect: () => navigateTo('/dashboard/products/create')
      },
      {
        label: 'สร้างบล็อกใหม่',
        icon: 'i-heroicons-document-plus',
        onSelect: () => navigateTo('/dashboard/blog/create')
      },
      {
        label: 'ตั้งค่าเว็บไซต์',
        icon: 'i-heroicons-cog-6-tooth',
        onSelect: () => navigateTo('/dashboard/settings')
      },
      {
        label: 'ดูรายงาน',
        icon: 'i-heroicons-chart-bar',
        onSelect: () => navigateTo('/dashboard/reports')
      }
    ]
  },
  {
    id: 'quick',
    label: 'ด่วน',
    items: [
      {
        label: 'ดูแจ้งเตือน',
        icon: 'i-heroicons-bell',
        onSelect: () => {
          // เปิด notification dropdown
          show.value = false
        }
      },
      {
        label: 'ข้อความแชท',
        icon: 'i-heroicons-chat-bubble-left-right',
        onSelect: () => {
          // เปิด chat dropdown
          show.value = false
        }
      },
      {
        label: 'เปลี่ยนธีม',
        icon: 'i-heroicons-moon',
        onSelect: () => {
          // toggle dark mode
          show.value = false
        }
      }
    ]
  }
])
</script>
<template>
  <UModal>
    <UButton label="Search users..." color="neutral" variant="subtle" icon="i-lucide-search">
      <!-- <Icon name="solar:card-search-line-duotone" class="w-6 h-6" /> -->
    </UButton>

    <template #content>
      <UCommandPalette v-model:search-term="searchTerm" :loading="status === 'pending'" :groups="groups"
        placeholder="ค้นหาเมนู, ผลิตภัณฑ์, ลูกค้า..." class="h-80" />
    </template>
  </UModal>
  <!-- <div class="relative">
    <button
      class="relative p-2 rounded-full hover:bg-neutral-100 dark:hover:bg-neutral-800 transition"
      @click="show = true"
      aria-label="Search"
      type="button"
    > 
      <Icon name="solar:card-search-line-duotone" class="w-6 h-6" />
    </button> -->

  <!-- Command Palette Modal -->
  <!-- <UModal v-model="show" :ui="{ width: 'sm:max-w-2xl' }">
      <UCommandPalette 
        v-model="value" 
        :groups="groups" 
        class="flex-1"
        placeholder="ค้นหาเมนู, ผลิตภัณฑ์, ลูกค้า..."
      />
    </UModal> -->
  <!-- </div> -->
</template>
<style scoped>
/* Custom styles if needed */
</style>
