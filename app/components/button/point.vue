<script setup lang="ts">
const props = defineProps<{ moneyPoint?: number; goldPoint?: number }>();

const money = computed(() => props.moneyPoint ?? 0);
// const gold = computed(() => props.goldPoint ?? 0);
</script>

<template>
  <!-- <div class="flex items-center gap-3">
    <div class="flex items-center gap-1 px-2 py-1 rounded bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
      <span class="i-heroicons-banknotes w-4 h-4 mr-1" />
      <span class="font-semibold">{{ money.toLocaleString() }}</span>
      <span class="text-xs ml-1">พ้อยท์เงิน</span>
    </div>
    <div class="flex items-center gap-1 px-2 py-1 rounded bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
      <span class="i-heroicons-trophy w-4 h-4 mr-1" />
      <span class="font-semibold">{{ gold.toLocaleString() }}</span>
      <span class="text-xs ml-1">พ้อยท์ทอง</span>
    </div>
  </div> -->
  <UTooltip text="เติมเงิน">
    <UButton color="primary" :label="money.toLocaleString() " icon="solar:wallet-money-bold-duotone" variant="soft" aria-label="Topup" to="/dashboard/topup" class="rounded-xl" />
  </UTooltip>
</template>