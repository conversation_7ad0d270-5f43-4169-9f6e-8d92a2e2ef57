

<script setup lang="ts"> 

const show = ref(false)
const chats = ref([
  { name: 'สมชาย', avatar: '🧑', message: 'สวัสดีครับ ขอสอบถามสินค้า', time: '1 นาทีที่แล้ว' },
  { name: 'คุณลูกค้า', avatar: '👩', message: 'ได้รับของแล้วค่ะ ขอบคุณค่ะ', time: '5 นาทีที่แล้ว' },
  { name: 'Admin', avatar: '🛒', message: 'แจ้งเตือน: ระบบจะอัปเดตคืนนี้', time: '30 นาทีที่แล้ว' },
  { name: 'Guest', avatar: '👤', message: 'ยังมีสินค้ารุ่นนี้ไหม?', time: '1 ชั่วโมงที่แล้ว' },
])

function toggleDropdown(e: Event) {
  e.stopPropagation()
  show.value = !show.value
}
</script>
<template>
  <div class="relative" v-click-outside="() => (show = false)">
    <button
      class="relative p-2 rounded-full hover:bg-neutral-100 dark:hover:bg-neutral-800 transition"
      @click="toggleDropdown"
      aria-label="Chat Messages"
      type="button"
    >
      <!-- Chat Icon -->
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12.75c0 3.727 3.978 6.75 8.75 6.75.98 0 1.93-.09 2.83-.***********.11 1.17.11 1.24 0 2.41-.37 3.34-**********.9.44 ********** 0 1.5-.67 1.5-1.5 0-.53-.27-1-.68-1.28.13-.41.2-.85.2-1.3 0-3.727-3.978-6.75-8.75-6.75S2.25 9.023 2.25 12.75z" />
      </svg>
      <!-- Badge -->
      <span v-if="chats.length" class="absolute -top-1 -right-1 bg-error text-white text-xs rounded-full px-1.5 py-0.5 border border-white dark:border-neutral-900">
        {{ chats.length }}
      </span>
    </button>
    <!-- Dropdown -->
    <transition name="fade">
      <div
        v-if="show"
        class="absolute right-0 mt-2 w-80 bg-white dark:bg-neutral-900 shadow-lg rounded-lg overflow-hidden z-50 border border-neutral-200 dark:border-neutral-800"
      >
        <div class="p-4 border-b border-neutral-100 dark:border-neutral-800 font-semibold">
          ข้อความแชทล่าสุด
        </div>
        <ul>
          <li
            v-for="(chat, idx) in chats"
            :key="idx"
            class="px-4 py-3 hover:bg-neutral-50 dark:hover:bg-neutral-800 cursor-pointer text-sm border-b last:border-b-0 border-neutral-100 dark:border-neutral-800 flex gap-3"
          >
            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-neutral-200 dark:bg-neutral-700 flex items-center justify-center text-lg">
              {{ chat.avatar }}
            </div>
            <div class="flex-1 min-w-0">
              <div class="font-medium truncate">{{ chat.name }}</div>
              <div class="text-xs text-neutral-500 truncate">{{ chat.message }}</div>
            </div>
            <div class="text-xs text-neutral-400 whitespace-nowrap self-start mt-0.5">{{ chat.time }}</div>
          </li>
        </ul>
        <div v-if="!chats.length" class="p-4 text-center text-neutral-400 text-sm">
          ไม่มีข้อความใหม่
        </div>
        <div class="p-2 text-center bg-neutral-50 dark:bg-neutral-800">
          <button class="text-primary text-xs hover:underline" type="button">ดูทั้งหมด</button>
        </div>
      </div>
    </transition>
  </div>
</template>
<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.15s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
