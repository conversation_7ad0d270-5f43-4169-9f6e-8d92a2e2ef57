
<script setup lang="ts">
const colorMode = useColorMode();
const isDark = computed(() => colorMode.value === "dark");

// สร้าง Broadcast Channel สำหรับโหมดสี (เฉพาะ Client-side)
const colorModeChannel = import.meta.client
	? new BroadcastChannel("color_mode_channel")
	: null;

function toggleColorMode() {
	const newMode = isDark.value ? "light" : "dark";
	colorMode.preference = newMode;

	// ส่งโหมดสีใหม่ผ่าน Broadcast Channel ไปยังแท็บอื่น
	if (import.meta.client && colorModeChannel) {
		colorModeChannel.postMessage({ type: "colorModeChange", mode: newMode });
	}
}

// ฟัง Listeners สำหรับ Broadcast Channel เพื่อ sync โหมดสีระหว่างแท็บ
if (import.meta.client && colorModeChannel) {
	colorModeChannel.onmessage = (event) => {
		console.log("Color Mode Broadcast Channel message received:", event.data);
		const { type, mode } = event.data;

		if (type === "colorModeChange" && mode) {
			// อัปเดตโหมดสีในแท็บปัจจุบัน
			colorMode.preference = mode;
		}
	};

	// เพิ่ม clean-up listener เมื่อ component ถูก unmounted
	onUnmounted(() => {
		colorModeChannel.close(); // ปิด channel เมื่อ component ไม่ได้ใช้งาน
	});
}
</script>

<template>
    <!-- <ClientOnly> -->
      <UButton 
        color="neutral"
        variant="ghost"
        aria-label="Toggle theme"
        @click="toggleColorMode"
        class="rounded-full"
       >
       <template #leading>
        <UIcon
        :name="!isDark ? 'line-md:sunny-outline-to-moon-alt-loop-transition' : 'line-md:moon-alt-to-sunny-outline-loop-transition'"
        class="size-7"
      /> 
      </template>
    </UButton>
    <!-- </ClientOnly> -->
  </template>
  