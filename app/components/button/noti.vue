

<script setup lang="ts"> 

const show = ref(false)
const notifications = ref([
  { title: 'มีออเดอร์ใหม่เข้ามา', time: '2 นาทีที่แล้ว' },
  { title: 'ลูกค้าโอนเงินแล้ว', time: '10 นาทีที่แล้ว' },
  { title: 'ระบบจะปิดปรับปรุงคืนนี้', time: '1 ชั่วโมงที่แล้ว' },
  { title: 'แพ็คเกจของคุณใกล้หมดอายุ', time: 'เมื่อวานนี้' },
])

function toggleDropdown(e: Event) {
  e.stopPropagation()
  show.value = !show.value
}
</script>
<template>
  <div class="relative" v-click-outside="() => (show = false)">
    <button
      class="relative p-2 rounded-full hover:bg-neutral-100 dark:hover:bg-neutral-800 transition"
      @click="toggleDropdown"
      aria-label="Notifications"
    >
      <!-- Bell Icon -->
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
        <path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a3.001 3.001 0 01-5.714 0M21 19.5a1.5 1.5 0 01-1.5-1.5V11a7.5 7.5 0 10-15 0v7a1.5 1.5 0 01-1.5 1.5h18z" />
      </svg>
      <!-- Badge -->
      <span v-if="notifications.length" class="absolute -top-1 -right-1 bg-error text-white text-xs rounded-full px-1.5 py-0.5 border border-white dark:border-neutral-900">
        {{ notifications.length }}
      </span>
    </button>
    <!-- Dropdown -->
    <transition name="fade">
      <div
        v-if="show"
        class="absolute right-0 mt-2 w-72 bg-white dark:bg-neutral-900 shadow-lg rounded-lg overflow-hidden z-50 border border-neutral-200 dark:border-neutral-800"
      >
        <div class="p-4 border-b border-neutral-100 dark:border-neutral-800 font-semibold">
          แจ้งเตือน
        </div>
        <ul>
          <li
            v-for="(noti, idx) in notifications"
            :key="idx"
            class="px-4 py-3 hover:bg-neutral-50 dark:hover:bg-neutral-800 cursor-pointer text-sm border-b last:border-b-0 border-neutral-100 dark:border-neutral-800"
          >
            <div class="font-medium">{{ noti.title }}</div>
            <div class="text-xs text-neutral-500 mt-0.5">{{ noti.time }}</div>
          </li>
        </ul>
        <div v-if="!notifications.length" class="p-4 text-center text-neutral-400 text-sm">
          ไม่มีแจ้งเตือน
        </div>
        <div class="p-2 text-center bg-neutral-50 dark:bg-neutral-800">
          <button class="text-primary text-xs hover:underline">ดูทั้งหมด</button>
        </div>
      </div>
    </transition>
  </div>
</template>
<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.15s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
