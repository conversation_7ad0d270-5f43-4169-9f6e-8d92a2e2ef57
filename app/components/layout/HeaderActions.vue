<script setup lang="ts">
import { useAuthStore } from '~/stores/auth'

// Props
interface Props {
    // User data (optional - will use authStore if not provided)
    user?: {
        avatar?: string
        firstName?: string
        lastName?: string
        email?: string
        moneyPoint?: number
    }

    // Custom user menu items (optional)
    customMenuItems?: any[]

    // Avatar configuration
    avatarSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'

    // Show/hide specific buttons
    showMoneyPoint?: boolean
    showLanguage?: boolean
    showTheme?: boolean

    // User menu configuration
    menuSize?: 'sm' | 'md' | 'lg'
    showVerifiedBadge?: boolean
    dropdownAlign?: 'start' | 'center' | 'end'
    dropdownWidth?: string
}

const props = withDefaults(defineProps<Props>(), {
    avatarSize: 'sm',
    showMoneyPoint: true,
    showLanguage: true,
    showTheme: true,
    menuSize: 'lg',
    showVerifiedBadge: true,
    dropdownAlign: 'end',
    dropdownWidth: 'w-36 min-w-fit'
})

// Composables
const authStore = useAuthStore()

// Computed properties
const currentUser: any = computed(() => {
    return props.user || authStore.userDetail
})
</script>
<template>
    <div class="flex flex-row gap-2 items-center justify-end">
        <ButtonSearch />
        <ButtonNoti />
        <ButtonChat />
        <!-- Money Point Button -->
        <ButtonPoint v-if="showMoneyPoint" :moneyPoint="currentUser?.moneyPoint || authStore?.userDetail?.moneyPoint" />

        <!-- Language Button -->
        <ButtonLang v-if="showLanguage" />

        <!-- Theme Button -->
        <ButtonTheme hydrate-on-visible v-if="showTheme" />

        <!-- User Menu -->
        <LayoutUserMenu :user="currentUser" :menu-items="customMenuItems" :avatar-size="avatarSize" :size="menuSize"
            :show-verified-badge="showVerifiedBadge" :dropdown-align="dropdownAlign" :dropdown-width="dropdownWidth" />
    </div>
</template>
<style scoped>
/* Custom styles if needed */
</style>