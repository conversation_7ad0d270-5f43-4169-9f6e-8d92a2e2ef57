<script setup lang="ts">
import { useAuthStore } from '~/stores/auth'
import { useMyImage } from '~/composables/useMyImage'

// Import useMyImage for avatar
const { imageUrl } = useMyImage()
// Props
interface Props {
    // User data (optional - will use authStore if not provided)
    user?: {
        avatar?: string
        firstName?: string
        lastName?: string
        email?: string
        name?: string
    }

    // Menu configuration
    menuItems?: any[]
    size?: 'sm' | 'md' | 'lg'

    // Avatar configuration
    avatarSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
    fallbackIcon?: string
    avatarClass?: string

    // UI configuration
    showVerifiedBadge?: boolean
    chipInset?: boolean
    dropdownAlign?: 'start' | 'center' | 'end'
    dropdownSide?: 'top' | 'bottom' | 'left' | 'right'
    dropdownWidth?: string
}

const props = withDefaults(defineProps<Props>(), {
    size: 'lg',
    avatarSize: 'sm',
    fallbackIcon: 'solar:user-circle-bold-duotone',
    showVerifiedBadge: true,
    chipInset: true,
    dropdownAlign: 'end',
    dropdownSide: 'bottom',
    dropdownWidth: 'w-36 min-w-fit'
})

// Composables
const authStore = useAuthStore()

// Computed properties
const currentUser = computed(() => {
    return authStore.userDetail || props.user
})

const userDisplayName = computed(() => {
    const user = currentUser.value
    if (!user) return 'ผู้ใช้'

    return user.firstName ||
        user.email ||
        'ผู้ใช้'
})

const userInitial = computed(() => {
    const user = currentUser.value
    if (!user) return 'U'

    const name = user.firstName ||
        user.email ||
        'U'
    return name.charAt(0).toUpperCase()
})

const dropdownContent = computed(() => ({
    align: props.dropdownAlign,
    side: props.dropdownSide,
    sideOffset: 8
}))

const dropdownUI = computed(() => ({
    content: props.dropdownWidth
}))

// Methods - ย้ายมาไว้ก่อน computed ที่ใช้งาน
const handleLogout = async () => {
    try {
        await authStore.doLogout()
        await navigateTo('/signin')
    } catch (error) {
        console.error('Logout error:', error)
    }
}

// Default menu items
const defaultMenuItems = computed(() => [
    [{
        label: userDisplayName.value,
        icon: 'solar:letter-line-duotone',
        to: '/dashboard/profile',
        slot: 'profile'
    }],
    [{
        label: 'การตั้งค่า',
        icon: 'i-heroicons-cog-6-tooth',
        to: '/dashboard/settings'
    }, {
        label: 'ช่วยเหลือ',
        icon: 'i-heroicons-question-mark-circle',
        to: '/help'
    }],
    [{
        label: 'ออกจากระบบ',
        icon: 'i-heroicons-arrow-right-on-rectangle',
        click: () => handleLogout()
    }]
])

const menuItems = computed(() => {
    return props.menuItems || defaultMenuItems.value
})
</script>
<template>
    <UDropdownMenu :items="menuItems" :ui="dropdownUI" :content="dropdownContent">
        <button role="button" tabindex="0"
            class="cursor-pointer rounded-full hover:ring-2 hover:ring-primary-500 transition-all">
            <ClientOnly>
                <UiImage :src="imageUrl(currentUser?.avatar, {
                    width: avatarSize === 'xs' ? 24 : avatarSize === 'sm' ? 32 : avatarSize === 'md' ? 40 : avatarSize === 'lg' ? 48 : 64,
                    height: avatarSize === 'xs' ? 24 : avatarSize === 'sm' ? 32 : avatarSize === 'md' ? 40 : avatarSize === 'lg' ? 48 : 64,
                    crop: 'thumb',
                    quality: 80
                })" :alt="userDisplayName" :fallback-icon="fallbackIcon" :fallback-text="userInitial"
                    class="rounded-full" :image-class="avatarClass" />
            </ClientOnly>
        </button>

        <template #profile-trailing>
            <UIcon v-if="showVerifiedBadge" name="solar:verified-check-bold" class="shrink-0 size-5 text-primary" />
        </template>
    </UDropdownMenu>
</template>

<style scoped>
/* Custom styles if needed */
</style>