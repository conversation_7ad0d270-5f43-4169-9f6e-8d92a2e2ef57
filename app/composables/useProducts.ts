
import type {
  Cart,
  Order,
  Product,
  ProductAnalytics,
  ProductFilters,
} from '../../shared/types/product';
import type { ApiResponse } from '../../shared/types/respone';

export const useProducts = () => {

  // Get products with filters
  const getProducts = async (
    siteId: string,
    options: {
      page?: number;
      limit?: number;
      status?: string;
      type?: string;
      category?: string;
      search?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}) => {
    try {
      const response = await $fetch(`/api/v1/product/${siteId}`, {
        query: {
          siteId,
          ...options,
        },
      });
      return response;
    } catch (error: any) {
      console.error('Error fetching teams:', error);
      throw error;
    }
  };

  // Get single product
  const getProduct = async (productId: string, siteId: string) => {
    try {
      const response = await $fetch(`/api/v1/product/${productId}`, {
        query: { siteId },
      });
      return response;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw error;
    }
  };

  // Create product
  const createProduct = async (siteId: string, productData: any) => {
    try {
      const response = await $fetch('/api/v1/product', {
        method: 'POST',
        body: { ...productData, siteId },
      });
      return response;
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  };

  // Update product
  const updateProduct = async (productId: string, siteId: string, productData: any) => {
    try {
      const response = await $fetch(`/api/v1/product/${productId}`, {
        method: 'PUT',
        body: { ...productData, siteId },
      });
      return response;
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  };

  // Delete product
  const deleteProduct = async (productId: string, siteId: string) => {
    try {
      const response = await $fetch(`/api/v1/product/${productId}`, {
        method: 'DELETE',
        body: { siteId },
      });
      return response;
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  };

  // Publish product
  const publishProduct = async (productId: string, siteId: string) => {
    try {
      const response = await $fetch(`/api/v1/product/${productId}/publish`, {
        method: 'POST',
        body: { siteId },
      });
      return response;
    } catch (error) {
      console.error('Error publishing product:', error);
      throw error;
    }
  };

  // Unpublish product
  const unpublishProduct = async (productId: string, siteId: string) => {
    try {
      const response = await $fetch(`/api/v1/product/${productId}/unpublish`, {
        method: 'POST',
        body: { siteId },
      });
      return response;
    } catch (error) {
      console.error('Error unpublishing product:', error);
      throw error;
    }
  };

  // Update inventory
  const updateInventory = async (
    productId: string,
    siteId: string,
    inventoryData: {
      quantity: number;
      lowStockThreshold?: number;
      trackInventory?: boolean;
    }
  ) => {
    try {
      const response = await $fetch(`/api/v1/product/${productId}/inventory`, {
        method: 'PUT',
        body: { ...inventoryData, siteId },
      });
      return response;
    } catch (error) {
      console.error('Error updating inventory:', error);
      throw error;
    }
  };

  // Add product review
  const addProductReview = async (
    productId: string,
    siteId: string,
    reviewData: {
      userId: string;
      rating: number;
      title?: string;
      comment?: string;
    }
  ) => {
    try {
      const response = await $fetch(`/api/v1/product/${productId}/reviews?siteId=${siteId}`, {
        method: 'POST',
        body: reviewData,
      });
      return response;
    } catch (error: any) {
      console.error('Error adding review:', error);
      throw error;
    }
  };

  // Update product analytics
  const updateProductAnalytics = async (
    productId: string,
    siteId: string,
    analytics: {
      views?: number;
      sales?: number;
      revenue?: number;
    }
  ) => {
    try {
      const response = await $fetch(`/api/v1/product/${productId}/analytics?siteId=${siteId}`, {
        method: 'PUT',
        body: analytics,
      });
      return response;
    } catch (error: any) {
      console.error('Error updating analytics:', error);
      throw error;
    }
  };

  // Get product categories
  const getProductCategories = async (siteId: string) => {
    try {
      const response = await $fetch('/api/v1/product/categories', {
        query: { siteId },
      });
      return response;
    } catch (error) {
      console.error('Error fetching product categories:', error);
      throw error;
    }
  };

  // Get product tags
  const getProductTags = async (siteId: string) => {
    try {
      const response = await $fetch('/api/v1/product/tags', {
        query: { siteId },
      });
      return response;
    } catch (error) {
      console.error('Error fetching product tags:', error);
      throw error;
    }
  };

  // Get featured products
  const getFeaturedProducts = async (siteId: string, limit = 10) => {
    try {
      const response = await $fetch('/api/v1/product/featured', {
        query: { siteId, limit },
      });
      return response;
    } catch (error) {
      console.error('Error fetching featured products:', error);
      throw error;
    }
  };

  // Get low stock products
  const getLowStockProducts = async (siteId: string, limit = 10) => {
    try {
      const response = await $fetch('/api/v1/product/low-stock', {
        query: { siteId, limit },
      });
      return response;
    } catch (error) {
      console.error('Error fetching low stock products:', error);
      throw error;
    }
  };

  // Get product reviews
  const getProductReviews = async (productId: string, siteId: string, page = 1, limit = 10) => {
    const response = await $fetch(
      `/api/v1/product/${productId}/reviews?siteId=${siteId}&page=${page}&limit=${limit}`,
      {
        method: 'GET',
      }
    );
    return response;
  };

  // Get product analytics
  const getProductAnalytics = async (productId: string, siteId: string, period = '30d') => {
    const response = await $fetch(
      `/api/v1/product/${productId}/analytics?siteId=${siteId}&period=${period}`,
      {
        method: 'GET',
      }
    );
    return response;
  };

  return {
    getProducts,
    getProduct,
    createProduct,
    updateProduct,
    deleteProduct,
    publishProduct,
    unpublishProduct,
    updateInventory,
    addProductReview,
    updateProductAnalytics,
    getProductCategories,
    getProductTags,
    getFeaturedProducts,
    getLowStockProducts,
    getProductReviews,
    getProductAnalytics,
  };
};
