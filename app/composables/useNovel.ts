export const useNovel = () => {
  const { $apiFetch } = useNuxtApp() as any;

  // ดึงรายการนิยาย
  const getNovels = async (siteId: string, params: any = {}) => {
    try {
      const response = await $apiFetch(`/api/v1/novel?siteId=${siteId}`, {
        method: 'GET',
        params,
      });
      return response;
    } catch (error) {
      console.error('Error fetching novels:', error);
      throw error;
    }
  };

  // ดึงนิยายตาม ID
  const getNovelById = async (siteId: string, novelId: string) => {
    try {
      const response = await $apiFetch(`/api/v1/novel/${novelId}?siteId=${siteId}`, {
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Error fetching novel by ID:', error);
      throw error;
    }
  };

  // สร้างนิยายใหม่
  const createNovel = async (siteId: string, data: any) => {
    try {
      const response = await $apiFetch(`/api/v1/novel?siteId=${siteId}`, {
        method: 'POST',
        body: data,
      });
      return response;
    } catch (error) {
      console.error('Error creating novel:', error);
      throw error;
    }
  };

  // อัปเดตนิยาย
  const updateNovel = async (siteId: string, novelId: string, data: any) => {
    try {
      const response = await $apiFetch(`/api/v1/novel/${novelId}?siteId=${siteId}`, {
        method: 'PUT',
        body: data,
      });
      return response;
    } catch (error) {
      console.error('Error updating novel:', error);
      throw error;
    }
  };

  // ลบนิยาย
  const deleteNovel = async (siteId: string, novelId: string) => {
    try {
      const response = await $apiFetch(`/api/v1/novel/${novelId}?siteId=${siteId}`, {
        method: 'DELETE',
      });
      return response;
    } catch (error) {
      console.error('Error deleting novel:', error);
      throw error;
    }
  };

  // ดึงสถิตินิยาย
  const getNovelStats = async (siteId: string) => {
    try {
      const response = await $apiFetch(`/api/v1/novel/stats?siteId=${siteId}`, {
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Error fetching novel stats:', error);
      throw error;
    }
  };

  // ดึงรายการตอน
  const getChapters = async (siteId: string, novelId: string, params: any = {}) => {
    try {
      const response = await $apiFetch(`/api/v1/novel/${novelId}/chapters?siteId=${siteId}`, {
        method: 'GET',
        params,
      });
      return response;
    } catch (error) {
      console.error('Error fetching chapters:', error);
      throw error;
    }
  };

  // ดึงตอนตาม ID
  const getChapterById = async (siteId: string, novelId: string, chapterId: string) => {
    try {
      const response = await $apiFetch(
        `/api/v1/novel/${novelId}/chapters/${chapterId}?siteId=${siteId}`,
        {
          method: 'GET',
        }
      );
      return response;
    } catch (error) {
      console.error('Error fetching chapter by ID:', error);
      throw error;
    }
  };

  // สร้างตอนใหม่
  const createChapter = async (siteId: string, novelId: string, data: any) => {
    try {
      const response = await $apiFetch(`/api/v1/novel/${novelId}/chapters?siteId=${siteId}`, {
        method: 'POST',
        body: data,
      });
      return response;
    } catch (error) {
      console.error('Error creating chapter:', error);
      throw error;
    }
  };

  // อัปเดตตอน
  const updateChapter = async (siteId: string, novelId: string, chapterId: string, data: any) => {
    try {
      const response = await $apiFetch(
        `/api/v1/novel/${novelId}/chapters/${chapterId}?siteId=${siteId}`,
        {
          method: 'PUT',
          body: data,
        }
      );
      return response;
    } catch (error) {
      console.error('Error updating chapter:', error);
      throw error;
    }
  };

  // ลบตอน
  const deleteChapter = async (siteId: string, novelId: string, chapterId: string) => {
    try {
      const response = await $apiFetch(
        `/api/v1/novel/${novelId}/chapters/${chapterId}?siteId=${siteId}`,
        {
          method: 'DELETE',
        }
      );
      return response;
    } catch (error) {
      console.error('Error deleting chapter:', error);
      throw error;
    }
  };

  // ไลค์นิยาย
  const likeNovel = async (novelId: string) => {
    try {
      const response = await $apiFetch(`/api/v1/novel/${novelId}/like`, {
        method: 'POST',
      });
      return response;
    } catch (error) {
      console.error('Error liking novel:', error);
      throw error;
    }
  };

  // เพิ่มบุ๊คมาร์ค
  const bookmarkNovel = async (novelId: string) => {
    try {
      const response = await $apiFetch(`/api/v1/novel/${novelId}/bookmark`, {
        method: 'POST',
      });
      return response;
    } catch (error) {
      console.error('Error bookmarking novel:', error);
      throw error;
    }
  };

  // ให้คะแนนนิยาย
  const rateNovel = async (novelId: string, rating: number, review?: string) => {
    try {
      const response = await $apiFetch(`/api/v1/novel/${novelId}/rate`, {
        method: 'POST',
        body: { rating, review },
      });
      return response;
    } catch (error) {
      console.error('Error rating novel:', error);
      throw error;
    }
  };

  return {
    getNovels,
    getNovelById,
    createNovel,
    updateNovel,
    deleteNovel,
    getNovelStats,
    getChapters,
    getChapterById,
    createChapter,
    updateChapter,
    deleteChapter,
    likeNovel,
    bookmarkNovel,
    rateNovel,
  };
};
