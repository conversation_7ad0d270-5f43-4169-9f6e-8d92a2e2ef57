export const useSession = () => {
  const authStore = useAuthStore();

  // Get current user from session
  const getCurrentUser = async () => {
    try {
      const response = await $fetch('/api/v1/user/profile');
      console.log('response User', response);
      return response.data || null;
    } catch (error: any) {
      console.error('Failed to get current user:', error);

      // ถ้าเป็น 401 error ให้ clear auth
      if (error?.statusCode === 401 || error?.status === 401) {
        console.log('401 error in getCurrentUser, clearing auth');
        await authStore.clearAuth();
      }

      throw error;
    }
  };

  // Refresh session
  const refreshSession = async () => {
    try {
      const response: any = await $fetch<ApiResponse<{ user: User }>>('/api/v1/auth/refresh', {
        method: 'POST',
      });
      console.log('response Refresh Session', response);
      return response?.success || false;
    } catch (error: any) {
      console.error('Failed to refresh session:', error);

      // ถ้า refresh ล้มเหลวให้ clear auth
      if (error?.statusCode === 401 || error?.status === 401) {
        console.log('401 error in refreshSession, clearing auth');
        await authStore.clearAuth();
      }

      throw error;
    }
  };

  // Logout
  const logout = async (skipApiCall = false) => {
    try {
      // ถ้าไม่ skip API call ให้เรียก signout API
      if (!skipApiCall) {
        await $fetch('/api/v1/auth/signout', {
          method: 'POST',
        });
      }
      return true;
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if API call fails
      return false;
    }
  };

  // Check if user is authenticated
  const isAuthenticated = () => {
    return authStore.isAuthenticated;
  };

  // Get current user from store
  const getUser = () => {
    return authStore.getUser;
  };

  return {
    getCurrentUser,
    refreshSession,
    logout,
    isAuthenticated,
    getUser,
  };
};
