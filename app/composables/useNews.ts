export const useNews = () => {
  const { $apiFetch } = useNuxtApp() as any;

  // ดึงรายการข่าว
  const getNews = async (siteId: string, params: any = {}) => {
    try {
      const response = await $apiFetch(`/api/v1/news?siteId=${siteId}`, {
        method: 'GET',
        params,
      });
      return response;
    } catch (error) {
      console.error('Error fetching news:', error);
      throw error;
    }
  };

  // ดึงข่าวตาม ID
  const getNewsById = async (siteId: string, newsId: string) => {
    try {
      const response = await $apiFetch(`/api/v1/news/${newsId}?siteId=${siteId}`, {
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Error fetching news by ID:', error);
      throw error;
    }
  };

  // สร้างข่าวใหม่
  const createNews = async (siteId: string, newsData: any) => {
    try {
      const response = await $apiFetch(`/api/v1/news?siteId=${siteId}`, {
        method: 'POST',
        body: newsData,
      });
      return response;
    } catch (error) {
      console.error('Error creating news:', error);
      throw error;
    }
  };

  // อัปเดตข่าว
  const updateNews = async (newsId: string, siteId: string, newsData: any) => {
    try {
      const response = await $apiFetch(`/api/v1/news/${newsId}?siteId=${siteId}`, {
        method: 'PUT',
        body: newsData,
      });
      return response;
    } catch (error) {
      console.error('Error updating news:', error);
      throw error;
    }
  };

  // ลบข่าว
  const deleteNews = async (newsId: string, siteId: string) => {
    try {
      const response = await $apiFetch(`/api/v1/news/${newsId}?siteId=${siteId}`, {
        method: 'DELETE',
      });
      return response;
    } catch (error) {
      console.error('Error deleting news:', error);
      throw error;
    }
  };

  // ดึงสถิติข่าว
  const getNewsStats = async (siteId: string, period?: string) => {
    const params = new URLSearchParams({ siteId });
    if (period) params.append('period', period);

    const response = await $apiFetch(`/api/v1/news/stats?${params.toString()}`, {
      method: 'GET',
    });
    return response;
  };

  // ดึงข่าวล่าสุด
  const getLatestNews = async (siteId: string, limit = 5) => {
    try {
      const response = await $apiFetch(`/api/v1/news/latest?siteId=${siteId}&limit=${limit}`, {
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Error fetching latest news:', error);
      throw error;
    }
  };

  // ดึงข่าวด่วน
  const getUrgentNews = async (siteId: string, limit = 3) => {
    try {
      const response = await $apiFetch(`/api/v1/news/urgent?siteId=${siteId}&limit=${limit}`, {
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Error fetching urgent news:', error);
      throw error;
    }
  };

  // กดไลค์ข่าว
  const likeNews = async (newsId: string, siteId: string) => {
    try {
      const response = await $apiFetch(`/api/v1/news/${newsId}/like`, {
        method: 'POST',
        body: { siteId },
      });
      return response;
    } catch (error) {
      console.error('Error liking news:', error);
      throw error;
    }
  };

  // แชร์ข่าว
  const shareNews = async (newsId: string, siteId: string, platform: string) => {
    try {
      const response = await $apiFetch(`/api/v1/news/${newsId}/share`, {
        method: 'POST',
        body: { siteId, platform },
      });
      return response;
    } catch (error) {
      console.error('Error sharing news:', error);
      throw error;
    }
  };

  return {
    getNews,
    getNewsById,
    createNews,
    updateNews,
    deleteNews,
    getNewsStats,
    getLatestNews,
    getUrgentNews,
    likeNews,
    shareNews,
  };
};
