export const useAddons = () => {
  // Types
  interface AddonFilters {
    search?: string;
    category?: string;
    status?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    page?: number;
    limit?: number;
  }

  // Get addons list
  const getAddons = async (siteId: string, filters?: AddonFilters) => {
    try {
      const params = new URLSearchParams();
      if (filters?.search) params.append('search', filters.search);
      if (filters?.category && filters.category !== 'all')
        params.append('category', filters.category);
      if (filters?.status && filters.status !== 'all') params.append('status', filters.status);
      if (filters?.sortBy) params.append('sortBy', filters.sortBy);
      if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const queryString = params.toString();
      const url = `/v1/site/${siteId}/addons${queryString ? `?${queryString}` : ''}`;

      return await $fetch(url);
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถโหลดข้อมูลแอดออนได้'
      );
    }
  };

  // Get single addon
  const getAddon = async (siteId: string, addonId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/addons/${addonId}`);
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถโหลดข้อมูลแอดออนได้'
      );
    }
  };

  // Install addon
  const installAddon = async (siteId: string, addonId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/addons/${addonId}/install`, {
        method: 'POST',
      });
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถติดตั้งแอดออนได้'
      );
    }
  };

  // Uninstall addon
  const uninstallAddon = async (siteId: string, addonId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/addons/${addonId}/uninstall`, {
        method: 'POST',
        body: {
          userId: 'test-user', // ใช้ test-user เป็น default
        },
      });
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถถอนการติดตั้งแอดออนได้'
      );
    }
  };

  // Update addon
  const updateAddon = async (siteId: string, addonId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/addons/${addonId}/update`, {
        method: 'POST',
      });
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถอัปเดตแอดออนได้'
      );
    }
  };

  // Configure addon
  const configureAddon = async (
    siteId: string,
    addonId: string,
    config: Record<string, unknown>
  ) => {
    try {
      return await $fetch(`/v1/site/${siteId}/addons/${addonId}/configure`, {
        method: 'POST',
        body: config,
      });
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถกำหนดค่าแอดออนได้'
      );
    }
  };

  // Get addon analytics
  const getAddonAnalytics = async (siteId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/addons/analytics`);
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถโหลดข้อมูลสถิติได้'
      );
    }
  };

  return {
    getAddons,
    getAddon,
    installAddon,
    uninstallAddon,
    updateAddon,
    configureAddon,
    getAddonAnalytics,
  };
};
