export const useSite = () => {
  const config = useRuntimeConfig();

  // Update site settings
  const updateSiteSettings = async (siteId: string, settings: any) => {
    try {
      const response = await $fetch(`/api/v1/site/${siteId}/settings`, {
        method: 'PUT',
        body: settings,
      });
      return response;
    } catch (error: any) {
      console.error('Error updating site settings:', error);
      throw error;
    }
  };

  // Update maintenance mode
  const updateMaintenanceMode = async (siteId: string, maintenance: any) => {
    try {
      const response = await updateSiteSettings(siteId, {
        status: maintenance.isEnabled ? 'maintenance' : 'published',
        settings: {
          maintenance: {
            isEnabled: maintenance.isEnabled,
            message: maintenance.message,
            allowedIPs: maintenance.allowedIPs,
            scheduledStart: maintenance.scheduledStart,
            scheduledEnd: maintenance.scheduledEnd,
            showMaintenancePage: maintenance.showMaintenancePage,
          },
        },
      });
      return response;
    } catch (error: any) {
      console.error('Error updating maintenance mode:', error);
      throw error;
    }
  };

  return {
    updateSiteSettings,
    updateMaintenanceMode,
  };
};
