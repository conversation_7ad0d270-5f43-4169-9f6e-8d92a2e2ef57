// Global flag เพื่อป้องกัน infinite loop
let isHandlingAuthError = false;

export const useAuthError = () => {
    const authStore = useAuthStore();
    const router = useRouter();

    // ฟังก์ชันจัดการ authentication errors
    const handleAuthError = async (error: any) => {
        // ป้องกัน infinite loop
        if (isHandlingAuthError) {
            console.log('Already handling auth error, skipping...');
            return;
        }

        console.log('Handling auth error:', error);

        // ตรวจสอบว่าเป็น authentication error หรือไม่
        if (error?.statusCode === 401) {
            isHandlingAuthError = true;

            try {
                console.log('Authentication error detected, clearing session...');

                // ล้าง auth store โดยไม่เรียก API logout (เพื่อป้องกัน loop)
                await authStore.clearAuth();

            } finally {
                // Reset flag หลังจาก handle เสร็จ
                setTimeout(() => {
                    isHandlingAuthError = false;
                }, 1000);
            }
        }
    };

    // ฟังก์ชันสำหรับ wrap API calls ที่อาจมี auth errors
    const withAuthErrorHandling = async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
        try {
            return await apiCall();
        } catch (error) {
            await handleAuthError(error);
            return null;
        }
    };

    return {
        handleAuthError,
        withAuthErrorHandling,
    };
};