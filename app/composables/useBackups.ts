export const useBackups = () => {
  // Types
  interface BackupForm {
    name?: string;
    description: string;
    type: 'full' | 'database' | 'files';
  }

  interface BackupFilters {
    search?: string;
    type?: string;
    status?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    page?: number;
    limit?: number;
  }

  // Get backups list
  const getBackups = async (siteId: string, filters?: BackupFilters) => {
    try {
      const params = new URLSearchParams();
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, String(value));
          }
        });
      }
      const url = `/v1/site/${siteId}/backups?${params.toString()}`;
      return await $fetch(url);
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถโหลดข้อมูลการสำรองได้'
      );
    }
  };

  // Get single backup
  const getBackup = async (siteId: string, backupId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/backups/${backupId}`);
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถโหลดข้อมูลการสำรองได้'
      );
    }
  };

  // Create backup
  const createBackup = async (siteId: string, data: BackupForm) => {
    try {
      return await $fetch(`/v1/site/${siteId}/backups`, {
        method: 'POST',
        body: data,
      });
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถสร้างการสำรองข้อมูลได้'
      );
    }
  };

  // Delete backup
  const deleteBackup = async (siteId: string, backupId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/backups/${backupId}`, {
        method: 'DELETE',
      });
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถลบการสำรองข้อมูลได้'
      );
    }
  };

  // Download backup
  const downloadBackup = async (siteId: string, backupId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/backups/${backupId}/download`, {
        method: 'GET',
      });
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถดาวน์โหลดการสำรองข้อมูลได้'
      );
    }
  };

  // Restore backup
  const restoreBackup = async (siteId: string, backupId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/backups/${backupId}/restore`, {
        method: 'POST',
      });
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถกู้คืนข้อมูลได้'
      );
    }
  };

  // Get backup status
  const getBackupStatus = async (siteId: string, backupId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/backups/${backupId}/status`);
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถตรวจสอบสถานะการสำรองได้'
      );
    }
  };

  // Get backup analytics
  const getBackupAnalytics = async (siteId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/backups/analytics`);
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถโหลดข้อมูลสถิติได้'
      );
    }
  };

  return {
    getBackups,
    getBackup,
    createBackup,
    deleteBackup,
    downloadBackup,
    restoreBackup,
    getBackupStatus,
    getBackupAnalytics,
  };
};
