import type { ApiResponse } from '../../shared/types/respone';

export const useBlog = () => {
  const { $apiFetch } = useNuxtApp() as unknown as { $apiFetch: typeof $fetch };

  // Get blog posts
  const getPosts = async (
    siteId: string,
    filters?: {
      category?: string;
      search?: string;
      status?: string;
      page?: number;
      limit?: number;
    }
  ) => {
    const params = new URLSearchParams({ siteId });
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }

    return await $apiFetch(`/v1/blog?${params.toString()}`);
  };

  // Get single blog post
  const getPost = async (postId: string, siteId: string) => {
    return await $apiFetch(`/v1/blog/${postId}?siteId=${siteId}`);
  };

  // Create blog post
  const createPost = async (siteId: string, postData: Record<string, unknown>) => {
    return await $apiFetch(`/v1/blog?siteId=${siteId}`, {
      method: 'POST',
      body: postData,
    });
  };

  // Update blog post
  const updatePost = async (postId: string, siteId: string, postData: Record<string, unknown>) => {
    return await $apiFetch(`/v1/blog/${postId}?siteId=${siteId}`, {
      method: 'PUT',
      body: postData,
    });
  };

  // Delete blog post
  const deletePost = async (postId: string, siteId: string) => {
    return await $apiFetch(`/v1/blog/${postId}?siteId=${siteId}`, {
      method: 'DELETE',
    });
  };

  // Get blog analytics
  const getBlogAnalytics = async (siteId: string, period?: string) => {
    const params = new URLSearchParams({ siteId });
    if (period) params.append('period', period);

    return await $apiFetch(`/v1/blog/analytics?${params.toString()}`);
  };

  // Get blog categories
  const getCategories = async (siteId: string) => {
    return await $apiFetch(`/v1/blog/categories?siteId=${siteId}`);
  };

  // Get blog tags
  const getTags = async (siteId: string) => {
    return await $apiFetch(`/v1/blog/tags?siteId=${siteId}`);
  };

  return {
    getPosts,
    getPost,
    createPost,
    updatePost,
    deletePost,
    getBlogAnalytics,
    getCategories,
    getTags,
  };
};
