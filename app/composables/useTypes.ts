import type { ApiResponse } from '../../shared/types/respone';
import type { Type, CreateTypeData, UpdateTypeData, TypeAnalytics } from '../../shared/types/type';

export const useTypes = () => {
  // ดึงรายการประเภททั้งหมด
  const getTypes = async (params: {
    siteId: string;
    page?: number;
    limit?: number;
    status?: string;
    contentType?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: string;
  }) => {
    return await $fetch<ApiResponse<{
      types: Type[];
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    }>>('/api/v1/types', {
      method: 'GET',
      query: params,
    });
  };

  // ดึงข้อมูลประเภทตาม ID
  const getType = async (typeId: string, siteId: string) => {
    return await $fetch<ApiResponse<Type>>(`/api/v1/types/${typeId}`, {
      method: 'GET',
      query: { siteId },
    });
  };

  // สร้างประเภทใหม่
  const createType = async (siteId: string, typeData: CreateTypeData) => {
    return await $fetch<ApiResponse<Type>>('/api/v1/types', {
      method: 'POST',
      query: { siteId },
      body: typeData,
    });
  };

  // อัปเดตประเภท
  const updateType = async (typeId: string, siteId: string, typeData: UpdateTypeData) => {
    return await $fetch<ApiResponse<Type>>(`/api/v1/types/${typeId}`, {
      method: 'PUT',
      query: { siteId },
      body: typeData,
    });
  };

  // ลบประเภท
  const deleteType = async (typeId: string, siteId: string) => {
    return await $fetch<ApiResponse<void>>(`/api/v1/types/${typeId}`, {
      method: 'DELETE',
      query: { siteId },
    });
  };

  // อัปเดตสถานะประเภท
  const updateTypeStatus = async (typeId: string, siteId: string, status: string) => {
    return await $fetch<ApiResponse<Type>>(`/api/v1/types/${typeId}`, {
      method: 'PUT',
      query: { siteId },
      body: { status },
    });
  };

  // ดึงประเภทตามประเภทเนื้อหา
  const getTypesByContentType = async (siteId: string, contentType: string) => {
    return await $fetch<ApiResponse<Type[]>>('/api/v1/types', {
      method: 'GET',
      query: { 
        siteId, 
        contentType,
        status: 'active',
        limit: 100 
      },
    });
  };

  // ตรวจสอบ slug ซ้ำ
  const checkSlugAvailability = async (siteId: string, slug: string, excludeId?: string) => {
    return await $fetch<ApiResponse<{ available: boolean }>>('/api/v1/types/check-slug', {
      method: 'POST',
      query: { siteId },
      body: { slug, excludeId },
    });
  };

  // ดึงสถิติการใช้งานประเภท
  const getTypeAnalytics = async (siteId: string) => {
    return await $fetch<ApiResponse<TypeAnalytics>>('/api/v1/types/analytics', {
      method: 'GET',
      query: { siteId },
    });
  };

  // คัดลอกประเภท
  const duplicateType = async (typeId: string, siteId: string, newName: string) => {
    return await $fetch<ApiResponse<Type>>(`/api/v1/types/${typeId}/duplicate`, {
      method: 'POST',
      query: { siteId },
      body: { newName },
    });
  };

  // นำเข้าประเภท
  const importTypes = async (siteId: string, typesData: CreateTypeData[]) => {
    return await $fetch<ApiResponse<{ imported: number; errors: any[] }>>('/api/v1/types/import', {
      method: 'POST',
      query: { siteId },
      body: { types: typesData },
    });
  };

  // ส่งออกประเภท
  const exportTypes = async (siteId: string, typeIds?: string[]) => {
    return await $fetch<ApiResponse<{ types: Type[] }>>('/api/v1/types/export', {
      method: 'POST',
      query: { siteId },
      body: { typeIds },
    });
  };

  return {
    getTypes,
    getType,
    createType,
    updateType,
    deleteType,
    updateTypeStatus,
    getTypesByContentType,
    checkSlugAvailability,
    getTypeAnalytics,
    duplicateType,
    importTypes,
    exportTypes,
  };
}; 