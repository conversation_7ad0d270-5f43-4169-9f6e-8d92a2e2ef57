export const useCampaigns = () => {
  // Types
  interface CampaignForm {
    name: string;
    description: string;
    type: 'promotional' | 'seasonal' | 'loyalty' | 'referral' | 'launch';
    status: 'active' | 'inactive' | 'completed' | 'draft';
    targetParticipants: number;
    startDate: string;
    endDate: string;
    budget: number;
  }

  interface CampaignFilters {
    search?: string;
    status?: string;
    type?: string;
    dateRange?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    page?: number;
    limit?: number;
  }

  // Get campaigns list
  const getCampaigns = async (siteId: string, filters?: CampaignFilters) => {
    try {
      const params = new URLSearchParams();
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, String(value));
          }
        });
      }
      const url = `/v1/site/${siteId}/campaigns?${params.toString()}`;
      return await $fetch(url);
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถโหลดข้อมูลแคมเปญได้'
      );
    }
  };

  // Get single campaign
  const getCampaign = async (siteId: string, campaignId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/campaigns/${campaignId}`);
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถโหลดข้อมูลแคมเปญได้'
      );
    }
  };

  // Create campaign
  const createCampaign = async (siteId: string, data: CampaignForm) => {
    try {
      return await $fetch(`/v1/site/${siteId}/campaigns`, {
        method: 'POST',
        body: data,
      });
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถสร้างแคมเปญได้'
      );
    }
  };

  // Update campaign
  const updateCampaign = async (siteId: string, campaignId: string, data: CampaignForm) => {
    try {
      return await $fetch(`/v1/site/${siteId}/campaigns/${campaignId}`, {
        method: 'PUT',
        body: data,
      });
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถอัปเดตแคมเปญได้'
      );
    }
  };

  // Delete campaign
  const deleteCampaign = async (siteId: string, campaignId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/campaigns/${campaignId}`, {
        method: 'DELETE',
      });
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถลบแคมเปญได้'
      );
    }
  };

  // Start campaign
  const startCampaign = async (siteId: string, campaignId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/campaigns/${campaignId}/start`, {
        method: 'POST',
      });
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถเริ่มแคมเปญได้'
      );
    }
  };

  // Stop campaign
  const stopCampaign = async (siteId: string, campaignId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/campaigns/${campaignId}/stop`, {
        method: 'POST',
      });
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถหยุดแคมเปญได้'
      );
    }
  };

  // Duplicate campaign
  const duplicateCampaign = async (siteId: string, campaignId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/campaigns/${campaignId}/duplicate`, {
        method: 'POST',
      });
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถคัดลอกแคมเปญได้'
      );
    }
  };

  // Get campaign analytics
  const getCampaignAnalytics = async (siteId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/campaigns/analytics`);
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถโหลดข้อมูลสถิติได้'
      );
    }
  };

  return {
    getCampaigns,
    getCampaign,
    createCampaign,
    updateCampaign,
    deleteCampaign,
    startCampaign,
    stopCampaign,
    duplicateCampaign,
    getCampaignAnalytics,
  };
};
