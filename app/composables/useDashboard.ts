export const useDashboard = () => {
  // Sidebar state
  const sidebarOpen = ref(false);
  const sidebarCollapsed = ref(false);

  // Current site state
  const currentSiteId = ref<string | null>(null);
  const currentSite = ref<any>(null);

  // Loading states
  const loading = ref(false);
  const sitesLoading = ref(false);

  // Sites data
  const sites = ref<any[]>([]);

  // Dashboard stats
  const dashboardStats = ref({
    totalSites: 0,
    totalProducts: 0,
    totalPosts: 0,
    totalMembers: 0,
    totalOrders: 0,
    totalRevenue: 0,
  });

  // Toggle sidebar
  const toggleSidebar = () => {
    sidebarOpen.value = !sidebarOpen.value;
  };

  // Close sidebar
  const closeSidebar = () => {
    sidebarOpen.value = false;
  };

  // Toggle sidebar collapse (for desktop)
  const toggleSidebarCollapse = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value;
  };

  // Set current site
  const setCurrentSite = (siteId: string) => {
    currentSiteId.value = siteId;
    currentSite.value = sites.value.find(site => site.id === siteId) || null;
  };

  // Load sites
  const loadSites = async () => {
    sitesLoading.value = true;
    try {
      // Mock API call - replace with actual API
      await new Promise(resolve => setTimeout(resolve, 1000));

      sites.value = [
        {
          id: 'site1',
          name: 'เว็บไซต์หลัก',
          domain: 'main.example.com',
          status: 'published',
          description: 'เว็บไซต์หลักของบริษัท',
          createdAt: new Date('2024-01-01'),
          content: {
            pages: Array(5).fill(null),
            products: Array(12).fill(null),
            blog: Array(8).fill(null),
          },
          subscription: { plan: 'premium' },
        },
        {
          id: 'site2',
          name: 'ร้านค้าออนไลน์',
          domain: 'shop.example.com',
          status: 'published',
          description: 'ร้านค้าออนไลน์สำหรับขายสินค้า',
          createdAt: new Date('2024-02-01'),
          content: {
            pages: Array(8).fill(null),
            products: Array(25).fill(null),
            blog: Array(15).fill(null),
          },
          subscription: { plan: 'business' },
        },
        {
          id: 'site3',
          name: 'บล็อกส่วนตัว',
          domain: 'blog.example.com',
          status: 'draft',
          description: 'บล็อกส่วนตัวสำหรับเขียนบทความ',
          createdAt: new Date('2024-03-01'),
          content: {
            pages: Array(3).fill(null),
            products: Array(0).fill(null),
            blog: Array(20).fill(null),
          },
          subscription: { plan: 'free' },
        },
      ];

      return { success: true, data: { sites: sites.value } };
    } catch (error) {
      console.error('Error loading sites:', error);
      return { success: false, error };
    } finally {
      sitesLoading.value = false;
    }
  };

  // Load dashboard stats
  const loadDashboardStats = async (siteId?: string) => {
    loading.value = true;
    try {
      // Mock API call - replace with actual API
      await new Promise(resolve => setTimeout(resolve, 500));

      dashboardStats.value = {
        totalSites: sites.value.length,
        totalProducts: sites.value.reduce(
          (sum, site) => sum + (site.content?.products?.length || 0),
          0
        ),
        totalPosts: sites.value.reduce((sum, site) => sum + (site.content?.blog?.length || 0), 0),
        totalMembers: 156,
        totalOrders: 89,
        totalRevenue: 125000,
      };

      return { success: true, data: dashboardStats.value };
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
      return { success: false, error };
    } finally {
      loading.value = false;
    }
  };

  // Get navigation items based on current site
  const getNavigationItems = (siteId?: string) => {
    if (!siteId) {
      return [
        {
          items: [
            {
              label: 'เลือกเว็บไซต์',
              icon: 'solar:home-angle-line-duotone',
              to: '/dashboard',
            },
          ],
        },
      ];
    }

    return [
      {
        items: [
          {
            label: 'หน้าแรก',
            icon: 'solar:home-angle-line-duotone',
            to: `/dashboard/${siteId}`,
          },
        ],
      },
      {
        title: 'จัดการเนื้อหา',
        items: [
          {
            label: 'เว็บไซต์',
            icon: 'solar:global-line-duotone',
            to: `/dashboard/${siteId}/sites`,
          },
          {
            label: 'สินค้า',
            icon: 'solar:bag-line-duotone',
            to: `/dashboard/${siteId}/products`,
          },
          {
            label: 'บล็อก',
            icon: 'solar:document-text-line-duotone',
            to: `/dashboard/${siteId}/blog`,
          },
        ],
      },
      {
        title: 'การจัดการ',
        items: [
          {
            label: 'ทีม',
            icon: 'solar:users-group-two-rounded-line-duotone',
            to: `/dashboard/${siteId}/teams`,
          },
          {
            label: 'ผู้ใช้',
            icon: 'solar:user-line-duotone',
            to: `/dashboard/${siteId}/users`,
          },
        ],
      },
      {
        title: 'วิเคราะห์',
        items: [
          {
            label: 'สถิติ',
            icon: 'solar:chart-line-duotone',
            to: `/dashboard/${siteId}/analytics`,
          },
        ],
      },
    ];
  };

  // Get page title based on route
  const getPageTitle = (path: string, siteId?: string) => {
    if (!siteId) return 'เลือกเว็บไซต์';

    if (path.includes('/analytics')) return 'สถิติ';
    if (path.includes('/blog')) return 'บล็อก';
    if (path.includes('/products')) return 'สินค้า';
    if (path.includes('/sites')) return 'เว็บไซต์';
    if (path.includes('/teams')) return 'ทีม';
    if (path.includes('/users')) return 'ผู้ใช้';

    return 'หน้าแรก';
  };

  // Check if route is active
  const isActiveRoute = (to: string, currentPath: string) => {
    return currentPath === to || currentPath.startsWith(to + '/');
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB',
    }).format(amount);
  };

  // Format date
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('th-TH');
  };

  // Get status color for badges
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'success';
      case 'draft':
        return 'warning';
      case 'maintenance':
        return 'info';
      case 'suspended':
        return 'error';
      default:
        return 'neutral';
    }
  };

  // Get status label
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'published':
        return 'เผยแพร่แล้ว';
      case 'draft':
        return 'ฉบับร่าง';
      case 'maintenance':
        return 'ปรับปรุง';
      case 'suspended':
        return 'ระงับ';
      default:
        return 'ไม่ทราบ';
    }
  };

  // Responsive breakpoint detection
  const isMobile = ref(false);
  const isTablet = ref(false);
  const isDesktop = ref(false);

  // Update breakpoints
  const updateBreakpoints = () => {
    if (process.client) {
      const width = window.innerWidth;
      isMobile.value = width < 768;
      isTablet.value = width >= 768 && width < 1024;
      isDesktop.value = width >= 1024;
    }
  };

  // Initialize breakpoints on mount
  onMounted(() => {
    updateBreakpoints();
    window.addEventListener('resize', updateBreakpoints);
  });

  // Cleanup on unmount
  onUnmounted(() => {
    if (process.client) {
      window.removeEventListener('resize', updateBreakpoints);
    }
  });

  return {
    // State
    sidebarOpen,
    sidebarCollapsed,
    currentSiteId,
    currentSite,
    loading,
    sitesLoading,
    sites,
    dashboardStats,
    isMobile,
    isTablet,
    isDesktop,

    // Methods
    toggleSidebar,
    closeSidebar,
    toggleSidebarCollapse,
    setCurrentSite,
    loadSites,
    loadDashboardStats,
    getNavigationItems,
    getPageTitle,
    isActiveRoute,
    formatCurrency,
    formatDate,
    getStatusColor,
    getStatusLabel,
    updateBreakpoints,
  };
};
