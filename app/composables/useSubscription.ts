export const useSubscription = () => {
  const { $toast } = useNuxtApp() as any;
  const router = useRouter();

  const loadingPackages = ref(false)
  const packages = ref<any[]>([])
  const selectedPlan = ref('monthly')
  const selectedPackage = computed(() => packages.value.find(p => p.id === selectedPlan.value))
  const originalPrice = computed(() => selectedPackage.value?.price || 0)

  const fetchPackages = async () => {
    loadingPackages.value = true
    try {
      const response: any = await $fetch('/api/v1/subscription/packages')
      if (response?.data?.packages) {
        packages.value = response.data.packages.map((pkg: any) => ({
          ...pkg,
          price: pkg.moneyPoint,
          period: pkg.days === 36500 ? '' : `${pkg.days} วัน`,
          popular: pkg.id === 'yearly',
        }))
      }
    } catch (error: any) {
      // ให้ Toast ไปอยู่ที่ component ที่ใช้
    } finally {
      loadingPackages.value = false
    }
  }

  // ตรวจสอบว่า subscription หมดอายุหรือไม่
  const isSubscriptionExpired = (subscription: any): boolean => {
    if (!subscription) return false;

    // ตรวจสอบจาก endDate
    if (subscription.endDate) {
      return new Date() > new Date(subscription.endDate);
    }

    // ตรวจสอบจาก currentPeriodEnd
    if (subscription.currentPeriodEnd) {
      return new Date() > new Date(subscription.currentPeriodEnd);
    }

    // ตรวจสอบจาก status
    if (subscription.status === 'expired' || subscription.status === 'past_due') {
      return true;
    }

    return false;
  };

  // ตรวจสอบว่า subscription จะหมดอายุในอีก 7 วันหรือไม่
  const isSubscriptionExpiringSoon = (subscription: any): boolean => {
    if (!subscription) return false;

    const sevenDaysFromNow = new Date();
    sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);

    if (subscription.endDate) {
      const endDate = new Date(subscription.endDate);
      return endDate <= sevenDaysFromNow && endDate > new Date();
    }

    if (subscription.currentPeriodEnd) {
      const endDate = new Date(subscription.currentPeriodEnd);
      return endDate <= sevenDaysFromNow && endDate > new Date();
    }

    return false;
  };

  // แสดงการแจ้งเตือนเมื่อ subscription หมดอายุ
  const showExpiredNotification = (siteName: string) => {
    $toast.add({
      title: 'การสมัครสมาชิกหมดอายุ',
      description: `เว็บไซต์ "${siteName}" หมดอายุแล้ว กรุณาต่ออายุการใช้งานเพื่อใช้งานต่อ`,
      color: 'error',
      timeout: 0, // ไม่หมดอายุอัตโนมัติ
      actions: [
        {
          label: 'ต่ออายุ',
          click: () => router.push('/dashboard/subscription/renew'),
        },
        {
          label: 'ปิด',
          click: () => { },
        },
      ],
    });
  };

  // แสดงการแจ้งเตือนเมื่อ subscription จะหมดอายุเร็วๆ นี้
  const showExpiringSoonNotification = (siteName: string, daysLeft: number) => {
    $toast.add({
      title: 'การสมัครสมาชิกจะหมดอายุเร็วๆ นี้',
      description: `เว็บไซต์ "${siteName}" จะหมดอายุในอีก ${daysLeft} วัน กรุณาต่ออายุการใช้งาน`,
      color: 'warning',
      timeout: 10000, // 10 วินาที
      actions: [
        {
          label: 'ต่ออายุ',
          click: () => router.push('/dashboard/subscription/renew'),
        },
      ],
    });
  };

  // ตรวจสอบและแสดงการแจ้งเตือน subscription
  const checkAndNotifySubscription = (site: any) => {
    if (!site || !site.subscription) return;

    if (isSubscriptionExpired(site.subscription)) {
      showExpiredNotification(site.name);
      // Redirect ไปหน้าต่ออายุหลังจาก 3 วินาที
      setTimeout(() => {
        router.push(`/dashboard/${site._id}/subscription/renew`);
      }, 3000);
      return;
    }

    if (isSubscriptionExpiringSoon(site.subscription)) {
      const endDate = new Date(site.subscription.endDate || site.subscription.currentPeriodEnd);
      const daysLeft = Math.ceil(
        (endDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      );
      showExpiringSoonNotification(site.name, daysLeft);
    }
  };

  // ตรวจสอบ subscription status สำหรับหลายเว็บไซต์
  const checkMultipleSitesSubscription = (sites: any[]) => {
    sites.forEach(site => {
      checkAndNotifySubscription(site);
    });
  };

  // ตรวจสอบ subscription ก่อนเข้าถึงหน้า dashboard
  const checkSubscriptionBeforeAccess = (siteId: string) => {
    // ในอนาคตควรเรียก API เพื่อดึงข้อมูล subscription ล่าสุด
    // สำหรับตอนนี้จะใช้ข้อมูลจาก store หรือ context
    return true;
  };

  return {
    loadingPackages,
    packages,
    selectedPlan,
    selectedPackage,
    originalPrice,
    fetchPackages,
    isSubscriptionExpired,
    isSubscriptionExpiringSoon,
    showExpiredNotification,
    showExpiringSoonNotification,
    checkAndNotifySubscription,
    checkMultipleSitesSubscription,
    checkSubscriptionBeforeAccess,
  };
};
