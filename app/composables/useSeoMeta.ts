export interface SeoMetaOptions {
  title?: string | ComputedRef<string>;
  description?: string | ComputedRef<string>;
  keywords?: string | ComputedRef<string>;
  image?: string | ComputedRef<string>;
  url?: string | ComputedRef<string>;
  author?: string | ComputedRef<string>;
  publishedTime?: string | ComputedRef<string>;
  modifiedTime?: string | ComputedRef<string>;
  section?: string | ComputedRef<string>;
  tags?: string[] | ComputedRef<string[]>;
  noIndex?: boolean;
  noFollow?: boolean;
  canonical?: string | ComputedRef<string>;
  locale?: string;
  siteName?: string | ComputedRef<string>;
  twitterSite?: string;
  twitterCreator?: string;
}

export const useCustomSeoMeta = (options: SeoMetaOptions = {}) => {
  const { locale, t } = useI18n();
  const route = useRoute();
  const runtimeConfig = useRuntimeConfig();

  // ฟังก์ชันสำหรับแปลงค่าที่อาจเป็น ComputedRef
  const resolveValue = <T>(value: T | ComputedRef<T>): T => {
    return isRef(value) ? unref(value) : value;
  };

  // ค่าเริ่มต้นตามภาษา - ใช้ i18n keys
  const getDefaultValues = () => {
    return {
      title: t('seo.default.title', 'ระบบเช่าใช้งานเว็บไซต์ครบวงจร'),
      description: t('seo.default.description', 'ระบบเช่าใช้งานเว็บไซต์ครบวงจร'),
      keywords: t('seo.default.keywords', 'dashboard, is1, vue, management, admin'),
      siteName: t('seo.default.title', 'ระบบเช่าใช้งานเว็บไซต์ครบวงจร'),
    };
  };

  const defaults = computed(() => getDefaultValues());
  const defaultImage = '/images/logo.avif';

  // สร้าง meta object แบบ reactive
  const meta = computed(() => {
    const {
      title,
      description,
      keywords,
      image,
      url,
      author,
      publishedTime,
      modifiedTime,
      section,
      tags,
      noIndex = false,
      noFollow = false,
      canonical,
      siteName,
      twitterSite,
      twitterCreator,
    } = options;

    // แปลงค่าทั้งหมดให้เป็น reactive
    const currentUrl = resolveValue(url) || `${runtimeConfig.public.siteUrl}${route.path}`;
    const currentTitle = resolveValue(title) || defaults.value.title;
    const currentDescription = resolveValue(description) || defaults.value.description;
    const currentKeywords = resolveValue(keywords) || defaults.value.keywords;
    const currentImage = resolveValue(image) || `${runtimeConfig.public.siteUrl}${defaultImage}`;
    const currentSiteName = resolveValue(siteName) || defaults.value.siteName;
    const currentAuthor = resolveValue(author);
    const currentPublishedTime = resolveValue(publishedTime);
    const currentModifiedTime = resolveValue(modifiedTime);
    const currentSection = resolveValue(section);
    const currentTags = resolveValue(tags);

    const metaArray = [
      // Basic meta tags
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      { name: 'format-detection', content: 'telephone=no' },

      // SEO meta tags
      { name: 'title', content: currentTitle },
      { name: 'description', content: currentDescription },
      { name: 'keywords', content: currentKeywords },
      ...(currentAuthor ? [{ name: 'author', content: currentAuthor }] : []),

      // Open Graph
      { property: 'og:title', content: currentTitle },
      { property: 'og:description', content: currentDescription },
      { property: 'og:image', content: currentImage },
      { property: 'og:url', content: currentUrl },
      { property: 'og:type', content: 'website' },
      { property: 'og:locale', content: options.locale || locale.value },
      { property: 'og:site_name', content: currentSiteName },

      // Twitter Card
      { name: 'twitter:card', content: 'summary_large_image' },
      { name: 'twitter:title', content: currentTitle },
      { name: 'twitter:description', content: currentDescription },
      { name: 'twitter:image', content: currentImage },
      ...(twitterSite ? [{ name: 'twitter:site', content: twitterSite }] : []),
      ...(twitterCreator ? [{ name: 'twitter:creator', content: twitterCreator }] : []),

      // Article specific meta
      ...(publishedTime
        ? [
            {
              property: 'article:published_time',
              content: currentPublishedTime,
            },
            ...(currentModifiedTime
              ? [
                  {
                    property: 'article:modified_time',
                    content: currentModifiedTime,
                  },
                ]
              : []),
            ...(currentSection ? [{ property: 'article:section', content: currentSection }] : []),
            ...(currentAuthor ? [{ property: 'article:author', content: currentAuthor }] : []),
            ...(currentTags?.map(tag => ({
              property: 'article:tag',
              content: tag,
            })) || []),
          ]
        : []),

      // Robots meta
      {
        name: 'robots',
        content: `${noIndex ? 'noindex' : 'index'},${noFollow ? 'nofollow' : 'follow'}`,
      },
    ].filter(meta => meta.content !== undefined && meta.content !== null && meta.content !== '');

    return metaArray;
  });

  // ใช้ useHead เพื่อตั้งค่า SEO แบบ reactive
  useHead({
    title: computed(() => resolveValue(options.title) || defaults.value.title),
    htmlAttrs: {
      lang: options.locale || locale.value,
      dir: 'ltr',
    },
    meta: meta.value,
  });

  // ใช้ useSeoMeta ของ Nuxt
  useSeoMeta({
    title: computed(() => resolveValue(options.title) || defaults.value.title),
    description: computed(() => resolveValue(options.description) || defaults.value.description),
    keywords: computed(() => resolveValue(options.keywords) || defaults.value.keywords),
    ogTitle: computed(() => resolveValue(options.title) || defaults.value.title),
    ogDescription: computed(() => resolveValue(options.description) || defaults.value.description),
    ogImage: computed(
      () => resolveValue(options.image) || `${runtimeConfig.public.siteUrl}${defaultImage}`
    ),
    ogUrl: computed(
      () => resolveValue(options.url) || `${runtimeConfig.public.siteUrl}${route.path}`
    ),
    ogType: 'website',
    ogLocale: options.locale || locale.value,
    ogSiteName: computed(() => resolveValue(options.siteName) || defaults.value.siteName),
    twitterCard: 'summary_large_image',
    twitterTitle: computed(() => resolveValue(options.title) || defaults.value.title),
    twitterDescription: computed(
      () => resolveValue(options.description) || defaults.value.description
    ),
    twitterImage: computed(
      () => resolveValue(options.image) || `${runtimeConfig.public.siteUrl}${defaultImage}`
    ),
    twitterSite: options.twitterSite,
    twitterCreator: options.twitterCreator,
    robots: computed(() => {
      const { noIndex = false, noFollow = false } = options;
      return `${noIndex ? 'noindex' : 'index'},${noFollow ? 'nofollow' : 'follow'}`;
    }),
  });

  // ตั้งค่า canonical URL
  if (options.canonical || route.path) {
    useHead({
      link: [
        {
          rel: 'canonical',
          href: computed(
            () => resolveValue(options.canonical) || `${runtimeConfig.public.siteUrl}${route.path}`
          ),
        },
      ],
    });
  }

  return {
    meta: readonly(meta),
    updateSeo: (newOptions: Partial<SeoMetaOptions>) => {
      Object.assign(options, newOptions);
    },
  };
};

// Composable สำหรับหน้า Dashboard
export const useDashboardSeo = (pageTitle?: string, pageDescription?: string) => {
  const { t } = useI18n();

  const getTitle = () => {
    if (!pageTitle) return t('dashboard.title', 'Dashboard');
    return `${pageTitle} - ${t('dashboard.title', 'Dashboard')}`;
  };

  const getDescription = () => {
    if (!pageDescription)
      return t(
        'dashboard.description',
        'Manage your website, products, and analytics from your dashboard'
      );
    return pageDescription;
  };

  return useCustomSeoMeta({
    title: getTitle(),
    description: getDescription(),
    noIndex: false,
  });
};

// Composable สำหรับหน้า Product
export const useProductSeo = (
  productName: string,
  productDescription: string,
  productImage?: string
) => {
  const { t } = useI18n();

  const getTitle = () => {
    return `${productName} - ${t('products.title', 'Products')}`;
  };

  return useCustomSeoMeta({
    title: getTitle(),
    description: productDescription,
    image: productImage,
    noIndex: false,
  });
};

// Composable สำหรับหน้า Blog/Article
export const useArticleSeo = (
  title: string,
  description: string,
  author: string,
  publishedTime: string,
  image?: string,
  tags?: string[]
) => {
  const { t } = useI18n();

  const getTitle = () => {
    return `${title} - ${t('blog.title', 'Blog')}`;
  };

  return useCustomSeoMeta({
    title: getTitle(),
    description,
    image,
    author,
    publishedTime,
    tags,
    noIndex: false,
  });
};

// Composable สำหรับหน้า Login/Auth
export const useAuthSeo = (isLogin = true) => {
  const { t } = useI18n();

  const getTitle = computed(() => {
    if (isLogin) {
      return t('auth.signin.title', 'Sign In - Dashboard');
    }
    return t('auth.signup.title', 'Sign Up - Dashboard');
  });

  const getDescription = computed(() => {
    if (isLogin) {
      return t('auth.signin.description', 'Sign in to your dashboard account');
    }
    return t('auth.signup.description', 'Create a new dashboard account');
  });

  return useCustomSeoMeta({
    title: getTitle,
    description: getDescription,
    noIndex: true,
    noFollow: true,
  });
};

// Composable สำหรับหน้าหลัก/Home
export const useHomeSeo = () => {
  const { t } = useI18n();

  const title = computed(() => t('seo.default.title', 'Nuxt 4 Dashboard'));
  const description = computed(
    () =>
      `${t('seo.default.description', 'Modern dashboard built with Nuxt 4')} ${t('dashboard.description', 'Manage your website, products, and analytics from your dashboard')}`
  );

  return useCustomSeoMeta({
    title,
    description,
  });
};

// Composable สำหรับหน้า Help/Support
export const useHelpSeo = () => {
  const { t } = useI18n();

  const title = computed(
    () => `${t('help.title', 'Help')} - ${t('seo.default.title', 'Nuxt 4 Dashboard')}`
  );
  const description = computed(() =>
    t('help.description', 'Get help and support for using the dashboard')
  );

  return useCustomSeoMeta({
    title,
    description,
  });
};

// Composable สำหรับหน้า Analytics
export const useAnalyticsSeo = () => {
  const { t } = useI18n();

  const title = computed(
    () => `${t('analytics.title', 'Analytics')} - ${t('dashboard.title', 'Dashboard')}`
  );
  const description = computed(() =>
    t('analytics.description', 'View detailed analytics and insights for your website')
  );

  return useCustomSeoMeta({
    title,
    description,
  });
};

// Composable สำหรับหน้า Orders
export const useOrdersSeo = () => {
  const { t } = useI18n();

  const title = computed(
    () => `${t('orders.title', 'Orders')} - ${t('dashboard.title', 'Dashboard')}`
  );
  const description = computed(() =>
    t('orders.description', 'Manage and track all your customer orders')
  );

  return useCustomSeoMeta({
    title,
    description,
  });
};

// Composable สำหรับหน้า Customers
export const useCustomersSeo = () => {
  const { t } = useI18n();

  const title = computed(
    () => `${t('customers.title', 'Customers')} - ${t('dashboard.title', 'Dashboard')}`
  );
  const description = computed(() =>
    t('customers.description', 'Manage your customer database and customer relationships')
  );

  return useCustomSeoMeta({
    title,
    description,
  });
};

// Composable สำหรับหน้า Blog List
export const useBlogSeo = () => {
  const { t } = useI18n();

  const title = computed(() => `${t('blog.title', 'Blog')} - ${t('dashboard.title', 'Dashboard')}`);
  const description = computed(
    () =>
      `${t('dashboard.description', 'Manage your website, products, and analytics from your dashboard')} ${t('blog.title', 'Blog')}`
  );

  return useCustomSeoMeta({
    title,
    description,
  });
};

// Composable สำหรับหน้า Products List
export const useProductsSeo = () => {
  const { t } = useI18n();

  const title = computed(
    () => `${t('products.title', 'Products')} - ${t('dashboard.title', 'Dashboard')}`
  );
  const description = computed(
    () =>
      `${t('dashboard.description', 'Manage your website, products, and analytics from your dashboard')} ${t('products.title', 'Products')}`
  );

  return useCustomSeoMeta({
    title,
    description,
  });
};

// Composable สำหรับหน้า Error/404
export const useErrorSeo = (errorCode = 404) => {
  const { t } = useI18n();

  const title = computed(() => {
    switch (errorCode) {
      case 404:
        return `ไม่พบหน้าที่ต้องการ - ${t('seo.default.title', 'Nuxt 4 Dashboard')}`;
      case 500:
        return `เกิดข้อผิดพลาดของเซิร์ฟเวอร์ - ${t('seo.default.title', 'Nuxt 4 Dashboard')}`;
      default:
        return `เกิดข้อผิดพลาด - ${t('seo.default.title', 'Nuxt 4 Dashboard')}`;
    }
  });

  const description = computed(() => {
    switch (errorCode) {
      case 404:
        return 'ไม่พบหน้าที่คุณต้องการ กรุณาตรวจสอบ URL หรือกลับไปหน้าหลัก';
      case 500:
        return 'เกิดข้อผิดพลาดของเซิร์ฟเวอร์ กรุณาลองใหม่อีกครั้งในภายหลัง';
      default:
        return 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง';
    }
  });

  return useCustomSeoMeta({
    title,
    description,
    noIndex: true,
    noFollow: true,
  });
};

// Utility function สำหรับสร้าง structured data
export const useStructuredData = (
  type: 'Organization' | 'WebSite' | 'Article' | 'Product',
  data: any
) => {
  const runtimeConfig = useRuntimeConfig();

  const structuredData = computed(() => {
    const baseData = {
      '@context': 'https://schema.org',
      '@type': type,
    };

    switch (type) {
      case 'Organization':
        return {
          ...baseData,
          name: data.name,
          url: runtimeConfig.public.siteUrl,
          logo: `${runtimeConfig.public.siteUrl}/images/logo.avif`,
          ...data,
        };
      case 'WebSite':
        return {
          ...baseData,
          name: data.name,
          url: runtimeConfig.public.siteUrl,
          potentialAction: {
            '@type': 'SearchAction',
            target: `${runtimeConfig.public.siteUrl}/search?q={search_term_string}`,
            'query-input': 'required name=search_term_string',
          },
          ...data,
        };
      case 'Article':
        return {
          ...baseData,
          headline: data.title,
          description: data.description,
          author: {
            '@type': 'Person',
            name: data.author,
          },
          datePublished: data.publishedTime,
          dateModified: data.modifiedTime || data.publishedTime,
          image: data.image,
          ...data,
        };
      case 'Product':
        return {
          ...baseData,
          name: data.name,
          description: data.description,
          image: data.image,
          offers: {
            '@type': 'Offer',
            price: data.price,
            priceCurrency: 'THB',
            availability: 'https://schema.org/InStock',
          },
          ...data,
        };
      default:
        return { ...baseData, ...data };
    }
  });

  useHead({
    script: [
      {
        type: 'application/ld+json',
        innerHTML: JSON.stringify(structuredData.value),
      },
    ],
  });

  return structuredData;
};
