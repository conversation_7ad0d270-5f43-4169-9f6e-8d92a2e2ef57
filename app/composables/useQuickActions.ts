export type QuickAction = {
  title: string;
  description: string;
  icon: string;
  to: string;
  color: string;
  gradient: string;
};

// Preset actions for easy selection
export const PRESET_ACTIONS: QuickAction[] = [
  {
    title: 'จัดการเว็บไซต์',
    description: 'แก้ไขการตั้งค่าเว็บไซต์',
    icon: 'i-heroicons-globe-alt',
    to: '/dashboard/{siteId}/sites',
    color: 'blue',
    gradient: 'from-blue-500 to-blue-600',
  },
  {
    title: 'เพิ่มสินค้า',
    description: 'เพิ่มสินค้าใหม่ลงในร้านค้า',
    icon: 'i-heroicons-shopping-bag',
    to: '/dashboard/{siteId}/products',
    color: 'green',
    gradient: 'from-green-500 to-green-600',
  },
  {
    title: 'เขียนบทความ',
    description: 'สร้างเนื้อหาใหม่สำหรับบล็อก',
    icon: 'i-heroicons-document-text',
    to: '/dashboard/{siteId}/blog',
    color: 'purple',
    gradient: 'from-purple-500 to-purple-600',
  },
  {
    title: 'จัดการทีม',
    description: 'เชิญสมาชิกและจัดการบทบาท',
    icon: 'i-heroicons-user-group',
    to: '/dashboard/{siteId}/teams',
    color: 'orange',
    gradient: 'from-orange-500 to-orange-600',
  },
  {
    title: 'จัดการคำสั่งซื้อ',
    description: 'ดูและจัดการคำสั่งซื้อ',
    icon: 'i-heroicons-shopping-cart',
    to: '/dashboard/{siteId}/orders',
    color: 'red',
    gradient: 'from-red-500 to-red-600',
  },
  {
    title: 'ลูกค้า',
    description: 'จัดการข้อมูลลูกค้า',
    icon: 'i-heroicons-users',
    to: '/dashboard/{siteId}/customers',
    color: 'indigo',
    gradient: 'from-indigo-500 to-indigo-600',
  },
  {
    title: 'รายงาน',
    description: 'ดูรายงานและสถิติ',
    icon: 'i-heroicons-chart-bar',
    to: '/dashboard/{siteId}/reports',
    color: 'teal',
    gradient: 'from-teal-500 to-teal-600',
  },
  {
    title: 'การตั้งค่า',
    description: 'จัดการการตั้งค่าระบบ',
    icon: 'i-heroicons-cog-6-tooth',
    to: '/dashboard/{siteId}/settings',
    color: 'gray',
    gradient: 'from-gray-500 to-gray-600',
  },
];

// Validation schema
export const validateQuickAction = (
  action: QuickAction
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!action.title.trim()) {
    errors.push('กรุณากรอกชื่อการดำเนินการ');
  } else if (action.title.length < 2) {
    errors.push('ชื่อต้องมีอย่างน้อย 2 ตัวอักษร');
  } else if (action.title.length > 50) {
    errors.push('ชื่อต้องไม่เกิน 50 ตัวอักษร');
  }

  if (!action.description.trim()) {
    errors.push('กรุณากรอกรายละเอียด');
  } else if (action.description.length > 200) {
    errors.push('รายละเอียดต้องไม่เกิน 200 ตัวอักษร');
  }

  if (!action.icon.trim()) {
    errors.push('กรุณาเลือกไอคอน');
  }

  if (!action.to.trim()) {
    errors.push('กรุณากรอกลิงก์');
  } else if (!action.to.startsWith('/dashboard/')) {
    errors.push('ลิงก์ต้องเริ่มต้นด้วย /dashboard/');
  }

  if (!action.color.trim()) {
    errors.push('กรุณาเลือกสี');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const useQuickActions = (siteId: string) => {
  const quickActions = ref<QuickAction[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Load quick actions from backend
  const loadQuickActions = async () => {
    loading.value = true;
    error.value = null;

    try {
      const { data } = await $fetch<{ data: QuickAction[] }>(`/v1/site/${siteId}/quick-actions`);
      quickActions.value = data;
    } catch (err: any) {
      console.error('Error loading quick actions:', err);
      quickActions.value = [];
    } finally {
      loading.value = false;
    }
  };

  // Save quick actions to backend
  const saveQuickActions = async () => {
    try {
      await $fetch(`/v1/site/${siteId}/quick-actions`, {
        method: 'PUT',
        body: { actions: quickActions.value },
      });
    } catch (err: any) {
      console.error('Error saving quick actions:', err);
      throw err;
    }
  };

  // Add new action
  const addAction = async (action: QuickAction) => {
    quickActions.value.push(action);
    await saveQuickActions();
  };

  // Update existing action
  const updateAction = async (index: number, action: QuickAction) => {
    if (index >= 0 && index < quickActions.value.length) {
      quickActions.value[index] = action;
      await saveQuickActions();
    }
  };

  // Remove action
  const removeAction = async (index: number) => {
    if (index >= 0 && index < quickActions.value.length) {
      quickActions.value.splice(index, 1);
      await saveQuickActions();
    }
  };

  // Reset to default actions
  const resetToDefault = async () => {
    await loadQuickActions();
  };

  return {
    quickActions: readonly(quickActions),
    loading: readonly(loading),
    error: readonly(error),
    loadQuickActions,
    saveQuickActions,
    addAction,
    updateAction,
    removeAction,
    resetToDefault,
  };
};
