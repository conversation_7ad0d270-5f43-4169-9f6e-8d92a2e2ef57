export const useTracking = () => {
  // Types
  interface TrackingEvent {
    id: string;
    type: 'page_view' | 'click' | 'scroll' | 'form_submit' | 'download';
    user: string;
    page: string;
    sessionId: string;
    userAgent: string;
    ip: string;
    location: string;
    duration?: number;
    element?: string;
    scrollDepth?: number;
    formName?: string;
    fileName?: string;
    createdAt: string;
  }

  interface TrackingFilters {
    search?: string;
    type?: string;
    dateRange?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    page?: number;
    limit?: number;
  }

  // Get tracking events
  const getTrackingEvents = async (siteId: string, filters?: TrackingFilters) => {
    try {
      const params = new URLSearchParams();
      if (filters?.search) params.append('search', filters.search);
      if (filters?.type && filters.type !== 'all') params.append('type', filters.type);
      if (filters?.dateRange && filters.dateRange !== 'all')
        params.append('dateRange', filters.dateRange);
      if (filters?.sortBy) params.append('sortBy', filters.sortBy);
      if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const queryString = params.toString();
      const url = `/v1/site/${siteId}/tracking${queryString ? `?${queryString}` : ''}`;

      return await $fetch(url);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลการติดตามได้');
    }
  };

  // Get single tracking event
  const getTrackingEvent = async (siteId: string, eventId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/tracking/${eventId}`);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลการติดตามได้');
    }
  };

  // Create tracking event
  const createTrackingEvent = async (siteId: string, data: Partial<TrackingEvent>) => {
    try {
      return await $fetch(`/v1/site/${siteId}/tracking`, {
        method: 'POST',
        body: data,
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถสร้างเหตุการณ์การติดตามได้');
    }
  };

  // Delete tracking event
  const deleteTrackingEvent = async (siteId: string, eventId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/tracking/${eventId}`, {
        method: 'DELETE',
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถลบเหตุการณ์การติดตามได้');
    }
  };

  // Clear all tracking data
  const clearTrackingData = async (siteId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/tracking/clear`, {
        method: 'DELETE',
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถล้างข้อมูลการติดตามได้');
    }
  };

  // Export tracking data
  const exportTrackingData = async (siteId: string, format: 'csv' | 'json' = 'csv') => {
    try {
      return await $fetch(`/v1/site/${siteId}/tracking/export?format=${format}`, {
        method: 'GET',
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถส่งออกข้อมูลการติดตามได้');
    }
  };

  // Get tracking analytics
  const getTrackingAnalytics = async (siteId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/tracking/analytics`);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลสถิติได้');
    }
  };

  return {
    getTrackingEvents,
    getTrackingEvent,
    createTrackingEvent,
    deleteTrackingEvent,
    clearTrackingData,
    exportTrackingData,
    getTrackingAnalytics,
  };
};
