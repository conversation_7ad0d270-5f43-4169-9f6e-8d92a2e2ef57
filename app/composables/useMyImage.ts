/**
 * Cloudinary Image composable
 * Handles both public and signed URL generation for Cloudinary images
 */
export function useMyImage() {
  const config = useRuntimeConfig()

  /**
   * Get optimized image URL
   */
  const getImageUrl = (
    publicId: string,
    options: {
      width?: number
      height?: number
      quality?: string | number
      format?: string
      crop?: string
      gravity?: string
      restricted?: boolean
    } = {}
  ) => {
    // Check if publicId is already a full URL
    if (publicId.startsWith('http://') || publicId.startsWith('https://')) {
      return publicId // Return as-is if it's already a full URL
    }

    const {
      width,
      height,
      quality = 'auto',
      format = 'auto',
      crop = 'fill',
      gravity = 'auto',
      restricted = false
    } = options

    // Build transformation string
    const transformations = []

    if (width || height) {
      transformations.push(`c_${crop}`)
      if (width) transformations.push(`w_${width}`)
      if (height) transformations.push(`h_${height}`)
      if (gravity !== 'auto') transformations.push(`g_${gravity}`)
    }

    transformations.push(`q_${quality}`)
    transformations.push(`f_${format}`)

    const transformation = transformations.join(',')

    if (restricted) {
      // For restricted images, we need signed URL from server
      return null // Will be handled by getSignedUrl
    }

    // Public image URL
    return `https://res.cloudinary.com/${config.public.cloudinaryCloudName}/image/upload/${transformation}/${publicId}`
  }

  /**
   * Get signed URL for restricted images
   */
  const getSignedUrl = async (
    publicId: string,
    options: {
      width?: number
      height?: number
      quality?: string | number
      format?: string
      restricted?: boolean
    } = {}
  ) => {
    try {
      const response = await $fetch<{ url?: string; error?: string }>('/api/v1/signed-url', {
        method: 'POST',
        body: {
          publicId,
          ...options,
          restricted: options.restricted ?? true
        }
      })

      if (response.error) {
        throw new Error(response.error)
      }

      return response.url
    } catch (error) {
      console.error('Failed to get signed URL:', error)
      return null
    }
  }

  /**
   * Get responsive image URLs for different screen sizes
   */
  const getResponsiveUrls = (
    publicId: string,
    options: {
      quality?: string | number
      format?: string
      crop?: string
      gravity?: string
      restricted?: boolean
    } = {}
  ) => {
    const sizes = [
      { name: 'sm', width: 640 },
      { name: 'md', width: 768 },
      { name: 'lg', width: 1024 },
      { name: 'xl', width: 1280 },
      { name: '2xl', width: 1536 }
    ]

    return sizes.reduce((acc, size) => {
      acc[size.name] = getImageUrl(publicId, {
        ...options,
        width: size.width
      })
      return acc
    }, {} as Record<string, string | null>)
  }

  /**
   * Generate srcset for responsive images
   */
  const getSrcSet = (
    publicId: string,
    options: {
      quality?: string | number
      format?: string
      crop?: string
      gravity?: string
      restricted?: boolean
    } = {}
  ) => {
    const breakpoints = [320, 640, 768, 1024, 1280, 1536]

    return breakpoints
      .map(width => {
        const url = getImageUrl(publicId, { ...options, width })
        return url ? `${url} ${width}w` : null
      })
      .filter(Boolean)
      .join(', ')
  }

  /**
   * Get image metadata (dimensions, format, etc.)
   */
  const getImageInfo = (publicId: string) => {
    // Return basic info URL for getting image metadata
    return `https://res.cloudinary.com/${config.public.cloudinaryCloudName}/image/upload/fl_getinfo/${publicId}.json`
  }

  /**
   * Simple helper function for backward compatibility
   */
  const imageUrl = (id: string | undefined | null, options?: Record<string, any>) => {
    if (!id) return null
    return getImageUrl(id, options)
  }



  // Generate responsive sizes attribute
  const generateSizes = (breakpoints: Record<string, number>) => {
    const entries = Object.entries(breakpoints)
      .sort(([, a], [, b]) => a - b)
      .map(([size, width]) => `(max-width: ${width}px) ${size}`)

    return entries.join(', ')
  }

  // Calculate optimal dimensions based on container and device
  const calculateOptimalSize = (
    containerWidth: number,
    containerHeight: number,
    devicePixelRatio: number = 1
  ) => {
    return {
      width: Math.ceil(containerWidth * devicePixelRatio),
      height: Math.ceil(containerHeight * devicePixelRatio)
    }
  }

  return {
    // Core Cloudinary functions
    getImageUrl,
    getSignedUrl,
    getResponsiveUrls,
    getSrcSet,
    getImageInfo,

    // Helper functions
    imageUrl,
    generateSizes,
    calculateOptimalSize
  }
} 