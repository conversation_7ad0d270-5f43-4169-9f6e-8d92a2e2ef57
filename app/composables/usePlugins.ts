export const usePlugins = () => {
  // Types
  interface Plugin {
    id: string;
    name: string;
    description: string;
    status: 'active' | 'inactive' | 'update';
    version: string;
    author: string;
    created_at: string;
    updated_at: string;
    icon: string;
    color: string;
    config?: any;
  }

  interface PluginForm {
    name: string;
    description: string;
    status: 'active' | 'inactive';
    version: string;
    author: string;
    icon: string;
    color: string;
    config?: any;
  }

  interface PluginFilters {
    search?: string;
    status?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    page?: number;
    limit?: number;
  }

  // Get plugins list
  const getPlugins = async (siteId: string, filters?: PluginFilters) => {
    try {
      const params = new URLSearchParams();
      if (filters?.search) params.append('search', filters.search);
      if (filters?.status && filters.status !== 'all') params.append('status', filters.status);
      if (filters?.sortBy) params.append('sortBy', filters.sortBy);
      if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const queryString = params.toString();
      const url = `/v1/site/${siteId}/plugins${queryString ? `?${queryString}` : ''}`;

      return await $fetch(url);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลปลั๊กอินได้');
    }
  };

  // Get single plugin
  const getPlugin = async (siteId: string, pluginId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/plugins/${pluginId}`);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลปลั๊กอินได้');
    }
  };

  // Install plugin
  const installPlugin = async (siteId: string, pluginId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/plugins/${pluginId}/install`, {
        method: 'POST',
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถติดตั้งปลั๊กอินได้');
    }
  };

  // Uninstall plugin
  const uninstallPlugin = async (siteId: string, pluginId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/plugins/${pluginId}/uninstall`, {
        method: 'POST',
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถถอนการติดตั้งปลั๊กอินได้');
    }
  };

  // Update plugin
  const updatePlugin = async (siteId: string, pluginId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/plugins/${pluginId}/update`, {
        method: 'POST',
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถอัปเดตปลั๊กอินได้');
    }
  };

  // Configure plugin
  const configurePlugin = async (siteId: string, pluginId: string, config: any) => {
    try {
      return await $fetch(`/v1/site/${siteId}/plugins/${pluginId}/configure`, {
        method: 'PUT',
        body: config,
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถกำหนดค่าปลั๊กอินได้');
    }
  };

  // Get plugin analytics
  const getPluginAnalytics = async (siteId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/plugins/analytics`);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลสถิติได้');
    }
  };

  return {
    getPlugins,
    getPlugin,
    installPlugin,
    uninstallPlugin,
    updatePlugin,
    configurePlugin,
    getPluginAnalytics,
  };
};
