export const useSubscriptionApi = () => {
  const { $fetch } = useNuxtApp() as any;

  // ดึงข้อมูล subscription ของเว็บไซต์ (ผ่าน GET /api/v1/site แล้วหา site ที่ต้องการ)
  const getSiteSubscription = async (siteId: string) => {
    try {
      const response = await $fetch('/v1/site');
      const sites = response.data?.sites || response.data || [];
      const site = sites.find((s: any) => s._id === siteId);

      if (!site) {
        throw new Error('ไม่พบเว็บไซต์นี้');
      }

      return {
        success: true,
        data: site,
      };
    } catch (error: any) {
      console.error('Error fetching subscription:', error);
      throw new Error(error.message || 'ไม่สามารถดึงข้อมูล subscription ได้');
    }
  };

  // ต่ออายุ subscription (ใช้ POST /api/v1/site/:siteId/upgrade)
  const renewSubscription = async (siteId: string, plan: string, billingCycle: string) => {
    try {
      const response = await $fetch(`/v1/site/${siteId}/upgrade`, {
        method: 'POST',
        body: {
          plan,
          billingCycle,
        },
      });
      return response;
    } catch (error: any) {
      console.error('Error renewing subscription:', error);
      throw new Error(error.message || 'ไม่สามารถต่ออายุ subscription ได้');
    }
  };

  // ดึงรายการเว็บไซต์ทั้งหมดพร้อม subscription จาก backend จริง
  const getAllSitesWithSubscription = async () => {
    try {
      // เรียก API จริง GET /api/v1/site
      const response = await $fetch('/v1/site');
      return response;
    } catch (error: any) {
      console.error('Error fetching sites with subscription:', error);
      throw new Error(error.message || 'ไม่สามารถดึงข้อมูลเว็บไซต์ได้');
    }
  };

  return {
    getSiteSubscription,
    renewSubscription,
    getAllSitesWithSubscription,
  };
};
