export const useAuthErrorHandler = () => {
    const authStore = useAuthStore();
    const router = useRouter();

    // Handle authentication errors
    const handleAuthError = async (error: any) => {
        console.log('Handling auth error:', error);

        // Check if it's an authentication-related error
        const isAuthError =
            error?.statusCode === 401 ||
            error?.status === 401 ||
            error?.data?.shouldRedirect ||
            error?.message?.includes('Authentication failed') ||
            error?.message?.includes('Token refresh failed') ||
            error?.message?.includes('No session found');

        if (isAuthError) {
            console.log('Authentication error detected, clearing auth state');

            // Clear auth state
            await authStore.clearAuth();

            // Show notification if available
            try {
                const { $toast } = useNuxtApp();
                if ($toast) {
                    $toast.add({
                        title: 'เซสชันหมดอายุ',
                        description: 'กรุณาเข้าสู่ระบบใหม่',
                        color: 'warning',
                        timeout: 5000
                    });
                }
            } catch (toastError) {
                console.log('Toast not available:', toastError);
            }

            // Redirect to signin if not already there
            if (router.currentRoute.value.path !== '/signin') {
                console.log('Redirecting to signin page');
                await router.push('/signin');
            }

            return true; // Error was handled
        }

        return false; // Error was not handled
    };

    // Setup global error listeners
    const setupGlobalErrorHandling = () => {
        if (import.meta.client) {
            // Handle unhandled promise rejections
            window.addEventListener('unhandledrejection', async (event) => {
                const handled = await handleAuthError(event.reason);
                if (handled) {
                    event.preventDefault(); // Prevent console error
                }
            });

            // Handle global errors
            window.addEventListener('error', async (event) => {
                const handled = await handleAuthError(event.error);
                if (handled) {
                    event.preventDefault(); // Prevent console error
                }
            });
        }
    };

    // Check if current session is valid
    const checkSessionValidity = async () => {
        if (!authStore.isAuthenticated) {
            return false;
        }

        try {
            // Try to make a simple authenticated request
            await $fetch('/api/v1/user/profile', {
                method: 'GET',
            });
            return true;
        } catch (error) {
            console.log('Session validation failed:', error);
            await handleAuthError(error);
            return false;
        }
    };

    // Auto-refresh token if needed
    const autoRefreshToken = async () => {
        if (!authStore.isAuthenticated) {
            return false;
        }

        try {
            const { refreshSession } = useSession();
            const success = await refreshSession();

            if (success) {
                console.log('Token auto-refreshed successfully');
                // Reload user data
                await authStore.loadUserDetail();
                return true;
            } else {
                console.log('Token auto-refresh failed');
                await handleAuthError({ statusCode: 401, message: 'Token refresh failed' });
                return false;
            }
        } catch (error) {
            console.error('Auto-refresh error:', error);
            await handleAuthError(error);
            return false;
        }
    };

    return {
        handleAuthError,
        setupGlobalErrorHandling,
        checkSessionValidity,
        autoRefreshToken,
    };
};