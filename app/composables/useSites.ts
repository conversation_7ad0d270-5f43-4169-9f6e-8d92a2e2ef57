import type { ApiResponse } from '../../shared/types/respone';
import type { Site, SiteAnalytics, SiteTemplate } from '../../shared/types/site';

export const useSites = () => {
  // สร้างเว็บไซต์
  const createSite = async (siteData: Partial<Site>) => {
    return await $fetch<ApiResponse<Site>>('/api/v1/site/create', {
      method: 'POST',
      body: siteData,
    });
  };

  // ดึงรายการเว็บไซต์
  const getSites = async (params: { page: number; limit: number }) => {
    return await $fetch<
      ApiResponse<{
        sites: Site[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
      }>
    >(`/api/v1/site?page=${params.page}&limit=${params.limit}`);
  };
  
  // ดึงข้อมูลเว็บไซต์ตาม ID
  const getSite = async (siteId: string) => {
    return await $fetch<ApiResponse<Site>>(`/api/v1/site/${siteId}`);
  };

  // อัปเดตเว็บไซต์
  const updateSite = async (siteId: string, siteData: Partial<Site>) => {
    return await $fetch<ApiResponse<Site>>(`/api/v1/site/${siteId}`, {
      method: 'PUT',
      body: siteData,
    });
  };

  // ลบเว็บไซต์
  const deleteSite = async (siteId: string) => {
    return await $fetch<ApiResponse<void>>(`/api/v1/site/${siteId}`, {
      method: 'DELETE',
    });
  };

  // เปิด/ปิดเว็บไซต์
  const toggleSiteStatus = async (siteId: string, isActive: boolean) => {
    return await $fetch<ApiResponse<Site>>(`/api/v1/site/${siteId}/status`, {
      method: 'PUT',
      body: { isActive },
    });
  };

  // ตรวจสอบความพร้อมใช้งานของ domain
  const checkDomainAvailability = async (domain: string) => {
    return await $fetch<
      ApiResponse<{
        available: boolean;
        suggestions?: string[];
      }>
    >(`/api/v1/site/check-domain?domain=${domain}`);
  };

  // อัปเดตการตั้งค่า SEO
  const updateSEOSettings = async (siteId: string, seoData: Site['settings']['seo']) => {
    return await $fetch<ApiResponse<Site>>(`/api/v1/site/${siteId}/seo`, {
      method: 'PUT',
      body: seoData,
    });
  };

  // อัปเดตธีม
  const updateTheme = async (siteId: string, themeData: Site['theme']) => {
    return await $fetch<ApiResponse<Site>>(`/api/v1/site/${siteId}/theme`, {
      method: 'PUT',
      body: themeData,
    });
  };

  // อัปเดตการตั้งค่า
  const updateSettings = async (siteId: string, settingsData: Partial<Site['settings']>) => {
    return await $fetch<ApiResponse<Site>>(`/api/v1/site/${siteId}/settings`, {
      method: 'PUT',
      body: settingsData,
    });
  };

  // ดึงข้อมูลวิเคราะห์
  const getAnalytics = async (siteId: string, period?: string) => {
    const params = new URLSearchParams({ siteId });
    if (period) params.append('period', period);

    return await $fetch<ApiResponse<SiteAnalytics>>(
      `/api/v1/site/${siteId}/analytics?${params.toString()}`
    );
  };

  // อัปโหลดไฟล์
  const uploadFile = async (siteId: string, file: File, type: 'image' | 'document' | 'media') => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    return await $fetch<
      ApiResponse<{
        url: string;
        filename: string;
        size: number;
        type: string;
      }>
    >(`/api/v1/site/${siteId}/upload`, {
      method: 'POST',
      body: formData,
    });
  };

  return {
    createSite,
    getSites,
    getSite,
    updateSite,
    deleteSite,
    toggleSiteStatus,
    checkDomainAvailability,
    updateSEOSettings,
    updateTheme,
    updateSettings,
    getAnalytics,
    uploadFile,
  };
};
