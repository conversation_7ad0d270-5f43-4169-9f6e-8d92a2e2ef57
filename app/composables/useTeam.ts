import type { ApiResponse } from '../../shared/types/respone';

import type {
  Team,
  TeamAnalytics,
  TeamInvitation,
  TeamMember,
  TeamPermission,
  TeamRole,
} from '../../shared/types/team';

import { useRoute } from 'vue-router';

export const useTeam = () => {
  // const toast = useToast();
  const route: any = useRoute();
  const siteId = route.params.siteId;

  // Get teams by site ID
  const getTeams = async (siteId: string) => {
    try {
      const response = await $fetch(`/api/v1/team/${siteId}`);
      return response;
    } catch (error: any) {
      console.error('Error fetching teams:', error);
      throw error;
    }
  };


  // Create invitation
  const createInvitation = async (invitationData: any) => {
    try {
      const response = await $fetch(`/api/v1/team/${siteId}/invitations`, {
        method: 'POST',
        body: invitationData,
      });
      return response;
    } catch (error: any) {
      console.error('Error creating invitation:', error);
      throw error;
    }
  };
  

  // Get team by ID
  const getTeamById = async (siteId: string, teamId: string) => {
    try {
      const response = await $fetch(`/api/v1/team/${siteId}/${teamId}`);
      return response;
    } catch (error: any) {
      console.error('Error fetching team:', error);
      throw error;
    }
  };

  // Update team
  const updateTeam = async (siteId: string, teamId: string, teamData: any) => {
    try {
      const response = await $fetch(`/api/v1/team/${siteId}/${teamId}`, {
        method: 'PUT',
        body: teamData,
      });
      return response;
    } catch (error: any) {
      console.error('Error updating team:', error);
      throw error;
    }
  };

  // Delete team
  const deleteTeam = async (siteId: string, teamId: string) => {
    try {
      const response = await $fetch(`/api/v1/team/${siteId}/${teamId}`, {
        method: 'DELETE',
      });
      return response;
    } catch (error: any) {
      console.error('Error deleting team:', error);
      throw error;
    }
  };

  // Get team members
  const getTeamMembers = async (siteId: string, teamId: string) => {
    try {
      const response = await $fetch(`/api/v1/team/${siteId}/members`);
      return response;
    } catch (error: any) {
      console.error('Error fetching team members:', error);
      throw error;
    }
  };

  // Invite member to team
  const inviteMember = async (siteId: string, invitationData: any) => {
    try {
      const response = await $fetch(`/api/v1/team/${siteId}/invitations`, {
        method: 'POST',
        body: invitationData,
      });
      return response;
    } catch (error: any) {
      console.error('Error inviting member:', error);
      throw error;
    }
  };

  // Update team member
  const updateMember = async (siteId: string, userId: string, memberData: any) => {
    try {
      const response = await $fetch(`/api/v1/team/${siteId}/members/${userId}`, {
        method: 'PUT',
        body: memberData,
      });
      return response;
    } catch (error: any) {
      console.error('Error updating member:', error);
      throw error;
    }
  };

  // Remove team member
  const removeMember = async (siteId: string, userId: string) => {
    try {
      const response = await $fetch(`/api/v1/team/${siteId}/members/${userId}`, {
        method: 'DELETE',
      });
      return response;
    } catch (error: any) {
      console.error('Error removing member:', error);
      throw error;
    }
  };


  // ดึงข้อมูลบทบาท
  const getRoles = async (siteId: string) => {
    return await $fetch<ApiResponse<TeamRole[]>>(`/api/v1/team/${siteId}/roles`);
  };

  // สร้างบทบาทใหม่
  const createRole = async (siteId: string, roleData: Partial<TeamRole>) => {
    return await $fetch<ApiResponse<TeamRole>>(`/api/v1/team/${siteId}/roles`, {
      method: 'POST',
      body: roleData,
    });
  };

  // อัปเดตบทบาท
  const updateRole = async (siteId: string, roleId: string, roleData: Partial<TeamRole>) => {
    return await $fetch<ApiResponse<TeamRole>>(`/api/v1/team/${siteId}/roles/${roleId}`, {
      method: 'PUT',
      body: roleData,
    });
  };

  // ลบบทบาท
  const deleteRole = async (siteId: string, roleId: string) => {
    return await $fetch<ApiResponse<void>>(`/api/v1/team/${siteId}/roles/${roleId}`, {
      method: 'DELETE',
    });
  };

  // ดึงรายการ permissions
  const getPermissions = async () => {
    return await $fetch<ApiResponse<TeamPermission[]>>(`/api/v1/team/permissions`);
  };

  // ดึง default roles
  const getDefaultRoles = async () => {
    return await $fetch<ApiResponse<TeamRole[]>>(`/api/v1/team/roles/default`);
  };

  // ดึงข้อมูลวิเคราะห์
  const getAnalytics = async (siteId: string, period?: string) => {
    const params = new URLSearchParams();
    if (period) params.append('period', period);

    return await $fetch<ApiResponse<TeamAnalytics>>(
      `/api/v1/team/${siteId}/analytics?${params.toString()}`
    );
  };

  // ดึงรายละเอียดคำเชิญ
  const getInvitationDetails = async (token: string) => {
    return await $fetch<ApiResponse<any>>(`/api/v1/team/invitations/details`, {
      method: 'POST',
      body: { token },
    });
  };

  // เข้าร่วมทีม
  const joinTeam = async (token: string) => {
    return await $fetch<ApiResponse<any>>(`/api/v1/team/join`, {
      method: 'POST',
      body: { token },
    });
  };

  return {
    getTeams,
    getTeamById,
    updateTeam,
    deleteTeam,
    getTeamMembers,
    inviteMember,
    updateMember,
    removeMember,
    createInvitation,
    getRoles,
    createRole,
    updateRole,
    deleteRole,
    getPermissions,
    getDefaultRoles,
    getAnalytics,
    getInvitationDetails,
    joinTeam,
  };
};
