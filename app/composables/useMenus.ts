// Define ApiResponse interface locally
interface ApiResponse<T = any> {
  success: boolean;
  status: number;
  message: string;
  data?: T;
  timestamp: string;
}

export interface MenuItem {
  id: string;
  label: string;
  type: 'page' | 'custom' | 'category' | 'none';
  pageId?: string;
  url?: string;
  categoryId?: string;
  openInNewTab: boolean;
  order: number;
}

export interface Menu {
  _id: string;
  siteId: string;
  name: string;
  description: string;
  location: 'header' | 'footer' | 'sidebar' | 'mobile';
  status: 'active' | 'inactive';
  items: MenuItem[];
  showOnMobile: boolean;
  showOnDesktop: boolean;
  cssClass: string;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface MenuAnalytics {
  totalMenus: number;
  activeMenus: number;
  totalMenuItems: number;
  averageItems: number;
  topLocation: string;
  growthRate: number;
}

export interface MenuFilters {
  page?: number;
  limit?: number;
  status?: string;
  location?: string;
  search?: string;
}

export const useMenus = () => {
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Get menus with filters
  const getMenus = async (siteId: string, filters: MenuFilters = {}) => {
    loading.value = true;
    error.value = null;

    try {
      const queryParams = new URLSearchParams();

      if (filters.page) queryParams.append('page', filters.page.toString());
      if (filters.limit) queryParams.append('limit', filters.limit.toString());
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.location) queryParams.append('location', filters.location);
      if (filters.search) queryParams.append('search', filters.search);

      const response = await $fetch<
        ApiResponse<{
          menus: Menu[];
          total: number;
          page: number;
          limit: number;
          totalPages: number;
        }>
      >(`/v1/menu?siteId=${siteId}&${queryParams}`);

      return response;
    } catch (err: any) {
      error.value = err.data?.message || 'เกิดข้อผิดพลาดในการดึงข้อมูลเมนู';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // Get menu analytics
  const getMenuAnalytics = async (siteId: string) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await $fetch<ApiResponse<MenuAnalytics>>(
        `/v1/menu/analytics?siteId=${siteId}`
      );
      return response;
    } catch (err: any) {
      error.value = err.data?.message || 'เกิดข้อผิดพลาดในการดึงข้อมูลสถิติ';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // Get single menu
  const getMenu = async (siteId: string, menuId: string) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await $fetch<ApiResponse<Menu>>(`/v1/menu/${menuId}?siteId=${siteId}`);
      return response;
    } catch (err: any) {
      error.value = err.data?.message || 'เกิดข้อผิดพลาดในการดึงข้อมูลเมนู';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // Create menu
  const createMenu = async (
    siteId: string,
    menuData: {
      name: string;
      description?: string;
      location: string;
      status?: string;
      items?: MenuItem[];
      showOnMobile?: boolean;
      showOnDesktop?: boolean;
      cssClass?: string;
      order?: number;
    }
  ) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await $fetch<ApiResponse<Menu>>(`/v1/menu?siteId=${siteId}`, {
        method: 'POST',
        body: menuData,
      });
      return response;
    } catch (err: any) {
      error.value = err.data?.message || 'เกิดข้อผิดพลาดในการสร้างเมนู';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // Update menu
  const updateMenu = async (siteId: string, menuId: string, updateData: Partial<Menu>) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await $fetch<ApiResponse<Menu>>(`/v1/menu/${menuId}?siteId=${siteId}`, {
        method: 'PUT',
        body: updateData,
      });
      return response;
    } catch (err: any) {
      error.value = err.data?.message || 'เกิดข้อผิดพลาดในการอัปเดตเมนู';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // Delete menu
  const deleteMenu = async (siteId: string, menuId: string) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await $fetch<ApiResponse<{ success: boolean }>>(
        `/v1/menu/${menuId}?siteId=${siteId}`,
        {
          method: 'DELETE',
        }
      );
      return response;
    } catch (err: any) {
      error.value = err.data?.message || 'เกิดข้อผิดพลาดในการลบเมนู';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // Duplicate menu
  const duplicateMenu = async (siteId: string, menuId: string) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await $fetch<ApiResponse<Menu>>(
        `/v1/menu/${menuId}/duplicate?siteId=${siteId}`,
        {
          method: 'POST',
        }
      );
      return response;
    } catch (err: any) {
      error.value = err.data?.message || 'เกิดข้อผิดพลาดในการคัดลอกเมนู';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    loading: readonly(loading),
    error: readonly(error),
    getMenus,
    getMenuAnalytics,
    getMenu,
    createMenu,
    updateMenu,
    deleteMenu,
    duplicateMenu,
  };
};
