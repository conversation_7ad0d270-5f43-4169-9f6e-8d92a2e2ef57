import { ref, computed, watch } from 'vue'
import { useToast } from '#imports'

export function useDiscount(originalPrice: any, selectedPackage: any) {
  const discountCode = ref('')
  const checkingDiscount = ref(false)
  const appliedDiscount = ref<any>(null)

  const discountAmount = computed(() => appliedDiscount.value?.discountAmount || 0)
  const finalPrice = computed(() => appliedDiscount.value?.finalAmount || originalPrice.value)

  const applyDiscount = async () => {
    if (!discountCode.value.trim()) return
    checkingDiscount.value = true
    try {
      const response = await $fetch('/api/v1/discount/validate', {
        method: 'POST',
        body: {
          discountCode: discountCode.value.trim(),
          userId: '',
          orderAmount: originalPrice.value,
          target: 'package',
          items: [
            {
              id: selectedPackage.value?.id,
              type: 'package',
              name: selectedPackage.value?.name || '',
              price: originalPrice.value,
              quantity: 1
            }
          ],
          isFirstTime: false
        }
      })
      if (response.success && response.data?.discount) {
        const discount = response.data.discount
        appliedDiscount.value = {
          _id: discount._id,
          discountCode: discount.discountCode,
          name: discount.name,
          description: discount.description,
          type: discount.type,
          value: discount.value,
          maxDiscountAmount: discount.maxDiscountAmount,
          discountAmount: response.data.discountAmount,
          finalAmount: response.data.finalAmount,
          conditions: discount.conditions
        }
        const discountText = discount.type === 'percentage'
          ? `${discount.value}%`
          : `฿${discount.value}`
        const actualDiscountText = `฿${response.data.discountAmount}`
        useToast().add({
          title: response.statusMessage || 'ใช้รหัสส่วนลดสำเร็จ',
          description: `${discount.name} - ส่วนลด ${discountText} (ประหยัด ${actualDiscountText})`,
          color: 'success',
          duration: 4000,
        })
      } else {
        useToast().add({
          title: 'รหัสส่วนลดไม่ถูกต้อง',
          description: response.message || 'กรุณาตรวจสอบรหัสส่วนลดอีกครั้ง',
          color: 'error',
          duration: 3000,
        })
      }
    } catch (error: any) {
      useToast().add({
        title: 'ผิดพลาด',
        description: error.data?.message || 'ไม่สามารถตรวจสอบรหัสส่วนลดได้',
        color: 'error',
        duration: 3000,
      })
    } finally {
      checkingDiscount.value = false
    }
  }

  // reset discount เมื่อเปลี่ยน package
  watch([originalPrice, selectedPackage], () => {
    appliedDiscount.value = null
    discountCode.value = ''
  })

  return {
    discountCode,
    checkingDiscount,
    appliedDiscount,
    discountAmount,
    finalPrice,
    applyDiscount,
  }
} 