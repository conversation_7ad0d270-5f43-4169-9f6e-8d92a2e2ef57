export const usePages = () => {
  // Types
  interface PageForm {
    id?: string;
    title: string;
    slug: string;
    content: string;
    excerpt: string;
    status: 'published' | 'draft' | 'archived';
    type: 'page' | 'post' | 'landing' | 'custom';
    metaTitle?: string;
    metaDescription?: string;
    featuredImage?: string;
    seoKeywords?: string[];
    isHomepage?: boolean;
    allowComments?: boolean;
    passwordProtected?: boolean;
    password?: string;
    publishDate?: string;
    author?: string;
    tags?: string[];
  }

  interface PageFilters {
    search?: string;
    status?: string;
    type?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    page?: number;
    limit?: number;
  }

  // Get pages list
  const getPages = async (siteId: string, filters?: PageFilters) => {
    try {
      const params = new URLSearchParams();
      if (filters?.search) params.append('search', filters.search);
      if (filters?.status && filters.status !== 'all') params.append('status', filters.status);
      if (filters?.type && filters.type !== 'all') params.append('type', filters.type);
      if (filters?.sortBy) params.append('sortBy', filters.sortBy);
      if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const queryString = params.toString();
      const url = `/v1/site/${siteId}/pages${queryString ? `?${queryString}` : ''}`;

      return await $fetch(url);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลหน้าเพจได้');
    }
  };

  // Get single page
  const getPage = async (siteId: string, pageId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/pages/${pageId}`);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลหน้าเพจได้');
    }
  };

  // Create page
  const createPage = async (siteId: string, data: PageForm) => {
    try {
      return await $fetch(`/v1/site/${siteId}/pages`, {
        method: 'POST',
        body: data,
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถสร้างหน้าเพจได้');
    }
  };

  // Update page
  const updatePage = async (siteId: string, pageId: string, data: PageForm) => {
    try {
      return await $fetch(`/v1/site/${siteId}/pages/${pageId}`, {
        method: 'PUT',
        body: data,
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถอัปเดตหน้าเพจได้');
    }
  };

  // Delete page
  const deletePage = async (siteId: string, pageId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/pages/${pageId}`, {
        method: 'DELETE',
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถลบหน้าเพจได้');
    }
  };

  // Get page analytics
  const getPageAnalytics = async (siteId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/pages/analytics`);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลสถิติได้');
    }
  };

  return {
    getPages,
    getPage,
    createPage,
    updatePage,
    deletePage,
    getPageAnalytics,
  };
};
