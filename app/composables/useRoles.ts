export interface UserInfo {
  _id: string
  moneyPoint: number
  goldPoint: number
  email: string
  avatar: string
  createdAt: string
  updatedAt: string
}

export interface Role {
  _id: string
  role: string
  siteId: string
  userInfo: UserInfo
  createdAt: string
  updatedAt: string
  user: string
  joinedAt: string
  status: string
}

export const useRoles = () => {
  const roles = ref<Role[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // ดึงข้อมูล roles
  const fetchRoles = async (siteId?: string) => {
    loading.value = true
    error.value = null

    try {
      const { data } = await $fetch<{ data: Role[] }>('/api/roles', {
        query: siteId ? { siteId } : {}
      })

      roles.value = data
    } catch (err) {
      error.value = 'ไม่สามารถดึงข้อมูลได้'
      console.error('Error fetching roles:', err)
    } finally {
      loading.value = false
    }
  }

  // อัพเดท role
  const updateRole = async (roleId: string, updates: Partial<Role>) => {
    loading.value = true
    error.value = null

    try {
      const { data } = await $fetch<{ data: Role }>(`/api/roles/${roleId}`, {
        method: 'PUT',
        body: updates
      })

      // อัพเดทข้อมูลใน array
      const index = roles.value.findIndex(r => r._id === roleId)
      if (index !== -1) {
        roles.value[index] = data
      }

      return data
    } catch (err) {
      error.value = 'ไม่สามารถอัพเดทข้อมูลได้'
      console.error('Error updating role:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // ลบ role
  const deleteRole = async (roleId: string) => {
    loading.value = true
    error.value = null

    try {
      await $fetch(`/api/roles/${roleId}`, {
        method: 'DELETE'
      })

      // ลบออกจาก array
      roles.value = roles.value.filter(r => r._id !== roleId)
    } catch (err) {
      error.value = 'ไม่สามารถลบข้อมูลได้'
      console.error('Error deleting role:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // เพิ่ม role ใหม่
  const addRole = async (newRole: Omit<Role, '_id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    error.value = null

    try {
      const { data } = await $fetch<{ data: Role }>('/api/roles', {
        method: 'POST',
        body: newRole
      })

      roles.value.push(data)
      return data
    } catch (err) {
      error.value = 'ไม่สามารถเพิ่มข้อมูลได้'
      console.error('Error adding role:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // ฟิลเตอร์ roles ตาม status
  const activeRoles = computed(() =>
    roles.value.filter(role => role.status === 'active')
  )

  const pendingRoles = computed(() =>
    roles.value.filter(role => role.status === 'pending')
  )

  // ฟิลเตอร์ roles ตาม role type
  const ownerRoles = computed(() =>
    roles.value.filter(role => role.role === 'owner')
  )

  const adminRoles = computed(() =>
    roles.value.filter(role => role.role === 'admin')
  )

  const memberRoles = computed(() =>
    roles.value.filter(role => role.role === 'member')
  )

  // สถิติ
  const roleStats = computed(() => ({
    total: roles.value.length,
    active: activeRoles.value.length,
    pending: pendingRoles.value.length,
    owners: ownerRoles.value.length,
    admins: adminRoles.value.length,
    members: memberRoles.value.length
  }))

  return {
    roles: readonly(roles),
    loading: readonly(loading),
    error: readonly(error),
    fetchRoles,
    updateRole,
    deleteRole,
    addRole,
    activeRoles,
    pendingRoles,
    ownerRoles,
    adminRoles,
    memberRoles,
    roleStats
  }
}