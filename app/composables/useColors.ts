import { colors, getColor, getColorWithOpacity, semanticColors, type ColorName } from '~/utils/colors'

export const useColors = () => {
	/**
	 * Get color by name
	 */
	const getColorByName = (name: ColorName) => getColor(name)

	/**
	 * Get color with opacity
	 */
	const getColorWithOpacityByName = (name: ColorName, opacity: number) => 
		getColorWithOpacity(name, opacity)

	/**
	 * Get semantic color
	 */
	const getSemanticColor = (semanticName: keyof typeof semanticColors) => 
		semanticColors[semanticName]

	/**
	 * Convert hex to rgb
	 */
	const hexToRgb = (hex: string) => {
		const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
		return result ? {
			r: parseInt(result[1], 16),
			g: parseInt(result[2], 16),
			b: parseInt(result[3], 16)
		} : null
	}

	/**
	 * Convert hex to rgba
	 */
	const hexToRgba = (hex: string, alpha: number) => {
		const rgb = hexToRgb(hex)
		return rgb ? `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${alpha})` : hex
	}

	/**
	 * Get color palette for a specific color family
	 */
	const getColorPalette = (family: keyof typeof colors) => {
		const palette: Record<string, string> = {}
		Object.keys(colors).forEach(key => {
			if (key.startsWith(family)) {
				palette[key] = colors[key as ColorName]
			}
		})
		return palette
	}

	/**
	 * Get all available colors
	 */
	const getAllColors = () => colors

	/**
	 * Get all semantic colors
	 */
	const getAllSemanticColors = () => semanticColors

	/**
	 * Check if color is light or dark
	 */
	const isLightColor = (hex: string) => {
		const rgb = hexToRgb(hex)
		if (!rgb) return false
		const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000
		return brightness > 128
	}

	/**
	 * Get contrasting text color (black or white)
	 */
	const getContrastColor = (hex: string) => {
		return isLightColor(hex) ? '#000000' : '#ffffff'
	}

	/**
	 * Generate color variants (lighter/darker)
	 */
	const generateColorVariants = (hex: string, steps: number = 5) => {
		const rgb = hexToRgb(hex)
		if (!rgb) return []

		const variants: string[] = []
		for (let i = 0; i < steps; i++) {
			const factor = (i + 1) / steps
			const lighter = {
				r: Math.round(rgb.r + (255 - rgb.r) * factor),
				g: Math.round(rgb.g + (255 - rgb.g) * factor),
				b: Math.round(rgb.b + (255 - rgb.b) * factor)
			}
			variants.push(`rgb(${lighter.r}, ${lighter.g}, ${lighter.b})`)
		}

		return variants
	}

	return {
		// Core functions
		getColorByName,
		getColorWithOpacityByName,
		getSemanticColor,
		
		// Utility functions
		hexToRgb,
		hexToRgba,
		getColorPalette,
		getAllColors,
		getAllSemanticColors,
		isLightColor,
		getContrastColor,
		generateColorVariants,
		
		// Direct access to colors
		colors,
		semanticColors
	}
} 