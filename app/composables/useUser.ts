import type { User } from '../../shared/types/auth';
import type { ApiResponse } from '../../shared/types/respone';

export const useUser = () => {
  const toast = useToast();

  // Get users by site ID
  const getUsers = async (siteId: string) => {
    try {
      const response = await $fetch<ApiResponse<any[]>>(`/api/v1/user/${siteId}`);
      return response;
    } catch (error: any) {
      console.error('Error fetching users:', error);
      throw error;
    }
  };

  // Create user
  const createUser = async (siteId: string, userData: any) => {
    try {
      const response = await $fetch<ApiResponse<any>>(`/api/v1/user/${siteId}`, {
        method: 'POST',
        body: userData,
      });
      return response;
    } catch (error: any) {
      console.error('Error creating user:', error);
      throw error;
    }
  };

  // Get user by ID
  const getUserById = async (siteId: string, userId: string) => {
    try {
      const response = await $fetch<ApiResponse<any>>(`/api/v1/user/${siteId}/${userId}`);
      return response;
    } catch (error: any) {
      console.error('Error fetching user:', error);
      throw error;
    }
  };

  // Update user
  const updateUser = async (siteId: string, userId: string, userData: any) => {
    try {
      const response = await $fetch<ApiResponse<any>>(`/api/v1/user/${siteId}/${userId}`, {
        method: 'PUT',
        body: userData,
      });
      return response;
    } catch (error: any) {
      console.error('Error updating user:', error);
      throw error;
    }
  };

  // Delete user
  const deleteUser = async (siteId: string, userId: string) => {
    try {
      const response = await $fetch<ApiResponse<any>>(`/api/v1/user/${siteId}/${userId}`, {
        method: 'DELETE',
      });
      return response;
    } catch (error: any) {
      console.error('Error deleting user:', error);
      throw error;
    }
  };

  // Get user profile
  const getUserProfile = async () => {
    try {
      const response = await $fetch<ApiResponse<any>>('/api/v1/user/profile');
      return response;
    } catch (error: any) {
      console.error('Error fetching user profile:', error);
      throw error;
    }
  };

  // Update user profile
  const updateUserProfile = async (profileData: any) => {
    try {
      const response = await $fetch<ApiResponse<any>>('/api/v1/user/profile', {
        method: 'PUT',
        body: profileData,
      });
      return response;
    } catch (error: any) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  };

  // Change password
  const changePassword = async (passwordData: any) => {
    try {
      const response = await $fetch<ApiResponse<any>>('/api/v1/user/change-password', {
        method: 'PUT',
        body: passwordData,
      });
      return response;
    } catch (error: any) {
      console.error('Error changing password:', error);
      throw error;
    }
  };

  // ดึงประวัติการทำรายการ
  const getTransactionHistory = async (type?: string, limit?: number) => {
    const params = new URLSearchParams();
    if (type) params.append('type', type);
    if (limit) params.append('limit', limit.toString());

    return await $fetch<ApiResponse<any[]>>(`/api/v1/user/transactions?${params.toString()}`);
  };

  // เติมเงิน
  const topupPoints = async (topupData: {
    type: 'moneyPoint' | 'goldPoint';
    amount: number;
    packageId: string;
    paymentMethod: string;
  }) => {
    return await $fetch<ApiResponse<any>>('/api/v1/user/topup', {
      method: 'POST',
      body: topupData,
    });
  };

  // ดึงสถิติผู้ใช้
  const getUserStats = async () => {
    return await $fetch<ApiResponse<any>>('/api/v1/user/stats');
  };

  return {
    getUsers,
    createUser,
    getUserById,
    updateUser,
    deleteUser,
    getUserProfile,
    updateUserProfile,
    changePassword,
    getTransactionHistory,
    topupPoints,
    getUserStats,
  };
};
