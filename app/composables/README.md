# Composables สำหรับ SEO และ Meta Tags

## ภาพรวม
ระบบ SEO Composables ที่รองรับหลายภาษา (ไทย, อังกฤษ, ลาว) และใช้งานง่าย พร้อมฟีเจอร์ครบครัน

## การใช้งาน SEO Meta ในโปรเจ็ค (รองรับหลายภาษา)

### 1. useSeoMeta (พื้นฐาน)
```typescript
// ใช้งานพื้นฐาน
useSeoMeta({
  title: 'หน้าเว็บของฉัน',
  description: 'คำอธิบายหน้าเว็บ',
  keywords: 'คำค้นหา, seo, meta',
  type: 'website'
})

// ใช้งานแบบ reactive
const title = ref('หน้าเว็บของฉัน')
const description = computed(() => `${title.value} - รายละเอียด`)

useSeoMeta({
  title,
  description,
  type: 'website'
})

// อัปเดต SEO แบบ dynamic
const { updateSeo } = useSeoMeta({
  title: 'หน้าเริ่มต้น'
})

// อัปเดตภายหลัง
updateSeo({
  title: 'หน้าใหม่',
  description: 'คำอธิบายใหม่'
})
```

### 2. Composables เฉพาะหน้า

#### หน้าหลัก/Home
```typescript
// app/pages/index.vue
const { t } = useI18n()

// ใช้ composable เฉพาะ
useHomeSeo()

// หรือใช้แบบกำหนดเอง
useSeoMeta({
  title: t('home') + ' - ' + t('seo.default.title'),
  description: t('seo.default.description') + ' ' + t('dashboard.description'),
})
```

#### หน้า Dashboard
```typescript
// app/pages/dashboard/index.vue
useDashboardSeo()

// หรือกำหนด title และ description เอง
useDashboardSeo('จัดการระบบ', 'หน้าจัดการระบบหลัก')
```

#### หน้า Blog
```typescript
// app/pages/blog/index.vue
useBlogSeo()

// สำหรับบทความเฉพาะ
useArticleSeo(
  'ชื่อบทความ',
  'คำอธิบายบทความ',
  'ชื่อผู้เขียน',
  '2024-01-01T00:00:00Z',
  '/images/article.jpg',
  ['tag1', 'tag2']
)
```

#### หน้า Products
```typescript
// app/pages/products/index.vue
useProductsSeo()

// สำหรับสินค้าเฉพาะ
useProductSeo(
  'ชื่อสินค้า',
  'คำอธิบายสินค้า',
  '/images/product.jpg'
)
```

#### หน้า Auth
```typescript
// app/pages/auth/signin.vue
useAuthSeo(true) // สำหรับ login

// app/pages/auth/signup.vue
useAuthSeo(false) // สำหรับ signup
```

#### หน้าอื่นๆ
```typescript
// Analytics
useAnalyticsSeo()

// Orders
useOrdersSeo()

// Customers
useCustomersSeo()

// Help
useHelpSeo()

// Error pages
useErrorSeo(404) // หรือ 500
```

### 3. Structured Data
```typescript
// สำหรับองค์กร
useStructuredData('Organization', {
  name: 'บริษัทของเรา',
  description: 'คำอธิบายบริษัท',
  address: 'ที่อยู่บริษัท'
})

// สำหรับเว็บไซต์
useStructuredData('WebSite', {
  name: 'ชื่อเว็บไซต์',
  description: 'คำอธิบายเว็บไซต์'
})

// สำหรับบทความ
useStructuredData('Article', {
  title: 'ชื่อบทความ',
  description: 'คำอธิบาย',
  author: 'ผู้เขียน',
  publishedTime: '2024-01-01T00:00:00Z',
  image: '/images/article.jpg'
})

// สำหรับสินค้า
useStructuredData('Product', {
  name: 'ชื่อสินค้า',
  description: 'คำอธิบาย',
  price: '1000',
  image: '/images/product.jpg'
})
```

## ตัวอย่างการใช้งานในแต่ละภาษา

### ภาษาไทย
```typescript
const { t } = useI18n()

useSeoMeta({
  title: t('seo.default.title'), // "แดชบอร์ด Nuxt 4"
  description: t('seo.default.description'), // "ระบบจัดการเว็บไซต์ที่ทันสมัย..."
  keywords: t('seo.default.keywords') // "แดชบอร์ด, nuxt, vue, จัดการ, admin"
})
```

### ภาษาอังกฤษ
```typescript
const { t } = useI18n()

useSeoMeta({
  title: t('seo.default.title'), // "Nuxt 4 Dashboard"
  description: t('seo.default.description'), // "Modern dashboard built with Nuxt 4"
  keywords: t('seo.default.keywords') // "dashboard, nuxt, vue, management, admin"
})
```

### ภาษาลาว
```typescript
const { t } = useI18n()

useSeoMeta({
  title: t('seo.default.title'), // "ແຜງຄວບຄຸມ Nuxt 4"
  description: t('seo.default.description'), // "ລະບົບຈັດການເວັບໄຊທ໌ທີ່ທັນສະໄໝ..."
  keywords: t('seo.default.keywords') // "ແຜງຄວບຄຸມ, nuxt, vue, ຈັດການ, admin"
})
```

## การใช้งานแบบรวม

### ตัวอย่างหน้า Home
```typescript
const { t } = useI18n()

useSeoMeta({
  title: t('home') + ' - ' + t('seo.default.title'),
  description: t('seo.default.description') + ' ' + t('dashboard.description'),
  keywords: t('seo.default.keywords'),
  type: 'website'
})
```

### ตัวอย่างหน้า Products
```typescript
const { t } = useI18n()

useSeoMeta({
  title: t('products.title') + ' - ' + t('dashboard.title'),
  description: t('dashboard.description') + ' ' + t('products.title'),
  keywords: t('seo.default.keywords'),
  type: 'website'
})
```

## ฟีเจอร์ขั้นสูง

### 1. Meta Tags ที่รองรับ
- Basic SEO (title, description, keywords)
- Open Graph (Facebook, LinkedIn)
- Twitter Cards
- Article meta (สำหรับบทความ)
- Product meta (สำหรับสินค้า)
- Robots meta (noindex, nofollow)
- Canonical URL

### 2. การใช้งานแบบ Reactive
```typescript
const productData = ref({
  name: 'สินค้าตัวอย่าง',
  description: 'คำอธิบายสินค้า'
})

const title = computed(() => `${productData.value.name} - ร้านค้า`)
const description = computed(() => productData.value.description)

useSeoMeta({
  title,
  description,
  type: 'product'
})

// เมื่อ productData เปลี่ยน SEO จะอัปเดตอัตโนมัติ
```

### 3. การจัดการ Locale
```typescript
useSeoMeta({
  title: 'หน้าเว็บ',
  description: 'คำอธิบาย',
  locale: 'th-TH' // กำหนด locale เฉพาะ
})
```

### 4. การใช้งาน Twitter และ Social Media
```typescript
useSeoMeta({
  title: 'หน้าเว็บ',
  description: 'คำอธิบาย',
  image: '/images/social-share.jpg',
  twitterSite: '@mywebsite',
  twitterCreator: '@author'
})
```

## การตั้งค่า i18n Keys ที่จำเป็น

ในไฟล์ `i18n/locales/*.json` ต้องมี keys เหล่านี้:

```json
{
  "seo": {
    "default.title": "ชื่อเว็บไซต์",
    "default.description": "คำอธิบายเว็บไซต์",
    "default.keywords": "คำค้นหา, seo, meta"
  },
  "auth": {
    "signin": {
      "title": "เข้าสู่ระบบ - แดชบอร์ด",
      "description": "เข้าสู่ระบบเพื่อใช้งานแดชบอร์ด"
    },
    "signup": {
      "title": "สมัครสมาชิก - แดชบอร์ด",
      "description": "สร้างบัญชีใหม่สำหรับแดชบอร์ด"
    }
  },
  "dashboard": {
    "title": "แดชบอร์ด",
    "description": "จัดการเว็บไซต์ สินค้า และข้อมูลวิเคราะห์"
  },
  "products": {
    "title": "สินค้า"
  },
  "blog": {
    "title": "บล็อก"
  },
  "analytics": {
    "title": "วิเคราะห์ข้อมูล",
    "description": "ดูข้อมูลวิเคราะห์และข้อมูลเชิงลึก"
  },
  "orders": {
    "title": "คำสั่งซื้อ",
    "description": "จัดการและติดตามคำสั่งซื้อ"
  },
  "customers": {
    "title": "ลูกค้า",
    "description": "จัดการฐานข้อมูลลูกค้า"
  },
  "help": {
    "title": "วิธีใช้งาน",
    "description": "คู่มือการใช้งานและความช่วยเหลือ"
  }
}
```

## Best Practices

1. **ใช้ Composables เฉพาะหน้า** - ใช้ `useHomeSeo()`, `useBlogSeo()` แทนการเขียน `useSeoMeta()` ซ้ำๆ
2. **ใช้ i18n keys** - อย่าใส่ข้อความตรงๆ ให้ใช้ `t('key')` เสมอ
3. **ใช้ Reactive values** - ใช้ `computed()` หรือ `ref()` สำหรับข้อมูลที่เปลี่ยนแปลง
4. **ตั้งค่า noIndex สำหรับหน้าที่ไม่ต้องการให้ search engine index** - เช่น หน้า login, error
5. **ใช้ Structured Data** - เพิ่ม `useStructuredData()` สำหรับ SEO ที่ดีขึ้น
6. **ตรวจสอบ Meta Tags** - ใช้ browser dev tools หรือ SEO tools ตรวจสอบ

## การแก้ไขปัญหา

### ปัญหา: ไม่พบ i18n key
```
WARN [intlify] Not found 'seo.default.title' key in 'th' locale messages.
```

**วิธีแก้:** เพิ่ม key ในไฟล์ `i18n/locales/th-TH.json`

### ปัญหา: Meta tags ไม่อัปเดต
**วิธีแก้:** ใช้ `computed()` หรือ `ref()` แทนค่าคงที่

### ปัญหา: Duplicate meta tags
**วิธีแก้:** ใช้เพียง composable เดียวต่อหน้า อย่าเรียกหลาย composables พร้อมกัน