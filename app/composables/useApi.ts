export const useApi = () => {
  // Types
  interface ApiKey {
    id: string;
    name: string;
    description: string;
    key: string;
    maskedKey: string;
    permissions: string[];
    requestCount: number;
    status: 'active' | 'inactive' | 'expired';
    rateLimit: number;
    createdAt: string;
    expiresAt?: string;
    lastUsedAt?: string;
  }

  interface ApiKeyForm {
    name: string;
    description: string;
    permissions: string[];
    rateLimit: number;
    expiresAt?: string;
  }

  interface ApiKeyFilters {
    search?: string;
    status?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    page?: number;
    limit?: number;
  }

  // Refactor getApiKeys เพื่อลด complexity
  const getApiKeys = async (siteId: string, filters?: ApiKeyFilters) => {
    try {
      const params = new URLSearchParams();
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, String(value));
          }
        });
      }
      const url = `/v1/site/${siteId}/api-keys?${params.toString()}`;
      return await $fetch(url);
    } catch (error: unknown) {
      throw new Error(
        (error as { data?: { message?: string } }).data?.message || 'ไม่สามารถโหลดข้อมูล API Key ได้'
      );
    }
  };

  // Get single API key
  const getApiKey = async (siteId: string, keyId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/api-keys/${keyId}`);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูล API Key ได้');
    }
  };

  // Create API key
  const createApiKey = async (siteId: string, data: ApiKeyForm) => {
    try {
      return await $fetch(`/v1/site/${siteId}/api-keys`, {
        method: 'POST',
        body: data,
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถสร้าง API Key ได้');
    }
  };

  // Update API key
  const updateApiKey = async (siteId: string, keyId: string, data: Partial<ApiKeyForm>) => {
    try {
      return await $fetch(`/v1/site/${siteId}/api-keys/${keyId}`, {
        method: 'PUT',
        body: data,
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถอัปเดต API Key ได้');
    }
  };

  // Delete API key
  const deleteApiKey = async (siteId: string, keyId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/api-keys/${keyId}`, {
        method: 'DELETE',
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถลบ API Key ได้');
    }
  };

  // Regenerate API key
  const regenerateApiKey = async (siteId: string, keyId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/api-keys/${keyId}/regenerate`, {
        method: 'POST',
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถสร้าง API Key ใหม่ได้');
    }
  };

  // Activate/Deactivate API key
  const toggleApiKey = async (siteId: string, keyId: string, status: 'active' | 'inactive') => {
    try {
      return await $fetch(`/v1/site/${siteId}/api-keys/${keyId}/toggle`, {
        method: 'POST',
        body: { status },
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถเปลี่ยนสถานะ API Key ได้');
    }
  };

  // Get API usage statistics
  const getApiUsage = async (siteId: string, keyId?: string) => {
    try {
      const url = keyId
        ? `/v1/site/${siteId}/api-keys/${keyId}/usage`
        : `/v1/site/${siteId}/api-keys/usage`;

      return await $fetch(url);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลการใช้งาน API ได้');
    }
  };

  // Get API analytics
  const getApiAnalytics = async (siteId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/api-keys/analytics`);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลสถิติได้');
    }
  };

  return {
    getApiKeys,
    getApiKey,
    createApiKey,
    updateApiKey,
    deleteApiKey,
    regenerateApiKey,
    toggleApiKey,
    getApiUsage,
    getApiAnalytics,
  };
};
