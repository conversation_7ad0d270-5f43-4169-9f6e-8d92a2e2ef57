export const useThemes = () => {
  // Types
  interface Theme {
    id: string;
    name: string;
    description: string;
    status: 'active' | 'inactive' | 'draft';
    version: string;
    author: string;
    created_at: string;
    updated_at: string;
    preview: string;
    color: string;
    config?: any;
  }

  interface ThemeForm {
    name: string;
    description: string;
    status: 'active' | 'inactive' | 'draft';
    version: string;
    author: string;
    preview: string;
    color: string;
    config?: any;
  }

  interface ThemeFilters {
    search?: string;
    status?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    page?: number;
    limit?: number;
  }

  // Get themes list
  const getThemes = async (siteId: string, filters?: ThemeFilters) => {
    try {
      const params = new URLSearchParams();
      if (filters?.search) params.append('search', filters.search);
      if (filters?.status && filters.status !== 'all') params.append('status', filters.status);
      if (filters?.sortBy) params.append('sortBy', filters.sortBy);
      if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const queryString = params.toString();
      const url = `/v1/site/${siteId}/themes${queryString ? `?${queryString}` : ''}`;

      return await $fetch(url);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลธีมได้');
    }
  };

  // Get single theme
  const getTheme = async (siteId: string, themeId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/themes/${themeId}`);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลธีมได้');
    }
  };

  // Activate theme
  const activateTheme = async (siteId: string, themeId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/themes/${themeId}/activate`, {
        method: 'POST',
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถเปิดใช้งานธีมได้');
    }
  };

  // Deactivate theme
  const deactivateTheme = async (siteId: string, themeId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/themes/${themeId}/deactivate`, {
        method: 'POST',
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถปิดใช้งานธีมได้');
    }
  };

  // Install theme
  const installTheme = async (siteId: string, themeId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/themes/${themeId}/install`, {
        method: 'POST',
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถติดตั้งธีมได้');
    }
  };

  // Uninstall theme
  const uninstallTheme = async (siteId: string, themeId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/themes/${themeId}/uninstall`, {
        method: 'POST',
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถถอนการติดตั้งธีมได้');
    }
  };

  // Configure theme
  const configureTheme = async (siteId: string, themeId: string, config: any) => {
    try {
      return await $fetch(`/v1/site/${siteId}/themes/${themeId}/configure`, {
        method: 'PUT',
        body: config,
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถกำหนดค่าธีมได้');
    }
  };

  // Preview theme
  const previewTheme = async (siteId: string, themeId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/themes/${themeId}/preview`);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถดูตัวอย่างธีมได้');
    }
  };

  // Get theme analytics
  const getThemeAnalytics = async (siteId: string) => {
    try {
      return await $fetch(`/v1/site/${siteId}/themes/analytics`);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลสถิติได้');
    }
  };

  return {
    getThemes,
    getTheme,
    activateTheme,
    deactivateTheme,
    installTheme,
    uninstallTheme,
    configureTheme,
    previewTheme,
    getThemeAnalytics,
  };
};
