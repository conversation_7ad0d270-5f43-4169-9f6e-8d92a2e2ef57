export const usePermissions = () => {
  // Types
  interface Permission {
    id: string;
    name: string;
    displayName: string;
    module: string;
    status: 'active' | 'inactive';
    description: string;
    roles: string[];
    createdAt: string;
    updatedAt: string;
  }

  interface PermissionForm {
    name: string;
    displayName: string;
    module: string;
    status: 'active' | 'inactive';
    description: string;
    roles: string[];
  }

  interface PermissionFilters {
    search?: string;
    status?: string;
    module?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    page?: number;
    limit?: number;
  }

  // Get permissions list
  const getPermissions = async (siteId: string, filters?: PermissionFilters) => {
    try {
      const params = new URLSearchParams();
      if (filters?.search) params.append('search', filters.search);
      if (filters?.status && filters.status !== 'all') params.append('status', filters.status);
      if (filters?.module && filters.module !== 'all') params.append('module', filters.module);
      if (filters?.sortBy) params.append('sortBy', filters.sortBy);
      if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const queryString = params.toString();
      const url = `/v1/team/${siteId}/permissions${queryString ? `?${queryString}` : ''}`;

      return await $fetch(url);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลสิทธิ์ได้');
    }
  };

  // Get single permission
  const getPermission = async (siteId: string, permissionId: string) => {
    try {
      return await $fetch(`/v1/team/${siteId}/permissions/${permissionId}`);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลสิทธิ์ได้');
    }
  };

  // Create permission
  const createPermission = async (siteId: string, data: PermissionForm) => {
    try {
      return await $fetch(`/v1/team/${siteId}/permissions`, {
        method: 'POST',
        body: data,
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถสร้างสิทธิ์ได้');
    }
  };

  // Update permission
  const updatePermission = async (siteId: string, permissionId: string, data: PermissionForm) => {
    try {
      return await $fetch(`/v1/team/${siteId}/permissions/${permissionId}`, {
        method: 'PUT',
        body: data,
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถอัปเดตสิทธิ์ได้');
    }
  };

  // Delete permission
  const deletePermission = async (siteId: string, permissionId: string) => {
    try {
      return await $fetch(`/v1/team/${siteId}/permissions/${permissionId}`, {
        method: 'DELETE',
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถลบสิทธิ์ได้');
    }
  };

  // Duplicate permission
  const duplicatePermission = async (siteId: string, permissionId: string) => {
    try {
      return await $fetch(`/v1/team/${siteId}/permissions/${permissionId}/duplicate`, {
        method: 'POST',
      });
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถคัดลอกสิทธิ์ได้');
    }
  };

  // Get permission analytics
  const getPermissionAnalytics = async (siteId: string) => {
    try {
      return await $fetch(`/v1/team/${siteId}/permissions/analytics`);
    } catch (error: any) {
      throw new Error(error.data?.message || 'ไม่สามารถโหลดข้อมูลสถิติได้');
    }
  };

  return {
    getPermissions,
    getPermission,
    createPermission,
    updatePermission,
    deletePermission,
    duplicatePermission,
    getPermissionAnalytics,
  };
};
