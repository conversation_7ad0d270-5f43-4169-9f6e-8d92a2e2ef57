// ทดสอบ Cloudinary URL generation
import { v2 as cloudinary } from 'cloudinary';

// ตั้งค่า Cloudinary
cloudinary.config({
  cloud_name: 'dsoy8tgfc',
  api_key: '139741931552624',
  api_secret: 'LR7ShBIjTR4kjZajyOl-geawG4s',
});

console.log('=== ทดสอบ Cloudinary URLs ===');

// ทดสอบ Public URL
const publicUrl = cloudinary.url('sample', {
  secure: true,
  resource_type: 'image',
  transformation: [{ quality: 'auto', fetch_format: 'auto' }],
});

console.log('Public URL:', publicUrl);

// ทดสอบ Signed URL สำหรับ restricted image
const timestamp = Math.round(Date.now() / 1000);

const signedUrl = cloudinary.url('wrqxdnopfn29xi4kamda', {
  secure: true,
  sign_url: true,
  type: 'authenticated',
  resource_type: 'image',
  timestamp: timestamp,
  transformation: [{ quality: 'auto', fetch_format: 'auto' }],
});

console.log('Signed URL:', signedUrl);

// ทดสอบ URL อื่นๆ
const simplePublic = cloudinary.url('sample');
console.log('Simple Public URL:', simplePublic);

const simpleRestricted = cloudinary.url('wrqxdnopfn29xi4kamda', {
  sign_url: true,
  type: 'authenticated',
});
console.log('Simple Restricted URL:', simpleRestricted);
