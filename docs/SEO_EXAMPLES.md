# ตัวอย่างการใช้งาน SEO Composables

## การใช้งานพื้นฐาน

### 1. หน้าหลัก (Homepage)
```vue
<!-- app/pages/index.vue -->
<script setup>
// วิธีใหม่ - ง่ายและสะอาด
useHomeSeo();

// หรือถ้าต้องการปรับแต่ง
useSeoMeta({
  title: computed(() => `${t('home')} - ${t('seo.default.title')}`),
  description: computed(() => t('seo.default.description')),
  type: 'website'
});
</script>
```

### 2. หน้าสินค้า (Product Page)
```vue
<!-- app/pages/products/[id].vue -->
<script setup>
const route = useRoute();
const productId = route.params.id;

// ดึงข้อมูลสินค้า
const { data: product } = await $fetch(`/api/products/${productId}`);

// ใช้ SEO สำหรับสินค้า
useProductSeo(
  product.name,
  product.description,
  product.image
);

// หรือแบบ reactive
const title = computed(() => `${product.name} - ${t('products.title')}`);
const description = computed(() => product.description);

useSeoMeta({
  title,
  description,
  image: product.image,
  type: 'product'
});
</script>
```

### 3. หน้าบทความ (Blog Post)
```vue
<!-- app/pages/blog/[slug].vue -->
<script setup>
const route = useRoute();
const slug = route.params.slug;

const { data: post } = await $fetch(`/api/posts/${slug}`);

// ใช้ SEO สำหรับบทความ
useArticleSeo(
  post.title,
  post.excerpt,
  post.author.name,
  post.published_at,
  post.featured_image,
  post.tags
);

// เพิ่ม Structured Data
useStructuredData('Article', {
  title: post.title,
  description: post.excerpt,
  author: post.author.name,
  publishedTime: post.published_at,
  modifiedTime: post.updated_at,
  image: post.featured_image,
  tags: post.tags
});
</script>
```

### 4. หน้า Dashboard
```vue
<!-- app/pages/dashboard/index.vue -->
<script setup>
// ใช้ SEO สำหรับ Dashboard
useDashboardSeo();

// หรือกำหนดเอง
useDashboardSeo('ภาพรวม', 'ดูสถิติและข้อมูลสำคัญของเว็บไซต์');
</script>
```

## การใช้งานขั้นสูง

### 1. SEO แบบ Dynamic
```vue
<script setup>
const pageData = ref({
  title: 'หน้าเริ่มต้น',
  description: 'คำอธิบายเริ่มต้น'
});

// สร้าง SEO และเก็บ function สำหรับอัปเดต
const { updateSeo } = useSeoMeta({
  title: pageData.value.title,
  description: pageData.value.description
});

// อัปเดต SEO เมื่อข้อมูลเปลี่ยน
const updatePageData = (newData) => {
  pageData.value = newData;
  updateSeo({
    title: newData.title,
    description: newData.description
  });
};

// หรือใช้ watch
watch(pageData, (newData) => {
  updateSeo({
    title: newData.title,
    description: newData.description
  });
}, { deep: true });
</script>
```

### 2. SEO แบบ Reactive
```vue
<script setup>
const user = ref({ name: 'John Doe', bio: 'Developer' });
const settings = ref({ siteName: 'My Website' });

// ใช้ computed สำหรับค่าที่เปลี่ยนแปลง
const title = computed(() => `${user.value.name} - ${settings.value.siteName}`);
const description = computed(() => user.value.bio);

useSeoMeta({
  title,
  description,
  type: 'profile'
});

// เมื่อ user หรือ settings เปลี่ยน SEO จะอัปเดตอัตโนมัติ
</script>
```

### 3. SEO สำหรับหน้า E-commerce
```vue
<!-- app/pages/shop/[category]/[product].vue -->
<script setup>
const route = useRoute();
const { category, product: productSlug } = route.params;

const { data: product } = await $fetch(`/api/products/${productSlug}`);
const { data: categoryData } = await $fetch(`/api/categories/${category}`);

// SEO สำหรับสินค้า
useSeoMeta({
  title: computed(() => `${product.name} - ${categoryData.name} | ${t('seo.default.title')}`),
  description: computed(() => product.description),
  keywords: computed(() => [product.name, categoryData.name, ...product.tags].join(', ')),
  image: product.images[0],
  type: 'product',
  canonical: computed(() => `${useRuntimeConfig().public.siteUrl}/shop/${category}/${productSlug}`)
});

// Structured Data สำหรับสินค้า
useStructuredData('Product', {
  name: product.name,
  description: product.description,
  image: product.images,
  price: product.price,
  currency: 'THB',
  availability: product.stock > 0 ? 'InStock' : 'OutOfStock',
  brand: product.brand,
  category: categoryData.name
});
</script>
```

### 4. SEO สำหรับหน้าค้นหา
```vue
<!-- app/pages/search.vue -->
<script setup>
const route = useRoute();
const query = computed(() => route.query.q as string || '');
const results = ref([]);

// SEO สำหรับหน้าค้นหา
const title = computed(() => {
  if (!query.value) return `ค้นหา - ${t('seo.default.title')}`;
  return `ผลการค้นหา "${query.value}" - ${t('seo.default.title')}`;
});

const description = computed(() => {
  if (!query.value) return 'ค้นหาสินค้าและบทความ';
  return `พบ ${results.value.length} รายการสำหรับ "${query.value}"`;
});

useSeoMeta({
  title,
  description,
  noIndex: true, // ไม่ให้ search engine index หน้าค้นหา
  noFollow: true
});
</script>
```

## การใช้งานตามภาษา

### ภาษาไทย
```vue
<script setup>
// ระบบจะใช้ i18n keys อัตโนมัติ
useHomeSeo(); // จะได้ "แดชบอร์ด Nuxt 4"

// หรือกำหนดเอง
useSeoMeta({
  title: 'หน้าแรก - เว็บไซต์ของเรา',
  description: 'ยินดีต้อนรับสู่เว็บไซต์ของเรา',
  keywords: 'เว็บไซต์, ไทย, ธุรกิจ'
});
</script>
```

### ภาษาอังกฤษ
```vue
<script setup>
// เปลี่ยนภาษาใน i18n
const { locale } = useI18n();
locale.value = 'en';

useHomeSeo(); // จะได้ "Nuxt 4 Dashboard"
</script>
```

### ภาษาลาว
```vue
<script setup>
const { locale } = useI18n();
locale.value = 'lo';

useHomeSeo(); // จะได้ "ແຜງຄວບຄຸມ Nuxt 4"
</script>
```

## การจัดการ Error Pages

### หน้า 404
```vue
<!-- app/error.vue -->
<script setup>
const props = defineProps<{
  error: { statusCode: number, statusMessage: string }
}>();

// SEO สำหรับหน้า error
useErrorSeo(props.error.statusCode);

// หรือกำหนดเอง
useSeoMeta({
  title: computed(() => {
    switch (props.error.statusCode) {
      case 404: return 'ไม่พบหน้าที่ต้องการ';
      case 500: return 'เกิดข้อผิดพลาดของเซิร์ฟเวอร์';
      default: return 'เกิดข้อผิดพลาด';
    }
  }),
  description: 'กรุณาตรวจสอบ URL หรือกลับไปหน้าหลัก',
  noIndex: true,
  noFollow: true
});
</script>
```

## Best Practices

### 1. ใช้ Composables เฉพาะหน้า
```vue
<!-- ดี ✅ -->
<script setup>
useHomeSeo();
useBlogSeo();
useProductsSeo();
</script>

<!-- ไม่ดี ❌ -->
<script setup>
useSeoMeta({
  title: t('home') + ' - ' + t('seo.default.title'),
  description: t('seo.default.description'),
  // ... ซ้ำๆ ในทุกหน้า
});
</script>
```

### 2. ใช้ Reactive Values
```vue
<!-- ดี ✅ -->
<script setup>
const product = ref({ name: 'iPhone', price: 25000 });
const title = computed(() => `${product.value.name} - ราคา ${product.value.price} บาท`);

useSeoMeta({ title });
</script>

<!-- ไม่ดี ❌ -->
<script setup>
const product = ref({ name: 'iPhone', price: 25000 });

useSeoMeta({
  title: `${product.value.name} - ราคา ${product.value.price} บาท` // ไม่ reactive
});
</script>
```

### 3. ใช้ i18n Keys
```vue
<!-- ดี ✅ -->
<script setup>
const { t } = useI18n();
useSeoMeta({
  title: t('seo.default.title'),
  description: t('seo.default.description')
});
</script>

<!-- ไม่ดี ❌ -->
<script setup>
useSeoMeta({
  title: 'Nuxt 4 Dashboard', // hardcode
  description: 'Modern dashboard' // hardcode
});
</script>
```

### 4. ตั้งค่า noIndex สำหรับหน้าที่ไม่ต้องการ
```vue
<!-- หน้า Login, Admin, Error -->
<script setup>
useSeoMeta({
  title: 'เข้าสู่ระบบ',
  description: 'เข้าสู่ระบบเพื่อใช้งาน',
  noIndex: true, // ไม่ให้ Google index
  noFollow: true
});
</script>
```

### 5. ใช้ Structured Data
```vue
<script setup>
// สำหรับองค์กร
useStructuredData('Organization', {
  name: 'บริษัทเรา',
  url: 'https://ourcompany.com',
  logo: 'https://ourcompany.com/logo.png'
});

// สำหรับบทความ
useStructuredData('Article', {
  title: 'บทความของเรา',
  author: 'ผู้เขียน',
  publishedTime: '2024-01-01T00:00:00Z'
});
</script>
```

## การทดสอบ

### 1. ตรวจสอบ Meta Tags
```javascript
// ใน Browser Console
console.log(document.querySelector('title').textContent);
console.log(document.querySelector('meta[name="description"]').content);
console.log(document.querySelector('meta[property="og:title"]').content);
```

### 2. ตรวจสอบ Structured Data
```javascript
// ดู JSON-LD
console.log(document.querySelector('script[type="application/ld+json"]').textContent);
```

### 3. ใช้เครื่องมือ SEO
- Google Search Console
- Facebook Sharing Debugger
- Twitter Card Validator
- Rich Results Test (Google)

## การแก้ไขปัญหา

### ปัญหา: Meta tags ไม่อัปเดต
```vue
<!-- แก้ไข: ใช้ computed -->
<script setup>
const data = ref({ title: 'เริ่มต้น' });

// ❌ ไม่ reactive
useSeoMeta({ title: data.value.title });

// ✅ reactive
const title = computed(() => data.value.title);
useSeoMeta({ title });
</script>
```

### ปัญหา: i18n key ไม่พบ
```vue
<!-- แก้ไข: ตรวจสอบ key ในไฟล์ JSON -->
<script setup>
const { t } = useI18n();

// ❌ key ไม่มีในไฟล์ JSON
useSeoMeta({ title: t('page.not.exist') });

// ✅ key ที่มีอยู่จริง
useSeoMeta({ title: t('seo.default.title') });
</script>
```

### ปัญหา: Duplicate meta tags
```vue
<!-- แก้ไข: ใช้เพียง composable เดียว -->
<script setup>
// ❌ ใช้หลาย composables
useHomeSeo();
useSeoMeta({ title: 'อื่น' }); // จะ conflict

// ✅ ใช้เพียงอันเดียว
useHomeSeo();
</script>
```