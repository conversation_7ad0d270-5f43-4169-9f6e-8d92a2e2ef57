# ระบบ Auto-Logout เมื่อ Token หมดอายุ

## 🎯 วัตถุประสงค์
เมื่อ JWT token หมดอายุหรือไม่สามารถ refresh ได้ ระบบจะทำการ logout อัตโนมัติและ redirect ไปหน้า `/signin`

## 🏗️ โครงสร้างที่เพิ่มเข้ามา

### 1. **Enhanced Server-side Auth Handler**
```typescript
// server/utils/auth.ts
async function handleAuthFailure(event: H3Event, reason: string): Promise<never> {
  console.log('Authentication failed:', reason);
  clearUserSession(event);
  
  throw createError({
    statusCode: 401,
    statusMessage: 'Authentication failed',
    data: {
      reason,
      shouldRedirect: true,
      redirectTo: '/signin'
    }
  });
}
```

### 2. **Auth Error Handler Composable**
```typescript
// app/composables/useAuthError.ts
export const useAuthError = () => {
  const handleAuthError = async (error: any) => {
    if (error?.statusCode === 401) {
      await authStore.doLogout();
      
      if (error?.data?.shouldRedirect) {
        await router.push(error.data.redirectTo || '/signin');
      }
    }
  };
}
```

### 3. **Global Error Handler Plugin**
```typescript
// app/plugins/auth-error-handler.client.ts
// จัดการ auth errors ทั่วทั้งแอป
// Override $fetch เพื่อจัดการ auth errors อัตโนมัติ
```

## 🔄 Flow การทำงาน

### เมื่อ Token หมดอายุ:
1. **Server Detection** - `requireAuth()` ตรวจพบ token หมดอายุ
2. **Refresh Attempt** - พยายาม refresh token
3. **Refresh Failed** - ถ้า refresh ไม่สำเร็จ
4. **Clear Session** - ล้าง server session
5. **Send Error Response** - ส่ง 401 error พร้อม `shouldRedirect: true`
6. **Client Handling** - Client รับ error และทำ logout
7. **Auto Redirect** - Redirect ไป `/signin` อัตโนมัติ

### เมื่อ JWT Verification ล้มเหลว:
1. **Invalid Token** - JWT verification ล้มเหลว (เช่น signature ไม่ตรง)
2. **Return Null Session** - `getUserSession()` return null
3. **Auth Required** - `requireAuth()` ตรวจพบไม่มี session
4. **Handle Failure** - เรียก `handleAuthFailure()`
5. **Auto Logout** - ทำ logout และ redirect อัตโนมัติ

## 📁 ไฟล์ที่เกี่ยวข้อง

### Server-side
- `server/utils/auth.ts` - Enhanced auth handler
- `server/utils/session.ts` - JWT verification with clock tolerance
- `server/utils/apiFetch.ts` - API fetch utility

### Client-side
- `app/composables/useAuthError.ts` - Auth error handler
- `app/composables/useSession.ts` - Session management
- `app/stores/auth.ts` - Auth store with error handling
- `app/middleware/auth.ts` - Auth middleware
- `app/plugins/auth-error-handler.client.ts` - Global error handler

## ✅ สถานการณ์ที่จัดการได้

### 1. **Token หมดอายุ**
- ✅ ตรวจจับได้ทันที
- ✅ พยายาม refresh อัตโนมัติ
- ✅ ถ้า refresh ไม่ได้ → logout + redirect

### 2. **Refresh Token หมดอายุ**
- ✅ ตรวจจับเมื่อ refresh ล้มเหลว
- ✅ Logout + redirect ทันที

### 3. **JWT Signature ไม่ถูกต้อง**
- ✅ JWT verification ล้มเหลว
- ✅ Session เป็น null → logout + redirect

### 4. **Network/Backend ล้มเหลว**
- ✅ API call ล้มเหลว → จัดการ error
- ✅ ถ้าเป็น 401 → logout + redirect

### 5. **Manual Token Tampering**
- ✅ JWT verification จะล้มเหลว
- ✅ Auto logout + redirect

## 🔧 การตั้งค่าเพิ่มเติม

### JWT Clock Tolerance
```typescript
// server/utils/session.ts
const { payload } = await jwtVerify(sessionCookie, JWT_SECRET, {
  clockTolerance: '5m', // อนุญาตให้เวลาต่างกันได้ 5 นาที
});
```

### Debug Logging
```typescript
console.log('Token expiry check:', {
  now,
  exp: session.exp,
  expired: session.exp && session.exp < now,
  timeLeft: session.exp ? session.exp - now : 'N/A'
});
```

## 🚀 ประโยชน์ที่ได้รับ

### Security
- ✅ ป้องกัน unauthorized access
- ✅ Auto-cleanup expired sessions
- ✅ Consistent auth state management

### User Experience
- ✅ Seamless logout experience
- ✅ Auto-redirect ไม่ต้องรอ
- ✅ Clear error messages

### Developer Experience
- ✅ Centralized error handling
- ✅ Debug logging ครบถ้วน
- ✅ Easy to maintain

## 🧪 การทดสอบ

### Test Cases
1. **Token หมดอายุ** - ตั้ง exp ให้เป็นอดีต
2. **Refresh token หมดอายุ** - ลบ refresh token
3. **JWT signature ผิด** - เปลี่ยน JWT secret
4. **Backend ล้มเหลว** - ปิด backend server
5. **Network error** - ตัดเน็ต

### Expected Behavior
- ทุก case ต้อง logout + redirect ไป `/signin`
- ไม่มี infinite loops
- Error messages ชัดเจน

## 📋 Checklist

### ✅ Completed
- [x] Enhanced server-side auth handler
- [x] Client-side error handling
- [x] Global error handler plugin
- [x] Auth middleware improvements
- [x] JWT clock tolerance
- [x] Debug logging

### 🔄 Next Steps (Optional)
- [ ] Add retry mechanism for network errors
- [ ] Implement progressive auth degradation
- [ ] Add auth event analytics
- [ ] Create auth health monitoring

## 🎉 สรุป

ระบบ auto-logout เมื่อ token หมดอายุได้ถูกติดตั้งเรียบร้อยแล้ว! 

**ผลลัพธ์:**
- เมื่อ token หมดอายุ → ระบบจะ logout และ redirect ไป `/signin` อัตโนมัติ
- เมื่อ refresh ไม่ได้ → ระบบจะ logout และ redirect ไป `/signin` อัตโนมัติ
- เมื่อ JWT ไม่ถูกต้อง → ระบบจะ logout และ redirect ไป `/signin` อัตโนมัติ

ระบบพร้อมใช้งานแล้ว! 🚀