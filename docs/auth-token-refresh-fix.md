# 🔧 แก้ไขปัญหา Token Refresh System

## 🚨 ปัญหาที่พบ

```
JWT verification failed: {
  error: '"exp" claim timestamp check failed',
  cause: { claim: 'exp', reason: 'check_failed', payload: { exp: 1753246428 } },
  currentTime: 1753250646
}
```

**สาเหตุ:**
- JWT token หมดอายุแล้ว (exp: 1753246428 < current: 1753250646)
- ระบบ refresh token ไม่ทำงาน
- API calls ได้รับ 401 Authentication failed

## 🔍 การวิเคราะห์

### 1. JWT Verification ล้มเหลว
- Token หมดอายุ 4,218 วินาที (ประมาณ 1 ชั่วโมง)
- `clockTolerance: '5m'` ไม่เพียงพอ
- `getUserSession()` return null

### 2. Refresh Token Flow ไม่ทำงาน
- เมื่อ `getUserSession()` return null
- `requireAuth()` เรียก `handleAuthFailure()` ทันที
- ไม่ได้เข้าสู่ refresh token logic

## ✅ วิธีแก้ไข

### 1. ปรับปรุง getUserSession() ให้รองรับ expired token

```typescript
// server/utils/session.ts
export async function getUserSession(event: H3Event, allowExpired = false): Promise<SessionData | null> {
  const sessionCookie = getCookie(event, 'session');
  const refreshTokenCookie = getCookie(event, 'refreshToken');

  if (!sessionCookie) {
    return null;
  }

  try {
    const { payload } = await jwtVerify(sessionCookie, JWT_SECRET, {
      clockTolerance: allowExpired ? '24h' : '5m', // อนุญาต expired token เมื่อต้องการ
    });

    return {
      ...payload as SessionData,
      accessToken: sessionCookie,
      refreshToken: refreshTokenCookie,
    };
  } catch (error) {
    // ถ้า allowExpired = true และ error เป็น exp check failed
    if (allowExpired && error.code === 'ERR_JWT_EXPIRED') {
      try {
        // Decode token โดยไม่ verify exp
        const { payload } = await jwtVerify(sessionCookie, JWT_SECRET, {
          clockTolerance: '24h'
        });
        
        return {
          ...payload as SessionData,
          accessToken: sessionCookie,
          refreshToken: refreshTokenCookie,
          expired: true // flag ว่า token หมดอายุ
        };
      } catch (decodeError) {
        console.error('Token decode failed:', decodeError);
        return null;
      }
    }
    
    console.error('JWT verification failed:', error);
    return null;
  }
}
```

### 2. ปรับปรุง requireAuth() ให้จัดการ expired token

```typescript
// server/utils/auth.ts
export async function requireAuth(event: H3Event) {
  // ลองดึง session ปกติก่อน
  let session = await getUserSession(event);
  
  // ถ้าไม่มี session ลองดึงแบบ allowExpired
  if (!session) {
    session = await getUserSession(event, true);
    
    if (!session) {
      await handleAuthFailure(event, 'No session found');
    }
  }

  // ตรวจสอบ token หมดอายุ
  const now = Math.floor(Date.now() / 1000);
  const isExpired = session.expired || (session.exp && session.exp < now);

  if (isExpired) {
    console.log('Token expired, attempting refresh');

    if (!session.refreshToken) {
      console.log('No refresh token available');
      await handleAuthFailure(event, 'Session expired - no refresh token');
    }

    try {
      // เรียก backend เพื่อ refresh token
      const response = await apiFetch('/user/refresh-token', {
        method: 'POST',
        body: { refreshToken: session.refreshToken },
        requireAuth: false,
      }, event);

      const newToken = response?.data?.token;
      const newRefreshToken = response?.data?.refreshToken;

      if (!newToken) {
        await handleAuthFailure(event, 'Token refresh failed - no new token');
      }

      await refreshSessionToken(event, newToken, newRefreshToken);
      const newSession = await getUserSession(event);
      console.log('Token refreshed successfully');
      return newSession;

    } catch (error) {
      console.error('Token refresh error:', error);
      await handleAuthFailure(event, 'Token refresh failed');
    }
  }

  return session;
}
```

### 3. เพิ่ม Error Handling ที่ดีขึ้น

```typescript
// server/utils/auth.ts
async function handleAuthFailure(event: H3Event, reason: string): Promise<never> {
  console.log('Authentication failed:', reason);

  // ล้าง session
  clearUserSession(event);

  // ส่ง response พิเศษที่บอกให้ client redirect
  throw createError({
    statusCode: 401,
    statusMessage: 'Authentication failed',
    data: {
      reason,
      shouldRedirect: true,
      redirectTo: '/signin',
      timestamp: new Date().toISOString()
    }
  });
}
```

## 🧪 การทดสอบ

### Test Cases:
1. **Valid Token** - ควรผ่าน authentication
2. **Expired Token + Valid Refresh Token** - ควร refresh สำเร็จ
3. **Expired Token + Invalid Refresh Token** - ควร redirect ไป signin
4. **No Token** - ควร redirect ไป signin

### Expected Behavior:
- Token หมดอายุ → Auto refresh → Continue request
- Refresh ล้มเหลว → Clear session → Redirect signin
- ไม่มี token → Redirect signin

## 🚀 ผลลัพธ์ที่คาดหวัง

หลังจากแก้ไข:
1. ✅ Token หมดอายุจะ refresh อัตโนมัติ
2. ✅ API calls จะทำงานต่อเนื่อง
3. ✅ User ไม่ต้อง login ใหม่บ่อยๆ
4. ✅ ระบบ authentication มั่นคงและ seamless

## 📋 Checklist

- [ ] ปรับปรุง `getUserSession()` ให้รองรับ `allowExpired`
- [ ] ปรับปรุง `requireAuth()` ให้จัดการ expired token
- [ ] เพิ่ม error handling ที่ดีขึ้น
- [ ] ทดสอบ refresh token flow
- [ ] ทดสอบ error scenarios
- [ ] ตรวจสอบ security implications