# ระบบจัดการธีม (Themes)

ระบบจัดการธีมสำหรับปรับแต่งรูปลักษณ์และการออกแบบของเว็บไซต์

## โครงสร้างไฟล์

```
app/pages/dashboard/[siteId]/themes/
├── index.vue                    # หน้าแสดงรายการธีม
├── customize.vue                # หน้าปรับแต่งธีม
└── import.vue                   # หน้านำเข้าธีม
```

## คุณสมบัติหลัก

### 1. หน้าจัดการธีม (`index.vue`)
- แสดงธีมปัจจุบันที่ใช้งาน
- เลือกธีมจากธีมที่มีให้
- กรองธีมตามหมวดหมู่
- ค้นหาธีม
- แสดงสถิติธีม
- ตัวอย่างธีมแบบ Live Preview
- ส่งออกและนำเข้าธีม

### 2. หน้าปรับแต่งธีม (`customize.vue`)
- **สี (Colors)**: ปรับสีหลัก สีรอง สีเน้น สีพื้นหลัง สีข้อความ
- **ตัวอักษร (Typography)**: เลือกฟอนต์ ขนาดตัวอักษร น้ำหนักตัวอักษร
- **เลย์เอาต์ (Layout)**: ความกว้างคอนเทนเนอร์ ความสูงส่วนต่างๆ
- **คอมโพเนนต์ (Components)**: มุมโค้ง เงา ขอบ
- **ส่วนหัว (Header)**: สี ความสูง การแสดงผล
- **ส่วนท้าย (Footer)**: สี จำนวนคอลัมน์ การแสดงผล
- **แบนเนอร์ (Banner)**: ตำแหน่ง สี ข้อความ

### 3. หน้านำเข้าธีม (`import.vue`)
- นำเข้าจากไฟล์ JSON
- นำเข้าจาก URL
- เลือกจาก Marketplace
- ตัวอย่างธีมก่อนใช้งาน

## ประเภทธีม

### หมวดหมู่ธีม
1. **พื้นฐาน** (basic) - ธีมเริ่มต้นและง่ายๆ
2. **โมเดิร์น** (modern) - ธีมสไตล์ทันสมัย
3. **ธุรกิจ** (business) - ธีมสำหรับองค์กร
4. **มินิมอล** (minimal) - ธีมเรียบง่าย
5. **สร้างสรรค์** (creative) - ธีมสำหรับงานศิลปะ
6. **อีคอมเมิร์ซ** (ecommerce) - ธีมสำหรับร้านค้า

### ระดับธีม
- **ฟรี** - ใช้งานได้ทันที
- **พรีเมียม** - ต้องมีแพ็คเกจพรีเมียม

## การตั้งค่าธีม

### โครงสร้างการตั้งค่า
```typescript
interface ThemeSettings {
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    success: string;
    warning: string;
    error: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
  };
  
  typography: {
    fontFamily: string;
    headingFont: string;
    fontSize: {
      xs: string;
      sm: string;
      base: string;
      lg: string;
      xl: string;
      '2xl': string;
      '3xl': string;
      '4xl': string;
    };
    fontWeight: {
      normal: string;
      medium: string;
      semibold: string;
      bold: string;
    };
    lineHeight: {
      tight: string;
      normal: string;
      relaxed: string;
    };
  };
  
  layout: {
    containerWidth: string;
    headerHeight: string;
    footerHeight: string;
    sidebarWidth: string;
    spacing: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
      '2xl': string;
    };
  };
  
  components: {
    buttonRadius: string;
    cardRadius: string;
    inputRadius: string;
    imageRadius: string;
    shadowLevel: string;
    borderWidth: string;
  };
  
  header: {
    backgroundColor: string;
    textColor: string;
    height: string;
    sticky: boolean;
    showLogo: boolean;
    showNavigation: boolean;
    showSearch: boolean;
    showCart: boolean;
    logoPosition: string;
    navigationStyle: string;
  };
  
  footer: {
    backgroundColor: string;
    textColor: string;
    showSocialLinks: boolean;
    showNewsletter: boolean;
    showSitemap: boolean;
    columns: number;
  };
  
  banner: {
    show: boolean;
    position: string;
    backgroundColor: string;
    textColor: string;
    height: string;
    text: string;
  };
}
```

## คุณสมบัติเพิ่มเติม

### การปรับแต่งสี
- เลือกสีจาก Color Picker
- ชุดสีแนะนำ (Color Presets)
- ตัวอย่างสีแบบ Real-time
- รองรับ HEX, RGB, HSL

### การจัดการฟอนต์
- ฟอนต์ Web Safe
- Google Fonts
- ฟอนต์ไทย (Noto Sans Thai, Sarabun)
- ตัวอย่างฟอนต์แบบ Live Preview

### Live Preview
- ตัวอย่างแบบ Real-time
- รองรับ Responsive (Desktop, Tablet, Mobile)
- แสดงผลทุกส่วนของเว็บไซต์
- อัปเดตทันทีเมื่อเปลี่ยนการตั้งค่า

### การนำเข้าและส่งออก
- ส่งออกเป็นไฟล์ JSON
- นำเข้าจากไฟล์ JSON
- นำเข้าจาก URL
- Marketplace Integration

## การใช้งาน

### เลือกธีม
1. เข้าไปที่หน้า "ธีม"
2. เลือกธีมที่ต้องการ
3. คลิก "ดูตัวอย่าง" เพื่อดูตัวอย่าง
4. คลิก "เปิดใช้งาน" เพื่อใช้ธีม

### ปรับแต่งธีม
1. คลิก "ปรับแต่งธีม"
2. เลือกแท็บที่ต้องการแก้ไข
3. ปรับการตั้งค่าตามต้องการ
4. ดูตัวอย่างแบบ Real-time
5. คลิก "บันทึก" เพื่อใช้การตั้งค่า

### นำเข้าธีม
1. คลิก "นำเข้าธีม"
2. เลือกวิธีการนำเข้า:
   - อัปโหลดไฟล์ JSON
   - ใส่ URL ของธีม
   - เลือกจาก Marketplace
3. ดูตัวอย่างธีม
4. คลิก "ใช้ธีมนี้"

### ส่งออกธีม
1. คลิก "ส่งออกธีม"
2. ไฟล์ JSON จะถูกดาวน์โหลด
3. สามารถนำไปใช้กับเว็บไซต์อื่นได้

## การพัฒนาธีม

### โครงสร้างไฟล์ธีม
```json
{
  "id": "theme-id",
  "name": "ชื่อธีม",
  "description": "คำอธิบายธีม",
  "version": "1.0.0",
  "author": "ผู้สร้าง",
  "category": "modern",
  "isPremium": false,
  "preview": "url-to-preview-image",
  "settings": {
    // การตั้งค่าธีมตามโครงสร้างข้างต้น
  },
  "exportedAt": "2024-01-01T00:00:00Z"
}
```

### CSS Custom Properties
ระบบใช้ CSS Custom Properties สำหรับการปรับแต่ง:

```css
:root {
  --color-primary: #3B82F6;
  --color-secondary: #64748B;
  --color-accent: #F59E0B;
  --color-background: #FFFFFF;
  --color-surface: #F8FAFC;
  --color-text: #1F2937;
  --font-family: 'Inter';
  --font-size-base: 16px;
  --border-radius-button: 8px;
  --border-radius-card: 12px;
}
```

### การสร้างธีมใหม่
1. สร้างไฟล์ JSON ตามโครงสร้าง
2. กำหนดการตั้งค่าทั้งหมด
3. ทดสอบการทำงาน
4. ส่งออกและแชร์

## เทคโนโลยีที่ใช้

- **Vue 3** - Framework หลัก
- **Nuxt 3** - Meta framework
- **TypeScript** - Type safety
- **Tailwind CSS** - Utility-first CSS
- **CSS Custom Properties** - Dynamic theming
- **Nuxt UI** - UI Components

## การปรับแต่งขั้นสูง

### เพิ่มฟอนต์ใหม่
```typescript
const availableFonts = ref([
  // ฟอนต์ที่มีอยู่...
  { value: 'New Font', label: 'New Font', preview: 'New Font Preview' }
]);
```

### เพิ่มชุดสีใหม่
```typescript
const colorPresets = ref([
  // ชุดสีที่มีอยู่...
  {
    name: 'New Color Scheme',
    colors: {
      primary: '#FF6B6B',
      secondary: '#4ECDC4',
      accent: '#45B7D1'
    }
  }
]);
```

### เพิ่มระดับเงาใหม่
```typescript
const shadowLevels = ref([
  // ระดับเงาที่มีอยู่...
  { value: '2xl', label: 'เงาใหญ่พิเศษ', preview: 'shadow-2xl' }
]);
```

## การทดสอบ

### Test Cases ที่ควรมี
1. **การเลือกธีม** - ทดสอบการเปลี่ยนธีม
2. **การปรับแต่ง** - ทดสอบการเปลี่ยนการตั้งค่า
3. **Live Preview** - ทดสอบการแสดงผลแบบ Real-time
4. **การนำเข้า/ส่งออก** - ทดสอบการจัดการไฟล์
5. **Responsive** - ทดสอบการแสดงผลในหน้าจอต่างๆ
6. **Performance** - ทดสอบความเร็วในการโหลด

## การพัฒนาต่อ

### คุณสมบัติที่อาจเพิ่มในอนาคต
1. **Theme Builder** - เครื่องมือสร้างธีมแบบ Visual
2. **Animation Settings** - การตั้งค่า Animation และ Transition
3. **Advanced Layout** - Grid System และ Flexbox Settings
4. **Component Variants** - รูปแบบคอมโพเนนต์ที่หลากหลาย
5. **Dark Mode Toggle** - การสลับ Dark/Light Mode
6. **Theme Marketplace** - ร้านค้าธีมออนไลน์
7. **Version Control** - การจัดการเวอร์ชันของธีม
8. **Collaboration** - การทำงานร่วมกันในการออกแบบ

### API Endpoints ที่ควรมี
```
GET    /api/themes                # ดึงรายการธีม
POST   /api/themes                # สร้างธีมใหม่
GET    /api/themes/:id            # ดึงข้อมูลธีม
PUT    /api/themes/:id            # อัปเดตธีม
DELETE /api/themes/:id            # ลบธีม

GET    /api/themes/marketplace    # ดึงธีมจาก Marketplace
POST   /api/themes/import         # นำเข้าธีม
GET    /api/themes/export/:id     # ส่งออกธีม

PUT    /api/sites/:id/theme       # ใช้ธีมกับเว็บไซต์
GET    /api/sites/:id/theme       # ดึงธีมปัจจุบัน
```

## ข้อควรระวัง

### Performance
- ใช้ CSS Custom Properties แทน inline styles
- Lazy load ธีมที่ไม่ได้ใช้
- Optimize รูปภาพตัวอย่าง
- Cache การตั้งค่าธีม

### Accessibility
- ตรวจสอบ Color Contrast
- รองรับ Screen Reader
- Keyboard Navigation
- Focus Indicators

### Browser Compatibility
- ทดสอบใน Browser หลักๆ
- Fallback สำหรับ CSS ที่ไม่รองรับ
- Progressive Enhancement

## สรุป

ระบบจัดการธีมนี้ให้ความยืดหยุ่นในการปรับแต่งรูปลักษณ์ของเว็บไซต์ ด้วยเครื่องมือที่ครบครันและใช้งานง่าย ผู้ใช้สามารถสร้างเว็บไซต์ที่มีเอกลักษณ์เฉพาะตัวได้อย่างง่ายดาย

การออกแบบที่เน้นการใช้งานจริงและ Performance ทำให้ระบบสามารถรองรับการใช้งานในระดับ Production ได้อย่างมีประสิทธิภาพ