{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "predeploy": "bunx vercel deploy --prebuilt", "deploy": "bunx vercel deploy --prod", "dev:old": "nuxt dev", "dev": "node --max-old-space-size=8192 node_modules/.bin/nuxi dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "format": "bunx biome format --write", "lint": "bunx biome lint --write", "check": "bunx biome check --write", "clean": "rm -rf .nuxt node_modules package-lock.json pnpm-lock.yaml yarn.lock .output .nuxt", "kill": "pkill -f 'nuxt'"}, "dependencies": {"@biomejs/biome": "2.1.2", "@compodium/nuxt": "0.1.0-beta.11", "@nuxt/image": "1.10.0", "@nuxt/ui": "^3.3.0", "@nuxtjs/cloudinary": "^4.0.0", "@nuxtjs/i18n": "^10.0.3", "@nuxtjs/robots": "5.4.0", "@pinia/nuxt": "^0.11.2", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@vueuse/nuxt": "13.5.0", "cloudinary": "^2.7.0", "jose": "^6.0.12", "luxon": "^3.7.1", "nuxt": "^4.0.1", "nuxt-security": "2.3.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "typescript": "^5.8.3", "vercel": "^44.6.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify-json/emojione": "^1.2.1", "@iconify-json/lucide": "^1.2.58", "@iconify-json/simple-icons": "^1.2.44", "@iconify-json/heroicons": "^1.2.2", "@iconify-json/noto-v1": "^1.2.2", "@iconify-json/solar": "^1.2.2", "@types/luxon": "^3.6.2", "vite-plugin-inspect": "^11.3.0"}}