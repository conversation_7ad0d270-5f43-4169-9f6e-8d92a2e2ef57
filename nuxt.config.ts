// https://nuxt.com/docs/api/configuration/nuxt-config
import Inspect from 'vite-plugin-inspect';
import './server/process-handlers';

export default defineNuxtConfig({
  compatibilityDate: '2025-07-21', // ตั้งค่าเวลาการทำงานของ Nuxt ให้ตรงกับรุ่นที่ต้องการ
  devtools: { enabled: false },
  ssr: true,
  devServer: {
    port: Number(process.env.NUXT_PUBLIC_APP_PORT) || 8000,
    host: '0.0.0.0',
  },

  // Nitro configuration for better error handling
  nitro: {
    errorHandler: '~/error-handler.ts',
    experimental: {
      wasm: true,
    },
  },
  // Modules
  modules: [
    '@nuxt/ui',
    '@pinia/nuxt',
    'pinia-plugin-persistedstate/nuxt',
    '@compodium/nuxt',
    '@nuxtjs/i18n',
    // "nuxt-api-shield",
    '@nuxt/image',
    '@vueuse/nuxt',
    '@nuxtjs/robots',
    // 'nuxt-security',
    // '@regle/nuxt',
  ],

  // regle: {
  //   setupFile: '~/regle-config.ts'
  // },

  // security: {
  //   // ตัวเลือกการตั้งค่า เช่น
  //   rateLimiter: {
  //     tokensPerInterval: 10,
  //     interval: "day",
  //     // route: "/my-custom-route"
  //   }
  // },
  // nuxtApiShield: {
  //   limit: { max: 12, duration: 108, ban: 3600 },
  //   delayOnBan: true,
  //   errorMessage: "Too Many Requests",
  //   retryAfterHeader: false,
  //   log: { path: "logs", attempts: 100 },
  //   routes: ["/api/v2/"]
  // },
  // nitro: {
  //   storage: {
  //     shield: { driver: "memory" } // หรือใช้ redis
  //   },
  //   experimental: { tasks: true },
  //   scheduledTasks: { "*/15 * * * *": ["shield:clean"] }
  // },

  image: {
    cloudinary: {
      baseURL: `https://res.cloudinary.com/${process.env.NUXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/image/upload`,
    },
  },

  fonts: {
    families: [
      { name: 'Sarabun', provider: 'google' },
      { name: 'K2D', provider: 'google' },
    ],
  },

  ui: {
    theme: {
      colors: ['primary', 'secondary', 'tertiary', 'info', 'success', 'warning', 'error'],
      transitions: true,
    },
  },

  // Color mode configuration
  colorMode: {
    preference: 'system',
    fallback: 'light',
    classSuffix: '',
    storageKey: 'nuxt-color-mode',
  },
  // icon: {
  //   fallbackToApi: false,
  // },
  // CSS
  css: ['~/assets/css/main.css'],

  i18n: {
    vueI18n: './i18n.config.ts',
    // strategy: 'prefix_except_default',
    strategy: 'no_prefix',
    // defaultLocale: 'th',
    // baseUrl: process.env.NUXT_PUBLIC_SITE_URL,
    detectBrowserLanguage: {
      cookieKey: 'i18n_redirected',
      redirectOn: 'all',
      useCookie: true,
      alwaysRedirect: true,
    },
    locales: [
      {
        code: 'th',
        name: 'ไทย',
        language: 'th-TH',
        file: 'th-TH.json',
      },
      {
        code: 'en',
        name: 'English',
        language: 'en-US',
        file: 'en-US.json',
      },
      {
        code: 'lo',
        name: 'ລາວ',
        language: 'lo-LA',
        file: 'lo-LA.json',
      },
    ],
  },

  // Components configuration
  // components: [
  //   {
  //     path: '~/components',
  //     pathPrefix: false,
  //   },
  // ],

  // Runtime config
  runtimeConfig: {
    // Private keys (server-side)
    csrfToken: process.env.NUXT_PUBLIC_CSRF_TOKEN,
    jwtPublicKey: process.env.NUXT_JWT_PUBLIC_KEY,
    jwtSecret: process.env.NUXT_PRIVATE_JWT_SECRET,

    cloudinaryApiSecret: process.env.NUXT_PRIVATE_CLOUDINARY_API_SECRET,
    cloudinaryApiKey: process.env.NUXT_PUBLIC_CLOUDINARY_API_KEY,

    // Public keys (client-side)
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:5000',
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:8000',
      nodeEnv: process.env.NUXT_NODE_ENV,
      apiVersion: process.env.NUXT_PUBLIC_API_VERSION || '/v1',
      isProduction: process.env.NODE_ENV === 'production',
      isDevelopment: process.env.NODE_ENV === 'development',
      appPort: process.env.NUXT_PUBLIC_APP_PORT || '8000',

      cloudinaryCloudName: process.env.NUXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
    },
  },

  routeRules: {
    '/': { prerender: true },
    '/dashboard/**': { ssr: false },
    '/api/**': { cors: true },
  },
  // imports: {
  //   dirs: ['stores', 'composables', 'utils', 'types'],
  //   imports: [],
  // },
  vite: {
    plugins: [Inspect()],
    server: {
      watch: {
        usePolling: true,
        interval: 100,
      },
      // ปรับการตั้งค่า HMR
      hmr: {
        protocol: 'ws',
        host: 'localhost',
        // port: 24678,
        // clientPort: 24678,
        // timeout: 60000,
      } 
    },
    build: {
      sourcemap: false,
    },
    optimizeDeps: {
      include: ['jose', '@nuxt/ui/locale'],
    },
  },
  // hooks: {
  //   'vite:extendConfig' (viteInlineConfig, env) {
  //     viteInlineConfig.server = {
  //       ...viteInlineConfig.server,
  //       hmr: {
  //         protocol: 'ws',
  //         host: 'localhost',
  //       },
  //     }
  //   },
  // },
  sourcemap: {
    server: false,
    client: false,
  },

  // App config
  app: {
    head: {
      title: 'เรื่องเว็บไซต์ เป็นเรื่องง่าย',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        {
          name: 'description',
          content: 'ระบบเช่าใช้งานเว็บไซต์ครบวงจร',
        },
      ],
      link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }],
    },
  },
  telemetry: { enabled: false }, // ปิดการส่งข้อมูลให้ Nuxt
  appConfig: {
    buildDate: new Date().toISOString(),
  },
  experimental: {
    // componentIslands: true,
    // asyncContext: true,
    // renderJsonPayloads: true,
    // sharedPrerenderData: true,
    // typedPages: true,
    // viewTransition: true,
    // buildCache: true,
  },
});
