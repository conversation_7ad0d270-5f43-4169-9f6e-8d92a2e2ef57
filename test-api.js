// ทดสอบ API endpoint
const testAPI = async () => {
  try {
    console.log('=== ทดสอบ API Endpoint ===');

    // ทดสอบ Public Image
    const publicResponse = await fetch('http://localhost:8000/api/signed-url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        publicId: 'sample',
        restricted: false,
      }),
    });

    const publicData = await publicResponse.json();
    console.log('Public Response:', publicData);

    // ทดสอบ Restricted Image
    const restrictedResponse = await fetch('http://localhost:8000/api/signed-url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        publicId: 'wrqxdnopfn29xi4kamda',
        restricted: true,
      }),
    });

    const restrictedData = await restrictedResponse.json();
    console.log('Restricted Response:', restrictedData);
  } catch (error) {
    console.error('Test Error:', error);
  }
};

testAPI();
