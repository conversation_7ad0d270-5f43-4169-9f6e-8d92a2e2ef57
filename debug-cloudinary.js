// Debug Cloudinary URL generation with expiration
import { v2 as cloudinary } from 'cloudinary';

// ตั้งค่า Cloudinary
cloudinary.config({
  cloud_name: 'dsoy8tgfc',
  api_key: '139741931552624',
  api_secret: 'LR7ShBIjTR4kjZajyOl-geawG4s',
});

console.log('=== Debug Cloudinary URLs with Expiration ===');
const publicId = 'wrqxdnopfn29xi4kamda';

// สร้าง expiration time (1 ชั่วโมงจากตอนนี้)
const expiration = Math.round(new Date().getTime() / 1000) + 60 * 60;
console.log('Expiration timestamp:', expiration);
console.log('Expiration date:', new Date(expiration * 1000));

console.log('\n1. Signed URL with expiration:');
const signedWithExpiration = cloudinary.url(publicId, {
  secure: true,
  sign_url: true,
  type: 'upload',
  resource_type: 'image',
  expires_at: expiration,
  quality: 'auto',
  fetch_format: 'auto',
});
console.log(signedWithExpiration);

console.log('\n2. Simple signed URL:');
const simpleSigned = cloudinary.url(publicId, {
  secure: true,
  sign_url: true,
  type: 'upload',
  resource_type: 'image',
});
console.log(simpleSigned);

console.log('\n3. ทดสอบ fetch URLs:');

// ทดสอบ fetch แต่ละ URL
const testUrls = [
  { name: 'Signed with Expiration', url: signedWithExpiration },
  { name: 'Simple Signed', url: simpleSigned },
];

for (const test of testUrls) {
  try {
    console.log(`\nTesting ${test.name}:`);
    console.log(`URL: ${test.url}`);
    const response = await fetch(test.url, { method: 'HEAD' });
    console.log(`Status: ${response.status} ${response.statusText}`);

    if (response.status === 200) {
      console.log('✅ SUCCESS - This URL works!');
    } else {
      console.log('❌ FAILED');
    }
  } catch (error) {
    console.log(`Error: ${error.message}`);
  }
}
